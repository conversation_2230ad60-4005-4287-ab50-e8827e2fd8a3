import { fileURLToPath, URL } from "node:url";
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: {
          "primary-color": "rgb(24, 144, 255)", // 全局主色
          "secondary-color": "rgba(34, 34, 34, 0.65)",
          "primary-1": "#f5f5f5", //全局hover背景色
          "link-color": "#4E729F", //链接颜色
          "body-background": "#f5f5f5",
          "success-color": "#33BE4F", // 成功色
          "error-color": "#EA0C28", // 错误色
          "primary-white": "#ffffff", // 白色
        },
        javascriptEnabled: true,
      },
    },
  },
  optimizeDeps: {
    exclude: ['iconfont.css', 'iconfont.woff2'],
  },
  build: {
    cssMinify: false,
  },
  server: {
    open: true,
    port: 3005,
    host: "0.0.0.0",
  },
});
