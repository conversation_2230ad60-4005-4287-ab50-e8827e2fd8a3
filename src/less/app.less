@import url(./tabs.less);
@import url(./list.less);
@import url(./table.less);
@import url(./antd.less);

body {
  color: theme("colors.title");
}

p {
  margin: 0;
}

hr {
  border-bottom: none;
  border-left: none;
  border-right: none;
  border-top: 1px solid #e6e6e8;
  margin: 0;
}

img {
  object-fit: cover;
}

.flex {
  display: flex;
}

.content-area {
  width: 90%;
  margin: 0 auto;
  position: relative;
}
// @media screen and (max-width: 1520px) {
//   .content-area {
//     width: 1400px;
//   }
// }

// @media screen and (max-width: 1430px) {
//   .content-area {
//     width: 1300px;
//   }
// }
// @media screen and (max-width: 1366px) {
//   .content-area {
//     width: 1100px;
//   }
// }
// @media screen and (max-width: 1125px) {
//   .content-area {
//     width: 1000px;
//   }
// }
// @media screen and (min-width: 1921px) {
//   .content-area {
//     width: 1440px;
//   }
// }

.message {
  background: #f3f5f7;
  border-radius: 4px;
  padding: 12px;
  margin-left: 40px;
  margin-top: 8px;
  color: #222222;
  // line-height: 24px;
  margin-bottom: 12px;

  .content {
    font-weight: 600;
    color: #222222;
  }

  hr {
    margin: 8px 0;
  }

  .option {
    display: inline;
    line-height: 24px;
    font-size: 14px;

    span:first-child {
      color: rgba(34, 34, 34, 0.8);
      margin-right: 24px;
    }

    :deep(.ant-btn) {
      padding: 4px 12px;
    }
  }
}

.cursor {
  cursor: pointer;
}

.primary-color {
  color: @primary-color;
}

.secondary-color {
  color: @secondary-color;
}

.success-color {
  color: @success-color;
}

.error-color {
  color: @error-color;
}

.icon {
  width: 16px;
  margin-right: 8px;
}

.drawer-option {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 4px 4px;
}

.ant-message .anticon {
  vertical-align: text-bottom;
  top: 0;
    vertical-align: -3px !important;
}

// 菜单样式
.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: #fff;
}

.ant-drawer-body {
  padding-bottom: 53px;
}

.money {
  color: #fe6700;
}

.ant-btn {
  i {
    margin-right: 8px;
    vertical-align: bottom;
  }

  img {
    margin-top: -0.125rem;
  }
}

.ant-layout {
  background: @body-background;
}

// .ant-layout-content {
//   min-height: calc(~"100vh - 114px");
// }
.ant-layout-footer {
  padding: 23px 0;
  color: #999;
  background: @body-background;

  a {
    color: #999;
  }
}

// .user-search {
//   .ant-input-search {
//     border: none;
//     border-bottom: 1px solid #f3f5f7;
//     padding-bottom: 16px;
//     .ant-input-search-icon {
//       font-size: 20px;
//     }
//   }
//   .ant-input-affix-wrapper-focused {
//     box-shadow: none;
//   }
// }
.ant-table-row {

  // color: rgba(34, 34, 34, 0.8);
  .ant-badge-status-text {
    color: rgba(34, 34, 34, 0.8);
  }
}

.overflow {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}

.mutiLineOverflow {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.login-bar {
  // display: flex;
  // justify-content: space-between;
  // align-items: center;
  position: absolute;
  bottom: 12px;
  box-sizing: border-box;
  left: 12px;
  right: 12px;

  .login-policy {
    font-size: 12px;

    .policy-text {
      color: #a0a0a0;
    }
  }
}

//表单报错信息汇总
.mw-form {
  min-height: 360px;
  position: relative;

  .ant-form-item-has-error+.ant-form-item-has-error {
    .ant-form-item-explain.ant-form-item-explain-error {
      display: none;
    }
  }

  .ant-show-help-leave {
    display: none;
  }

  .ant-form-item-explain.ant-form-item-explain-error {
    position: absolute;
    top: 104px;
  }

  .ant-col {
    position: static;
  }

  .ant-form-item-with-help {
    margin-bottom: 16px;
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .header {
    color: #595959;
    margin-bottom: 12px;
  }
}



.name-round {
  width: 80px;
  height: 80px;
  background: rgb(242, 219, 222);
  font-size: 30px;
  color: #ea0c28;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 24px;
  font-size: 20px;
}

.ant-input-group.ant-input-group-compact>*:last-child.ant-select {
  //为了解决：联合搜索框，下拉框右边会有个黑线
  border-right: none;
}

.bg-white {
  background-color: #fff;
  overflow: hidden;
}

//time-line样式
// .ant-timeline-item {
//   padding-top: 5px;
//   padding-bottom: 10px;
//   color: #595959;
// }
// .ant-timeline-item:first-child {
//   color: #141414;
// }
// .ant-timeline-item-head {
//   background: #d9d9d9;
//   border: 2px solid #ffffff;
//   width: 12px;
//   height: 12px;
// }
// .ant-timeline-item:first-child .ant-timeline-item-head-blue {
//   background: #595959;
//   border: 2px solid #d9d9d9;
// }
// .ant-timeline-item-tail {
//   left: 5px;
//   border-style: dashed;
// }
// .allActive{
//   .ant-timeline-item{
//     color: #141414;
//   }
//   .ant-timeline-item-head {
//     background: #595959;
//     border: 2px solid #d9d9d9;
//   }
// }
.ant-calendar-selected-day .ant-calendar-date {
  color: #fff;
}

.scrollbar,
.ant-table-body {
  overflow-y: auto;
  overflow-x: auto;

  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 10px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 10px;
  }

  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    background-color: #d9d9d9;
  }

  &::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    background: #fff;
    border-radius: 10px;
  }
}

.text-white {
  color: #ffffff;
}

// 标题样式
.title {
  font-size: 16px;
  line-height: 24px;
  color: theme("colors.title");
}

.bg-linear-gray {
  background: linear-gradient(180deg, #FFFFFF 0%, #F5F7F7 100%);
}

:deep(.ant-table-thead > tr > th) {
  color: theme("colors.secondar-text");
}

.ant-form-item {
  margin-bottom: 16px;
}

.ant-form-item-with-help {
  margin-bottom: 0 !important;
}

.ant-form-item-explain.ant-form-item-explain-connected {
  min-height: 16px;
  height: 16px;
  font-size: 12px;
}

:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  background: #fff;
}

:deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
  background: #fff;
}
:deep(.ant-message-custom-content .ant-message-error) {
 display: flex;
}

