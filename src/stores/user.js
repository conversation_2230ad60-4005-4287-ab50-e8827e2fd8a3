import { reactive, computed, toRefs } from "vue";
import { defineStore } from "pinia";
import { setToken, getToken, removeToken, removeTenant } from "@/utils/auth.js";
import { loginApi, getUserInfo, logoutApi } from "@/api/login.js";
import router from "@/router";

export const useUserStore = defineStore("user", () => {
  const state = reactive({
    roles: [],
    permissions: [],
    name: "",
    avatar: "",
    token: getToken(),
    tenantid: "",
    tenantName: "",
    cacheObject: true,
    paramsBomNo: undefined,
    user: {},
  });
  const setLoginData = (result, redirect) => {
    let { access_token, tenant_id, expires_in } = result.data;
    var expires = new Date(new Date().getTime() + expires_in * 60 * 1000);
    setToken(access_token, expires);
    setTimeout(() => {
      const channel = new BroadcastChannel('auth-channel');
      channel.postMessage({
        type: 'force-refresh',
        token: access_token, // 发送新的token
        tenant_id: tenant_id
      });
      channel.close();  // 发送后关闭channel
    }, 100);
    router.replace(redirect || "/");
  };
  const login = async (param, smsType) => {
    let result = await loginApi(param, smsType);
    setLoginData(result);
  };

  const LogOut = () => {
    return new Promise((resolve, reject) => {
      logoutApi(state.token)
        .then(() => {
          state.token = "";
          state.roles = [];
          state.permissions = [];
          state.tenantid = "";
          state.cacheObject = false;
          removeToken();
          removeTenant();
          resolve();
        })
        .catch((error) => {
          reject(error);
          console.log(error);
        });
    });
  };
  const getInfo = async () => {
    let res = await getUserInfo();
    const { user, roles, permissions, tenantName, cacheObject } = res;
    // const avatar =
    //   user.avatar == '' || user.avatar == null
    //     ? require('@/assets/images/profile.jpg')
    //     : user.avatar

    // getUserInfo
    localStorage.setItem("perms", JSON.stringify(permissions));
    localStorage.setItem("user", JSON.stringify(user));
    // localStorage.setItem("perms",permissions)
    if (roles && roles.length > 0) {
      // 验证返回的roles是否是一个非空数组
      state.roles = roles;
      state.permissions = permissions;
      state.user = user;
    } else {
      state.roles = ["ROLE_DEFAULT"];
    }
    state.name = user.nickName;
    state.tenantName = tenantName;
    state.cacheObject = cacheObject;
    // commit('SET_AVATAR', avatar)
  };
  const setParamsBomNo = (val) => {
    state.paramsBomNo = val;
  };

  const bind = () => {
    app.directive("permission", {
      beforeMount(el, binding) { },
    });
  };

  return {
    setLoginData,
    login,
    LogOut,
    getInfo,
    setParamsBomNo,
    ...toRefs(state),
    bind,
  };
});
