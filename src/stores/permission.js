import { defineStore } from "pinia";
import auth from "@/plugins/auth";
import router, { constantRoutes, dynamicRoutes } from "@/router";
import { useRoute, useRouter } from "vue-router";
import { getRouters } from "@/api/menu";
import Layout from "@/views/layout/index.vue";
// import ParentView from '@/components/ParentView'
// import InnerLink from '@/layout/components/InnerLink'
import { toRefs, reactive, computed } from "vue";

//引入所有views下.vue文件
const modules = import.meta.glob("../views/**/**.vue");

export const usePermissionStore = defineStore("permission", () => {
  const state = reactive({
    routes: [],
    addRoutes: [],
    defaultRoutes: [],
    topbarRouters: [],
    sidebarRouters: [],
  });

  const GenerateRoutes = () => {
    return new Promise((resolve) => {
      // 向后端请求路由数据
      getRouters().then((res) => {
        const sdata = JSON.parse(JSON.stringify(res.data));
        const rdata = JSON.parse(JSON.stringify(res.data));
        const sidebarRoutes = filterAsyncRouter(sdata);
        const rewriteRoutes = filterAsyncRouter(rdata, false, true);
        const asyncRoutes = filterDynamicRoutes(dynamicRoutes);
        rewriteRoutes.push({
          path: "/:catchAll(.*)*",
          redirect: "/404",
          hidden: true,
        });
        asyncRoutes.forEach((route) => {
          router.addRoute(route);
        });
        state.addRoutes = rewriteRoutes;
        let routes = constantRoutes.concat(rewriteRoutes);
        state.routes = routes;
        state.sidebarRouters = routes;
        state.defaultRoutes = routes;
        state.topbarRouters = sidebarRoutes;
        resolve(rewriteRoutes);

      });
    });
  };
  const routerInfo = useRouter();
  const currentRouteInfo = computed(() => {
    const currentName = routerInfo.currentRoute?.value.name;
    let currentRouter = state.sidebarRouters.find((r) => r.name == currentName,);
    return currentRouter;
  });
  return { GenerateRoutes, ...toRefs(state), currentRouteInfo };
});
// 遍历后台传来的路由字符串，转换为组件对象d
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
  return asyncRouterMap.filter((route) => {
    if (type && route.children) {
      route.children = filterChildren(route.children);
    }
    if (route.component) {
      // Layout ParentView 组件特殊处理
      if (route.component === "Layout") {
        route.component = Layout;
        // } else if (route.component === 'ParentView') {
        //   route.component = ParentView
        // } else if (route.component === 'InnerLink') {
        //   route.component = InnerLink
      } else {
        // route.component = loadView(route.component);
        route.component = modules[`../views/${route.component}.vue`];
      }
    }
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route, type);
    } else {
      delete route["children"];
      delete route["redirect"];
    }
    return true;
  });
}

function filterChildren(childrenMap, lastRouter = false) {
  var children = [];
  childrenMap.forEach((el, index) => {
    if (el.children && el.children.length) {
      if (el.component === "ParentView" && !lastRouter) {
        el.children.forEach((c) => {
          c.path = el.path + "/" + c.path;
          if (c.children && c.children.length) {
            children = children.concat(filterChildren(c.children, c));
            return;
          }
          children.push(c);
        });
        return;
      }
    }
    if (lastRouter) {
      el.path = lastRouter.path + "/" + el.path;
    }
    children = children.concat(el);
  });
  return children;
}

// 动态路由遍历，验证是否具备权限
export function filterDynamicRoutes(routes) {
  const res = [];
  routes.forEach((route) => {
    if (route.permissions) {
      if (auth.hasPermiOr(route.permissions)) {
        res.push(route);
      }
    } else if (route.roles) {
      if (auth.hasRoleOr(route.roles)) {
        res.push(route);
      }
    }
  });
  return res;
}

export const loadView = (view) => {
  return async () => await import(`./src/views/${view}.vue`);

};
