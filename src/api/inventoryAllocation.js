import { Post, Get } from "@/common/request";
export function allocationPage(urlParam, bodyParam) {
  return Get({
    url: "/wms/allocation/list",
    urlParam,
    bodyParam,
  });
}
export function allocationAdd(bodyParam) {
  return Post({
    url: "/wms/allocation/add",
    bodyParam,
  });
}
// 库存仓库集合-用于调拨明细
export function warehouseCollect(bodyParam) {
  return Post({
    url: "/wms/warehouse/listByMaterial",
    bodyParam,
  });
}
export function menuListCollect(bodyParam, urlParam) {
  return Post({
    url: "/wms/menu/list",
    bodyParam,
    urlParam
  });
}

