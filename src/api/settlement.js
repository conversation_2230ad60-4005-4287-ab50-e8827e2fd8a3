import { Post, Get } from "@/common/request";

let baseUrl = "/wms/settlement/";
export function page(urlParam, bodyParam) {
  return Post({
    url: baseUrl + "page",
    urlParam,
    bodyParam,
  });
}
export function uploadInvoiceFile(bodyParam) {
  return Post({
    url: baseUrl + "uploadInvoiceFile",
    bodyParam,
  });
}
export function settlemenupdatePurchaseSettlement(bodyParam) {
  return Post({
    url: "/wms/settlement/updatePurchaseSettlement",
    bodyParam,
  });
}

// 采购发票期初
export function addInitialTreatment(bodyParam) {
  return Post({
    url: "/wms/settlement/initialTreatment",
    bodyParam: bodyParam,
  });
}

export function getInfo(id) {
  return Get({
    url: baseUrl + "getInfo",
    urlParam: { id },
  });
}
// 采购通过订单中所有得供应商信息
export function CgSelectAllSupplier(bodyParam) {
  return Post({
    url: "/wms/supplier/list",
    bodyParam: {},
  });
}
// oa审批

export function oaDetail(urlParam) {
  return Get({
    url: baseUrl + "oaDetail",
    urlParam: urlParam,
  });
}
// 详情
export function getDetail(urlParam) {
  return Get({
    url: "/wms/settlement/getDetail",
    urlParam: urlParam,
  });
}
// 详情
export function getDetailV1(urlParam) {
  return Get({
    url: "/wms/settlement/getDetailV1",
    urlParam: urlParam,
  });
}
// 采供应商--下面的结算明细列表---普通
export function getUnSettlementBySupplierIdV1(urlParam) {
  return Get({
    url: "/wms/order/getUnSettlementBySupplierIdV1",
    urlParam: urlParam,
  });
}
export function getUnSettlementBySupplierIdV2(urlParam) {
  return Get({
    url: "/wms/order/getUnSettlementBySupplierIdV2",
    urlParam: urlParam,
  });
}
// 采供应商--下面的结算明细列表---期初
export function settlementGetUnSettlementBySupplierIdV1(urlParam) {
  return Get({
    url: "/wms/settlement/getUnSettlementBySupplierIdV1",
    urlParam: urlParam,
  });
}
export function addPurchaseSettlement(bodyParam) {
  return Post({
    url: "/wms/settlement/addPurchaseSettlement",
    bodyParam: bodyParam,
  });
}
// 导出
export function settlementExportExcel(param, urlParam) {
  return Post({
    url: "/wms/settlement/exportExcel",
    bodyParam: param,
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}

// 全局导出
export function setTleInfoExportExce(urlParam) {
  return Post({
    url: "/wms/settlement/infoExportExcel",
    urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}
