import { Post, Get, Put } from "@/common/request";

let baseUrl = "/system/dept";
export function list(param) {
  return Get({
    url: baseUrl + "/list",
    urlParam: param,
  });
}
export function add(bodyParam) {
  return Post({
    url: baseUrl,
    bodyParam,
  });
}
export function update(bodyParam) {
  return Put({
    url: baseUrl,
    bodyParam,
  });
}
export function getInfo(id) {
  return Get({
    url: baseUrl + "/" + id,
  });
}
