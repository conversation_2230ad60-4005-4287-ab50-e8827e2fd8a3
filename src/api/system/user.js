import { parseStrEmpty } from "@/utils/util.js";
import { Post, Get, Put, Delete } from "@/common/request";

// 查询用户列表
export function list(query) {
  return Get({
    url: "/system/user/list",
    urlParam: query,
  });
}
export function getAllUser() {
  return Get({
    url: "/system/user/list",
    urlParam: { pageNum: 1, pageSize: 10000000 },
  });
}

// 查询用户详细
export function getUser(userId) {
  return Get({
    url: "/system/user/" + parseStrEmpty(userId),
  });
}

// 新增用户
export function addUser(data) {
  return Post({
    url: "/system/user",
    bodyParam: data,
  });
}

// 修改用户
export function updateUser(data) {
  return Put({
    url: "/system/user",
    bodyParam: data,
  });
}

// 删除用户
export function delUser(userId) {
  return Delete({
    url: "/system/user/" + userId,
  });
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password,
  };
  return Put({
    url: "/system/user/resetPwd",
    bodyParam: data,
  });
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status,
  };
  return Put({
    url: "/system/user/changeStatus",
    method: "put",
    bodyParam: data,
  });
}

// 查询用户个人信息
export function getUserProfile() {
  return Get({
    url: "/system/user/profile",
  });
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return Put({
    url: "/system/user/profile",
    method: "put",
    bodyParam: data,
  });
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword,
  };
  return Put({
    url: "/system/user/profile/updatePwd",
    bodyParam: data,
  });
}

// 用户头像上传
export function uploadAvatar(data) {
  return Post({
    url: "/system/user/profile/avatar",
    bodyParam: data,
  });
}

// 查询授权角色
export function getAuthRole(userId) {
  return Get({
    url: "/system/user/authRole/" + userId,
  });
}

// 保存授权角色
export function updateAuthRole(data) {
  return Put({
    url: "/system/user/authRole",
    method: "put",
    urlParam: data,
  });
}

// 查询部门下拉树结构
export function deptTreeSelect() {
  return Get({
    url: "/system/user/deptTree",
  });
}

// 查询全部部门数据
export function alldeptTreeSelect() {
  return Get({
    url: "/system/user/deptTreeV1",
  });
}