import { Post, Get, Put, Delete } from "@/common/request";

// 查询菜单列表
export function listMenu(query) {
  return Get({
    url: "/system/menu/list",
    urlParam: query,
  });
}

// 查询菜单（精简)列表
export function listSimpleMenus() {
  return Get({
    url: "/system/menu/list-all-simple",
  });
}

// 查询菜单详细
export function getMenu(menuId) {
  return Get({
    url: "/system/menu/" + menuId,
  });
}

// 查询菜单下拉树结构
export function treeselect() {
  return Get({
    url: "/system/menu/treeselect",
  });
}

// 根据角色ID查询菜单下拉树结构
export function roleMenuTreeselect(roleId) {
  return Get({
    url: "/system/menu/roleMenuTreeselect/" + roleId,
  });
}

// 新增菜单
export function addMenu(data) {
  return Post({
    url: "/system/menu",
    bodyParam: data,
  });
}

// 修改菜单
export function updateMenu(data) {
  return Put({
    url: "/system/menu",
    bodyParam: data,
  });
}

// 删除菜单
export function delMenu(menuId) {
  return Delete({
    url: "/system/menu/" + menuId,
  });
}
