import { Post, Get, Put, Delete } from "@/common/request";

// 查询角色列表
export function list(query) {
  return Get({
    url: "/system/role/list",
    urlParam: query,
  });
}

// 查询角色详细
export function getRole(roleId) {
  return Get({
    url: "/system/role/" + roleId,
  });
}

// 新增角色
export function add(data) {
  return Post({
    url: "/system/role",
    bodyParam: data,
  });
}

// 修改角色
export function updateRole(data) {
  return Put({
    url: "/system/role",
    bodyParam: data,
  });
}

// 角色数据权限
export function dataScope(data) {
  return Put({
    url: "/system/role/dataScope",
    bodyParam: data,
  });
}

// 角色状态修改
export function changeRoleStatus(roleId, status) {
  const data = {
    roleId,
    status,
  };
  return Put({
    url: "/system/role/changeStatus",
    bodyParam: data,
  });
}

// 删除角色
export function delRole(roleId) {
  return Delete({
    url: "/system/role/" + roleId,
  });
}

// 查询角色已授权用户列表
export function allocatedUserList(query) {
  return Get({
    url: "/system/role/authUser/allocatedList",
    urlParam: query,
  });
}

// 查询角色未授权用户列表
export function unallocatedUserList(query) {
  return Get({
    url: "/system/role/authUser/unallocatedList",
    urlParam: query,
  });
}

// 取消用户授权角色
export function authUserCancel(data) {
  return Put({
    url: "/system/role/authUser/cancel",
    bodyParam: data,
  });
}

// 批量取消用户授权角色
export function authUserCancelAll(data) {
  return Put({
    url: "/system/role/authUser/cancelAll",
    bodyParam: data,
  });
}

// 授权用户选择
export function authUserSelectAll(data) {
  return Put({
    url: "/system/role/authUser/selectAll",
    bodyParam: data,
  });
}

// 根据角色ID查询部门树结构
export function deptTreeSelect(roleId) {
  return Get({
    url: "/system/role/deptTree/" + roleId,
  });
}
