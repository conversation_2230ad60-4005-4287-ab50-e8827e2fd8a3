import { Post, Get } from "@/common/request";
let baseUrl = "/wms/contract/";
// 合同评审+添加
export function postContractReviewAdd(bodyParam) {
  return Post({
    url: baseUrl + "add",
    bodyParam,
  });
}
// 合同评审+列表
export function getContractReviewList(urlParam, bodyParam) {
  return Post({
    url: baseUrl + "page",
    urlParam,
    bodyParam: bodyParam,
  });
}
// 合同评审+详情
export function getContractReviewDetail(urlParam) {
  return Get({
    url: baseUrl + "detail",
    urlParam,
  });
}
// 合同评审+编辑
export function postContractReviewUpdate(bodyParam) {
  return Post({
    url: baseUrl + "update",
    bodyParam,
  });
}
// 合同评审+不分页列表（也是关联-销售合同接口）
export function postNoContractReviewList(bodyParam) {
  return Post({
    url: baseUrl + "list",
    bodyParam,
  });
}
// 合同评审+作废
export function getCancellation(urlParam) {
  return Get({
    url: baseUrl + "/cancellation",
    urlParam,
  });
}
// 合同评审+导出
export function contractExportExcel(bodyParam) {
  return Post({
    url: "/wms/contract/exportExcel",
    bodyParam: bodyParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}

// 合同评审+客户代码下的业务人
export function getCustomerManager(urlParam) {
  return Get({
    url: "/wms/contract/getCustomerManager",
    urlParam,
  });
}

const baseUrlOrder = "/wms/marketOrder/";

// 销售订单+添加
export function postSaleOrderAdd(bodyParam) {
  return Post({
    url: baseUrlOrder + "add",
    bodyParam,
  });
}
export function postSaleOrderUpdate(bodyParam) {
  return Post({
    url: baseUrlOrder + "update",
    bodyParam,
  });
}
//销售订单+列表--分页
export function postSaleOrderList(urlParam, bodyParam) {
  return Post({
    url: baseUrlOrder + "page",
    urlParam,
    bodyParam: bodyParam,
  });
}
//销售订单+列表--不分页
export function postNoSaleOrderList(bodyParam) {
  return Post({
    url: baseUrlOrder + "list",
    bodyParam: bodyParam,
  });
}
//销售订单+列表+单独更新的列表
export function marketOrderListV2(bodyParam) {
  return Post({
    url: "wms/marketOrder/listV2",
    bodyParam: bodyParam,
  });
}
// 订单退货中，审批通过的订单
export function marketOrderListV3(bodyParam) {
  return Post({
    url: "wms/marketOrder/listV3",
    bodyParam: bodyParam,
  });
}
// 销售订单详情
export function getSaleOrderDetail(urlParam) {
  return Get({
    url: baseUrlOrder + "getInfo",
    urlParam: urlParam,
  });
}

// 销售订单详情--id
export function getSelectProductListByOrder(urlParam) {
  return Get({
    url: baseUrlOrder + "selectMaterialListByOrderId",
    urlParam: urlParam,
  });
}
// 取消销售订单
export function postCancel(bodyParam) {
  return Post({
    url: "/wms/marketOrder/cancel",
    bodyParam,
  });
}
// 销售订单-导出Excel
export function marketOrderExportExcel(bodyParam) {
  return Post({
    url: "/wms/marketOrder/exportExcel ",
    bodyParam: bodyParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}
// 查询未退货的销售订单
export function selectUnReturn(urlParam) {
  return Get({
    url: "/wms/marketReturn/orders",
    urlParam,
  });
}

// 退货--销售订单下的客户
export function returnGetCustomerByOrderId(urlParam) {
  return Get({
    url: "/wms/marketReturn/getCustomerByOrderId",
    urlParam,
  });
}
//销售订单修改bom
export function updateBom(bodyParam) {
  return Post({
    url: "/wms/material/updateBom",
    bodyParam,
  });
}

// 销售发货
const baseUrlShipping = "/wms/shippingRecord/";

// 销售发货订单+添加
export function postShippingAdd(bodyParam) {
  return Post({
    url: baseUrlShipping + "add",
    bodyParam,
  });
}

// 销售发货-物流信息
export function getChinaCompanyList(urlParam) {
  return Get({
    url: "/wms/shippingRecord/getChinaExpressCompanyList",
    urlParam: urlParam,
  });
}
// 销售发货订单+列表--分页
export function postShippingList(urlParam, bodyParam) {
  return Post({
    url: baseUrlShipping + "page",
    urlParam,
    bodyParam: bodyParam,
  });
}
// 销售发货订单+详情
export function getShippingDetail(urlParam) {
  return Get({
    url: baseUrlShipping + "getInfo",
    urlParam,
  });
}
// 销售产品下的仓库
export function listByMaterialIgnoreNoStockV1(bodyParam) {
  return Post({
    url: "/wms/warehouse/listByMaterialIgnoreNoStockV1",
    bodyParam,
  });
}

// 销售订单列表（发货订单使用）
export function getSelectUnshippedlist(urlParam) {
  return Get({
    url: "/wms/marketOrder/selectUnshippedlist",
    urlParam,
  });
}

// 销售发货+导出
export function shippingRecordExportPdf(urlParam) {
  return Post({
    url: "/wms/shippingRecord/exportPdf",
    urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}

// 订单发票+选择客户下面的订单
export function getCustomerNotInvoiceProduct(urlParam) {
  return Get({
    url: "/wms/invoice/getCustomerNotInvoiceProduct",
    urlParam,
  });
}
// 订单发票+期初保存
export function initialTreatment(bodyParam) {
  return Post({
    url: "/wms/invoice/initialTreatment",
    bodyParam,
  });
}

// 销售发票+新增
export function postInvoiceAdd(bodyParam) {
  return Post({
    url: "/wms/invoice/save",
    bodyParam,
  });
}

// 销售发票列表（分页）
export function postInvoicePage(urlParam, bodyParam) {
  return Post({
    url: "/wms/invoice/page",
    urlParam,
    bodyParam,
  });
}
// 销售发票-详情
export function postInvoiceDetail(urlParam) {
  return Get({
    url: "/wms/invoice/detail",
    urlParam,
  });
}

// 销售发票-导出Excel
export function invoiceExportExcel(bodyParam) {
  return Post({
    url: "/wms/invoice/exportExcel",
    bodyParam: bodyParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}

// 销售发票订单下面的产品
export function getOrderList(bodyParam) {
  return Post({
    url: "/wms/shippingRecord/getOrderList",
    bodyParam,
  });
}
// 销售发票+发货单
export function getShippingList(urlParam) {
  return Get({
    url: "/wms/invoice/getShippingList",
    urlParam,
  });
}
// 销售发票退货单
export function getShippingListV1(urlParam) {
  return Get({
    url: "/wms/invoice/getShippingListV1",
    urlParam,
  });
}


// 销售回馈单-新增编辑（新增或者修改）

export function postReceiptAdd(bodyParam) {
  return Post({
    url: "/wms/receipt/save",
    bodyParam,
  });
}
// 销售回馈单--查询销售回款单列表（分页）
export function postReceiptPage(urlParam, bodyParam) {
  return Post({
    url: "/wms/receipt/page",
    urlParam,
    bodyParam,
  });
}
// 销售回馈单-详情
export function postReceiptDetail(urlParam) {
  return Get({
    url: "/wms/receipt/detail",
    urlParam,
  });
}

// 查询开票产线信息
export function getNotInvoiceProduct(urlParam) {
  return Post({
    url: "/wms/invoice/getNotInvoiceProduct",
    urlParam,
  });
}

// 销售退货--接口
// 销售退货新增接口
export function returnAdd(bodyParam) {
  return Post({
    url: "/wms/return/add",
    bodyParam,
  });
}
// 销售退货列表接口
// export function returnList(bodyParam) {
//   return Post({
//     url: "/wms/return/list",
//     bodyParam,
//   });
// }

// 通过供应商id查询订单中物料信息
export function returnSelectMaterialListBySupplierId(urlParam) {
  return Get({
    url: "/wms/return/selectMaterialListBySupplierId",
    urlParam,
  });
}

// 销售-退货列表
export function marketReturnPage(bodyParam) {
  return Post({
    url: "/wms/marketReturn/page",
    bodyParam,
  });
}

// 销售-新增退货
export function marketReturnAdd(bodyParam) {
  return Post({
    url: "/wms/marketReturn/add",
    bodyParam,
  });
}
// 销售-编辑退货
export function marketReturnUpdate(bodyParam) {
  return Post({
    url: "/wms/marketReturn/update",
    bodyParam,
  });
}

// 订单退货详情
export function marketReturnDetail(urlParam) {
  return Get({
    url: "/wms/marketReturn/detail",
    urlParam,
  });
}
// 通过订单id查询未退货的产品信息
// export function selectUnReturnedProductListByOrderList(urlParam) {
//   return Get({
//     url: "/wms/marketOrder/selectUnReturnedMaterialListByOrderId",
//     urlParam,
//   });
// }

//赠品

export function addGift(bodyParam) {
  return Post({
    url: "/wms/marketOrder/addGift",
    bodyParam,
  });
}

// 赠品记录
export function giftRecordList(urlParam) {
  return Get({
    url: "/wms/marketOrder/giftList",
    urlParam,
  });
}
// 查询客户及关联的合同信息

export function queryCustomerAndContract(bodyParam) {
  return Post({
    url: "/wms/receipt/queryCustomerAndContract",
    bodyParam,
  });
}

//查询银行账户
export function getBankList(urlParam) {
  return Get({
    url: "/wms/bank/listNoPage",
    urlParam,
  });
}

// 字典
export function dictDataType(param) {
  return Get({
    url: "/system/dict/data/type/" + param,
  });
}

// 发货是否录入接口
export function shippingRecord(bodyParam) {
  return Post({
    url: "/wms/shippingRecord/updateExpressInfo",
    bodyParam,
  });
}
