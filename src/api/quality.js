import { Post, Get } from "@/common/request";

let baseUrl = "/wms/qualityTest/";
export function page(urlParam, bodyParam) {
  return Post({
    url: baseUrl + "page",
    urlParam,
    bodyParam,
  });
}
export function check(bodyParam) {
  return Post({
    url: baseUrl + "check",
    bodyParam,
  });
}
export function remark(bodyParam) {
  return Post({
    url: baseUrl + "remark",
    bodyParam,
  });
}
// export function qualityTestExport(bodyParam) {
//   return Post({
//     url: "/wms/qualityTest/export",
//     bodyParam,
//   });
// }

export function qualityTestExport(bodyParam) {
  return Post({
    url: "/wms/qualityTest/export",
    bodyParam: bodyParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}

export function qualityTestExportV1(bodyParam) {
  return Post({
    url: "/wms/qualityTest/exportV1",
    bodyParam: bodyParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}
// 全部驳回
// /qualityTest/reject POST
export function reject(bodyParam) {
  return Post({
    url: baseUrl + "reject",
    bodyParam,
  });
}

// 全部退回
export function postWholeReturn(bodyParam) {
  return Post({
    url: "/wms/qualityTest/check",
    bodyParam,
  });
}

// 质检-备注
export function postQualityTestRemark(bodyParam) {
  return Post({
    url: "/wms/qualityTest/remark",
    bodyParam,
  });
}
//质检记录
export function qualityTestGetRecordInfo(urlParam) {
  return Get({
    url: "/wms/qualityTest/getRecordInfo",
    urlParam: urlParam,
  });
}

//质检详情

export function qualityTestGetInfo(urlParam) {
  return Get({
    url: "/wms/qualityTest/getInfo",
    urlParam: urlParam,
  });
}

//订单bom详情
export function qualityTestGetOrderInfo(urlParam) {
  return Get({
    url: "/wms/qualityTest/getOrderInfo",
    urlParam: urlParam,
  });
}

//质检关联订单bom详情
export function qualityTestGetOrderDetail(urlParam) {
  return Get({
    url: "/wms/bomMarketOrder/detailOrderNo",
    urlParam: urlParam,

  });
}
