import { Post, Get } from "@/common/request";

// 查询工作通知类型
export function noticeGeNoticeType(urlParam, bodyParam) {
  return Get({
    url: "/wms/notice/geNoticeType",
    urlParam,
    bodyParam,
  });
}
// 查询用户订阅的工作通知类型
export function noticeGetUserNoticeType(urlParam, bodyParam) {
  return Get({
    url: "/wms/notice/getUserNoticeType",
    urlParam,
    bodyParam,
  });
}

// 查询工作通知列表（分页）

export function noticePage(urlParam, bodyParam) {
  return Post({
    url: '/wms/notice/page',
    urlParam,
    bodyParam: bodyParam,
  });
}
// 工作通知订阅
export function noticeSubscription(bodyParam) {
  return Post({
    url: "/wms/notice/subscription",
    bodyParam,
  })
}
