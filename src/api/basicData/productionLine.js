import { Get, Post, Put, Delete } from '@/common/request'

// 生产线列表
export function getPage(bodyParam, urlParam) {
  return Post({
    url: '/wms/line/page',
    bodyParam: bodyParam,
    urlParam: urlParam,
  })
}
// 新增生产线
export function addNew(param) {
  return Post({
    url: '/wms/line/add',
    bodyParam: param
  })
}
// 更新
export function update(param) {
  return Post({
    url: '/wms/line/update',
    bodyParam: param
  })
}
// 删除
export function remove(urlParam) {
  return Get({
    url: '/wms/line/remove',
    urlParam: urlParam
  })
}
export function cancel(urlParam) {
  return Get({
    url: '/wms/line/cancel',
    urlParam: urlParam
  })
}// 取消


// 刷新方法
export function refreshToken() {
  return Post({
    url: '/auth/refresh'
  })
}

// 获取用户详细信息
export function getUserInfo() {
  return Get({
    url: '/system/user/getInfo'
  })
}

// 退出方法
export function logoutApi() {
  return Delete({
    url: '/auth/logout'
  })
}

// 获取验证码
export function getSmsCode(phone) {
  return Post({
    url: '/auth/send',
    bodyParam: { username: phone },
    config: {
      isToken: false
    }
  })
}

export function list(urlParam) {
  return Get({
    url: '/wms/line/list',
    urlParam: urlParam,
  })
}

export function exportLine(param, urlParam) {
  return Post({
    url: '/wms/line/export',
    bodyParam: param,
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    }
  })
}
