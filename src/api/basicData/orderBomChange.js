import { Get, Post, Put, Delete } from "@/common/request";

const baseUrl = "/wms/orderModifyRecord/";

export function getOrderModifyRecordAdd(bodyParam) {
  return Post({
    url: baseUrl + "add",
    bodyParam,
  });
}
export function getRemoveMaterialList(urlParam) {
  return Get({
    url: "/wms/orderModifyRecord/getRemoveMaterialList",
    urlParam: urlParam,
  });
}
// 分页
export function getOrderModifyRecordPage(bodyParam) {
  return Post({
    url: "/wms/orderModifyRecord/page",
    bodyParam: bodyParam,
  });
}
// /orderModifyRecord/page
// 详情

export function getOrderModifyRecordInfo(urlParam) {
  return Get({
    url: "/wms/orderModifyRecord/getInfo",
    urlParam: urlParam,
  });
}

// /orderModifyRecord/getAddMaterialList
// 获取新增料表
export function getAddMaterialList(urlParam) {
  return Get({
    url: "/wms/orderModifyRecord/getAddMaterialList",
    urlParam: urlParam,
  });
}
// 获取删除料表
export function getRemovelList(urlParam) {
  return Get({
    url: "/wms/orderModifyRecord/getRemoveMaterialList",
    urlParam: urlParam,
  });
}

// bom详情

export function getBomMarketOrderDetail(urlParam) {
  return Get({
    url: "/wms/bomMarketOrder/detail",
    urlParam: urlParam,
  });
}

export function getBomMarketOrderPage(urlParam, bodyParam) {
  return Post({
    url: "/wms/bomMarketOrder/page",
    urlParam,
    bodyParam,
  });
}

export function getOrderModifyRecordUpdate(bodyParam) {
  return Post({
    url: "/wms/orderModifyRecord/update",
    bodyParam: bodyParam,
  });
}

export function updateV1(bodyParam) {
  return Post({
    url: "/wms/orderModifyRecord/updateV1",
    bodyParam: bodyParam,
  });
}
export function updateFile(bodyParam) {
  return Post({
    url: "/wms/orderModifyRecord/updateFile",
    bodyParam: bodyParam,
  });
}

export function apiFinanceExportBom(bodyParam) {
  return Post({
    url: "/wms/bomMarketOrder/exportExcelFinancial",
    bodyParam: bodyParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}
export function apiExportBom(bodyParam) {
  return Post({
    url: "/wms/bomMarketOrder/exportExcel",
    bodyParam: bodyParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}
export function apiExportAccessoryZip(bodyParam) {
  return Post({
    url: "/wms/bomMarketOrder/exportAccessoryZip",
    bodyParam: bodyParam,
    headers: { "Content-Type": "application/zip" },
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}

export function orderList(urlParam) {
  return Get({
    url: "/wms/bomMarketOrder/list",
    urlParam: urlParam,
  });
}
export function orderListV1(bodyParam) {
  return Post({
    url: "/wms/bomMarketOrder/listV1",
    bodyParam: bodyParam,
  });
}
export function orderBomList(urlParam) {
  return Get({
    url: "/wms/bomMarketOrder/getBomMaterialList",
    urlParam: urlParam,
  });
}
// 修改备注
export function bomUpdateBomRemark(bodyParam) {
  return Post({
    url: "/wms/bomMarketOrder/updateBomRemark",
    bodyParam: bodyParam,
  });
}
export function exportOrderBom(bodyParam) {
  return Post({
    url: "/wms/invoice/exportBomExcel",
    bodyParam: bodyParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    }
  });
}

export function addMarketOrderV1(bodyParam) {
  return Post({
    url: "/wms/marketOrder/addV1",
    bodyParam: bodyParam,
  }); 
}