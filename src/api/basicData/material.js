import { Post, Get } from "@/common/request";

let baseUrl = "/wms/material/";
export function page(urlParam, bodyParam) {
  return Post({
    url: baseUrl + "page",
    urlParam,
    bodyParam,
  });
}
export function pageV1(urlParam, bodyParam) {
  return Post({
    url: baseUrl + "pageV1",
    urlParam,
    bodyParam,
  });
}
export function add(bodyParam) {
  return Post({
    url: baseUrl + "add",
    bodyParam,
  });
}
export function exportData(bodyParam) {
  return Post({
    url: baseUrl + "export",
    bodyParam,
  });
}
export function update(bodyParam) {
  return Post({
    url: baseUrl + "update",
    bodyParam,
  });
}
export function getInfo(id) {
  return Get({
    url: baseUrl + id,
  });
}
export function getInfoRepository(id) {
  return Get({
    url: "/wms/menu/" + id,
  });
}
export function cancelMaterial(id) {
  return Get({
    // cancelMaterial
    url: baseUrl + "stopMaterial",
    urlParam: { id },
  });
}

export function AllList(bodyParam) {
  return Post({
    url: baseUrl + "list",
    bodyParam: { ignoreCancel: true, ...bodyParam },
  });
}

export function AllPage(bodyParam,urlParam) {
  return Post({
    url: baseUrl + "page",
    bodyParam: { ignoreCancel: true, ...bodyParam },
    urlParam
  });
}

// 导出物料
export function exportMaterial(param, urlParam) {
  return Post({
    url: "/wms/material/export",
    bodyParam: param,
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}

// 生成物料编号
export function getMaterialGenerateMaterialNo(urlParam) {
  return Post({
    url: "/wms/material/generateMaterialNo",
    urlParam: urlParam,
  });
}
export function startMaterial(id, remark) {
  return Get({
    url: baseUrl + "startMaterial",
    urlParam: { id, remark },
  });
}