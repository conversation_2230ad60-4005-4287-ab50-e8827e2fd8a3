import { Post, Get, Delete } from "@/common/request";

let baseUrl = "/wms/classification/";
export function list(type) {
  return Post({
    url: baseUrl + "list",
    bodyParam: {
      parentId: 0,
      type,
      ignoreCancel: true,
    },
  });
}
export function add(bodyParam) {
  return Post({
    url: baseUrl + "add",
    bodyParam,
  });
}
export function update(bodyParam) {
  return Post({
    url: baseUrl + "update",
    bodyParam,
  });
}
export function getInfo(id) {
  return Get({
    url: baseUrl + id,
  });
}
export function deleteCategroy(id) {
  return Delete({
    url: baseUrl + id,
  });
}
export function getByParentId(parentId, type) {
  return Get({
    url: baseUrl + "getByParentId",
    urlParam: { parentId, type },
  });
}
