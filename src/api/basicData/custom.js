import { Post, Get ,Delete} from "@/common/request";
let baseUrl = "/wms/customer/";
export function customPage(bodyParam,urlParam) {
  return Post({
    url: baseUrl + "page",
    bodyParam:bodyParam,
    urlParam:urlParam,
  });
}
export function customAdd(bodyParam) {
  return Post({
    url: baseUrl + "add",
    bodyParam,
  });
}
export function customUpdate(bodyParam) {
  return Post({
    url: baseUrl + "update",
    bodyParam,
  });
}
export function getDetailInfo(urlParam) {
  return Get({
    url: baseUrl + "getInfo",
    urlParam:urlParam
  })
}

// export function deleteCustom(urlParam) {
//   return Delete({
//     url: baseUrl + "delete"  + id,
//   });
// }

export function deleteCustom(urlParam) {
  return Delete({
    url: baseUrl+'delete',
    urlParam:urlParam
  })
}