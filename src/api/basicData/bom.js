import { Get, Post, Put, Delete } from '@/common/request'

// 生产线列表
export function getPage(bodyParam, urlParam) {
  return Post({
    url: '/wms/bom/page',
    bodyParam: bodyParam,
    urlParam: urlParam,
  })
}

export function getAllBomList(urlParam) {
  return Post({
    url: '/wms/bom/list',
    urlParam: urlParam,
  })
}

export function bomDetail(urlParam) {
  return Get({
    url: "/wms/bom/detail",
    urlParam: urlParam
  })
}
export function getBomMaterialList(urlParam) {
  return Get({
    url: "/wms/bom/getBomMaterialList",
    urlParam: urlParam
  })
}

// 新增生产线
export function addNew(param) {
  return Post({
    url: '/wms/bom/add',
    bodyParam: param
  })
}

export function update(param) {
  return Post({
    url: '/wms/bom/update',
    bodyParam: param
  })
}
export function remove(urlParam) {
  return Get({
    url: '/wms/bom/remove',
    urlParam: urlParam
  })
}

export function cancel(urlParam) {
  return Get({
    url: '/wms/bom/cancel',
    urlParam: urlParam
  })
}

// 导出bom
export function exportBom(param, urlParam) {
  return Post({
    url: '/wms/bom/export',
    bodyParam: param,
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    }
  })
}


export function bomStop(urlParam) {
  return Get({
    url: '/wms/bom/stop',
    urlParam: urlParam
  })
}

// 没有关联bom的产品
export function listNoRelatedBom(bodyParam, urlParam) {
  return Post({
    url: '/wms/product/listNoRelatedBom',
    bodyParam: bodyParam,
    urlParam: urlParam,
  })
}
// 查询所有bom
export function listByAvailableStatus(urlParam) {
  return Post({
    url: '/wms/bom/listByAvailableStatus',
    urlParam: urlParam,
  })
}
export function getBomDetailById(urlParam) {
  return Get({
    url: '/wms/bom/detailId',
    urlParam: urlParam,
  })
}