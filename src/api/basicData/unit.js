import { Post, Get,Delete } from "@/common/request";
export function page(urlParam) {
  return Post({
    url: "/wms/unit/page",
    urlParam,
    bodyParam: {},
  });
}
export function add(bodyParam) {
  return Post({
    url: "/wms/unit/add",
    bodyParam,
  });
}
export function list() {
  return Post({
    url: "/wms/unit/list",
    bodyParam: {},
  });
}
export function update(bodyParam) {
  return Post({
    url: "/wms/unit/update",
    bodyParam,
  });
}
// 仓库删除按钮
export function unitDelete(urlParam) {
  return Delete({
    url: '/wms/unit/delete',
    urlParam:urlParam
  })
}