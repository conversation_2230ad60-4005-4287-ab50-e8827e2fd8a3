import { Post, Get } from "@/common/request";

let baseUrl = "/wms/menu/";
export function page(urlParam, bodyParam) {
  return Post({
    url: baseUrl + "list",
    urlParam,
    bodyParam,
  });
}
export function list(bodyParam) {
  return Post({
    url: baseUrl + "list",
    urlParam: { pageNum: 1, pageSize: 100000 },
    bodyParam,
  });
}
export function add(bodyParam) {
  return Post({
    url: baseUrl + "add",
    bodyParam,
  });
}
export function exportData(bodyParam, urlParam) {
  return Post({
    url: baseUrl + "export",
    bodyParam,
    urlParam
  });
}
export function update(bodyParam) {
  return Post({
    url: baseUrl + "update",
    bodyParam,
  });
}
export function getInfo(id) {
  return Get({
    url: baseUrl + id,
  });
}

export function exportRepository(param, urlParam) {
  return Post({
    url: '/wms/menu/export',
    bodyParam: param,
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    }
  })
}

export function warehouse(bodyParam, token) {
  return Post({
    url: '/wms/qrcode/page',
    // urlParam: { pageNum: 1, pageSize: 100000 },
    bodyParam,
    config: {
      // responseType: "blob",
      isToken: false,
      timeout: 100000,
      headers: {
        Authorization: `Bearer ${token}`
      }
    }
  })
}
export function noWarehouse(bodyParam, token) {
  return Post({
    url: '/wms/qrcode/skip/page',
    urlParam: { pageNum: 1, pageSize: 100000 },
    bodyParam,
    config: {
      // responseType: "blob",
      isToken: false,
      timeout: 100000,
      headers: {
        Authorization: `Bearer ${token}`
      }
    }
  })
}
// 仓库删除按钮
export function menuDelete(urlParam) {
  return Get({
    url: '/wms/menu/delete',
    urlParam: urlParam
  })
}


export function selectMenuListIgnoreNoStock(bodyParam) {
  return Post({
    url: "/wms/warehouse/listByMaterialIgnoreNoStock",
    bodyParam,
  });
}

export function warehouseInventory(bodyParam) {
  return Post({
    url: "/wms/qrcode/warehouseInventory",
    bodyParam,
  });
}
export function warehouseAllocation(bodyParam) {
  return Post({
    url: "/wms/qrcode/warehouseAllocation",
    bodyParam,
  });
}
export function listByMaterialIgnoreNoStock(bodyParam) {
  return Post({
    url: "/wms/qrcode/listByMaterialIgnoreNoStock",
    bodyParam,
  });
}

export function queryMaterial(bodyParam,) {
  return Post({
    url: '/wms/qrcode/queryMaterial',
    bodyParam,

  })
}

// /qrcode/warehouseInventory