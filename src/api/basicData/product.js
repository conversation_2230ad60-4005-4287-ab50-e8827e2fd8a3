import { Get, Post, Put, Delete } from '@/common/request'

// 生产线列表
export function getPage(bodyParam, urlParam) {
  return Post({
    url: '/wms/product/page',
    bodyParam: bodyParam,
    urlParam: urlParam,
  })
}
// 没有分页接口
export function list(bodyParam, urlParam) {
  return Post({
    url: '/wms/product/list',
    bodyParam: bodyParam,
    urlParam: urlParam,
  })
}

// 新增生产线
export function addNew(param) {
  return Post({
    url: '/wms/product/add',
    bodyParam: param
  })
}
// 更新
export function update(param) {
  return Post({
    url: '/wms/product/update',
    bodyParam: param
  })
}
// 删除
export function remove(urlParam) {
  return Get({
    url: '/wms/product/remove',
    urlParam: urlParam
  })
}
export function cancel(urlParam) {
  return Get({
    url: '/wms/product/cancel',
    urlParam: urlParam
  })
}// 编辑里面的取消/删除
export function productDetail(urlParam) {
  return Get({
    url: '/wms/product/detail',
    urlParam: urlParam
  })
}// 
// 产品删除按钮
export function productDelete(urlParam) {
  return Get({
    url: '/wms/product/delete',
    urlParam: urlParam
  })
}

// 刷新方法
export function refreshToken() {
  return Post({
    url: '/auth/refresh'
  })
}

// 获取用户详细信息
export function getUserInfo() {
  return Get({
    url: '/system/user/getInfo'
  })
}

// 退出方法
export function logoutApi() {
  return Delete({
    url: '/auth/logout'
  })
}

// 获取验证码
export function getSmsCode(phone) {
  return Post({
    url: '/auth/send',
    bodyParam: { username: phone },
    config: {
      isToken: false
    }
  })
}

// 导出产品
export function exportProduct(param, urlParam) {
  return Post({
    url: '/wms/product/export',
    bodyParam: param,
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    }
  })
}


// 根据排产订单的产品列表
export function listBySchedulingOrderId(urlParam) {
  return Get({
    url: '/wms/material/listBySchedulingOrderId',
    urlParam
  })
}

// 新出详情接口

export function getInfoByMaterialNo(urlParam) {
  return Get({
    url: '/wms/material/getInfoByMaterialNo',
    urlParam
  })
}