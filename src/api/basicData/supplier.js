import { Post, Get, Delete } from "@/common/request";

let baseUrl = "/wms/supplier/";
export function page(urlParam, bodyParam) {
  return Post({
    url: "/wms/supplier/page",
    urlParam,
    bodyParam,
  });
}
export function add(bodyParam) {
  return Post({
    url: "/wms/supplier/add",
    bodyParam,
  });
}
export function list() {
  return Post({
    url: "/wms/supplier/list",
    bodyParam: { ignoreCancel: true },
  });
}
export function exportData(bodyParam, urlParam) {
  return Post({
    url: "/wms/supplier/export",
    bodyParam,
    urlParam,
  });
}
export function update(bodyParam) {
  return Post({
    url: "/wms/supplier/update",
    bodyParam,
  });
}
export function zeroUpdate(bodyParam) {
  return Post({
    url: "/wms/supplier/zeroUpdate",
    bodyParam,
  });
}

export function getInfo(id) {
  return Get({
    url: "/wms/supplier/getInfo",
    urlParam: { id },
  });
}
export function removeMaterialSupplierRelationFlag(materialId, supplierId) {
  return Get({
    url: "/wms/supplier/removeMaterialSupplierRelationFlag",
    urlParam: { materialId, supplierId },
  });
}

// 导出供应商
export function exportSupplier(param, urlParam) {
  return Post({
    url: "/wms/supplier/export",
    bodyParam: param,
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}
// 供应商删除按钮
export function supplierDelete(urlParam) {
  return Delete({
    url: baseUrl + 'delete',
    urlParam: urlParam
  })
}

