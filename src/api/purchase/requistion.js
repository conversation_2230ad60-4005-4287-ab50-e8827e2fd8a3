import { Post,Get  } from "@/common/request";
let baseUrl = "/wms/purchasing/";

export function page(urlParam, bodyParam) {
  return Post({
    url: baseUrl + "page",
    urlParam,
    bodyParam,
  });
}
export function add(bodyParam) {
  return Post({
    url: baseUrl + "add",
    bodyParam,
  });
}
export function detail(urlParam) {
  return Get({
    url: baseUrl + "detail",
    urlParam:urlParam
  })
}// 
export function getDelete (urlParam) {
  return Get({
    url: baseUrl + "delete",
    urlParam:urlParam
  })
}// 