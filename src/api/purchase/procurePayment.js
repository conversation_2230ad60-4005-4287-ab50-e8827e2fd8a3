import { Post, Get } from "@/common/request";
let baseUrl = "/wms/order/";

export function paymentPage(bodyParam, urlParam,) {
  return Post({
    url: '/wms/payment/page',
    urlParam,
    bodyParam,
  });
}
export function paymentAdd(bodyParam) {
  return Post({
    url: '/wms/payment/add',
    bodyParam,
  });
}
export function getByLikeNumber(urlParam) {
  return Get({
    url: '/wms/order/getByLikeNumber',
    urlParam: urlParam
  })
}// 
// 查询供应商及关联的采购订单信息
export function querySupplierAndPurchase(bodyParam) {
  return Post({
    url: '/wms/payment/querySupplierAndPurchase',
    bodyParam,
  });
}
