import { Post, Get, Delete } from "@/common/request";

export function arrivalRecordPage(urlParam, bodyParam) {
  return Post({
    url: "/wms/arrivalRecord/page",
    urlParam,
    bodyParam,
  });
}

export function selectMaterialArrivalRecordByRecordNumber(urlParam) {
  return Get({
    url: "/wms/arrivalRecord/selectMaterialArrivalRecordByRecordNumber",
    urlParam,
  });
}
export function arrivalRecordLocking(bodyParam) {
  return Post({
    url: "/wms/arrivalRecord/locking",
    bodyParam,
  });
}

