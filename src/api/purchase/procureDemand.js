import { Post, Get } from "@/common/request";


export function requirePage(urlParam, bodyParam) {
  return Post({
    url: '/wms/require/page',
    urlParam,
    bodyParam,
  });
}
//不分页
export function requireList(bodyParam) {
  return Post({
    url: '/wms/require/list',
    bodyParam,
  });
}



// 生成采购订单
export function generatePurchaseOrder(bodyParam) {
  return Post({
    url: '/wms/require/generatePurchaseOrder',
    bodyParam,
  });
}
export function requireAdd(bodyParam) {
  return Post({
    url: '/wms/require/add',
    bodyParam,
  });
}


//采购需求
export function getPurchaseRequire(urlParam) {
  return Get({
    url: '/wms/order/getPurchaseRequire',
    urlParam: urlParam
  })
}// 
export function updateSupplier(bodyParam) {
  return Post({
    url: '/wms/require/updateSupplier',
    bodyParam,
  });
}

// 采购需求查找供应商

export function selectSupplierByMaterialId(urlParam) {
  return Get({
    url: '/wms/material/selectSupplierByMaterialId',
    urlParam: urlParam
  })
}
//采购详情
export function requireDetail(urlParam) {
  return Get({
    url: '/wms/require/detail',
    urlParam: urlParam
  })
}
//采购编辑
export function requireUpdate(bodyParam) {
  return Post({
    url: '/wms/require/update',
    bodyParam: bodyParam
  })
}

// 批量取消采购需求
export function batchCancel(bodyParam) {
  return Post({
    url: '/wms/require/updateBatch',
    bodyParam,
  });
}

