import { Post, Get } from "@/common/request";
// 退货进出库-添加
export function returnAdd(bodyParam) {
  return Post({
    url: '/wms/return/add',
    bodyParam,
  });
}
// 退货进出库-订单所有供应商
export function returnSelectAllSupplier(urlParam) {
  return Get({
    url: '/wms/return/selectAllSupplier',
    urlParam
  });
}
// 退货进出库-根据供应商id查询订单中物料
export function returnSelectMaterialListBySupplierId(urlParam) {
  return Get({
    url: '/wms/return/selectMaterialListBySupplierId',
    urlParam
  });
}
// 退货进出库-分页
export function returnList(bodyParam, urlParam) {
  return Post({
    url: '/wms/return/page',
    bodyParam,
    urlParam
  });
}



// 退货进出库-详情
export function returnGetInfo(urlParam) {
  return Get({
    url: '/wms/return/getInfo',
    urlParam
  });
}

export function returnSelectReturnableSupplier(bodyParam) {
  return Post({
    url: '/wms/return/selectReturnableSupplier',
    bodyParam
  });
}