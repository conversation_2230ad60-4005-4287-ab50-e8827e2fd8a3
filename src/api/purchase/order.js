import { Post, Get, Delete } from "@/common/request";
let baseUrl = "/wms/order/";

export function page(urlParam, bodyParam) {
  return Post({
    url: baseUrl + "page",
    urlParam,
    bodyParam,
  });
}
export function getInfo(id) {
  return Get({
    url: baseUrl + "getInfo",
    urlParam: { id },
  });
}
//变更采购人
export function changePurchaseBy(bodyParam) {
  return Post({
    url: baseUrl + "changePurchaseBy",
    bodyParam,
  });
}
//修改采购订单（上传合同、修改状态等）
export function updatePurchase(bodyParam) {
  return Post({
    url: baseUrl + "updatePurchase",
    bodyParam,
  });
}
//物料到货质检
export function materialQualityTesting(bodyParam) {
  return Post({
    url: baseUrl + "materialQualityTesting",
    bodyParam,
  });
}
//取消采购
export function cancelPurchase(id) {
  return Get({
    url: baseUrl + "cancelPurchase",
    urlParam: { id },
  });
}
//完成采购
export function completePurchase(id) {
  return Get({
    url: baseUrl + "completePurchase",
    urlParam: { id },
  });
}
//完成采购
export function purchasingRemark(bodyParam) {
  return Post({
    url: baseUrl + "purchasingRemark",
    bodyParam,
  });
}
//上传合同
export function uploadContract(bodyParam) {
  return Post({
    url: baseUrl + "uploadContract",
    bodyParam,
  });
}
// 物料到货记录
export function selectMaterialArrivalPage(param) {
  return Post({
    url: baseUrl + "selectMaterialArrivalPage",
    urlParam: { pageNum: 1, pageSize: 100000 },
    bodyParam: param,
  });
}
//通过物料id查询到货记录
export function selectMaterialArrivalRecordByMaterialId(
  purchaseNumber,
  materialId
) {
  return Post({
    url: baseUrl + "selectMaterialArrivalRecordByMaterialId",
    bodyParam: { purchaseNumber, materialId },
  });
}
// 查询采购订单物料的价格变化列表


export function newSelectMaterialPriceList(urlParam) {
  return Get({
    url: "/wms/order/selectMaterialPriceList",
    urlParam,
  });
}
// export function newSelectMaterialPriceList(urlParam) {
//   return Get({
//     url: "/wms/order/getMaterialDetail",
//     urlParam,
//   });
// }
export function getMaterialDetail(id) {
  return Get({
    url: '/wms/order/getMaterialDetail',
    urlParam: { id },
  });
}
// oa审批详情
export function oaDetail(urlParam) {
  return Get({
    url: "/wms/order/oaDetail",
    urlParam,
  });
}
// 查询采购合同信息
export function getContractInfo(urlParam) {
  return Get({
    url: "/wms/order/getContractInfo",
    urlParam,
  });
}
// 查询采购销售订单
export function getMarketOrder(urlParam) {
  return Get({
    url: "/wms/order/getMarketOrder",
    urlParam,
  });
}
// 查询采购销售订单采购结算
export function getPurchaseSettlement(urlParam) {
  return Get({
    url: "/wms/order/getPurchaseSettlement",
    urlParam,
  });
}
// // 查询采购未结算的列表
// export function getUnSettlementBySupplierId(urlParam, bodyParam) {
//   return Get({
//     url: "/wms/order/getUnSettlementBySupplierId",
//     urlParam,
//     bodyParam
//   })

// }



export function exportExcel(param, urlParam) {
  return Post({
    url: "/wms/order/exportExcel",
    bodyParam: param,
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    }
  })
}
//导出pdf
export function exportWarehousePdf(urlParam) {
  return Post({
    url: '/wms/order/exportPdf',
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,

    }
  })
}

// 通过采购订单ID查询到货记录
export function selectMaterialArrivalRecordByOrderIdList(urlParam) {
  return Get({
    url: "/wms/order/selectMaterialArrivalRecordByOrderId",
    urlParam,
  });
}

// 修改到货记录
export function updateMaterialArrivalRecord(bodyParam) {
  return Post({
    url: "/wms/order/updateMaterialArrivalRecord",
    bodyParam,
  });
}

// 删除物料

export function deleteMaterial(urlParam) {
  return Delete({
    url: "/wms/order/deleteMaterial",
    urlParam,
  });
}

//采购订单导出word
export function exportWord(urlParam) {
  return Post({
    url: '/wms/order/exportWord',
    urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    }
  })
}
// 修改到货记录
export function updateMaterialArrivalRecordRemark(bodyParam) {
  return Post({
    url: "/wms/order/updateMaterialArrivalRecordRemark",
    bodyParam,
  });
}

export function updateMaterialDetail(bodyParam) {
  return Post({
    url: "/wms/order/updateMaterialDetail",
    bodyParam: bodyParam,
  });
}

export function getContractNumber(bodyParam) {
  return Post({
    url: "/wms/contract/getContractNumber",
    bodyParam: bodyParam,
  });
}