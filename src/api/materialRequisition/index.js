import { Get, Post, Put, Delete } from "@/common/request";

// 领用列表
export function listMaterialRequisition(bodyParam, urlParam) {
  return Post({
    url: "/wms/receive/page",
    bodyParam,
    urlParam
  });
}

export function detailMaterialReceive(urlParam) {
  return Get({
    url: "/wms/receive/" + urlParam,
  });
}

// 领用新增
export function saveMaterialRequisition(bodyParam) {
  return Post({
    url: "/wms/receive/save",
    bodyParam,
  });
}
// 领用新增pack
export function saveMaterialRequisitionV1(bodyParam) {
  return Post({
    url: "/wms/receive/saveV1",
    bodyParam,
  });
}
// 领用新增电芯
export function saveMaterialRequisitionV2(bodyParam) {
  return Post({
    url: "/wms/receive/saveV2",
    bodyParam,
  });
}
// 选择物料列表
export function listMaterial(bodyParam) {
  return Post({
    url: "/wms/material/list",
    bodyParam,
  });
}
// 字典
export function dictionary(param) {
  return Get({
    url: "/system/dict/data/type/" + param,
  })
}

