import { Get, Post, Put, Delete } from "@/common/request";

// 登录方法
export function loginApi(param, smsType) {
  return Post({
    url: "/auth/login",
    config: {
      isToken: false,
      headers: {
        "tenant-id": smsType,
      },
    },
    bodyParam: { type: "mobile", ...param },
  });
}

// 刷新方法
export function refreshToken() {
  return Post({
    url: "/auth/refresh",
  });
}

// 获取用户详细信息
export function getUserInfo() {
  return Get({
    url: "/system/user/getInfo",
  });
}

// 退出方法
export function logoutApi() {
  return Delete({
    url: "/auth/logout",
  });
}

// 获取验证码
export function getSmsCode(phone, agentIdVal, type) {
  return Post({
    url: "/auth/send",
    bodyParam: { username: phone, agentId: agentIdVal },
    config: {
      isToken: false,
      headers: {
        "tenant-id": type,
      },
    },
  });
}
// 钉钉内部用获取验证码
export function getDingtalkSmsCode(phone, dingUserId) {
  return Post({
    url: "/auth/sendDingtalk",
    bodyParam: { username: phone, dingUserId },
    config: {
      isToken: false,
    },
  });
}

// // 文件上传
// export function fileUpload(bizid, fileType,file) {
//   return Post({
//     url: '/home/<USER>',
//     config: {
//       isToken: false
//     },
//     bodyParam: { bizid, fileType,file }
//   })
// }

export function fileUpload(file, fileType) {
  let formData = new FormData();
  formData.append("file", file);
  return Post({
    url: "/system/home/<USER>" + fileType,
    bodyParam: formData,
    config: {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    },
  });
}
export function updateGuidePage(status) {
  return Get({
    url: "/system/user/updateGuidePage?status=" + status,
  });
}
export function DDFreeAuthenLogin(authCode, corpId, appId, type) {
  return Get({
    url: "/wms/dingtalk/loginInnerDingtalk",
    urlParam: { authCode: authCode, corpId: corpId, appId: appId, type: type },
  });

}
export function DDFreeAuthenLoginNoCropId(authCode) {
  return Get({
    url: "/wms/dingtalk/loginNoCropId",
    urlParam: { authCode },
  });
}
export function DDUpdateMobile(param) {
  return Get({
    url: "/wms/dingtalk/updateMobile",
    urlParam: param,
  });
}
export function logInAppIdOrAgentId(param) {
  return Get({
    url: "/auth/logInAppIdOrAgentId",
    urlParam: param,
  });
}

export function getTenantByMobilePhone(mobile) {
  return Get({
    url: "/system/tenant/getTenantByMobile?mobile=" + mobile,
  });
}


export function loginInnerV1(param, tenantid) {
  return Post({
    url: "/auth/loginInnerV1",
    config: {
      isToken: false,
      headers: {
        "tenant-id": tenantid,
      },
    },
    bodyParam: { type: "mobile", ...param },
  });
}
export function loginInnerV2(urlParam, tenantid) {
  return Get({
    url: "/auth/loginInnerV1",
    config: {
      isToken: false,
      headers: {
        "tenant-id": tenantid,
      },
    },
    // bodyParam: { type: "mobile", ...param },
    urlParam: urlParam
  });
}