import { Get, Post, Put, Delete } from '@/common/request'

// 生产线列表
export function getPage(bodyParam, urlParam) {
  return Post({
    url: '/wms/plan/page',
    bodyParam: bodyParam,
    urlParam: urlParam,
  })
}

export function planDetail(urlParam) {
  return Get({
    url: "/wms/plan/detail",
    urlParam: urlParam
  })
}
export function getBomMaterialList(urlParam) {
  return Get({
    url: "/wms/bom/getBomMaterialList",
    urlParam: urlParam
  })
}

// 新增生产线
export function addNew(param) {
  return Post({
    url: '/wms/plan/add',
    bodyParam: param
  })
}
export function schedule(urlParam) {
  return Get({
    url: "/wms/records/schedule",
    urlParam: urlParam
  })
}
// 生产记录物料消耗分野
export function recordsMaterialPage(bodyParam, urlParam) {
  return Post({
    url: '/wms/recordsMaterial/page',
    bodyParam: bodyParam,
    urlParam: urlParam,
  })
}
export function getBom(urlParam) {
  return Get({
    url: "/wms/plan/getSingleBomByPlanNo",
    urlParam: urlParam
  })
}
// 新增生产记录
export function addRecords(param) {
  return Post({
    url: '/wms/records/add',
    bodyParam: param
  })
}
export function getMaterialAllotBaseInfo(urlParam,type) {
  let url = ''
  if(type == 13007){
    url = "/wms/plan/materialIncreaseInfo"
  }else{
    url = "/wms/plan/materialResidueInfo"
  }
  return Get({
    url:url,
    urlParam: urlParam
  })
}
export function materialAllot(param) {
  return Post({
    url: '/wms/plan/materialAllot',
    bodyParam: param
  })
}
export function bomDetail(urlParam) {
  return Get({
    url: "/wms/plan/bomDetail",
    urlParam: urlParam
  })
}
// 

export function productTestAllot(param) {
  return Post({
    url: '/wms/plan/productTestAllot',
    bodyParam: param
  })
}



export function update(param) {
  return Post({
    url: '/wms/bom/update',
    bodyParam: param
  })
}
export function remove(urlParam) {
  return Get({
    url: '/wms/bom/remove',
    urlParam: urlParam
  })
}

export function cancel(urlParam) {
  return Get({
    url: '/wms/plan/cancel',
    urlParam: urlParam
  })
}

// 生产线列表
export function schedulingOrder(bodyParam, urlParam) {
  return Post({
    url: '/wms/schedulingOrder/list',
    bodyParam: bodyParam,
    urlParam: urlParam,
  })
}
// /plan/new/add新新增生产计划
export function postNewAdd(bodyParam, urlParam) {
  return Post({
    url: '/wms/plan/new/add',
    bodyParam: bodyParam,
    urlParam: urlParam,
  })
}


// 生产线列表
export function postNewPage(bodyParam, urlParam) {
  return Post({
    url: '/wms/plan/new/page',
    bodyParam: bodyParam,
    urlParam: urlParam,
  })
}


// 详情明细导出
export function postExportPlanDetail(urlParam) {
  return Post({
    url: '/wms/plan/export',
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    }
  })
}




//生产计划导出


export function planExportExcel(param, urlParam) {
  return Post({
    url: "/wms/plan/exportExcel",
    bodyParam: param,
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    }
  })
}
