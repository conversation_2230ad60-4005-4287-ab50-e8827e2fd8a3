import { Post, Get, Delete } from "@/common/request";

let baseUrl = "/wms/warehouse/";
//物料库存列表
export function page(urlParam, bodyParam) {
  return Post({
    url: baseUrl + "listWareHouseNum",
    urlParam,
    bodyParam,
  });
}
//物料出库列表
export function listOutMaterial(urlParam, bodyParam) {
  return Post({
    url: baseUrl + "listOutMaterial",
    urlParam,
    bodyParam: { ...bodyParam, type: "RAWMATERIAL_NORMAL_OUT" },
    // type: "RAWMATERIAL_NORMAL_OUT"
  });
}
//物料入库列表
export function listInsertList(urlParam, bodyParam) {
  return Post({
    url: baseUrl + "listInsertList",
    urlParam,
    bodyParam,
  });
}
//物料产品批量入库
export function checkIn(bodyParam) {
  return Post({
    url: baseUrl + "checkIn",
    bodyParam: bodyParam,
  });
}
// 物料产品批量出库
export function checkOut(bodyParam, menuType = "material") {
  return Post({
    url: baseUrl + "checkOut",
    bodyParam: { ...bodyParam, menuType },
  });
}
// 库存仓库信息集合
export function listByMaterial(bodyParam) {
  return Post({
    url: baseUrl + "listByMaterial",
    bodyParam: { ...bodyParam },
  });
}
// 进出库记录--老列表接口
export function recordList(urlParam, bodyParam) {
  return Post({
    url: baseUrl + "recordList",
    urlParam,
    bodyParam,
  });
}
//进出库记录--新列表接口V1
export function recordListV1(urlParam, bodyParam) {
  return Post({
    url: baseUrl + "recordListV1",
    urlParam,
    bodyParam,
  });
}
//手动入库
export function checkManualIn(bodyParam, submitToken) {
  return Post({
    url: `${baseUrl}checkManualIn?submitToken=${submitToken}`,
    bodyParam,
  });
}
//手动出库
export function checkManualOut(bodyParam, submitToken) {
  return Post({
    url: `${baseUrl}checkManualOut?submitToken=${submitToken}`,
    bodyParam,
  });
}
//查看备注
export function remark(urlParam) {
  return Get({
    url: baseUrl + "remark",
    urlParam,
  });
}

// 导出仓库
export function exportWarehouseDetail(param, urlParam) {
  return Post({
    url: "/wms/warehouse/exportWarehouseDetail",
    bodyParam: param,
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}

// 导出产品
export function exportWarehouseExport(param, urlParam) {
  return Post({
    url: "/wms/warehouse/export",
    bodyParam: param,
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}
// 导出Pdf
export function exportWarehousePdf(param, urlParam) {
  return Post({
    url: "/wms/warehouse/exportPdf",
    bodyParam: param,
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}

// 导出产品
export function exportProduct(param, urlParam) {
  return Post({
    url: "/wms/warehouse/export",
    bodyParam: param,
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}

// 导出Pdf
export function transferExportPdf(param, urlParam) {
  return Post({
    url: "/wms/warehouse/transferExportPdf",
    bodyParam: param,
    urlParam: urlParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}

// export function warehouseListOutMaterial(urlParam, param) {
//   return Post({
//     url: '/wms/warehouse/listOutMaterialByTransferId',
//     urlParam: urlParam,
//     bodyParam: param,

//   })
// }

//进出库记录--新列表接口V1
export function warehouseListOutMaterial(urlParam, bodyParam) {
  return Post({
    url: "/wms/warehouse/listOutMaterialByTransferId",
    urlParam,
    bodyParam,
  });
}

//库存调拨列表
export function transferList(urlParam) {
  return Get({
    url: "/wms/allocation/list",
    urlParam,
  });
}

//新增调拨明细
export function transferAdd(bodyParam) {
  return Post({
    url: "/wms/allocation/add",
    bodyParam,
  });
}
// 新增线边库类型的接口调拨
export function transferAddV1(bodyParam) {
  return Post({
    url: "/wms/allocation/addV1",
    bodyParam,
  });
}

export function transferAddV2(bodyParam) {
  return Post({
    url: "/wms/allocation/addV2",
    bodyParam,
  });
}
//新增调拨明细
export function transferExportExcel(bodyParam) {
  return Post({
    url: "/wms/warehouse/transferExportExcel",
    bodyParam,
    config: {
      responseType: "blob",
      timeout: 100000,
    },
  });
}

// 退出方法
export function warehouseRemove(urlParam) {
  return Delete({
    url: "/wms/warehouse/remove",
    urlParam,
  });
}
// 查询出入库类型

export function getWarehouseTypeList(urlParam) {
  return Get({
    url: "/wms/warehouse/typeList",
    urlParam,
  });
}


export function getInfoPriceByMaterialId(urlParam) {
  return Get({
    url: "/wms/material/getInfoPriceByMaterialId",
    urlParam,
  });
}
