import { Post, Get, Delete } from "@/common/request";
let baseUrl = "/wms/schedulingOrder/";
// 列表
export function postProSchedulPage(urlParam, bodyParam) {
  return Post({
    url: baseUrl + 'page',
    urlParam,
    bodyParam: bodyParam,
  });
}
//   新增
export function postProSchedulAdd(bodyParam) {
  return Post({
    url: baseUrl + 'add',
    bodyParam,
  });
}

// /schedulingOrder/new/add新添加排产订单
export function postProNewAdd(bodyParam) {
  return Post({
    url: baseUrl + 'new/add',
    bodyParam,
  });
}
// 客户信息
export function postCustomerPage(urlParam) {
  return Post({
    url: '/wms/customer/list',
    urlParam,
    bodyParam: {},
  });
}
//   产品信息
export function productList(urlParam, bodyParam) {
  return Post({
    url: '/wms/product/list',
    urlParam,
    bodyParam: bodyParam ? bodyParam : {},
  });
}

// 取消按钮
export function schedulingOrderCancel(urlParam) {
  return Get({
    url: '/wms/schedulingOrder/cancel',
    urlParam
  });
}



// 生产线列表
export function getSchedulingOrderPage(bodyParam) {
  return Post({
    url: '/wms/schedulingOrder/new/page',
    bodyParam: bodyParam,
  })
}


// 获取用户详细信息
export function getListByOrderId(urlParam) {
  return Get({
    url: '/wms/material/listByOrderId',
    urlParam,
  })
}
// 
// ​/marketOrder​/waitingSchedulingList
// 待排
export function getWaitingSchedulingList(urlParam) {
  return Get({
    url: '/wms/marketOrder/waitingSchedulingList',
    urlParam,
  })
}

export function waitingSchedulingListV1(bodyParam) {
  return Post({
    url: '/wms/marketOrder/waitingSchedulingListV1',
    bodyParam: bodyParam
  })
}