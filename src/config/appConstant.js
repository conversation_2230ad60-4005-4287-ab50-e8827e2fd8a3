let alioss = 'https://ming-enterprise.oss-cn-hangzhou.aliyuncs.com/static'
let setting = {
  mingwork: {
    logoHeader: 'logo.svg',
    proApiBaseUrl: 'https://outgateway.mingwork.com',
    proEnterpriseServiceDomain: 'https://www.mingwork.com',
    storeDomain: '.store.mingwork.com',
    miniShop: 'miniShop/',
    weChatAppId: 'wx41316c49103ed6ac',
    copyRight: '©2024 上海明我信息技术有限公司',
    beian: '沪ICP备**********号-1',
    businessLicenseNo: '沪B2-20211453',
    recordcode: '31011002005440',
    market: {
      name: '可乐',
      position: '商务经理',
      phone: '***********',
      email: '<EMAIL>',
      qrcode: 'home/QRcode-xieqinan.png'
    },
    versionIds: {
      standard: '1610923211488202753',
      professional: '1610926870393491457',
      ultimate: '1610934216672112642'
    },
    merchantAgreementName: '服务条款',
    manageAgreementName: '隐私政策',
    login: {
      background: alioss + '/login/background-mingwork.jpg',
      title1: '',
      title2: '助力推动制造业数字化升级',
      p1: '【生产计划】全面打造数字化生产流水线',
      p2: '【仓库管理】数字化构建物料/成品仓库',
      p3: '【角色权限】将制造业各环节角色深度融合',
      p4: '【产品/BOM】 深度构建数字化基础数据库'
    }
  },
}

export default setting['mingwork']
