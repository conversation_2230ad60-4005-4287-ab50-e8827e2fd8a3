export const sortOrder = {
  ascend: "asc",
  descend: "desc",
};
export const suffixThumbnails = {
  pdf: "pdf.svg",
  default: "default.svg",
};

export const sortType = {
  createTime: "create_time",
  publishTime: "publish_time",
  terminateTime: "terminate_time",
  licenseeTime: "licensee_time",
  expireTime: "expire_time",
  appointmentTime: "appointment_time",
  inchargeTotal: "incharge_total",
  inchargeToday: "incharge_today",
  dischargeTotal: "discharge_total",
  dischargeToday: "discharge_today",
  moneyTotal: "money_total",
  moneyToday: "money_today",
  malfunctionTotal: "malfunction_total",
  malfunctionToday: "malfunction_today",
};
//全部单独使用
export const wholeType = [
  {
    label: "全部",
    value: "",
  },
];
export const approvalStatus = [
  {
    label: "待审批",
    value: 7,
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  {
    label: "审批通过",
    value: 8,
    color: "rgba(34, 34, 34, 1)",
    backGroundColor: "rgba(34, 34, 34, 0.04)",
    hide: true,
  },
  {
    label: "审批拒绝",
    value: 9,
    color: "rgba(34, 34, 34, 1)",
    backGroundColor: "rgba(34, 34, 34, 0.04)",
  },
  {
    label: "审批撤销",
    value: 10,
    color: "rgba(34, 34, 34, 1)",
    backGroundColor: "rgba(34, 34, 34, 0.04)",
  },
];
export const productionStatus = [
  {
    label: "未开始",
    value: 0,
    color: "rgba(149, 158, 195, 1)",
    backGroundColor: "rgba(149, 158, 195, 0.10)",
  },
  {
    label: "进行中",
    value: 1,
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  {
    label: "已取消",
    value: 2,
    color: "rgba(34, 34, 34, 1)",
    backGroundColor: "rgba(34, 34, 34, 0.04)",
  },
  {
    label: "已完成",
    value: 3,
    color: "rgba(82, 196, 26, 1)",
    backGroundColor: "rgba(246, 255, 237, 1)",
  },
  {
    label: "创建失败",
    value: 4,
    color: "rgba(255, 77, 79, 1)",
    backGroundColor: "rgba(255, 77, 79, .2)",
  },
  {
    label: "计划延期",
    value: 5,
    color: "rgba(255, 77, 79, 1)",
    backGroundColor: "rgba(255, 77, 79, .2)",
  },
  ...approvalStatus,
];
export const productionStatusFilter = [
  {
    label: "全部",
    value: "",
    color: "rgba(149, 158, 195, 1)",
    backGroundColor: "rgba(149, 158, 195, 0.10)",
  },
  ...productionStatus.filter((s) => !s.hide),
];
export const productionLineStatus = [
  {
    label: "未使用",
    value: 0,
    color: "rgba(149, 158, 195, 1)",
    backGroundColor: "rgba(149, 158, 195, 0.10)",
  },
  {
    label: "使用中",
    value: 1,
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  {
    label: "已取消",
    value: 2,
    color: "rgba(34, 34, 34, 1)",
    backGroundColor: "rgba(34, 34, 34, 0.04)",
  },
  {
    label: "已停用",
    value: 3,
    color: "rgba(255, 0, 0, 1)",
    backGroundColor: "rgba(255, 0, 0, 0.04)",
  },
  ...approvalStatus,
];
export const productionLineStatusFilter = [
  {
    label: "全部状态",
    value: "",
  },
  ...productionLineStatus.filter((s) => !s.hide),
];
export const supplierStatus = [
  {
    label: "正常合作",
    value: 0,
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  {
    label: "停止合作",
    value: 1,
    color: "rgba(149, 158, 195, 1)",
    backGroundColor: "rgba(149,158,195,0.1)",
  },
  ...approvalStatus,
];
export const supplierStatusFilters = [
  {
    label: "全部状态",
    value: "",
  },
  ...supplierStatus.filter((s) => !s.hide),
];

export const supplierLevels = [
  {
    label: "A级",
    value: "A",
  },
  {
    label: "B级",
    value: "B",
  },
  {
    label: "C级",
    value: "C",
  },
  {
    label: "D级",
    value: "D",
  },
];
export const supplierLevelsFilters = [
  {
    label: "全部等级",
    value: "",
  },
  ...supplierLevels.filter((s) => !s.hide),
];

export const categoryStatus = [
  {
    label: "正常使用",
    value: 0,
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  {
    label: "已取消",
    value: 1,
    color: "rgba(149, 158, 195, 1)",
    backGroundColor: "rgba(149,158,195,0.1)",
  },
  {
    label: "审批中",
    value: 5,
    color: "rgba(149, 158, 195, 1)",
    backGroundColor: "rgba(149,158,195,0.1)",
  },
];
export const materialStatus = [
  // {
  //   label: "未使用",
  //   value: 0,
  //   color: "rgba(149, 158, 195, 1)",
  //   backGroundColor: "rgba(149, 158, 195, 0.10)",
  // },
  // {
  //   label: "使用中",
  //   value: 1,
  //   color: "rgba(24, 144, 255, 1)",
  //   backGroundColor: "rgba(230, 247, 255, 1)",
  // },
  {
    label: "已审批",
    value: 1,
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  {
    label: "已取消",
    value: 2,
    color: "rgba(34, 34, 34, 1)",
    backGroundColor: "rgba(34, 34, 34, 0.04)",
  },
  ...approvalStatus,
];
export const departmentStatus = [
  {
    label: "正常",
    value: "0",
    color: "rgba(149, 158, 195, 1)",
    backGroundColor: "rgba(149, 158, 195, 0.10)",
  },
  {
    label: "停用",
    value: "1",
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
];
//采购状态
export const purchaseStatusList = [
  {
    label: "采购中",
    value: 0,
    color: "rgba(149, 158, 195, 1)",
    backGroundColor: "rgba(149, 158, 195, 0.10)",
  },
  {
    label: "已完成",
    value: 1,
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  {
    label: "已取消",
    value: 2,
    color: "rgba(34, 34, 34, 1)",
    backGroundColor: "rgba(34, 34, 34, 0.04)",
  },
  ...approvalStatus,
];
export const searchStatus = [
  {
    label: "全部状态",
    value: "",
  },
  {
    label: "采购中",
    value: 0,
    color: "rgba(149, 158, 195, 1)",
    backGroundColor: "rgba(149, 158, 195, 0.10)",
  },
  {
    label: "已完成",
    value: 1,
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  {
    label: "已取消",
    value: 2,
    color: "rgba(34, 34, 34, 1)",
    backGroundColor: "rgba(34, 34, 34, 0.04)",
  },
];
export const purchaseStatus = [
  {
    label: "采购中",
    value: 0,
    color: "rgba(149, 158, 195, 1)",
    backGroundColor: "rgba(149, 158, 195, 0.10)",
  },
  {
    label: "已完成",
    value: 1,
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  {
    label: "已取消",
    value: 2,
    color: "rgba(34, 34, 34, 1)",
    backGroundColor: "rgba(34, 34, 34, 0.04)",
  },
  ...approvalStatus,
];
//"采购订单类型，
export const purchaseOrderType = [
  {
    label: "全部类型",
    value: "",
  },
  {
    label: "普通采购订单",
    value: "NORMAL",
    color: "rgba(34, 34, 34, 1)",
    backGroundColor: "rgba(34, 34, 34, 0.04)",
  },
  {
    label: "安全库存采购",
    value: "SAFE_STOCK",
    color: "rgba(34, 34, 34, 1)",
    backGroundColor: "rgba(34, 34, 34, 0.04)",
  },
  {
    label: "销售采购订单",
    value: "MARKET_ORDER",
    color: "rgba(34, 34, 34, 1)",
    backGroundColor: "rgba(34, 34, 34, 0.04)",
  },
  {
    label: "修改订单BOM采购",
    value: "UPDATE_ORDER_BOM",
    color: "rgba(34, 34, 34, 1)",
    backGroundColor: "rgba(34, 34, 34, 0.04)",
  },
];
export const purchaseStatusFilters = [
  {
    label: "全部状态",
    value: "",
  },
  ...purchaseStatus.filter((s) => !s.hide),
];
export const qualityStatus = [
  {
    label: "未完成",
    value: 0,
    color: "rgba(149, 158, 195, 1)",
    backGroundColor: "rgba(149, 158, 195, 0.10)",
  },
  {
    label: "已完成",
    value: 1,
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  ...approvalStatus,
];

export const qualityType = [
  {
    label: "销售排产",
    value: "market",
    color: "rgba(149, 158, 195, 1)",
    backGroundColor: "rgba(149, 158, 195, 0.10)",
  },
  {
    label: "手动排产",
    value: "manual",
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  ...approvalStatus,
];
export const qualityStatusFilters = [
  {
    label: "全部状态",
    value: "",
  },
  ...qualityStatus.filter((s) => !s.hide),
];
export const qualitySource = [
  {
    label: "全部",
    value: "",
  },
  {
    label: "生产退料",
    value: "plan_residue",
  },
  {
    label: "产成品入库",
    value: "plan_product",
  },
  {
    label: "采购到货",
    value: "purchase",
  },
  {
    label: "订单退货",
    value: "order_return",
  },
  {
    label: "售后物料退库",
    value: "other_after_return",
  },
  {
    label: "普通退货（退款）",
    value: "other_money_return",
  },
  {
    label: "普通退货（不退款）",
    value: "other_no_money_return",
  }
];
//调拨状态
export const warehouseStatus = [
  {
    label: "未完成",
    value: 0,
    color: "rgba(149, 158, 195, 1)",
    backGroundColor: "rgba(149, 158, 195, 0.10)",
  },
  {
    label: "已完成",
    value: 1,
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  // ...approvalStatus,
];
export const warehouseStatusFilters = [
  {
    label: "全部状态",
    value: "",
  },
  ...warehouseStatus.filter((s) => !s.hide),
];

export const warehouseType = [
  {
    label: "生产材料出库",
    value: "PRODUCTION_MATERIAL_OUT",
    type: "material",
  },
  {
    label: "生产增料出库",
    value: "PRODUCTION_INCREASE_OUT",
    type: "material",
  },
  {
    label: "销售出库",
    value: "PRODUCT_SHIPPING_OUT",
    type: "material",
  },

  {
    label: "盘点出库",
    value: "INVENTORY_OUT",
    type: "material",
  },
  {
    label: "成品入库",
    value: "FINISHED",
    type: "material",
  },
  {
    label: "生产退料入库",
    value: "PLAN_RESIDUE",
    type: "material",
  },
  //  {
  //   label: '盘点入库',
  //   value: 'INVENTORY',
  //   type: "material",
  // },
  {
    label: "生产材料出库",
    value: "PRODUCTION_MATERIAL_OUT",
    type: "product",
  },
  {
    label: "生产增料出库",
    value: "PRODUCTION_INCREASE_OUT",
    type: "product",
  },
  {
    label: "销售出库",
    value: "PRODUCT_SHIPPING_OUT",
    type: "product",
  },

  {
    label: "盘点出库",
    value: "INVENTORY_OUT",
    type: "product",
  },
  {
    label: "其他入库",
    value: "OTHERS",
    type: "material",
    inOutType: "in",
  },
  {
    label: "成品入库",
    value: "FINISHED",
    type: "product",
  },
  {
    label: "生产退料入库",
    value: "PLAN_RESIDUE",
    type: "product",
  },
  {
    label: "盘点入库",
    value: "INVENTORY",
    type: "product",
  },
  // 以上是新增的类型

  {
    label: "调拨入库",
    value: "TRANSFER",
    type: "material",
    inOutType: "in",
  },
  {
    label: "报废品入库",
    value: "WASTE",
  },
  {
    label: "采购入库",
    value: "PURCHASE",
    type: "material",
    inOutType: "in",
  },
  {
    label: "剩余入库",
    value: "PLAN_RESIDUE",
  },

  {
    label: "耗损出库",
    value: "RAWMATERIAL_WASTE_OUT",
    type: "material",
    inOutType: "out",
  },
  {
    label: "其他出库",
    value: "RAWMATERIAL_NORMAL_OUT",
    type: "material",
    inOutType: "out",
  },
  {
    label: "不合格品入库",
    value: "UNQUALIFIED",
  },
  {
    label: "半成品入库",
    value: "SEMIFINISHED",
  },
  {
    label: "生产入库",
    value: "FINISHED",
    type: "product",
    inOutType: "in",
  },
  {
    label: "退货入库",
    value: "RETURN",
    type: "product",
    inOutType: "in",
  },
  {
    label: "销售出库",
    value: "PRODUCT_NORMAL_OUT",
    type: "product",
    inOutType: "out",
  },
  {
    label: "耗损出库",
    value: "PRODUCT_WASTE_OUT",
    type: "product",
    inOutType: "out",
  },
  {
    label: "其他出库",
    value: "PRODUCT_OTHER_OUT",
  },
  {
    label: "设备易耗品",
    value: "CONSUMABLE",
  }, {
    label: "研发领用",
    value: "DEVELOPMENT",
  },
  {
    label: "赠品出库",
    value: "GIFT_OUT",
    type: "material",
    inOutType: "out",
  },
  {
    label: "赠品出库",
    value: "GIFT_OUT",
    type: "product",
    inOutType: "out",
  },
  {
    label: "耗损出库",
    value: "WASTE_OUT",
    inOutType: "out",
  },
  {
    label: "调拨入库",
    value: "ALLOCATION_IN",
    inOutType: "in",
  },
  {
    label: "调拨出库",
    value: "ALLOCATION_OUT",
    inOutType: "out",
  },
  {
    label: "整单数出库",
    value: "WHOLE_ORDER_OUT",
    inOutType: "out",
  },
  {
    label: "经济物料领用",
    value: "MATERIAL_RECEIVE_OUT",
    inOutType: "out",
  },
  {
    label: "经济物料入库",
    value: "ECONOMIC_MATERIAL_IN",
    inOutType: "in",
  },
  {
    label: "经济物料出库",
    value: "ECONOMIC_MATERIAL_OUT",
    inOutType: "out",
  },
  {
    label: "退货出库",
    value: "PURCHASE_RETURN_OUT",
  },
  {
    label: "订单退货",
    value: "ORDER_RETURN",
    type: "material",
  },
  {
    label: "劳保用品出库",
    value: "LABOR_PROTECT_OUT",
  },
  {
    label: "售后物料领用",
    value: "AFTER_SALES_OUT",
  },
  {
    label: "售后物料退库",
    value: "AFTER_RETURN",
  },
  {
    label: "其他入库",
    value: "OTHERS_ACCIDENT_RETURN"
  },
  {
    label: "售后物料退库",
    value: "AFTER_RETURN",
  },
  {
    label: "普通退货（退款）",
    value: "RETURN_MONEY",
  },
  {
    label: "普通退货（不退款）",
    value: "RETURN_NO_MONEY",
  },

];

export const warehouseTypeFilters = [
  {
    label: "全部进出库类型",
    value: "",
  },
  ...warehouseType.filter((s) => !s.hide),
];
//结算状态
export const settlementStatus = [
  {
    label: "待结算",
    value: 0,
    color: "rgba(149, 158, 195, 1)",
    backGroundColor: "rgba(149, 158, 195, 0.10)",
  },
  {
    label: "已付款",
    value: 1,
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  {
    label: "暂存",
    value: 20,
    color: "rgba(149, 158, 195, 1)",
    backGroundColor: "rgba(149, 158, 195, 0.10)",
  },
  ...approvalStatus,
];
export const settlementStatusFilters = [
  {
    label: "全部状态",
    value: "",
  },
  ...settlementStatus.filter((s) => !s.hide),
];
export const storedStatus = [
  {
    label: "等待质检",
    value: 0,
  },
  {
    label: "质检进度",
    value: 1,
  },
  {
    label: "等待入仓",
    value: 2,
  },
  {
    label: "物料入仓",
    value: 3,
  },
  {
    label: "物料退货",
    value: 4,
  },
];

export const proSchedulType = [
  {
    label: "全部排产类型",
    value: "",
  },
  {
    label: "销售排产",
    value: "market",
  },
  {
    label: "手动排产",
    value: "manual",
  },
  // ...qualityStatus.filter((s) => !s.hide),
];
export const proSchedulStatus = [
  {
    label: "全部排产状态",
    value: "",
  },
  {
    label: "未排产",
    value: 0,
  },
  {
    label: "已排产",
    value: 1,
  },
  {
    label: "已取消",
    value: 2,
  },
  ...qualityStatus.filter((s) => !s.hide),
];
//客户等级
export const customGrade = [
  {
    label: "全部等级",
    value: "",
  },
  {
    label: "优质",
    value: "excellent",
  },
  {
    label: "良好",
    value: "good",
  },
  {
    label: "一般",
    value: "ordinary",
  },
];
// 合同类型
export const contractClassifyList = [
  {
    label: "普通合同",
    value: "Normal",
  },
  {
    label: "补充合同 ",
    value: "Supplement",
  },
];
// 单据类型

// 单据类型
export const invoiceTypeList = [
  {
    label: "正常流程",
    value: "Normal",
  },
  {
    label: "先取编号",
    value: "Prefetch number",
  },
];
// 是否自动生成编号
export const autoGenerationCodeFlagList = [
  {
    label: "是",
    value: 1,
  },
  {
    label: "否",
    value: 0,
  },
];
// 合同类型contractType  -> 业务类型
export const contractTypeList = [
  {
    label: "普通合同",
    value: "Normal contract",
  },
  {
    label: "研发合同",
    value: "Development contract",
  },
  {
    label: "样品合同",
    value: "Sample contract",
  },
  // {
  //   label: "内部合同",
  //   value: "Internal contract",
  // },
  {
    label: "备料合同",
    value: "Stock contract",
  },
];
// 业务类型
export const businessTypes = [
  {
    label: "国内业务（C）",
    value: "Domestic business",
    no: 'C'
  },
  {
    label: "国际贸易（G）",
    value: "International trade",
    no: 'G'
  },
  {
    label: "研发需求（Y）",
    value: "RD requirements",
    no: 'Y'
  },
  {
    label: "售后业务（W）",
    value: "After sales service",
    no: 'W'
  },
]


// 产品分类  -> 产品类别
export const productMarketClassificationList = [
  {
    label: "外贸",
    value: "Foreign trade",
  },
  {
    label: "特种车",
    value: "Special vehicle",
  },
  {
    label: "机车",
    value: "locomotive",
  },
  {
    label: "矿用",
    value: "mine",
  },
  {
    label: "储能",
    value: "Stored energy",
  },
  {
    label: "再制造市场",
    value: "Remanufacturing market",
  },
  {
    label: "其他",
    value: "Other",
  },
];
// 产品类别
export const productTypes = [
  {
    name: "矿用产品",
    label: "矿用产品（1）",
    value: "Mining product",
    no: 1,
  },
  {
    name: "非煤矿用产品",
    label: "非煤矿用产品（2）",
    value: "Non - coal mining product",
    no: 2,
  },
  {
    name: "工业车辆产品",
    label: "工业车辆产品（3）",
    value: "Industrial vehicle product",
    no: 3,
  },
  {
    name: "再制造产品",
    label: "再制造产品（5）",
    value: "Remanufactured product",
    no: 5,
  },
  {
    name: "储能产品",
    label: "储能产品（6）",
    value: "Energy storage product",
    no: 6,
  },
  {
    name: "综合业务",
    label: "综合业务（7）",
    value: "Comprehensive business",
    no: 7,
  },
]


// 合同状态
export const statusList = [
  {
    label: "待下单",
    value: 0,
    backGroundColor: "rgba(255, 165, 0, 0.10)",
    color: "#FAAD14",
  },
  {
    label: "部分下单",
    value: 4,
    backGroundColor: "rgba(255, 165, 0, 0.10)",
    color: "#FAAD14",
  },
  {
    label: "已下单",
    value: 1,
    backGroundColor: "#E6F7FF",
    color: "#1890FF",
  },
  {
    label: "待审批",
    value: 7,
    backGroundColor: "#E6F7FF",
    color: "#1890FF",
  },
  {
    label: "已完成",
    value: 5,
    backGroundColor: "#F6FFED",
    color: "#52C41A",
  },
  {
    label: "暂存",
    value: 3,
    backGroundColor: "rgba(255, 165, 0, 0.10)",
    color: "#FAAD14",
  },
  {
    label: "已作废",
    value: 2,
    backGroundColor: "rgba(255, 0, 0, 0.10)",
    color: "red",
  },
  {
    label: "审批通过",
    value: 8,
    backGroundColor: "#F6FFED",
    color: "#52C41A",
  },
  {
    label: "审批不通过",
    value: 9,
    backGroundColor: "rgba(255, 0, 0, 0.10)",
    color: "red",
  },
  {
    label: "审批撤销",
    value: 10,
    backGroundColor: "rgba(255, 0, 0, 0.10)",
    color: "red",
  },
];
// 使用国家
export const useCountryList = [
  {
    label: "国内",
    value: 0,
  },
  {
    label: "国外 ",
    value: 1,
  },
];

// 发票类型
export const billingTypeList = [
  {
    label: "增值税专用发票",
    value: 1,
  },
  {
    label: "普通发票",
    value: 2,
  },
  {
    label: "其他",
    value: 3,
  },
];
export const billTypes = [
  {
    label: "全部",
    value: '',
  },
  {
    label: "蓝字",
    value: '1',
  },
  {
    label: "红字",
    value: '2',
  },

]

// 产品明细
export const orderConfigList = {
  // 单体标识，枚举（1 常规； 2 贴MA及参数； 3 中性； 4 特殊 ），存储时以逗号分割。
  monomerIdentification: [
    {
      label: "常规",
      value: "1",
    },
    {
      label: "贴MA及参数",
      value: "2",
    },
    {
      label: "中性",
      value: "3",
    },
    {
      label: "特殊",
      value: "4",
    },
  ],
  // packingRequirement 1 常规包装； 2 木箱包装； 3 危包箱包装(木箱/纸箱)； 4 免熏蒸木板）
  packingRequirement: [
    {
      label: "常规包装",
      value: "1",
    },
    {
      label: "木箱包装",
      value: "2",
    },
    {
      label: "危包箱包装(木箱/纸箱)",
      value: "3",
    },
    {
      label: "免熏蒸木板",
      value: "4",
    },
  ],
  // accompanyingDocument   1 合格证、检验报告； 2 中性； 3 英文； 4 说明书； 5 装箱清单； 6 保修手册 ）
  accompanyingDocument: [
    {
      label: "合格证、检验报告",
      value: "1",
    },
    {
      label: "中性",
      value: "2",
    },
    {
      label: "英文",
      value: "3",
    },
    {
      label: "说明书",
      value: "4",
    },
    {
      label: "装箱清单",
      value: "5",
    },
    {
      label: "保修手册",
      value: "6",
    },
  ],
  //   chassisIdentification
  // string
  // 可选
  // 机箱标识，枚举（0 无； 1 常规； 2 中性； 3 全英文； 4 客供； 5 贴二维码）

  chassisIdentification: [
    {
      label: "无",
      value: "0",
    },
    {
      label: "常规",
      value: "1",
    },
    {
      label: "中性",
      value: "2",
    },
    {
      label: "全英文",
      value: "3",
    },
    {
      label: "客供",
      value: "4",
    },
    {
      label: "贴二维码",
      value: "5",
    },
  ],

  //
  // specification 说明书，枚举（0 无； 1 通用； 2 专用； 3 中性； 4 中英文； 5 全英文）
  specification: [
    {
      label: "无",
      value: "0",
    },
    {
      label: "通用",
      value: "1",
    },
    {
      label: "专用",
      value: "2",
    },
    {
      label: "中性",
      value: "3",
    },
    {
      label: "中英文",
      value: "4",
    },
    {
      label: "全英文",
      value: "5",
    },
  ],
  // dogtag 铭牌，枚举（0 无； 1 中文； 2 中英文； 3 全英文； 4 中性； 5 客供； 6 MA； 7 KY； 8 KA）
  dogtag: [
    {
      label: "常规",
      value: "0",
    },
    {
      label: "中文",
      value: "1",
    },
    {
      label: "中英文",
      value: "2",
    },
    {
      label: "全英文",
      value: "3",
    },
    {
      label: "中性",
      value: "4",
    },
    {
      label: "客供",
      value: "5",
    },
    {
      label: "MA",
      value: "6",
    },
    {
      label: "KY",
      value: "7",
    },
    {
      label: "KA",
      value: "8",
    },
  ],

  // chargerNameplate 充电机铭牌（0 无； 1 中性； 2 英文； 3 中文； 4 贴公司logo）
  chargerNameplate: [
    {
      label: "无",
      value: "0",
    },
    {
      label: "中性",
      value: "1",
    },
    {
      label: "英文",
      value: "2",
    },
    {
      label: "中文",
      value: "3",
    },
    {
      label: "贴公司logo",
      value: "4",
    },
  ],

  // chargerDisplay 充电机显示屏（0 无； 1 英文； 2 中文）
  chargerDisplay: [
    {
      label: "无",
      value: "0",
    },
    {
      label: "英文",
      value: "1",
    },
    {
      label: "中文",
      value: "2",
    },
  ],

  // chargerManual 充电机说明书，枚举（0 无； 1 英文； 2 中文； 3 中英文）
  chargerManual: [
    {
      label: "无",
      value: "0",
    },
    {
      label: "英文",
      value: "1",
    },
    {
      label: "中文",
      value: "2",
    },
    {
      label: "中英文",
      value: "3",
    },
  ],
};
// 收款类型
export const collectionType = [
  {
    label: "销售收款",
    value: "1",
  },
  {
    label: "销售退款",
    value: "2",
  },
  {
    label: "其他收款",
    value: "3",
  },
];
// 结算方式结算方式（1-现汇，2-银行承兑，3-现金，4-转账，5-其他，6-商业承兑）
export const settlementType = [
  {
    label: "现汇",
    value: "1",
  },
  {
    label: "银行承兑",
    value: "2",
  },
  {
    label: "商业承兑",
    value: "6",
  },
  {
    label: "其他",
    value: "5",
  },
  // {
  //   label: '现金',
  //   value: '3',
  // }, {
  //   label: '转账',
  //   value: '4',
  // },
];
// 类型
export const mateOrProdType = [
  {
    label: "物料",
    value: "material",
  },
  {
    label: "成品",
    value: "product",
  },
];
export const calculateType = [
  {
    label: "全部",
    value: "",
  },
  {
    label: "盘盈",
    value: "1",
  },
  {
    label: "盘亏",
    value: "0",
  },
];

//oa审批类型
export const oaExamineType = [
  {
    label: "正常执行任务",
    value: "EXECUTE_TASK_NORMAL",
  },
  {
    label: "代理人执行任务",
    value: "EXECUTE_TASK_AGENT",
  },
  {
    label: "前加签任务",
    value: "APPEND_TASK_BEFORE",
  },
  {
    label: "后加签任务",
    value: "APPEND_TASK_AFTER",
  },
  {
    label: "转交任务",
    value: "REDIRECT_TASK",
  },
  {
    label: "发起流程实例",
    value: "START_PROCESS_INSTANCE",
  },
  {
    label: "终止(撤销)流程实例",
    value: "TERMINATE_PROCESS_INSTANCE",
  },
  {
    label: "结束流程实例",
    value: "FINISH_PROCESS_INSTANCE",
  },
  {
    label: "添加评论",
    value: "ADD_REMARK",
  },
  {
    label: "审批退回",
    value: "REDIRECT_PROCESS",
  },
  {
    label: "抄送",
    value: "PROCESS_CC",
  },
];

//订单状态
export const orderStatus = [
  {
    label: "研发中",
    value: 0,
  },
  {
    label: "生产中",
    value: 1,
  },
  {
    label: "待出库",
    value: 2,
  },
  {
    label: "已邮递",
    value: 3,
  },
  {
    label: "已交付",
    value: 4,
  },
];
// 产品和物料

export const matProType = [
  {
    label: "物料",
    value: "MATERIAL",
  },
  {
    label: "产品",
    value: "PRODUCT",
  },
];
export const ProcurementRequirementStatus = [
  {
    label: "待生成订单",
    value: 0,
  },
  {
    label: "已生成订单",
    value: 1,
  },
  {
    label: "已取消",
    value: 2,
  },
  {
    label: "待审批",
    value: 7,
  },
  {
    label: "审批通过",
    value: 8,
  },
  {
    label: "审批不通过",
    value: 9,
  },
  {
    label: "已撤销",
    value: 10,
  },
];

export const outOrIn = [
  {
    label: "全部出入库",
    value: "",
  },
  {
    label: "出库",
    value: "out",
  },
  {
    label: "入库",
    value: "in",
  },
];
export const paymentType = [
  {
    label: "全部款项",
    value: "",
  },
  {
    label: "应付款",
    value: 0,
  },
  {
    label: "预付款",
    value: 1,
  },
  {
    label: "其他款项",
    value: 2,
  },
];

export const examineAndApproveStatus = [
  {
    label: "处理中",
    value: "RUNNING",
  },
  {
    label: "同意",
    value: "AGREE",
  },
  {
    label: "拒绝",
    value: "REFUSE",
  },
  {
    label: "未处理",
    value: "NONE",
  },
];
export const requirementStatus = [
  {
    label: "待生成订单",
    value: 0,
  },
  {
    label: "已取消",
    value: 2,
  },
];
export const whole = [
  {
    label: "全部状态",
    value: "",
  },
];
export const rawMaterialStatus = [
  ...whole.filter((s) => !s.hide),
  {
    label: "待审批",
    value: "7",
  },
  // {
  //   label: "未使用",
  //   value: "0",
  // },
  // {
  //   label: "使用中",
  //   value: "1",
  // },
  {
    label: "已审批",
    value: "1",
  },
  {
    label: "审批拒绝",
    value: "9",
  },
  {
    label: "停用",
    value: "3",
  },
];
// 发票状态

export const invoiceStatus = [
  {
    label: "全部",
    value: "",
  },
  {
    label: "已审核",
    value: 0,
  },
  {
    label: "财务已审核",
    value: 1,
  },
  {
    label: "待审批",
    value: 7,
  },
  {
    label: "审批不通过",
    value: 9,
  },
  {
    label: "审批撤回",
    value: 10,
  },
  {
    label: "暂存",
    value: 20,
  },
];
export const issueInvoiceTypeList = [
  {
    label: "蓝字发票",
    value: 1,
  },
  {
    label: "红字发票",
    value: 2,
  },
];

export const bomTypeList = [
  {
    label: "基础BOM",
    value: "foundationBom",
  },
  {
    label: "订单BOM",
    value: "orderBom",
  },
];
export const warehousingTypeList = [
  {
    label: "产成品入库",
    value: "FINISHED",
  },
  {
    label: "采购入库",
    value: "PURCHASE",
  },
  {
    label: "生产退料入库",
    value: "PLAN_RESIDUE",
  },
  {
    label: "订单退货",
    value: "ORDER_RETURN",
  },
  {
    label: "售后物料退库",
    value: "AFTER_RETURN",
  },
  {
    label: "普通退货（退款）",
    value: "RETURN_MONEY",
  },
  {
    label: "普通退货（不退款）",
    value: "RETURN_NO_MONEY",
  },

];
//出库申请状态的查询条件
export const newWarehouseType = [
  {
    label: "生产材料出库",
    value: "PRODUCTION_MATERIAL_OUT",
  },
  {
    label: "生产增料出库",
    value: "PRODUCTION_INCREASE_OUT",
  },
  {
    label: "经济物料领用",
    value: "MATERIAL_RECEIVE_OUT",
  },
  {
    label: "其他物料领用",
    value: "PRODUCT_OTHER_OUT",
  },
  {
    label: "设备易耗品",
    value: "CONSUMABLE",
  }, {
    label: "研发领用",
    value: "DEVELOPMENT",
  },
  {
    label: "整单数出库",
    value: "WHOLE_ORDER_OUT",
  },
  {
    label: "销售出库",
    value: "PRODUCT_SHIPPING_OUT",
  },
  {
    label: "采购退货出库",
    value: "PURCHASE_RETURN_OUT",
  },
  {
    label: "劳保用品出库",
    value: "LABOR_PROTECT_OUT",
  },
  {
    label: "售后物料领用",
    value: "AFTER_SALES_OUT",
  },
  {
    label: "售后物料退库",
    value: "AFTER_RETURN",
  },
  {
    label: "PACK线边库领料",
    value: "PACK_LINE_OTHERS",
  },
  {
    label: "电芯线边库领料",
    value: "BATTERY_LINE_OTHERS",
  },
];

//生产订单
export const orderTypeList = [
  {
    label: "外协订单",
    value: "OUT_SOURCE_ORDER",
  },
  {
    label: "生产订单",
    value: "PRODUCTION_ORDER",
  },
  {
    label: "内部订单",
    value: "INTERNAL_ORDER",
  },
  {
    label: "售后订单",
    value: "AFTER_SALE_ORDER",
  },
  {
    label: "研发订单",
    value: "DEVELOPMENT_ORDER",
  },
];
// 订单bom变更类型
export const orderAlterationType = [
  {
    label: "设计变更",
    value: "1",
  },
  {
    label: "订单变更",
    value: "2",
  },
  {
    label: "BOM错误",
    value: "3",
  },
];
// 是否需要调试
export const whetherDebugging = [
  {
    label: "是",
    value: "1",
  },
  {
    label: "否",
    value: "0",
  },
];
// 操作类型
export const operateTypeList = [
  {
    label: "新增",
    value: "1",
  },
  {
    label: "修改",
    value: "2",
  },
  {
    label: "删除",
    value: "3",
  },
  {
    label: "通知",
    value: "4",
  },
];

// 结算方式结算方式（1-现汇，2-银行承兑，3-现金，4-转账，5-其他，6-商业承兑）
export const settlementMethod = [
  {
    label: "现汇",
    value: 1,
  },
  {
    label: "银行承兑",
    value: 2,
  },
  {
    label: "商业承兑",
    value: 6,
  },
  {
    label: "其他",
    value: 5,
  },
];
// 开具类型
export const invoiceIssuance = [
  {
    label: "已开具",
    value: 1,
  },
  {
    label: "未开具",
    value: 2,
  },
];
// 销售订单状态
export const purchaseReturnsList = [
  {
    label: "已审核",
    value: 0,
    backGroundColor: "rgba(255, 165, 0, 0.10)",
    color: "#FAAD14",
  },
  {
    label: "生产中",
    value: 1,
    backGroundColor: "rgba(255, 165, 0, 0.10)",
    color: "red",
  },
  {
    label: "已交付",
    value: 4,
    backGroundColor: "rgba(44, 241, 20, 0.10)",
  },
  {
    label: "已完成",
    value: 6,
    backGroundColor: "#F6FFED",
    color: "#52C41A",
  },
  {
    label: "待出库",
    value: 2,
    backGroundColor: "rgba(255, 165, 0, 0.10)",
    color: "red",
  },
  {
    label: "已邮递",
    value: 3,
    backGroundColor: "rgba(44, 241, 20, 0.10)",
  },
  {
    label: "已取消",
    value: 5,
    backGroundColor: "rgba(255, 0, 0, 0.10)",
    color: "red",
  },
  {
    label: "待审批",
    value: 7,
    backGroundColor: "#E6F7FF",
    color: "#1890FF",
  },
  {
    label: "审批通过",
    value: 8,
    backGroundColor: "rgba(44, 241, 20, 0.10)",
    color: "#52C41A",
  },
  {
    label: "审批不通过",
    value: 9,
    backGroundColor: "rgba(255, 0, 0, 0.10)",
    color: "red",
  },
  {
    label: "审批撤回",
    value: 10,
    backGroundColor: "rgba(255, 0, 0, 0.10)",
    color: "red",
  },
];

// 订单bom类型
export const orderBOMType = [
  {
    label: "进行中",
    value: 0,
  },
  {
    label: "待审批",
    value: 7,
  },
  {
    label: "已完成",
    value: 6,
  },
];
// 订单bom类型
export const arrivalRecordStatus = [
  {
    label: "未锁定",
    value: 1,
  },
  {
    label: "审核中",
    value: 2,
  },
  {
    label: "已锁定",
    value: 3,
  },
];
// 退货订单状态
export const returnNoStatus = [
  {
    label: "待审核",
    value: 0,
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  {
    label: "审核通过",
    value: 1,
    backGroundColor: "#F6FFED",
    color: "#52C41A",
  },
  {
    label: "审核不通过",
    value: 2,
    backGroundColor: "rgba(255, 0, 0, 0.10)",
    color: "red",
  },
  {
    label: "审核撤回",
    value: 3,
    backGroundColor: "rgba(255, 0, 0, 0.10)",
    color: "red",
  },
];

export const applicantTypeList = [
  {
    label: "采购",
    value: "purchase",
  },
  {
    label: "生产",
    value: "plan",
  },
  {
    label: "订单",
    value: "market",
  },
  {
    label: "入库",
    value: "return",
  },
  {
    label: "退库",
    value: "others_return",
  }
];
// PRODUCTION_MATERIAL_OUT
export const orderBomIsLockedList = [
  {
    label: "已锁定",
    value: 1,
  },
  {
    label: "未锁定",
    value: 0,
  },
];
// 发货的签收和未牵手
export const whetherSignForReceipt = [
  {
    label: "未签收",
    value: 0,
  },
  {
    label: "已签收",
    value: 1,
  },
];
export const taxRateList = [
  {
    label: 0,
    value: 0,
  },
  {
    label: 6,
    value: 6,
  },
  {
    label: 13,
    value: 13,
  },
  // {
  //   label: "12",
  //   value: 12,
  // },
];

export const materialTypes = [
  {
    label: "初始bom物料",
    value: 'zero',
  },
  {
    label: "修改bom增加物料",
    value: 'one',
  },
  {
    label: "非bom物料",
    value: 'two',
  },
]