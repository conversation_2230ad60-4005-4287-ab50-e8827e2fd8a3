import {
  ref,
  onBeforeUnmount,
  onMounted,
  reactive,
  getCurrentInstance,
  computed,
} from "vue";
import { useRouter } from "vue-router";

// import Clipboard from 'clipboard'

//发送验证码按钮禁用
export function useDisableTime() {
  const disableTime = ref(0);
  let disableTimeInterval = null;
  const setDisableTime = () => {
    if (disableTimeInterval) {
      clearInterval(disableTimeInterval);
    }
    disableTime.value = 60;
    disableTimeInterval = setInterval(() => {
      disableTime.value--;
      if (disableTime.value == 0) {
        clearInterval(disableTimeInterval);
      }
    }, 1000);
  };
  onBeforeUnmount(() => {
    if (disableTimeInterval) {
      clearInterval(disableTimeInterval);
    }
    disableTime.value = 0;
  });
  return {
    disableTime,
    setDisableTime,
  };
}
//分页下拉加载
export function useLoadMore(pageSize, getList) {
  const page = reactive({
    current: 1, //当前
    size: pageSize, //每页条数
    totalPages: 0, //总页数
  });
  const setTotalPage = (total) => {
    page.totalPages = total;
  };
  const btnMoreLoading = ref(false);
  const handleMore = async () => {
    if (!btnMoreLoading.value && page.current < page.totalPages) {
      btnMoreLoading.value = true;
      page.current++;
      await getList();
      btnMoreLoading.value = false;
    }
  };
  onMounted(async () => {
    window.addEventListener("scroll", function () {
      //真实内容的高度
      var pageHeight = Math.max(
        document.body.scrollHeight,
        document.body.offsetHeight
      );
      //视窗的高度
      var viewportHeight =
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight ||
        0;
      //隐藏的高度
      var scrollHeight =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop ||
        0;
      //判断是否到底部
      if (pageHeight - viewportHeight - scrollHeight <= 392) {
        //事件
        handleMore && handleMore();
      }
    });
  });
  return {
    btnMoreLoading,
    setTotalPage,
    page,
  };
}
import { sortType, sortOrder } from "@/common/constant.js";
export function usePagenation(fnGetList, pageSize = 7) {
  const paginationProps = ref({
    pageSize,
    current: 1,
    total: 0,
    sortType: "create_time",
    sortOrder: "desc",
    pageSizeOptions: ["7", "10", "50", "100"],
  });
  const pageParam = computed(() => {
    let { current, pageSize, sortType, sortOrder, position } =
      paginationProps.value;
    return { pageNum: current, pageSize, sortType, sortOrder, position };
  });
  const onTableChange = async (pagination, filters, sorter) => {
    if (!pagination.isTrusted) {
      paginationProps.value.current = pagination.current;
      paginationProps.value.pageSize = pagination.pageSize;
      if (sorter?.order) {
        paginationProps.value.sortType = sortType[sorter.columnKey];
        paginationProps.value.sortOrder = sortOrder[sorter.order];
      } else {
        paginationProps.value.sortType = undefined;
        paginationProps.value.sortOrder = undefined;
      }
      await fnGetList();
    }
  };
  const refresh = async () => {
    paginationProps.value.current = 1;
    await fnGetList();
  };
  return {
    paginationProps,
    onTableChange,
    refresh,
    pageParam,
  };
}
export function useCopy(selector) {
  const { proxy } = getCurrentInstance();
  const copy = () => {
    var clipboard = new Clipboard(selector);
    clipboard.on("success", (e) => {
      proxy.$message.success("复制成功");
      // 释放内存
      clipboard.destroy();
    });
    clipboard.on("error", (e) => {
      // 不支持复制
      console.log("该浏览器不支持自动复制");
      // 释放内存
      clipboard.destroy();
    });
  };
  return {
    copy,
  };
}

export function downFile(url, fileName) {
  const xhr = new XMLHttpRequest();
  xhr.open("GET", url, true);
  xhr.responseType = "blob";

  xhr.onload = function () {
    const blob = xhr.response;
    const fileType = url.split(".").pop();

    // 处理下载
    const downloadLink = document.createElement("a");
    downloadLink.href = window.URL.createObjectURL(blob);
    downloadLink.download = fileName;
    downloadLink.style.display = "none";
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);

    const userAgent = navigator.userAgent;
    const isAndroid =
      userAgent.indexOf("Android") > -1 || userAgent.indexOf("Adr") > -1; // android 终端
    const isIos = !!userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios 终端

    // 处理预览（根据文件类型判断）
    if (
      [
        "jpg",
        "png",
        "pdf",
        "docx",
        "txt",
        "html",
        "htm",
        "xls",
        "doc",
        "xlsx",
        "DWG",
        "dwg",
        "cad",
        "CAD",
        "SWG",
        "swg",
        "rar",
        "RAR",
        "rtf",
        "zip",
        "ZIP",
      ].includes(fileType)
    ) {
      if (["xlsx", "docx", "doc", "xls"].includes(fileType)) {
        if (isIos) {
          window.location.href = `https://view.officeapps.live.com/op/view.aspx?src=${url}`;
        } else {
          // 对于 docx 可以尝试其他预览方式
          // 这里可以根据实际需求进一步处理
          window.open(
            `https://view.officeapps.live.com/op/view.aspx?src=${url}`
          );
        }
      } else {
        console.log("000");
        if (
          [
            "DWG",
            "dwg",
            "cad",
            "CAD",
            "SWG",
            "swg",
            "rar",
            "ZIP",
            "zip",
          ].includes(fileType)
        ) {
          if (isIos) {
            console.log(1);
            window.location.href = url;
          } else {
            console.log(2);
            // window.open(url);
          }
        } else {
          if (isIos) {
            console.log(3);
            window.location.href = url;
          } else {
            console.log(4);
            window.open(url);
          }
        }
      }
    }
  };

  xhr.send();
}
