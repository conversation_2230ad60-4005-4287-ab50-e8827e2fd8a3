import { createVNode } from "vue";
import { $http, RequestUse, ResponseUse } from "./base";
import { responseIntercept, createResponseInterceptError } from "./errorHandle";
import { Post, Delete, Put, Patch, Get } from "./methods";
import { message } from "ant-design-vue";
import router from "@/router";
import { Modal } from "ant-design-vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { getToken } from "@/utils/auth.js";
import { useUserStore } from "@/stores/user.js";

const errorMsg = {
  10040: "短信验证码错误",
  10005: "图形验证码错误",
  10109: "手机号未注册，请校验后重新输入",
  10042: "账号或密码错误，请校验后重新输入",
  10107: "手机号已经被注册",
  10108: "邮箱已经被注册",
  10110: "用户不存在",
  10044: "邮箱验证码错误",
  100206: "企业名称已存在",
  10700: "清单中包含解除协助",
  10118: "当前企业已切换，请刷新后重试",
};
let showVersionConfirm = false,
  show401 = false;
const showConfirm = () => {
  showVersionConfirm = true;
  Modal.confirm({
    title: () => "检测到当前有版本更新，请刷新升级版本",
    icon: () => createVNode(ExclamationCircleOutlined),
    okText: "刷新",
    onOk() {
      showVersionConfirm = false;
      location.reload();
    },
    cancelButtonProps: {
      ghost: true,
    },
  });
};

let errorHandler = async (status, errorMessage) => {
  if (status == 10009) {
    if (!showVersionConfirm) {
      showConfirm();
    }
  } else if (
    status === 401 ||
    // status === 403 ||
    status == 10115 ||
    status == 10117
  ) {
    const store = useUserStore();
    if (!show401) {
      show401 = true;
      message.error(errorMessage);
      await store.LogOut();
      router.push("/login");
      setTimeout(() => {
        show401 = false;
      }, 200);
    }
    return;
  } else if (status === 60001) {
    router.push("/");
  } else {
    errorMessage = errorMsg[status] || errorMessage;
    message.error(errorMessage);
    if (status === 10814) {
      router.push("/preorder");
    }
  }
};
RequestUse((config) => {
  if (config.isToken != false) {
    const token = getToken();
    config.headers.Authorization = `Bearer ${token}`;
  }else{
    config.headers.Authorization = undefined;
  }
  // config.headers.platform = 'mingwork'
  // let supplierPcClientVersion = localStorage.getItem('supplierPcClientVersion')
  // if (supplierPcClientVersion) {
  //   config.headers.supplierPcClientVersion = supplierPcClientVersion
  // }
  return config;
});
ResponseUse(
  responseIntercept(errorHandler),
  createResponseInterceptError(errorHandler)
);

export { Post, Delete, Put, Patch, Get, $http, RequestUse, ResponseUse };
