let createResponseInterceptError = (errorHandler) => {
  return function (error) {
    let _message = null;
    if (error && error.response) {
      switch (error.response.status) {
        case 400:
          _message = "错误请求";
          break;
        case 453:
          if (error.response.data.msg) {
            _message = error.response.data.msg;
          } else {
            _message = "业务错误";
          }
          break;
        case 404:
          _message = "错误请求";
          break;
        case 401:
          _message = "对不起，您的登录已失效...请重新登录!";
          break;
        // case 403:
        //   _message = "无权限操作，请联系相关人员";
        //   break;
        case 408:
          _message = "请求超时";
          break;
        case 500:
          _message = "网络错误，请稍后再试";
          break;
        case 501:
          _message = "功能未实现";
          break;
        case 503:
          _message = "服务不可用";
          break;
        case 504:
          _message = "网关错误";
          break;
        default:
          _message = error.response.data.errorMessage;
      }
      errorHandler && errorHandler(error.response.status || 500, _message);
    } else {
      _message = "连接到服务器失败";
      errorHandler && errorHandler(500, _message);
    }
    return Promise.reject(error);
  };
};

let responseIntercept = function (errorHandler) {
  return function (response) {


    if (response?.data && response.data.code) {
      let { code, msg } = response.data;
      if (code != 200) {
        errorHandler && errorHandler(code, msg);
        // return Promise.reject(msg);//此返回获取不到接口接口下面代码无法执行所以直接返回结果-- return response.data
        return response.data
      }
    }
    return response?.data;
  };
};

export { responseIntercept, createResponseInterceptError };
