import axios from 'axios'

let $http = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  headers: {
    'X-Requested-With': 'XMLHttpRequest'
  },
  timeout: 30000
})

// console.log(process.env.NODE_ENV)

function RequestUse(request, requestError) {
  $http.interceptors.request.use(request, requestError)
}

function ResponseUse(response, responseError) {
  $http.interceptors.response.use(response, responseError)
}

export { $http, RequestUse, ResponseUse }
