// 乘法
export function numMulti(num1, num2) {
  var baseNum = 0;
  try {
    baseNum += num1.toString().split(".")[1].length;
  } catch (e) {}
  try {
    baseNum += num2.toString().split(".")[1].length;
  } catch (e) {}
  return (
    (Number(num1.toString().replace(".", "")) *
      Number(num2.toString().replace(".", ""))) /
    Math.pow(10, baseNum)
  );
}

export function subtractTwo(arg1, arg2) {
  var r1, r2, m, n;
  try {
    r1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  //last modify by deeka
  //动态控制精度长度
  n = r1 >= r2 ? r1 : r2;
  return (arg2 * m - arg1 * m) / m;
}

// 减法
export const subtract = (arg1, arg2, arg3, arg4, arg5) => {
  var r1, r2, r3, r4, r5, m, n;
  try {
    r1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  try {
    r3 = arg3.toString().split(".")[1].length;
  } catch (e) {
    r3 = 0;
  }
  try {
    r4 = arg4.toString().split(".")[1].length;
  } catch (e) {
    r4 = 0;
  }
  try {
    r5 = arg5.toString().split(".")[1].length;
  } catch (e) {
    r5 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2, r3, r4));
  //last modify by deeka
  //动态控制精度长度
  n = r1 >= r2 ? r1 : r2;
  return (arg1 * m - arg2 * m - arg3 * m - arg4 * m - arg5 * m) / m < 0
    ? 0
    : (arg1 * m - arg2 * m - arg3 * m - arg4 * m - arg5 * m) / m;
};
