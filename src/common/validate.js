import { mobile, email, password, money } from "@/common/reg";
import isLocalFileExist$ from "dingtalk-jsapi/api/union/isLocalFileExist";
let checkPhone = async (rule, value) => {
  if (!value) {
    return false;
  }
  if (!mobile.test(value)) {
    return Promise.reject("请填写正确的手机号码");
  }
};
let checkEmail = async (rule, value) => {
  if (!value) {
    return false;
  }
  if (!email.test(value)) {
    return Promise.reject("请填写正确的邮箱");
  }
};
let checkPassword = async (rule, value) => {
  if (!value) {
    return Promise.reject("请填写密码");
  } else if (value.length < 8) {
    return Promise.reject("密码至少为8位");
  } else if (!password.test(value)) {
    return Promise.reject("密码只能为数字、字母、下划线");
  }
};
let checkMoney = async (rule, value) => {
  if (!value) {
    return Promise.reject("请填写预算金额");
  } else if (!money.test(value)) {
    return Promise.reject("请填写正确的金额");
  }
};
export function roundNumFun(num, decimals) {
  const fraction = Math.pow(10, decimals);
  const integerPart = Math.round(num * fraction);
  let roundNum = integerPart / fraction;
  if (roundNum) {
    const num = roundNum.toString();
    if (num.indexOf(".") != -1) {
      const nums = num.slice(num.indexOf(".") + 1);
      if (nums.length == 1) {
        return roundNum;
      }
      return roundNum;
    }
  }
  return roundNum;
}
let formatter2 = (value) => {
  let valueNum = `${value}`
    .replace(/\B(?=(\d{3})+(?!\d))/g, "")
    .replace(/^(-)*(\d+)\.(\d{2}).*$/, "$1$2.$3");
  if (valueNum > 0) {
    return valueNum;
  } else {
    return 0;
  }
};
// let formatter4 = (value) => {
//   let valueNum = `${value}`
//     .replace(/\B(?=(\d{3})+(?!\d))/g, "")
//     .replace(/^(-)*(\d+)\.(\d{4}).*$/, "$1$2.$3");
//   if (valueNum > 0) {
//     return valueNum;
//   } else {
//     return 0;
//   }
// };
const formatter4 = (value) => {
  if (typeof value !== 'number' && typeof value !== 'string') {
    return 0;
  }
  const valueNum = Number(value);
  if (isNaN(valueNum) || valueNum <= 0) {
    return 0;
  }
  return valueNum.toFixed(4).replace(/\.?0+$/, '');
};
let formatter6 = (value) => {
  let valueNum = `${value}`
    .replace(/\B(?=(\d{3})+(?!\d))/g, "")
    .replace(/^(-)*(\d+)\.(\d{6}).*$/, "$1$2.$3");
  if (valueNum > 0) {
    return valueNum;
  } else {
    return 0;
  }
};
let formatter8 = (value) => {
  console.log(value, "value");
  let valueNum = `${value}`
    .replace(/\B(?=(\d{3})+(?!\d))/g, "")
    .replace(/^(-)*(\d+)\.(\d{8}).*$/, "$1$2.$3");
  if (valueNum > 0) {
    return valueNum;
  } else {
    return 0;
  }
  // return valueNum;
};
let formatter3 = (value) => {
  let valueNum = `${value}`
    .replace(/\B(?=(\d{3})+(?!\d))/g, "")
    .replace(/^(-)*(\d+)\.(\d{3}).*$/, "$1$2.$3");
  if (valueNum > 0) {
    return valueNum;
  } else {
    return 0;
  }
};

// 20240805格式的当天时间
const today = new Date();
const formattedToday = `${today.getFullYear()}${String(
  today.getMonth() + 1
).padStart(2, "0")}${String(today.getDate()).padStart(2, "0")}`;
console.log(formattedToday, "时间");
// 订单中的计算
function calculateUntaxedAmount(taxIncludedUnitPrice, quantity, taxRate) {
  console.log(
    taxIncludedUnitPrice,
    quantity,
    taxRate,
    "taxIncludedUnitPrice, quantity, taxRate"
  );
  // 含税单价  数量  税率
  let multiplier = 1 + taxRate / 100;
  let numerator = taxIncludedUnitPrice * quantity;
  let denominator = multiplier;

  // 将乘法和除法运算转换为整数运算，通过乘以适当的倍数来避免浮点数精度问题
  let scale = 100;
  let scaledNumerator = numerator * scale;
  let scaledDenominator = denominator * scale;
  console.log(scaledNumerator / scaledDenominator);

  let result = scaledNumerator / scaledDenominator;
  // 保留两位小数
  return roundNumFun(result, 2);
}
//乘法
function numMulti(num1, num2) {
  var baseNum = 0;
  try {
    baseNum += num1.toString().split(".")[1].length;
  } catch (e) { }
  try {
    baseNum += num2.toString().split(".")[1].length;
  } catch (e) { }
  return (
    (Number(num1.toString().replace(".", "")) *
      Number(num2.toString().replace(".", ""))) /
    Math.pow(10, baseNum)
  );
}
//除法
const accDiv = (arg1, arg2) => {
  let t1 = 0;
  let t2 = 0;
  let r1, r2;
  try {
    t1 = arg1.toString().split(".")[1].length;
  } catch (e) { }
  try {
    t2 = arg2.toString().split(".")[1].length;
  } catch (e) { }
  r1 = Number(arg1.toString().replace(".", ""));
  r2 = Number(arg2.toString().replace(".", ""));
  let intDiv = r1 / r2;
  let pow = Math.pow(10, t2 - t1);
  return numMulti(intDiv, pow); // 这里用上面定义好的乘法运算
};
// 输入小数时候验证，不包含四舍五入
const roundNumFunNoHalfAdjust = (num, decimals) => {
  const fraction = Math.pow(10, decimals);
  const integerPart = Math.floor(num * fraction);
  let roundNum = integerPart / fraction;
  if (roundNum) {
    const numStr = roundNum.toString();
    if (numStr.indexOf(".") !== -1) {
      const decimalPart = numStr.slice(numStr.indexOf(".") + 1);
      if (decimalPart.length === 1) {
        return roundNum;
      }
    }
  }
  return roundNum;
};
export {
  checkPhone,
  checkEmail,
  checkPassword,
  checkMoney,
  formatter2,
  formatter4,
  formatter6,
  formatter8,
  formatter3,
  formattedToday,
  calculateUntaxedAmount,
  accDiv,
  roundNumFunNoHalfAdjust,
};
