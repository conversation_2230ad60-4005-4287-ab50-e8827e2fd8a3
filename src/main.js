import { createApp } from "vue";
import { createPinia } from "pinia";

import App from "./App.vue";
import router from "./router";
import appConstant from "@/config/appConstant.js";
import Antd from "ant-design-vue";
import { Modal, message } from "ant-design-vue";
import locale from "ant-design-vue/es/date-picker/locale/zh_CN";
// import "ant-design-vue/dist/antd.css";
import "ant-design-vue/dist/reset.css";
import "@/less/app.less";
import "@/less/tailwind.css";
import "@/assets/iconfont/iconfont.css";

import mwTable from "@/components/table/mwTable.vue";
import mwButton from "@/components/mwButton.vue";
import Dictionary from "@/components/dictionary.vue";
import RadioGroup from "@/components/radioGroup.vue";
import mwDrawer from "@/components/Drawer.vue";
import empty from "@/components/table/empty.vue";
import MaterialSelect from "@/components/MaterialSelect.vue";
import VueSignaturePad from "vue-signature-pad";

const app = createApp(App);
app.config.warnHandler = () => null;
app.config.globalProperties.$appConstant = appConstant;
app.config.globalProperties.$message = message;
app.config.globalProperties.$Modal = Modal;
app.directive("permission", {
  mounted(el, binding) {
    // 使用 mounted 钩子代替 beforeMount
    const btnList = JSON.parse(localStorage.getItem("perms")) || [];
    const needPermission = binding.value;
    const hasPermission = btnList.includes(needPermission);
    // console.log('[ btnList ] >', btnList)
    if (!hasPermission) {
      el.style.display = "none";
      setTimeout(function () {
        el.parentNode.removeChild(el);
      }, 0);
    }
  },
});
app.use(createPinia());
app.use(router);
app.use(Antd);
app.use(locale);
app.component("mw-table", mwTable);
app.component("mw-button", mwButton);
app.component("mw-drawer", mwDrawer);
app.component("dictionary", Dictionary);
app.component("radioGroup", RadioGroup);
app.component("empty", empty);
app.component("MaterialSelect", MaterialSelect);
app.use(VueSignaturePad);

// alert("main.js:window.location.search" + window.location.search);
if (window.location.search) {
  let arr = window.location.search.split("corpId=");

  let appIdArr = window.location.search.split("appid=");
  let typeId = window.location.search.split("type=");

  if (arr.length > 1 && arr[1]) {
    let corpId = arr[1];
    let appId = appIdArr[1];
    let type = typeId[1];
    localStorage.setItem("dingtalkCorpId", corpId);
    localStorage.setItem("dingtalkAppId", appId);
    localStorage.setItem("dingtalkType", type);
    // alert("main.js:corpId" + corpId);
  }
}
app.mount("#app");
