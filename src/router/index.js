import { createRouter, createWebHistory } from "vue-router";
import Layout from "@/views/layout/index.vue";
import { getToken } from "@/utils/auth";
import { useUserStore } from "@/stores/user.js";
import { usePermissionStore } from "@/stores/permission.js";
import NProgress from "nprogress";
import "nprogress/nprogress.css";

NProgress.configure({ showSpinner: false });

// 公共路由
export const constantRoutes = [

  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect.vue"),
      },
    ],
  },
  // {
  //   path: "/warehouse-area",
  //   component: () => import("@/views/agreement/qrCode/warehousearea.vue"),

  //   hidden: true,
  // },
  {
    path: "/warehouse-area",
    name: 'warehouseArea',
    component: () => import("@/views/agreement/qrCode/newWarehousearea.vue"),
    hidden: true,
  },
  {
    path: "/materialQrcode",
    name: 'materialQrcode',
    component: () => import("@/views/agreement/qrCode/newWarehousearea.vue"),
    hidden: true,
  },
  {
    path: "/empty",
    component: () => import("@/views/agreement/empty.vue"),
    hidden: true,
  },
  {
    path: "/login",
    component: () => import("@/views/auth/login.vue"),
    hidden: true,
  },
  {
    path: "/dingTalkCallback",
    name: "dingTalkCallback",
    component: () => import("@/views/auth/dingTalkCallback.vue"),
  },
  {
    path: "/404",
    component: () => import("@/views/error/404.vue"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error/401.vue"),
    hidden: true,
  },

  {
    path: "/",
    component: Layout,
    // redirect: 'home',
    children: [
      {
        path: "",
        component: () => import("@/views/home/<USER>"),
        name: "home",
        meta: { title: "首页", icon: "dashboard", affix: true },
      },
    ],
  },
  {
    path: "/agreement-service",
    component: () => import("@/views/agreement/service.vue"),
    hidden: true,
  },
  {
    path: "/agreement-privacy",
    component: () => import("@/views/agreement/privacy.vue"),
    hidden: true,
  },
  {
    path: "/intelligent-assistant",
    component: () => import("@/views/system/ai/index.vue"),
    hidden: true,
  },
];
// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  // {
  //   path: "/system/user-auth",
  //   component: Layout,
  //   hidden: true,
  //   permissions: ["system:user:edit"],
  //   children: [
  //     {
  //       path: "role/:userId(\\d+)",
  //       component: () => import("@/views/system/user/authRole"),
  //       name: "AuthRole",
  //       meta: { title: "分配角色", activeMenu: "/system/user" },
  //     },
  //   ],
  // },
  // {
  //   path: "/system/role-auth",
  //   component: Layout,
  //   hidden: true,
  //   permissions: ["system:role:edit"],
  //   children: [
  //     {
  //       path: "user/:roleId(\\d+)",
  //       component: () => import("@/views/system/role/authUser"),
  //       name: "AuthUser",
  //       meta: { title: "分配用户", activeMenu: "/system/role" },
  //     },
  //   ],
  // },
  // {
  //   path: "/system/dict-data",
  //   component: Layout,
  //   hidden: true,
  //   permissions: ["system:dict:list"],
  //   children: [
  //     {
  //       path: "index/:dictId(\\d+)",
  //       component: () => import("@/views/system/dict/data"),
  //       name: "Data",
  //       meta: { title: "字典数据", activeMenu: "/system/dict" },
  //     },
  //   ],
  // },
  // {
  //   path: "/tool/gen-edit",
  //   component: Layout,
  //   hidden: true,
  //   permissions: ["tool:gen:edit"],
  //   children: [
  //     {
  //       path: "index/:tableId(\\d+)",
  //       component: () => import("@/views/tool/gen/editTable"),
  //       name: "GenEdit",
  //       meta: { title: "修改生成配置", activeMenu: "/tool/gen" },
  //     },
  //   ],
  // },
  // {
  //   path: "/monitor/cache-info",
  //   component: Layout,
  //   hidden: true,
  //   permissions: ["monitor:cache:list"],
  //   children: [
  //     {
  //       path: "index",
  //       component: () => import("@/views/monitor/cache/cacheInfo"),
  //       name: "CacheInfo",
  //       meta: { title: "缓存监控详情", activeMenu: "/monitor/cache" },
  //     },
  //   ],
  // },
];
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: constantRoutes,
});

const whiteList = [
  "/login",
  "/register",
  "/agreement-service",
  "/agreement-privacy",
  "/dingTalkCallback",
  // '/warehouse-area',
  // '/newWarehouse-area',
  // '/materialQrcode',
  '/empty'
];
router.beforeEach((to, from, next) => {
  NProgress.start();
  const store = useUserStore();
  const permissionStore = usePermissionStore();
  //微信登录回调拦截
  if (to.path == "/dingTalkCallback" && to.query.code) {
    debugger;
  } else if (getToken()) {
    // to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
    /* has token*/
    if (to.path === "/login") {
      next({ path: "/" });
      NProgress.done();
    } else {

      if (store.roles.length === 0) {
        // isRelogin.show = true
        // 判断当前用户是否已拉取完user_info信息
        store
          .getInfo()
          .then(() => {
            // isRelogin.show = false
            permissionStore.GenerateRoutes().then((accessRoutes) => {
              // 根据roles权限生成可访问的路由表
              accessRoutes.forEach((route) => {
                router.addRoute(route); // 动态添加可访问路由表
              });
              next({ ...to, replace: true }); // hack方法 确保addRoutes已完成
            });
          })
          .catch((err) => {
            // store.dispatch('LogOut').then(() => {
            //   Message.error(err)
            //   next({ path: '/' })
            // })
          });
      } else {
        if (to.path === "/") {
          let { topbarRouters } = permissionStore;
          if (
            topbarRouters &&
            topbarRouters[0] &&
            topbarRouters[0].children &&
            topbarRouters[0].children[0]
          ) {
            next({ name: topbarRouters[0].children[0].name });
          } else {
            next();
          }
        } else {
          next();
        }
        NProgress.done();
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      console.log(whiteList.indexOf(to.path), 'whiteList.indexOf(to.path)');
      // 在免登录白名单，直接进入
      next();
    } else {
      next(`/login?redirect=${to.fullPath}`); // 否则全部重定向到登录页
    }
    NProgress.done();
  }
});
router.onError((error) => {
  // 路由异步加载出现TypeError: Failed to fetch dynamically imported module, 重新加载一次页面
  const pattern = /TypeError: Failed to fetch dynamically/g;
  const isChunkLoadFailed = error.toString().match(pattern);
  // // const targetPath = router.options.history.location;
  if (isChunkLoadFailed) {
    location.reload();
  }
});
export default router;
