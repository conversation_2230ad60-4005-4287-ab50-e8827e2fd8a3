<template>
  <mw-header />

  <menu-page :hide="currentRouteInfo?.hidden" />

  <!-- <goback v-if="currentRouteInfo?.hidden" /> -->
  <breadcrumb-vue v-if="currentRouteInfo?.hidden" />
  <a-layout :class="getContentClass">
    <a-layout>
      <a-layout-content class="p-4 rounded-lg bg-white m-0">
        <router-view v-slot="{ Component }">
          <!-- <keep-alive> -->
          <component :is="Component" />
          <!-- </keep-alive> -->
        </router-view>
      </a-layout-content>
      <a-layout-footer style="text-align: center">
        <!-- :size="24" -->
        <a-space>
          <div>{{ $appConstant.copyRight }}</div>
          <div>
            版权所有|
            <a
              href="https://beian.miit.gov.cn/#/Integrated/index"
              target="_blank"
              >{{ $appConstant.beian }}</a
            >
          </div>
          <div>经营许可证编号：{{ $appConstant.businessLicenseNo }}</div>
          <!-- <div>反诈劝阻电话：962110</div> -->
          <div>
            <a
              target="_blank"
              :href="`http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=${$appConstant.recordcode}`"
              >沪公网安备 {{ $appConstant.recordcode }}号</a
            >
          </div>
        </a-space>
      </a-layout-footer>
    </a-layout>
  </a-layout>
</template>
<script>
import {
  defineComponent,
  reactive,
  ref,
  watch,
  toRefs,
  onMounted,
  onBeforeMount,
  computed,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import mwHeader from "@/components/header.vue";
import MenuPage from "@/components/MenuPage.vue";
// import initWebSocket from '@/common/websocket'
import breadcrumbVue from "@/components/breadcrumb.vue";
import { usePermissionStore } from "@/stores/permission.js";
import Goback from "@/components/Goback.vue";

export default defineComponent({
  components: { mwHeader, MenuPage, breadcrumbVue, Goback },
  setup() {
    const route = useRoute(),
      router = useRouter(),
      selectedKeys = ref(["/"]),
      store = usePermissionStore();
    const currentRouteInfo = computed(() => {
      return store.currentRouteInfo;
    });
    const goto = ({ item, key, keyPath }) => {
      router.push(`/${key}`);
    };
    const state = reactive({
      path: "",
      collapsed: false,
      openKeys: ["order", "goods", "setting", "customer"],
      preOpenKeys: ["order", "goods", "setting", "customer"],
    });
    const toggleCollapsed = () => {
      state.collapsed = !state.collapsed;
    };
    watch(
      () => route.path,
      (val) => {
        selectedKeys.value = [val.replace("/", "")];
        state.path =
          selectedKeys.value[0].indexOf("/") > 0
            ? selectedKeys.value[0].slice(0, selectedKeys.value[0].indexOf("/"))
            : selectedKeys.value[0];
      },
      { immediate: true }
    );
    watch(
      () => state.openKeys,
      (_val, oldVal) => {
        state.preOpenKeys = oldVal;
      }
    );
    onBeforeMount(async () => {});
    const getContentClass = computed(() => {
      // 判断当前路由是否是全屏宽度的页面，根据需要进行修改
      const isFullWidthPage = route.meta.isFullWidthPage;
      return {
        "content-area": !isFullWidthPage, // 1200px 宽度的样式
        "w-full": isFullWidthPage, // 全屏宽度的样式
      };
    });
    return {
      selectedKeys,
      goto,
      ...toRefs(state),
      toggleCollapsed,
      route,
      getContentClass,
      currentRouteInfo,
    };
  },
});
</script>
<style lang="less" scoped>
.parent-menu {
  font-size: 14px;
}
.menu {
  &.iconfont {
    font-size: 24px;
    margin-right: 6px;
  }
}
:deep(.ant-menu-inline .ant-menu-item::after) {
  border: none;
}
:deep(.ant-menu-item-icon) {
  display: inline-block;
  height: 40px;
  line-height: 40px;
  vertical-align: top;
}
:deep(.ant-menu-title-content) {
  display: inline-block;
  height: 40px;
  vertical-align: top;
  line-height: 40px;
  font-size: 14px;
}
:deep(.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-active) {
  background-color: #fff;
}
:deep(
    .ant-menu-item .ant-menu-item-icon + span,
    .ant-menu-submenu-title .ant-menu-item-icon + span,
    .ant-menu-item .anticon + span,
    .ant-menu-submenu-title .anticon + span
  ) {
  margin-left: -2px;
}
:deep(.ant-menu-submenu-title .ant-menu-item-icon + span) {
  margin-left: -2px;
}
.menubox {
  position: relative;
  .toggleCollapsedBtn {
    position: absolute;
    right: 12px;
    top: 9px;
    z-index: 3;
    span {
      font-size: 20px;
    }
  }
}
.ant-menu.ant-menu-inline-collapsed {
  width: 80px !important;
}
:deep(.ant-menu.ant-menu-inline-collapsed > .ant-menu-item) {
  padding: 0 12px;
}
:deep(
    .ant-menu.ant-menu-inline-collapsed
      > .ant-menu-submenu
      > .ant-menu-submenu-title
  ) {
  padding: 0 12px;
}
:deep(.ant-menu-item .ant-menu-item-icon + span) {
  transition: opacity 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), color 0.3s;
}
:deep(.ant-menu-inline.ant-menu-root .ant-menu-item) {
  display: block;
}
:deep(.ant-menu-inline.ant-menu-root .ant-menu-submenu-title) {
  display: block;
}
:deep(.ant-layout-sider) {
  z-index: 9;
}
:deep(.ant-menu-sub.ant-menu-inline) {
  background: #f5f5f5;
}
:deep(.ant-menu-sub.ant-menu-inline > .ant-menu-item, ) {
  padding-left: 40px !important;
}
</style>
