<template>
  <div class="proSchedulPage">
    <div class="proSchedulPage-head">
      <div>
        <div>
          <b>排产订单列表</b>
          <span class="head-total">[排产订单总计{{ totalTitle }}]</span>
        </div>
      </div>
    </div>
    <search :searchData="searchData" @search="refresh"> </search>
    <a-row class="list-header">
      <a-col :span="4">{{ title }}产品名称/编码/销售订单</a-col>
      <a-col :span="4">产品规格</a-col>
      <a-col :span="3" class="text-right">排产开始时间</a-col>
      <a-col :span="4" class="text-right">排产结束时间</a-col>
      <a-col :span="3" class="text-right">本次排产数</a-col>
      <a-col :span="3" class="text-right">订单时间</a-col>
    </a-row>
    <a-list
      :data-source="data"
      :grid="{ gutter: 0, column: 1 }"
      :loading="loading"
      :pagination="{
        ...paginationProps,
        showTotal: (total) => `总计 ${total} 条数据`,
        onChange: (page) => {
          onTableChange({ current: page, pageSize: paginationProps.pageSize });
        },
      }"
    >
      <!--      @click="showCreateDrawer(item)" -->
      <template #renderItem="{ item }">
        <a-list-item
          class="divide-y divide-solid divide-dividers"
          :class="item.status == 0 ? 'cursor-pointer' : 'cursor-auto'"
        >
          <div class="py-4 flex">
            <div>
              <span><info-circle-filled class="text-primary mr-1" /></span>
              <span class="mr-5">排产名称：{{ item.schedulingName }}</span>
              <span class="mr-5"
                >排产单号：{{ item.schedulingOrderNumber }}
                <dictionary
                  :statusOptions="qualitySource"
                  :value="item.source"
                  :showBadge="false"
              /></span>
              <span class="mr-5"
                >客户信息：{{ item.customerName }}

                <span v-if="item.realName || item.phone"
                  >[{{ item.realName }} {{ item.phone }}]</span
                >
              </span>
              <span class="mr-5"
                >排产总数：{{ item.schedulingTotal }}

                <span v-if="item.realName || item.phone"
                  >[{{ item.realName }} {{ item.phone }}]</span
                >
              </span>
              <span class="mr-5"
                >排产状态：
                <dictionary
                  :statusOptions="proSchedulStatus"
                  :value="item.status"
                  isBackgroundColor
                />
              </span>
              <span :span="3">
                <dictionary
                  :statusOptions="qualityType"
                  :value="item.type"
                  isBackgroundColor
                />
              </span>
              <span :span="3" class="ml-5">
                <mw-button title="详情" @click="onDetailList(item)"></mw-button>
              </span>
            </div>
          </div>
          <a-row
            v-for="(info, index) in item.schedulingMarketMaterialRelations"
            class="py-4"
            :key="index"
          >
            <a-col :span="5">
              <p>
                {{ info.materialName }} /{{ info.marketOrderNo }}/{{
                  info.marketOrderNoTrue
                }}
              </p>
            </a-col>
            <a-col :span="3"> {{ info.specification }} </a-col>
            <!-- <a-col :span="3">
              {{ info.schedulingMarketProductRelations || 0 }}
            </a-col> -->
            <!-- <a-col :span="3">{{ info.price || 0 }}</a-col>
              <a-col :span="3" >{{ info.totalPrice || "0" }}</a-col> -->
            <!-- <a-col :span="2">{{
              info.waitingProductionSchedulingQuantity
            }}</a-col> -->
            <!-- <a-col :span="3">{{ item.schedulingDay }}</a-col> -->
            <a-col :span="5">{{ item.planStartTime }}</a-col>
            <a-col :span="4">{{ item.planEndTime }}</a-col>

            <!-- <a-col :span="2">{{ item.schedulingTotal }}</a-col> -->
            <a-col :span="2">{{
              info.currentProductionSchedulingQuantity
            }}</a-col>
            <a-col :span="3">{{ item.createTime }}</a-col>

            <!-- createTime -->
            <a-col :span="3" class="text-right">
              <div v-if="info.status == 0">
                <a-popconfirm
                  title="是否取消该条排产订单，取消后将无法恢复！"
                  ok-text="是"
                  cancel-text="否"
                  @confirm="onCancellation(info.schedulingOrderInfoId)"
                >
                  <mw-button title="取消"></mw-button>
                </a-popconfirm>
              </div>
              <div v-if="info.status == 1">
                <span
                  style="color: rgb(24, 144, 255)"
                  @click="onDetail(info.planNo)"
                  >{{ info.planNo }}</span
                >
              </div>
              <div v-if="info.status == 2">已取消</div>
            </a-col>
          </a-row>
        </a-list-item>
      </template>
    </a-list>
    <!-- <increase-drawer
        v-model:visible="inOutCheckVisible"
        @finish="getList" 
       /> -->
    <new-increase-drawer
      v-model:visible="inOutCheckVisible"
      @finish="getList"
    />
    <detail-increase-drawer
      v-model:visible="detailListVisible"
      :detailList="detailList"
    />
  </div>
</template>
<script setup>
import newIncreaseDrawer from "./newIncreaseDrawer.vue";
import detailIncreaseDrawer from "./detailIncreaseDrawer.vue";
import { ref, reactive, onBeforeMount, toRaw, getCurrentInstance } from "vue";
import Search from "@/components/search/index.vue";
import { usePagenation } from "@/common/setup";
import {
  getSchedulingOrderPage,
  schedulingOrderCancel,
} from "@/api/proSchedul/scheduling.js";
import {
  qualityType,
  proSchedulType,
  proSchedulStatus,
  qualitySource,
} from "@/common/constant.js";
import { useRoute, useRouter } from "vue-router";
const router = useRouter();
const { proxy } = getCurrentInstance();
const detailList = ref();
const props = defineProps({
  type: {
    type: String,
    default: "material",
  },
});
const data = ref([]);
const totalTitle = ref("");
const loading = ref(false);
const detailListVisible = ref(false);
const proSchedulStatusData = [
  {
    label: "全部排产状态",
    value: "",
  },
  {
    label: "未排产",
    value: 0,
  },
  {
    label: "已排产",
    value: 1,
  },
  {
    label: "已取消",
    value: 2,
  },
];
const searchData = reactive({
  fields: {
    rangeDate: {
      type: "a-range-picker",
      valueFormat: "YYYY-MM-DD",
      value: [],
      width: "240px",
      allowClear: true,
    },
    // type: {
    //   name: "订单来源",
    //   type: "a-select",
    //   options: proSchedulType,
    //   placeholder: "订单来源",
    //   width: "140px",
    //   value: "",
    // },
    status: {
      name: "排产状态",
      type: "a-select",
      options: proSchedulStatusData,
      placeholder: "排产状态",
      width: "140px",
      value: "",
      allowClear: true,
    },
    schedulingOrderNumber: {
      type: "a-input",
      placeholder: "输入排产单号",
      width: "150px",
      allowClear: true,
    },
    materialName: {
      type: "a-input",
      placeholder: "输入产品名称",
      width: "150px",
      allowClear: true,
    },
    marketOrderNo: {
      type: "a-input-search",
      placeholder: "输入销售单号",
      width: "160px",
      allowClear: true,
    },
  },
});
const getList = async () => {
  loading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;

    let { rangeDate } = toRaw(searchParam);
    if (rangeDate && rangeDate.length > 0) {
      searchParam.planStartTime = rangeDate[0] + " 00:00:00";
      searchParam.planEndTime = rangeDate[1] + " 23:59:59";
    }
  }
  let result = await getSchedulingOrderPage({
    ...searchParam,
    ...pageParam.value,
    // bizType: props.type,
  });
  data.value = result.data;
  totalTitle.value = result.total;
  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const inOutCheckVisible = ref(false);
const { refresh, onTableChange, pageParam, paginationProps } =
  usePagenation(getList);
const inOutType = ref("in");
const showInOutCheckDrawer = (t) => {
  // inOutType.value = t;
  inOutCheckVisible.value = true;
};
// 取消订单状态
const onCancellation = async (schedulingOrderInfoId) => {
  // schedulingOrderInfoId
  let Param = { schedulingOrderInfoId: schedulingOrderInfoId };
  let res = await schedulingOrderCancel(Param);
  if (res.code == 200) {
    proxy.$message.success("取消成功");
  }
  getList();
};
const onDetailList = (val) => {
  detailListVisible.value = true;
  detailList.value = val;
};
const onDetail = (val) => {
  const url = import.meta.env.VITE_DOMAIN + "/production/detail?planNo=" + val;
  router.push(url);
  //   router.push({
  //   name: "detail",
  //   params:{ planNo: val },
  // });
  inOutCheckVisible.value = true;
};
const openPDF = (file) => {
  const fileVisitUrl = file.fileVisitUrl; // 替换成实际的文件访问链接
  window.open(fileVisitUrl, "_blank"); // 在新标签页中打开 PDF
};
</script>
<style lang="less" scoped>
.proSchedulPage {
  .proSchedulPage-head {
    display: flex;
    justify-content: space-between;
    font-size: 17px;
    .head-total {
      color: #ccc;
      font-size: 14px;
      margin-left: 10px;
    }
  }
  .list-header {
    background: theme("colors.background");
    height: 40px;
    line-height: 40px;
    padding: 0 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    color: theme("colors.primary-text");
  }
  :deep(.ant-list-item) {
    // background: theme("colors.background");
    border: 1px solid theme("colors.dividers") !important;
    padding: 0 16px !important;
    border-radius: 8px;
  }
  :deep(.ant-pagination) {
    text-align: center;
  }
  .flex {
    display: flex;
    justify-content: space-between;
  }
}
</style>
