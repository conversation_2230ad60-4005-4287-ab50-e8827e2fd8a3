<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    :customTitle="'新增排产订单'"
    width="50%"
  >
    <template #header>
      <mw-button
        title="确定"
        @click="formSubmit"
        :loading="submitLoading"
      ></mw-button>
    </template>
    <div>
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="horizontal"
        :colon="false"
        :label-col="{ span: 5, offset: -1 }"
      >
        <a-form-item label="排产名称" name="schedulingName" required>
          <a-input
            v-model:value="formData.schedulingName"
            placeholder="请输入排产名称"
          ></a-input>
        </a-form-item>
        <a-form-item label="计划-开始时间" name="planTime" required>
          <a-range-picker
            v-model:value="formData.planTime"
            style="width: 100%"
            valueFormat="YYYY-MM-DD"
          />
          <!-- <a-date-picker
            v-model:value="formData.planStartTime"
            style="width: 100%"
            valueFormat="YYYY-MM-DD"
            :disabled-date="disabledDateNoConThenBefore"
          /> -->
        </a-form-item>
        <!-- <a-form-item label="计划-结束时间" name="planEndTime" required>
          <a-date-picker
            v-model:value="formData.planEndTime"
            style="width: 100%"
            valueFormat="YYYY-MM-DD"
            :disabled-date="disabledDateNoConThenBefore"
          />
        </a-form-item> -->
        <a-form-item label="排产明细">
          <mw-button
            title="新增"
            :font="'iconfont icon-xianxing-121'"
            @click="handleAdd"
          >
          </mw-button>
        </a-form-item>
        <a-table
          bordered
          :data-source="formData.relationSchedulingOrderList"
          :columns="columnsRelationSchedulingOrderList"
          :scroll="{ x: 'max-content' }"
          force-render
          :loading="loadingTab"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'marketOrderId'">
              <a-select
                style="width: 100%"
                placeholder="关联合同"
                :options="postNoContractReviewListOptions"
                v-model:value="record.marketOrderId"
                optionFilterProp="orderName"
                @change="customerChange(record)"
                :field-names="{
                  label: 'orderName',
                  value: 'id',
                }"
                show-search
              >
              </a-select>
            </template>
            <!-- <template v-if="column.dataIndex === 'orderCommandNumber'">
              <a-input-number
                style="width: 100%"
                id="inputNumber"
                v-model:value="record.orderCommandNumber"
                :max="99999"
                placeholder="请输入"
              />
            </template> -->
            <template v-if="column.dataIndex === 'materialId'">
              <a-select
                :disabled="props.id ? true : false"
                show-search
                style="width: 100%"
                placeholder="请选择销售产品"
                :options="record.productListData"
                v-model:value="record.materialId"
                optionFilterProp="materialName"
                :field-names="{
                  label: 'materialName',
                  value: 'id',
                }"
                @change="productListChange(record)"
              >
              </a-select>
            </template>
            <template
              v-if="column.dataIndex === 'waitingProductionSchedulingQuantity'"
            >
              {{ record.waitingProductionSchedulingQuantity }}
              <!-- <a-input-number
                style="width: 100%"
                id="inputNumber"
                v-model:value="record.waitingProductionSchedulingQuantity"
                :max="99999"
                placeholder="请输入"
              /> -->
            </template>
            <template
              v-if="column.dataIndex === 'currentProductionSchedulingQuantity'"
            >
              <a-input-number
                style="width: 100%"
                id="inputNumber"
                v-model:value="record.currentProductionSchedulingQuantity"
                placeholder="请输入"
                :stringMode="true"
              />
            </template>

            <template v-if="column.dataIndex === 'operation'">
              <!-- <a-popconfirm title="确定是否删除" @confirm="onDelete(record)"> -->
              <a-popconfirm
                title="确定是否删除"
                ok-text="是"
                cancel-text="否"
                @confirm="onDelete(record)"
              >
                <mw-button title="删除" danger></mw-button>
              </a-popconfirm>
            </template>
          </template>
        </a-table>
      </a-form>
    </div>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
  defineProps,
  defineEmits,
  watch,
} from "vue";
import _cloneDeep from "lodash/cloneDeep";

import {
  postProNewAdd,
  productList,
  getListByOrderId,
  getWaitingSchedulingList,
  waitingSchedulingListV1,
} from "@/api/proSchedul/scheduling.js";
import { useRouter, useRoute } from "vue-router";
const emit = defineEmits(["update:visible", "finish"]);
const route = useRoute();
const router = useRouter();
const getListByOrderList = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  selectedRowKeys: {
    type: Array,
    default: [],
  },
  orderIds: {
    type: Array,
    default: [],
  },
});

const { proxy } = getCurrentInstance();
const formRef = ref();
const loadingTab = ref();
const submitLoading = ref(false);
const postNoContractReviewListOptions = ref([]);
const postNoContractList = ref([]);
const formData = reactive({
  relationSchedulingOrderList: [],
  schedulingName: undefined,
});
const columnsRelationSchedulingOrderList = [
  {
    title: "关联合同",
    dataIndex: "marketOrderId",
    key: "marketOrderId",
    width: "150px",
  },
  {
    title: "销售订单号",
    dataIndex: "orderNo",
    key: "orderNo",
    width: "115px",
  },
  {
    title: "产品名称",
    dataIndex: "materialId",
    key: "materialId",
    width: "115px",
  },
  {
    title: "待排产数",
    dataIndex: "waitingProductionSchedulingQuantity",
    key: "waitingProductionSchedulingQuantity",
    width: "115px",
  },
  {
    title: "本次排产数",
    dataIndex: "currentProductionSchedulingQuantity",
    key: "currentProductionSchedulingQuantity",
    width: "115px",
  },
  {
    title: "操作",
    dataIndex: "operation",
    key: "operation",
    width: "100px",
  },

  // {
  //   title: "待生产数",
  //   dataIndex: "currentProductionSchedulingQuantity",
  //   key: "currentProductionSchedulingQuantity",
  //   width: "215px",
  // },
  // {
  //   title: "完成生产数",
  //   dataIndex: "productName",
  //   key: "productName",
  //   width: "215px",
  // },
  // {
  //   title: "完成进度",
  //   dataIndex: "productName",
  //   key: "productName",
  //   width: "215px",
  // },
];
const rules = reactive({
  materialIdOrProductId: [
    {
      required: true,
      message: "请选择" + props.name,
      trigger: "blur",
    },
  ],
  successNum: [
    {
      required: true,
      message: "请输入需求数量",
      trigger: "blur",
    },
  ],
  successWarehouseId: [
    {
      required: true,
      message: "请选择仓库",
      trigger: "change",
    },
  ],
  successWarehouseArea: [
    {
      required: true,
      message: "请选择区域",
      trigger: "change",
    },
  ],
  warehouseType: [
    {
      required: true,
      message: "请选择类型",
      trigger: "change",
    },
  ],
});
// const disabledDate = (current) => {
//   return current && current < dayjs().subtract(1, "days").endOf("day");
// };
// 产品删除
const onDelete = (val) => {
  formData.relationSchedulingOrderList.forEach((element, ind) => {
    if (val.id) {
      if (val.id == element.id) {
        formData.relationSchedulingOrderList.splice(ind, 1);
      }
    } else {
      if (element.index == val.index) {
        formData.relationSchedulingOrderList.splice(ind, 1);
      }
    }
  });
};
// 关联-销售合同
const postNoContractReview = async () => {
  loadingTab.value = true;
  let param = {
    orderIds: props.orderIds,
  };
  let res = await waitingSchedulingListV1(param);
  postNoContractReviewListOptions.value = res.data;
  loadingTab.value = false;
  return res.data;
};

// 选择产品信息
const getListByOrder = async (val, item) => {
  let res = await getListByOrderId({ orderId: val });
  item.productListData = res.data;
  return res.data;
};

const customerChange = (val) => {
  val.productListData = [];
  val.materialId = void 0;
  val.waitingProductionSchedulingQuantity = void 0;
  postNoContractReviewListOptions.value.forEach((item, index) => {
    if (item.id == val.marketOrderId) {
      getListByOrder(item.id, val);
      formData.customerId = item.customerId;
      val.orderNo = item.orderNo;
    }
  });
  formData.relationSchedulingOrderList.forEach((item, index) => {
    if (item.index && val.index && item.index == val.index) {
      item.productId = undefined;
      val.productId = undefined;
    } else if (item.id == val.id) {
      item.productId = undefined;
      val.productId = undefined;
    }
  });
};
const productListChange = (val) => {
  val.productListData.forEach((item, index) => {
    if (item.id == val.materialId) {
      val.waitingProductionSchedulingQuantity = item.materialQuantity;
    }
  });
};
// 新增排产明细
const handleAdd = () => {
  const newData = {
    id: Date.now(),
  };
  formData.relationSchedulingOrderList.push(newData);
};

// 确认新增
const formSubmit = async () => {
  formRef.value.validate().then(async () => {
    const hasEmptyName = Array.from(formData.relationSchedulingOrderList).every(
      (obj) =>
        obj.currentProductionSchedulingQuantity &&
        obj.marketOrderId &&
        obj.materialId
    );
    if (!hasEmptyName) {
      proxy.$message.error("请检查产品明细不可以空缺!");
      return;
    }
    formData.planStartTime = formData.planTime[0];
    formData.planEndTime = formData.planTime[1];
    // formData.relationSchedulingOrderList.forEach((item, index) => {
    //   delete item.id;
    // });
    // const newArr = formData.relationSchedulingOrderList.map(
    //   (item) => item.productId + item.marketOrderId
    // );
    // const isRepeat = newArr.some(
    //   (item, index, arr) => arr.indexOf(item) != index
    // );
    // if (isRepeat) {
    //   proxy.$message.error("订单不可重复!");
    //   return;
    // }
    formData.relationSchedulingOrderList.forEach((item, index) => {
      if (item.productId && item.marketOrderId) {
        item.NumFalse = Number(item.productId) + Number(item.marketOrderId);
      }
    });
    // const newArr = formData.relationSchedulingOrderList.map(
    //   (item) => item.NumFalse
    // );
    // const isRepeat = newArr.some(
    //   (item, index, arr) => arr.indexOf(item) != index
    // );
    // if (isRepeat) {
    //   proxy.$message.error("订单不可重复!");
    //   return;
    // }

    let param = {
      ...formData,
    };
    try {
      submitLoading.value = true;
      let res = await postProNewAdd(param);
      if (res.code == 200) {
        onClose();
        router.push({ name: "Index" });
      }

      submitLoading.value = false;
      emit("finish");
    } catch (error) {
      submitLoading.value = false;
    }
  });
};
// 关闭弹窗
function onClose() {
  emit("update:visible", false);
  formData.planStartTime = undefined;
  formData.planTime = void 0;
  formData.planEndTime = undefined;
  formData.relationSchedulingOrderList = [];
  formData.schedulingName = undefined;
}

const getListBy = async (data) => {
  for (let i = 0; i < data.length; i++) {
    data[i].materialId = Number(data[i].materialId);
    await getList(data[i]);
  }
  formData.relationSchedulingOrderList = data;
};

const getList = async (data) => {
  loadingTab.value = true;
  const dataList = await getListByOrder(data.marketOrderId, data);
  const isBooMarketOrder = postNoContractList.value.some(
    (item) => item.id == data.marketOrderId
  );
  data.marketOrderId = isBooMarketOrder ? Number(data.marketOrderId) : void 0;
  const isBooProduct = dataList.some((item) => item.id == data.productId);
  data.productId = isBooProduct ? Number(data.productId) : void 0;
  data.relationSchedulingOrderList = dataList;
  formData.customerId = data.customerId;
  data.orderNo = data.marketOrderNo;
  data.waitingProductionSchedulingQuantity = data.unSchedulingQuantity;
  loadingTab.value = false;
};

watch(
  () => props.visible,
  async (val) => {
    if (val) {
      formData.relationSchedulingOrderList = [];
      const data = _cloneDeep(props.selectedRowKeys);
      postNoContractList.value = await postNoContractReview();
      if (data) {
        getListBy(data);
      }
    }
  },
  { deep: true, immediate: true }
);
</script>
<style lang="less" scoped>
.addpro {
  display: flex;
  justify-content: center;
  margin: auto;
  margin-top: 10%;
}
.marginbot {
  margin-bottom: 10px;
}
</style>
