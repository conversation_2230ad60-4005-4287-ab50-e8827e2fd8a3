<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    :customTitle="'详情'"
    width="50%"
  >
    <mw-table
      :columns="columns"
      :data-source="detailList.schedulingMarketMaterialRelations"
      :pagination="pagination"
    ></mw-table>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
  defineProps,
  defineEmits,
  watch,
} from "vue";

const emit = defineEmits(["update:visible", "finish"]);

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  detailList: {},
});
const { proxy } = getCurrentInstance();
const columns = ref([
  {
    title: "订单编号",
    dataIndex: "marketOrderNo",
    key: "marketOrderNo",
  },
  {
    title: "产品名称",
    dataIndex: "materialName",
    key: "materialName",
  },
  {
    title: "订单产品数量",
    dataIndex: "waitingProductionSchedulingQuantity",
    key: "waitingProductionSchedulingQuantity",
  },
]);

function onClose() {
  // formRef.value.resetFields();
  // allMater(ialList.value = [];
  emit("update:visible", false);
}
watch(
  () => props.visible,
  async (val) => {
    // getSelectProductByOrderList();
  }
);
</script>
<style lang="less" scoped></style>
