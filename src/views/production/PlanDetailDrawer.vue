<template>
  <mw-drawer
    :visible="visible"
    @close="closeDrawer"
    customTitle="生产计划"
    :width="getViewportSize() > 800 ? '35%' : '100%'"
    :spinning="spinning"
  >
    <template #header>
      <a-popconfirm
        title="确认取消？取消后将无法恢复。"
        ok-text="是"
        cancel-text="否"
        @confirm="deletePlan"
        placement="bottomRight"
        v-if="planNo && showBtnStatus.includes(formData.status)"
      >
        <mw-button title="取消计划" :loading="removeLoading"></mw-button>
      </a-popconfirm>
    </template>
    <div class="text-primary-text">
      <a-descriptions
        :colon="false"
        :column="1"
        :labelStyle="{ width: '68px', color: 'rgba(31,31,31,.65)' }"
        :contentStyle="{ color: 'rgba(31,31,31,.65)' }"
      >
        <a-descriptions-item label="生产计划">{{
          formData.planName
        }}</a-descriptions-item>
        <a-descriptions-item label="生产线">{{
          formData.lineName
        }}</a-descriptions-item>
        <a-descriptions-item label="负责人">{{
          formData.responsibleUser
        }}</a-descriptions-item>
        <a-descriptions-item label="关联产品"
          ><div class="cursor-pointer inline-block">
            {{ formData.productName }}
            <span
              class="underline"
              style="color: #959ec3"
              @click="productVisible = true"
              >查看产品</span
            >
          </div></a-descriptions-item
        >
        <a-descriptions-item label="需求数量">
          {{ formData.requiredQuantity }}
        </a-descriptions-item>
        <a-descriptions-item label="已完成数">
          {{ formData.passQuantity }}
        </a-descriptions-item>
        <a-descriptions-item label="完成日期">{{
          formData.plannedCompletionTime
        }}</a-descriptions-item>
        {{ formData }}
      </a-descriptions>
      <div>
        生产计划详情

        <mw-button
          title="导出"
          @click="exportPlanDetails"
          :loading="exportLoading"
          class="mb-2"
        ></mw-button>
      </div>
      <mw-table
        :scroll="{ x: 'max-content' }"
        :columns="columns"
        :data-source="formData.planDetails"
        bordered
      ></mw-table>
    </div>
    <ProductDrawer
      v-model:visible="productVisible"
      v-model:productNo="formData.materialId"
    />
  </mw-drawer>
</template>

<script setup>
import {
  reactive,
  ref,
  defineEmits,
  onBeforeMount,
  getCurrentInstance,
  defineProps,
  watch,
  toRaw,
} from "vue";
import { planDetail, cancel, postExportPlanDetail } from "@/api/plan.js";
import ProductDrawer from "./productDrawer.vue";
import { exportExecl } from "@/utils/util.js";
import { getViewportSize } from "@/common/reg";
const { proxy } = getCurrentInstance();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  planNo: {
    type: String,
    required: true,
  },
});

const spinning = ref(false),
  exportLoading = ref(false),
  productVisible = ref(false);
const formData = reactive({
  planName: undefined,
  lineNo: undefined,
  responsibleUserId: undefined,
  productNo: undefined,
  requiredQuantity: undefined,
  plannedCompletionTime: undefined,
  lineName: undefined,
  responsibleUser: undefined,
  productName: undefined,
  passQuantity: undefined,
  status: undefined,
  materialId: undefined,
});
const showBtnStatus = ref([0, 1]);
const removeLoading = ref(false);
const emit = defineEmits(["update:visible", "update:planNo", "finish"]);
const closeDrawer = () => {
  emit("update:visible", false);
};
const columns = ref([
  {
    title: "订单名称",
    dataIndex: "orderName",
    width: "100px",
  },
  {
    title: "订单编号",
    dataIndex: "orderNo",
    width: "100px",
  },
  {
    title: "产品名称",
    dataIndex: "materialName",
    width: "100px",
  },
  {
    title: "产品编号",
    dataIndex: "materialNo",
    width: "100px",
  },

  {
    title: "开始时间",
    dataIndex: "productionStartTime",
    width: "100px",
  },
  {
    title: "结束时间",
    dataIndex: "productionEndTime",
    width: "100px",
  },
  {
    title: "生产数量",
    dataIndex: "productionQuantity",
    width: "100px",
  },
]);
const deletePlan = async () => {
  removeLoading.value = true;
  try {
    let res = await cancel({ planNo: props.planNo });

    if (res.code == 200) {
      proxy.$message.success("取消成功");
      closeDrawer();
      emit("finish");
    }

    removeLoading.value = false;

    // }

    // emit("finish");
  } catch (error) {
    removeLoading.value = false;
  }
};
// proxy.$message.success("操作成功");
// 获取plan详情
const getPlanDetail = async () => {
  spinning.value = true;
  let res = await planDetail({ planNo: props.planNo });
  for (let key in formData) {
    formData[key] = res.data[key];
    formData.planDetails = res.data.planDetails;
  }
  // canceled.value = res.data.status == 2
  spinning.value = false;
};
const exportPlanDetails = async () => {
  exportLoading.value = true;
  let res = await postExportPlanDetail({ planNo: props.planNo });
  const fileName = "生产计划.xlsx";
  exportExecl(fileName, res);
  exportLoading.value = false;
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      getPlanDetail();
    }
  }
);
</script>

<style lang="less" scoped>
:deep(.ant-form-item) {
  margin-bottom: 16px;
}
</style>
