<template>
  <mw-drawer
    :visible="visible"
    @close="closeDrawer"
    customTitle="订单详情"
    :spinning="spinning"
    :push="0"
    :width="getViewportSize() > 800 ? '90%' : '100%'"
  >
    <a-space direction="vertical" class="w-full">
      <a-collapse collapsible="header">
        <a-collapse-panel class="w-full">
          <template #header>
            <b>订单信息</b>订单号：{{ data?.marketOrderNo }}
          </template>
          <div class="w-full flex">
            <p class="w-1/2">订单名称：{{ data.marketOrderName }}</p>
            <p class="w-1/3 ml-5" v-if="data.marketForeignCountry !== null">
              使用国家：{{ data.marketUseCountry == 0 ? "国内" : "国外" }}
              <span v-if="data.marketForeignCountry"
                >（{{ data.marketForeignCountry }}）</span
              >
            </p>
            <p class="w-1/3">订单备注：{{ data.marketOrderRemark }}</p>
          </div>
          <a-row style="display: flex; justify-content: space-between">
            <a-col class="" :span="24">
              单体标识：
              {{
                getLabelList(
                  orderConfigList.monomerIdentification,
                  data.orderConfigVO?.monomerIdentification
                )
              }}
            </a-col>

            <a-col class="flex mb-4" :span="24">
              <div>电源：</div>
              <div>
                <div>
                  1. 机箱标识：{{
                    getLabelList(
                      orderConfigList.chassisIdentification,
                      data.orderConfigVO?.chassisIdentification
                    )
                  }}
                </div>
                <div>
                  2. 说明书：{{
                    getLabelList(
                      orderConfigList.specification,
                      data.orderConfigVO?.specification
                    )
                  }}
                </div>
                <div>
                  3. 铭牌：{{
                    getLabelList(
                      orderConfigList.dogtag,
                      data.orderConfigVO.dogtag
                    )
                  }}
                </div>
                <div>
                  4. 充电机铭牌：{{
                    getLabelList(
                      orderConfigList.chargerNameplate,
                      data.orderConfigVO?.chargerNameplate
                    )
                  }}
                </div>
                <div>
                  5. 充电机显示屏：{{
                    getLabelList(
                      orderConfigList.chargerDisplay,
                      data.orderConfigVO?.chargerDisplay
                    )
                  }}
                </div>
                <div>
                  6. 充电机说明书：
                  {{
                    getLabelList(
                      orderConfigList.chargerManual,
                      data.orderConfigVO?.chargerManual
                    )
                  }}
                </div>
              </div>
            </a-col>
            <a-col class="mb-4" :span="24"
              >包装要求：{{
                getLabelList(
                  orderConfigList.packingRequirement,
                  data.orderConfigVO?.packingRequirement
                )
              }}</a-col
            >
            <a-col class="mb-4" :span="24"
              >随货文件：{{
                getLabelList(
                  orderConfigList.accompanyingDocument,
                  data.orderConfigVO?.accompanyingDocument
                )
              }}</a-col
            >
            <a-col class="mb-4" :span="24"
              >特殊要求： {{ data.orderConfigVO?.specialRequirements }}</a-col
            >
          </a-row>
        </a-collapse-panel>
      </a-collapse>
      <a-collapse collapsible="header">
        <a-collapse-panel>
          <template #header>
            <b>订单BOM附件</b>
            <span class="ml-3"
              >附件数量：{{
                data?.bomOrderOther?.publicFile
                  ? data?.bomOrderOther?.publicFile.length
                  : 0
              }}</span
            >
          </template>
          <div>
            <div
              class="overflow"
              v-for="(item, index) in data.bomOrderOther.publicFile"
              :key="index"
            >
              <i
                class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                style="color: #959ec3"
              ></i>
              <a
                @click="downFile(item.fileVisitUrl, item.fileName)"
                :title="item.fileName"
                class="underline"
                style="color: #959ec3"
                >{{ item.fileName }}
              </a>
            </div>
          </div>
        </a-collapse-panel>
      </a-collapse>
      <a-collapse
        v-for="(item, index) in props.orderType !== 'qualityInspection'
          ? data?.detailVOList
          : data?.bomProductList"
        :key="index"
      >
        <a-collapse-panel>
          <template #header>
            <b>产品名称：{{ item?.bomName || "--" }}</b>
            <span class="ml-3">关联产品：{{ item?.materialName || "--" }}</span>
            <span class="ml-3">关联编码：{{ item?.materialNo || "--" }}</span>
            <span class="ml-3"
              >订单台数：{{ item?.orderQuantity || "--" }}</span
            >
          </template>
          <div class="w-full flex mb-3" v-if="item.bomName !== '整单数'">
            <div class="w-1/3">
              产品规格： {{ item?.materialSpecification || "--" }}
            </div>
            <div class="w-1/3">
              产品分类：
              {{ item?.materialClassificationName || "--" }}
            </div>
            <div class="w-1/3">产品单位： {{ item?.materialUnit || "--" }}</div>
          </div>
          <div class="w-full flex mb-3" v-if="item.bomName !== '整单数'">
            <div class="w-1/3">
              是否调试：
              {{ item?.materialIsNeedDebugging || "--" }}
            </div>
            <div class="w-1/3">产品用途： {{ item?.materialUse || "--" }}</div>
          </div>
          <div class="w-full flex mb-3" v-if="item.bomName !== '整单数'">
            <div class="w-1/3">
              技术协议编号： {{ item?.productFileNumber || "--" }}
            </div>
            <div class="w-1/3">
              产品协议：
              <div
                class="overflow"
                v-for="(item, index) in item?.materialFile"
                :key="index"
              >
                <i
                  class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                  style="color: #959ec3"
                ></i>
                <a
                  :title="item?.fileName"
                  @click="downFile(item.fileVisitUrl, item.fileName)"
                  target="_blank"
                  class="underline"
                  style="color: #959ec3"
                  >{{ item?.fileName }}
                </a>
              </div>
            </div>
          </div>
          <div class="w-full flex mb-3" v-if="item.bomName !== '整单数'">
            <div class="w-1/3">
              备注信息：<span class="pr-1">{{
                item?.materialRemark || "--"
              }}</span>
            </div>
            <div class="w-1/3">
              备注附件：

              <div
                class="overflow"
                v-for="(item, index) in item?.materialRemarkFileList"
                :key="index"
              >
                <i
                  class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                  style="color: #959ec3"
                ></i>
                <a
                  :title="item?.fileName"
                  target="_blank"
                  @click="downFile(item.fileVisitUrl, item.fileName)"
                  class="underline"
                  style="color: #959ec3"
                  >{{ item?.fileName }}
                </a>
              </div>
            </div>
          </div>
          <div class="mb-3">
            <span>订单产品备注： </span>
            <span class=" ">{{ item.productRemark || "-" }}</span>
          </div>
          <div class="w-full flex mb-3">
            <div class="w-1/4">
              订单产品BOM附件：
              <div
                class="overflow"
                v-for="(item, index) in item?.publicFile"
                :key="index"
              >
                <i
                  class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                  style="color: #959ec3"
                  v-if="item"
                ></i>
                <a
                  :title="item.fileName"
                  target="_blank"
                  @click="downFile(item.fileVisitUrl, item.fileName)"
                  class="underline"
                  style="color: #959ec3"
                  >{{ item.fileName }}
                </a>
              </div>
            </div>
          </div>
          <div class="w-full flex mb-3">产品BOM备注：{{ item.bomRemark }}</div>
        </a-collapse-panel>
      </a-collapse>
      <a-collapse
        v-for="(item, index) in props.orderType !== 'qualityInspection'
          ? bomRelationVOList
          : bomProductList"
        :key="index"
      >
        <a-collapse-panel>
          <template #header>
            <b>BOM明细：{{ item.materialNo || "整单数" }}</b>
          </template>
          <mw-table
            :scroll="{ x: 'max-content' }"
            class="leading-5.5"
            :columns="columns"
            :data-source="
              props.orderType !== 'qualityInspection'
                ? item.materialRelationVOList
                : item.materialRelationList
            "
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex == 'specification'">
                <a-tooltip placement="topLeft" :title="record.specification">
                  <div class="truncate whitespace-pre-wrap">
                    {{ record.specification }}
                  </div>
                </a-tooltip>
              </template>
              <template v-if="column.dataIndex == 'isEconomicMaterial'">
                <div class="">
                  {{ record.isEconomicMaterial ? "是" : "否" }}
                </div>
              </template>
            </template>
          </mw-table>
        </a-collapse-panel>
      </a-collapse>
    </a-space>
  </mw-drawer>
</template>

<script setup>
import {
  reactive,
  ref,
  defineEmits,
  onBeforeMount,
  getCurrentInstance,
  defineProps,
  watch,
  toRaw,
} from "vue";
import { bomDetail } from "@/api/plan.js";
import { useRoute, useRouter } from "vue-router";
import { orderConfigList } from "@/common/constant.js";
import { getViewportSize } from "@/common/reg";
import { downFile } from "@/common/setup/index.js";
import {
  qualityTestGetOrderInfo,
  qualityTestGetOrderDetail,
} from "@/api/quality.js";
const router = useRouter(),
  route = useRoute(),
  data = ref({}),
  materialList = ref([]),
  spinning = ref(false);
const bomRelationVOList = ref();
const bomProductList = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  planNo: {
    type: String,
    default: "",
  },
  orderItem: {
    type: String,
    default: "",
  },
  orderType: {
    type: String,
    default: "",
  },
});
const columns = ref([
  {
    title: "物料编码",
    dataIndex: "materialNo",
    key: "materialNo",
  },
  {
    title: "物料名称",
    dataIndex: "materialName",
    key: "materialName",
    width: 240,
  },
  {
    title: "物料规格",
    dataIndex: "specification",
    key: "specification",
    width: 240,
  },
  {
    title: "经济物料",
    dataIndex: "isEconomicMaterial",
    key: "isEconomicMaterial",
    width: 240,
  },
  {
    title: "单台消耗数",
    dataIndex: "quantity",
    key: "quantity",
  },
  {
    title: "单位",
    dataIndex: "unit",
    key: "unit",
  },
  {
    title: "生产线剩余数量",
    dataIndex: "residueQuantity",
    key: "residueQuantity",
  },
  {
    title: "备注信息",
    dataIndex: "remark",
    key: "remark",
    width: 200,
  },
]);
const emit = defineEmits(["update:visible"]);
const closeDrawer = () => {
  emit("update:visible", false);
};
const getLabelList = (config, valueList) => {
  const labelList = valueList.map((value) => {
    const item = config.find((item) => item.value === value);
    return item ? item.label : "";
  });
  return labelList.join(" ， ");
};
const getBomDetail = async () => {
  try {
    spinning.value = true;
    let res = await bomDetail({ planNo: props.planNo });
    if (res.code == 200) {
      data.value = {
        ...res?.data.orderInfo,
        orderConfigVO: res?.data?.orderInfo.orderConfigVO,
        bomOrderOther: res?.data?.bomOrderOther,
        bomRelationVOList: res?.data?.bomRelationVOList,
        detailVOList: res?.data?.detailVOList,
      };
      materialList.value = res.data?.materialRelationVOList;
      bomRelationVOList.value = res.data?.bomRelationVOList;
      spinning.value = false;
    } else {
      spinning.value = false;
    }
  } catch (error) {
    spinning.value = false;
  }
};

const qualityTestGetOrderInfoData = async (val) => {
  spinning.value = true;
  let res = await qualityTestGetOrderDetail({ orderId: val });
  data.value = res?.data;
  materialList.value = res.data?.bomProductList;
  bomProductList.value = res.data.bomProductList;
  spinning.value = false;
};

watch(
  () => props.visible,
  (val) => {
    if (val) {
      if (props.orderType == "qualityInspection") {
        qualityTestGetOrderInfoData(props.orderItem.orderId);
      } else {
        getBomDetail();
      }
    }
  }
);
</script>

<style lang="less" scoped>
:deep(.ant-form-item) {
  margin-bottom: 12px;
}
</style>
