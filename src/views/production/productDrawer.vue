<template>
  <mw-drawer
    :visible="visible"
    @close="closeDrawer"
    customTitle="产品详情"
    :spinning="spinning"
    :width="getViewportSize() > 800 ? '35%' : '100%'"
  >
    <a-form-item label="产品名称">
      {{ data.materialName }}
    </a-form-item>
    <a-form-item label="产品规格">
      {{ data.specification }}
    </a-form-item>
    <a-form-item label="产品分类">
      {{ data.classificationName }}
    </a-form-item>
    <a-form-item label="产品单位">
      {{ data.unit }}
    </a-form-item>
    <a-form-item label="产品协议">
      <!-- {{ data }} -->
      <div
        v-for="(item, index) in data.file"
        :key="index"
        @click="downFile(item.fileVisitUrl, item.fileName)"
        :href="item?.fileVisitUrl"
        :title="item.fileName"
        style="color: #959ec3"
      >
        <i
          class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
          style="color: #959ec3"
        ></i
        ><span class="underline"> {{ item.fileName || "--" }}</span>
      </div>
    </a-form-item>
    <a-form-item label="关联BOM" name="bomNo">
      <div>
        {{ data.bomName }}
        <span
          class="cursor-pointer underline"
          style="color: #959ec3"
          @click="gotoBom(data.bomNo)"
          >查看BOM</span
        >
      </div>
    </a-form-item>
  </mw-drawer>
</template>

<script setup>
import {
  reactive,
  ref,
  defineEmits,
  onBeforeMount,
  getCurrentInstance,
  defineProps,
  watch,
  toRaw,
} from "vue";
import { productDetail } from "@/api/basicData/product.js";
import { getInfo } from "@/api/basicData/material.js";
import { useRoute, useRouter } from "vue-router";
import { useUserStore } from "@/stores/user.js";
import { getViewportSize } from "@/common/reg";
import { downFile } from "@/common/setup/index.js";
const router = useRouter(),
  route = useRoute(),
  data = ref({}),
  store = useUserStore(),
  spinning = ref(false);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  productNo: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["update:visible"]);
const closeDrawer = () => {
  emit("update:visible", false);
};
const getBomDetail = async () => {
  try {
    spinning.value = true;
    // let res = await productDetail({ productNo: props.productNo });
    let res = await getInfo(props.productNo);
    data.value = res.data;
    spinning.value = false;
  } catch (error) {
    spinning.value = false;
  }
};
// 预览
const openUrl = (url) => {
  window.open(url, "_blacnk");
};
const gotoBom = (bomNo) => {
  store.setParamsBomNo(bomNo);
  router.push({
    name: "Bom",
    params: { bomNo: bomNo },
  });
};
watch(
  () => props.visible,
  (val) => {
    if (val) {
      getBomDetail();
    }
  }
);
</script>

<style lang="scss" scoped></style>
