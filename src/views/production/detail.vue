<template>
  <div>
    <a-spin :spinning="spinning">
      <div
        class="px-4 flex justify-between items-center process-header mb-8 overflow-hidden overflow-x-scroll menu-page"
        style="height: 88px"
      >
        <div class="flex justify-between items-center text-primary">
          <div class="mr-2 break-inside-avoid-column whitespace-nowrap">
            计划进度
          </div>
          <div class="flex justify-between items-center">
            <div
              class="w-60 border border-primary rounded-full p-0.5 overflow-hidden"
              style="background: #e0eaf5"
            >
              <!-- 设置小于等于8的时候宽度为8  否则进度条不好看 -->
              <div
                class="bg-primary rounded-full h-5 relative w-full"
                :style="{
                  width: percent + '%',
                }"
              ></div>
            </div>
            <div class="ml-2 text-2xl font-bold">{{ info.percent }}%</div>
          </div>
        </div>
        <div class="flex justify-between items-center">
          <div class="statistic break-inside-avoid-column whitespace-nowrap">
            <div class="title">今日生产</div>
            <div class="data">{{ info.todayQuantity || 0 }}</div>
          </div>
          <div class="statistic break-inside-avoid-column whitespace-nowrap">
            <div class="title">累计已完成</div>
            <div class="data">{{ info.totalQuantity || 0 }}</div>
          </div>
          <div class="statistic break-inside-avoid-column whitespace-nowrap">
            <div class="title">质检入仓</div>
            <div class="data">{{ info.passQuantity || 0 }}</div>
          </div>
          <div class="statistic break-inside-avoid-column whitespace-nowrap">
            <div class="title">剩余未完成</div>
            <div class="data">
              {{ info.unCompletedQuantity > 0 ? info.unCompletedQuantity : 0 }}
            </div>
          </div>
        </div>
      </div>
    </a-spin>
    <search :searchData="searchData" @search="refresh" class="mt-1">
      <a-flex wrap="wrap" gap="small" class="mt-1">
        <mw-button
          title="报工"
          v-if="showBtnStatus.includes(info.status)"
          v-permission="'production:plan:getSingleBomByPlanNo'"
          @click="recordsVisible = true"
        ></mw-button>
        <mw-button
          title="增料"
          @click="
            allocationVisible = true;
            allotType = 13007;
          "
          v-if="showBtnStatus.includes(info.status)"
          v-permission="'production:plan:materialAllot'"
        ></mw-button>
        <mw-button
          title="退料"
          @click="
            RemainingStockVisible = true;
            allotType = 12008;
          "
          v-if="showBtnStatus.includes(info.status)"
          v-permission="'production:plan:materialAllot'"
        ></mw-button>
        <mw-button
          title="质检"
          @click="inspectionVisible = true"
          v-if="showBtnStatus.includes(info.status)"
          v-permission="'production:plan:productTestAllot'"
        ></mw-button>
        <mw-button
          title="订单 "
          @click="bomVisible = true"
          v-permission="'production:plan:bomDetail'"
        ></mw-button>

        <mw-button
          title="计划"
          @click="detailVisible = true"
          v-if="showBtnStatus.includes(info.status)"
          v-permission="'production:plan:materialAllotBaseInfo'"
        ></mw-button>
      </a-flex>
    </search>
    <mw-table
      :scroll="{ x: 'max-content' }"
      class="leading-5.5"
      :columns="columns"
      :data-source="data"
      :rowKey="(record) => record.id"
      :loading="tableLoading"
      :customRow="
        (record) => {
          return {
            onClick: (event) => {
              rowClick(record);
            },
          };
        }
      "
      :pageConfig="paginationProps"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'materialInfo'">
          <div class="overflow" :title="record.materialName">
            {{ record.materialName }}
          </div>
          <div class="text-secondar-text" :title="record.materialNo">
            编码：{{ record.materialNo }}
          </div>
          <div class="text-secondar-text overflow" :title="record.createTime">
            记录时间：{{ record.createTime }}
          </div>
        </template>
      </template>
    </mw-table>
  </div>
  <production-records-drawer
    v-model:visible="recordsVisible"
    @finish="getData"
    v-model:planNo="props.planNo"
  />
  <production-allocation
    v-model:visible="allocationVisible"
    v-model:allotType="allotType"
    v-model:planNo="props.planNo"
  />
  <RemainingStock
    v-model:visible="RemainingStockVisible"
    v-model:allotType="allotType"
    v-model:planNo="props.planNo"
  />
  <quality-inspection
    v-model:visible="inspectionVisible"
    v-model:planNo="props.planNo"
  />
  <bom-drawer v-model:visible="bomVisible" v-model:planNo="props.planNo" />
  <plan-detail-drawer
    v-model:visible="detailVisible"
    @finish="getData"
    v-model:planNo="props.planNo"
  />
</template>

<script setup>
import { onMounted } from "vue";
import Search from "@/components/search/index.vue";
import { ref, onBeforeMount, reactive } from "vue";
import { productionStatus } from "@/common/constant.js";
import {
  planDetail,
  cancel,
  schedule,
  recordsMaterialPage,
} from "@/api/plan.js";
import { useRoute, useRouter } from "vue-router";
import { usePagenation } from "@/common/setup/index.js";
import ProductionRecordsDrawer from "./ProductionRecordsDrawer.vue";
import ProductionAllocation from "./ProductionAllocation.vue";
import RemainingStock from "./RemainingStock.vue";
import QualityInspection from "./QualityInspection.vue";
import BomDrawer from "./BomDrawer.vue";
import PlanDetailDrawer from "./PlanDetailDrawer.vue";
import { dateFormat } from "@/utils/util.js";
const props = defineProps({
  planNo: {
    type: String,
    default: "",
  },
});
// 获取plan详情
const router = useRouter(),
  route = useRoute(),
  spinning = ref(false),
  info = ref({}),
  tableLoading = ref(false),
  data = ref([]),
  columns = ref([
    {
      title: "物料名称",
      dataIndex: "materialInfo",
      key: "materialInfo",
      width: "240px",
    },
    {
      title: "日期",
      dataIndex: "createTime",
      key: "createTime",
      width: "200px",
    },
    {
      title: "今日生产消耗",
      dataIndex: "todayConsume",
      key: "todayConsume",
      align: "right",
    },
    {
      title: "累计生产消耗",
      dataIndex: "totalConsume",
      key: "totalConsume",
      align: "right",
    },
    {
      title: "今日物料耗损",
      dataIndex: "todayLoss",
      key: "todayLoss",
      align: "right",
    },
    {
      title: "累计物料耗损",
      dataIndex: "totalLoss",
      key: "totalLoss",
      align: "right",
    },
    {
      title: "物料剩余",
      dataIndex: "residueStock",
      key: "residueStock",
      align: "right",
    },
  ]),
  recordsVisible = ref(false),
  allocationVisible = ref(false),
  RemainingStockVisible = ref(false),
  allotType = ref(),
  remainingVisible = ref(false),
  inspectionVisible = ref(false),
  bomVisible = ref(false),
  detailVisible = ref(false),
  showBtnStatus = ref([0, 1]),
  percent = ref(0);
const searchData = ref({
  searchButtons: [],
  fields: {
    date: {
      type: "a-date-picker",
      valueFormat: "YYYY-MM-DD",
      value: null,
      width: "140px",
    },
    keyword: {
      type: "a-input-search",
      placeholder: "输入物料名称/编号搜索",
      width: "240px",
      allowClear: true,
    },
  },
});
const getSchedule = async () => {
  spinning.value = true;
  let res = await schedule({ planNo: props.planNo });
  info.value = res.data;
  let resPercent = res.data.percent;
  percent.value =
    resPercent <= 0
      ? 0
      : resPercent > 0 && resPercent <= 8
      ? 8
      : resPercent > 8 && resPercent < 100
      ? resPercent
      : 100;
  // for (let key in formData) {
  //   formData[key] = res.data[key];
  // }
  // canceled.value = res.data.status == 2
  spinning.value = false;
};
const getPage = async () => {
  tableLoading.value = true;
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  //单个搜索信息
  // searchParam[searchData.filterData.value.filterBy] =
  //   searchData.filterData.value.filterValue;
  let result = await recordsMaterialPage(
    { ...searchParam, planNo: props.planNo },
    { ...pageParam.value }
  );
  data.value = result.data;
  // state.corpId = result.data.data.records[0].corpId;
  paginationProps.value.total = result.total;
  tableLoading.value = false;
};

const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getPage);
const getData = async () => {
  await getSchedule();
  await getPage();
};
onBeforeMount(async () => {
  // searchData.value.fields.date.value = dateFormat(new Date(), "YYYY-MM-DD");
  getData();
});
function rowClick(record) {
  // record.name = "改变后的计划" + record.id;
  // planNo.value = record.planNo;
  // router.push({
  //   path: "detail",
  //   query: { planNo: record.planNo },
  // });
  // detailVisible.value = true;
}
</script>

<style lang="less" scoped>
.process-header {
  background: linear-gradient(180deg, #efefef 0%, #ffffff 100%);
  box-shadow: 0px 0px 20px 0px rgba(34, 34, 34, 0.1);
  border-radius: 8px;
  border: 1px solid #ffffff;
}
.statistic {
  padding-left: 24px;
  padding-right: 24px;
  border-left: 4px solid theme("colors.border");
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 24px;
  column-gap: 8px;
  .title {
    font-size: 16px;
  }
  .data {
    font-size: 24px;
    font-weight: 600;
  }
}
</style>
<style lang="less" scoped>
:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  // background: #fff;
}
:deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
  // background: #fff;
}
.menu-page::-webkit-scrollbar {
  width: 0;
  height: 0; /* 设置滚动条的高度为0（根据需求调整）*/
  background: transparent; /* 如果需要的话 */
  display: none;
}
</style>
