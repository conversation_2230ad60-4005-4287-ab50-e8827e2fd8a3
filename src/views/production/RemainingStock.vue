<template>
  <mw-drawer
    :visible="visible"
    @close="closeDrawer"
    :customTitle="'剩余入库'"
    :spinning="spinning"
    width="90%"
  >
    <template #header>
      <mw-button
        :title="`入库确定申请`"
        @click="onSubmit"
        :loading="submitLoading"
      ></mw-button>
    </template>
    <div class="flex items-center gap-x-2 mb-3">
      <div>物料类型</div>
      <div>
        <a-select
          placeholder="请选择客户"
          :options="[{ label: '全部类型', value: '' }, ...materialTypes]"
          v-model:value="bizType"
          @change="onTypeChange"
          class="w-40"
        >
        </a-select>
      </div>
    </div>
    <div class="divide-y divide-border">
      <a-table
        bordered
        :data-source="data"
        :columns="columns"
        :rowKey="(record) => record.materialVO.materialId"
        :scroll="{ x: 'max-content' }"
        row
        :row-selection="rowSelection"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'materialType'">
            {{ getMaterialType(record.materialVO.type) }}
          </template>
          <template v-if="column.dataIndex === 'materialNo'">
            {{ record.materialVO.materialNo }}
          </template>

          <template v-if="column.dataIndex === 'materialName'">
            {{ record.materialVO.materialName }}
          </template>
          <template v-if="column.dataIndex === 'specification'">
            {{ record.materialVO.specification }}
          </template>
          <template v-if="column.dataIndex === 'residueQuantity'">
            {{ record.residueQuantity || 0 }}
          </template>
          <template v-if="column.dataIndex === 'consumeQuantity'">
            <span class="text-title"
              >{{ Number(record?.materialVO.quantity || 0).toFixed(2) }} </span
            >个
          </template>

          <template v-if="column.dataIndex === 'quantity'">
            <div class="flex items-center">
              <a-input-number
                v-model:value="record.quantity"
                placeholder="数值"
                :stringMode="true"
              />
            </div>
          </template>

          <template v-if="column.dataIndex === 'remark'">
            <a-input
              class="flex-1"
              v-model:value="record.remark"
              maxlength="50"
            ></a-input>
          </template>
        </template>
      </a-table>
    </div>
  </mw-drawer>
</template>
<script setup>
import {
  reactive,
  ref,
  defineEmits,
  onBeforeMount,
  getCurrentInstance,
  defineProps,
  watch,
  toRaw,
} from "vue";
import {
  getBom,
  addRecords,
  getMaterialAllotBaseInfo,
  materialAllot,
} from "@/api/plan.js";
import { useRoute, useRouter } from "vue-router";
import { getViewportSize } from "@/common/reg";
import { materialTypes } from "@/common/constant";
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const data = ref([]);
const formData = reactive({
  completedQuantity: 0,
});
const headerRemark = ref();
const formRef = ref();
const submitLoading = ref(false);
const spinning = ref(false);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  allotType: {
    type: Number,
    default: 0,
  },
  planNo: {
    type: String,
    default: "",
  },
});
const rules = ref({
  completedQuantity: [
    {
      required: true,
      message: "请输入数量",
      trigger: "blur",
    },
    {
      type: "number",

      message: "已完成数量最少为1",
    },
  ],
});
const emit = defineEmits(["update:visible", "finish"]);
const closeDrawer = () => {
  emit("update:visible", false);
};
const getBomDetail = async (type) => {
  spinning.value = true;
  let res = await getMaterialAllotBaseInfo(
    { planNo: props.planNo, bizType: bizType.value },
    type
  );
  data.value = res.data.map((item) => {
    return {
      ...item,
      quantity: 0,
      remark: undefined,
    };
  });
  spinning.value = false;
};
watch(
  () => props.visible,
  (val) => {
    if (val) {
      getBomDetail(props.allotType);
    }
  }
);
const columns = [
  {
    title: "物料类型",
    dataIndex: "materialType",
    key: "materialType",
  },
  {
    title: "物料编码",
    dataIndex: "materialNo",
    key: "materialNo",
  },
  {
    title: "物料名称",
    dataIndex: "materialName",
    key: "materialName",
  },
  {
    title: "物料规格",
    dataIndex: "specification",
    key: "specification",
  },
  {
    title: "生产计划剩余数量",
    dataIndex: "residueQuantity",
    key: "residueQuantity",
  },
  {
    title: "单台消耗数",
    dataIndex: "consumeQuantity",
    key: "consumeQuantity",
  },

  {
    title: "本次退料数量",
    dataIndex: "quantity",
    key: "quantity",
  },
  {
    title: "备注",
    dataIndex: "remark",
    key: "remark",
  },
];
const selectedRowKeysRes = ref([]);
const rowSelection = {
  onChange: (selectedRowKeys, selectedRows) => {
    console.log(
      `selectedRowKeys: ${selectedRowKeys}`,
      "selectedRows: ",
      selectedRows,
      data.value
    );
    selectedRowKeysRes.value = selectedRowKeys;
    // selectedMaterialList.value = res
    //   .filter((item) => item.residueQuantity !== 0)
    //   .map((item) => {
    //     return {
    //       materialId: item.materialVO.materialId,
    //       quantity: item.quantity,
    //       remark: item.remark,
    //     };
    //   });
    // console.log("[ materialList ] >", materialList);
  },
  getCheckboxProps: (record) => ({
    name: record.name,
  }),
};
const onSubmit = async () => {
  submitLoading.value = true;
  console.log("[ selectedRowKeysRes.value ] >", selectedRowKeysRes.value);
  let materialList = [];
  materialList = data.value
    .filter(
      (item) =>
        item.residueQuantity !== 0 &&
        selectedRowKeysRes.value.includes(item.materialVO.materialId)
    )
    .map((item) => {
      return {
        materialId: item.materialVO.materialId,
        quantity: item.quantity,
        remark: item.remark,
      };
    });
  console.log("[ materialList ] >", materialList);
  try {
    let res = await materialAllot({
      materialList: materialList,
      planNo: props.planNo,
      allotType: props.allotType,
      headerRemark: headerRemark.value,
    });
    if (res.code == 200) {
      proxy.$message.success("操作成功");

      emit("finish");
      closeDrawer();
    }
    submitLoading.value = false;
    // }
  } catch (error) {
    submitLoading.value = false;
  }
};
const getMaterialType = (val) => {
  const res = materialTypes.find((item) => item.value == val);
  return res ? res.label : "初始bom物料";
};
const bizType = ref("");
const onTypeChange = async () => {
  console.log("[ bizType.value ] >", bizType.value);
  await getBomDetail(props.allotType);
};
</script>
<style lang="less" scoped>
.material-li {
  margin-bottom: 16px;
  & + .material-li {
    padding-top: 16px;
  }
}
</style>
