<template>
  <div>
    <div class="content-region">
      <a-card
        style="width: 300px"
        class="mb-2"
        v-for="(item, index) in data"
        :key="index"
      >
        {{ item.createTime }} {{ item.createBy }}
        <p v-if="item.content">内容：{{ item.content }}</p>
        <div v-else>
          附件：
          <!-- <a
            :href="item?.file[0]?.fileVisitUrl"
            :title="item?.file[0]?.fileName"
            class="cursor-pointer inline-block"
            style="color: #959ec3"
          >
            <i
              class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
              style="color: #959ec3"
            ></i
            ><span class="underline">{{ item.file[0]?.fileName }}</span>
          </a> -->
          <i
            class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
            style="color: #959ec3"
          ></i>
          <a
            :href="item.file[0]?.fileVisitUrl"
            :title="item.file[0]?.fileName"
            target="_blank"
            class="underline"
            style="color: #959ec3"
            >{{ item.file[0]?.fileName }}
          </a>
        </div></a-card
      >
    </div>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
    >
      <a-form-item name="type" label="">
        <a-radio-group v-model:value="formData.type" name="radioGroup">
          <a-radio value="1">文字</a-radio>
          <a-radio value="2">附件</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item name="content" label="" v-if="formData.type == 1">
        <a-textarea
          v-model:value="formData.content"
          placeholder="请输入内容"
          :rows="4"
        />
      </a-form-item>
      <a-form-item name="file" label="" v-if="formData.type == 2">
        <form-upload
          v-model:value="formData.file"
          sence="article"
          :fileTypes="[]"
          :fileSize="100"
        ></form-upload>
      </a-form-item>
      <a-form-item name="" label="">
        <mw-button title="回复" @click="replySubmission"></mw-button>
      </a-form-item>
    </a-form>
  </div>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  onBeforeMount,
  defineEmits,
  reactive,
} from "vue";
import { list, page, add } from "@/api/discuss/discuss.js";
import FormUpload from "@/components/form-upload.vue";
const { proxy } = getCurrentInstance();
const formData = reactive({
  source: "PLAN",
  type: "1",
});
const formRef = ref();
const props = defineProps({
  planNo: {
    type: String,
    default: "",
  },
  planId: {
    type: String,
    default: "",
  },
  data: {
    type: Array,
    default: "",
  },
});
const discussData = ref([]);
const emit = defineEmits(["pageData"]);
// discussData.value = props.data;
// const requestPage = async () => {
//   let param = { referId: props.planId, source: "PLAN" };
//   let res;
//   res = await list(param);
//   props.data = res.data;
// };

const replySubmission = async () => {
  formRef.value.validate().then(async () => {
    let param = {
      referId: props.planId,
      ...formData,
    };
    let res = await add(param);
    if (res.code == 200) {
      proxy.$message.success("回复成功");
      emit("pageData");
      formRef.value.resetFields();
    }
  });
};

// watch(() => (val) => {
//   requestPage();
// });
</script>
<style lang="less" scoped>
.content-region {
  width: 100%;
  height: 60vh;
  overflow: auto;
}
</style>
