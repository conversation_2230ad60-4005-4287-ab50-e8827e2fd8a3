<template>
  <mw-drawer
    custom-title=""
    :visible="visible"
    @close="onClose"
    @spinning="spinning"
    :closable="false"
    destroyOnClose="true"
    :width="getViewportSize() > 800 ? '80%' : '100%'"
  >
    <a-tabs v-model:activeKey="activeKey" @tabClick="getTabName" animated>
      <a-tab-pane key="1" tab="详情">
        <detail :planNo="planNo"></detail>
      </a-tab-pane>
      <a-tab-pane key="2" tab="讨论" force-render>
        <discuss
          @pageData="getListData"
          :planNo="planNo"
          :planId="planId"
          :data="discussData"
        ></discuss>
      </a-tab-pane>
    </a-tabs>
  </mw-drawer>
</template>

<script setup>
import {
  onBeforeMount,
  reactive,
  ref,
  getCurrentInstance,
  computed,
  watch,
  defineEmits,
  defineProps,
} from "vue";

import {
  oaDetail,
  getContractInfo,
  getMarketOrder,
  getPurchaseSettlement,
} from "@/api/purchase/order.js";
import { list, page, add } from "@/api/discuss/discuss.js";
import detail from "../detail.vue";
import discuss from "./discuss.vue";
import { getViewportSize } from "@/common/reg";
const emit = defineEmits(["update:visible", "finish"]);
const activeKey = ref("1");
const activeKeyId = ref();
const discussData = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  planNo: {
    type: String,
    default: "",
  },
  planId: {
    type: String,
    default: "",
  },
});
const planId = ref();
const planNo = ref("");
const onClose = () => {
  emit("update:visible", false);
  activeKey.value = "1";
};
const getListData = async () => {
  let param = { referId: props.planId, source: "PLAN" };
  let res;
  res = await list(param);
  discussData.value = res.data;
};
const getTabName = async (data) => {
  let param = { referId: props.planId, source: "PLAN" };
  let res;
  if (data == 2) {
    res = await list(param);
    discussData.value = res.data;
  }
};
watch(
  () => props.visible,
  async (val) => {
    planNo.value = props.planNo;
    planId.value = props.planId;
  }
);
</script>

<style></style>
