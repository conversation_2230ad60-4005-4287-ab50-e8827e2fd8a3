<template>
  <a-modal
    :visible="controlVisible"
    title="订单排产列表"
    @ok="openModal"
    @cancel="handleCancel"
  >
    <a-table
      :row-selection="rowSelection"
      :columns="columns"
      :data-source="schedulingOrderData"
      :rowKey="(record) => record.schedulingOrderNumber"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'status'">
          {{ record.status == 0 ? "未使用" : "已使用" }}
        </template>
      </template>
    </a-table>
  </a-modal>
</template>
<script>
import { ref, computed, defineComponent } from "vue";
import { schedulingOrder } from "@/api/plan.js";

export default defineComponent({
  props: {},
  emits: ["relatedSchedulingOrderList"],
  setup(props, { emit }) {
    const columns = [
      { title: "订单编号", dataIndex: "schedulingOrderNumber" },
      { title: "订单数量", dataIndex: "quantity" },
      { title: "订单状态", dataIndex: "status" },
    ];

    const controlVisible = ref(false);
    const schedulingOrderData = ref([]);
    const selectedRowKeys = ref([]);

    const init = (val) => {
      controlVisible.value = true;
      getschedulingOrder(val.value);
    };

    const handleCancel = () => {
      controlVisible.value = false;
    };

    const getschedulingOrder = async (valId) => {
      let listData = await schedulingOrder({
        productId: valId,
        ignoreCancel: true,
      });
      schedulingOrderData.value = listData.data;
    };

    const selectRow = (record) => {
      const keys = [...selectedRowKeys.value];
      if (keys.indexOf(record.key) >= 0) {
        keys.splice(keys.indexOf(record.key), 1);
      } else {
        keys.push(record.key);
      }
      selectedRowKeys.value = keys;
    };

    const rowSelection = computed(() => {
      return {
        selectedRowKeys: selectedRowKeys.value,
        onChange: (keys) => {
          selectedRowKeys.value = keys;
        },
      };
    });

    const customRow = (record) => {
      return {
        onClick: () => {
          selectRow(record);
        },
      };
    };

    const openModal = () => {
      emit("relatedSchedulingOrderList", selectedRowKeys.value);
      controlVisible.value = false;
    };

    return {
      init,
      openModal,
      handleCancel,
      controlVisible,
      columns,
      schedulingOrderData,
      rowSelection,
      customRow,
    };
  },
});
</script>
