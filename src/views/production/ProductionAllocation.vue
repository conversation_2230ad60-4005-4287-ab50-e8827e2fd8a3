<template>
  <mw-drawer
    :visible="visible"
    @close="closeDrawer"
    :customTitle="allotType == 13007 ? '生产调拨' : '剩余入库'"
    :spinning="spinning"
    :width="getViewportSize() > 800 ? '40%' : '100%'"
  >
    <template #header>
      <mw-button
        :title="`${allotType === 13007 ? '调拨' : '入库'}确定申请`"
        @click="onSubmit"
        :loading="submitLoading"
      ></mw-button>
    </template>
    <!--     <div class="flex" v-if="allotType == 13007"> -->
    <a-flex
      :style="{ ...boxStyle }"
      :justify="justify"
      :align="alignItems"
      v-if="allotType == 13007"
    >
      <div>备注：</div>
      <div style="width: 88%">
        <a-textarea
          v-model:value="headerRemark"
          placeholder="备注"
          allow-clear
        />
      </div>
    </a-flex>
    <div class="divide-y divide-border">
      <template v-for="item in data" :key="item.materialVO.materialId">
        <!-- v-if="allotType == 13007 ? {} : item.residueQuantity !== 0" -->
        <!-- allotType == 12008 && item.residueQuantity !== 0这里有个为0判断，3.13号晚上说不展示，现在将为0 放开不加限制 -->
        <div
          class="text-primary-text leading-5.5 space-y-1 material-li"
          v-show="allotType == 12008 || allotType == 13007"
        >
          <div class="text-title">
            {{ item.materialVO.materialName }}「{{
              item.materialVO.materialNo
            }}」
          </div>

          <div class="flex justify-between items-end">
            <div class="space-y-1">
              <div class>规格：{{ item.materialVO.specification }}</div>
              <div v-if="allotType == 13007">
                生产计划剩余数：{{
                  item.residueQuantity > 0 ? item.residueQuantity : 0
                }}
              </div>
              <div>
                调拨{{ allotType == 13007 ? "出库" : "入库" }}/{{
                  allotType == 13007 ? "仓库库存数" : "生产计划剩余数"
                }}：
              </div>
            </div>
            <div>
              <a-input-number
                v-model:value="item.quantity"
                placeholder="数值"
                :stringMode="true"
              />
              /
              {{
                allotType == 13007
                  ? item.menuTotalQuantity
                    ? item.menuTotalQuantity
                    : 0
                  : item.residueQuantity
                  ? item.residueQuantity
                  : 0
              }}
            </div>
          </div>
          <div v-if="allotType == 12008" class="flex items-center gap-x-2">
            <div>退料备注:</div>
            <a-input
              class="flex-1"
              v-model:value="item.remark"
              maxlength="50"
            ></a-input>
          </div>
        </div>
      </template>
    </div>
  </mw-drawer>
</template>
<script setup>
import {
  reactive,
  ref,
  defineEmits,
  onBeforeMount,
  getCurrentInstance,
  defineProps,
  watch,
  toRaw,
} from "vue";
import {
  getBom,
  addRecords,
  getMaterialAllotBaseInfo,
  materialAllot,
} from "@/api/plan.js";
import { useRoute, useRouter } from "vue-router";
import { getViewportSize } from "@/common/reg";
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const data = ref({});
const formData = reactive({
  completedQuantity: 0,
});
const headerRemark = ref();
const formRef = ref();
const submitLoading = ref(false);
const spinning = ref(false);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  allotType: {
    type: Number,
    default: 0,
  },
  planNo: {
    type: String,
    default: "",
  },
});
const rules = ref({
  completedQuantity: [
    {
      required: true,
      message: "请输入数量",
      trigger: "blur",
    },
    {
      type: "number",

      message: "已完成数量最少为" + props.allotType == 13007 ? 0 : 1,
    },
  ],
});
const emit = defineEmits(["update:visible", "finish"]);
const closeDrawer = () => {
  emit("update:visible", false);
};
const getBomDetail = async (type) => {
  spinning.value = true;
  let res = await getMaterialAllotBaseInfo({ planNo: props.planNo }, type);
  data.value = res.data.map((item) => {
    return {
      ...item,
      quantity: props.allotType == 13007 ? 0 : 0,
      remark: undefined,
    };
  });
  spinning.value = false;
};
watch(
  () => props.visible,
  (val) => {
    if (val) {
      getBomDetail(props.allotType);
    }
  }
);

const onSubmit = async () => {
  submitLoading.value = true;
  let materialList = [];
  if (props.allotType == "13007") {
    materialList = data.value.map((item, index) => {
      return {
        materialId: item.materialVO.materialId,
        quantity: item.quantity,
      };
    });
  } else {
    materialList = data.value
      .filter((item) => item.residueQuantity !== 0)
      .map((item) => {
        return {
          materialId: item.materialVO.materialId,
          quantity: item.quantity,
          remark: item.remark,
        };
      });
  }
  // return;
  // materialList.some((item) => item);
  // const hasZero = materialList.some((item) => item.quantity <= 0);
  // if (hasZero && props.allotType == 12008) {
  //   proxy.$message.error("请输入正确的数量");
  //   submitLoading.value = false;
  //   return;
  // }
  try {
    //
    let res = await materialAllot({
      materialList: materialList,
      planNo: props.planNo,
      allotType: props.allotType,
      headerRemark: headerRemark.value,
    });
    if (res.code == 200) {
      proxy.$message.success("添加成功");

      emit("finish");
      closeDrawer();
    }
    submitLoading.value = false;
    // }
  } catch (error) {
    submitLoading.value = false;
  }
};
</script>
<style lang="less" scoped>
.material-li {
  margin-bottom: 16px;
  & + .material-li {
    padding-top: 16px;
  }
}
</style>
