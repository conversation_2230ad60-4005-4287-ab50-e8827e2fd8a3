<template>
  <mw-drawer
    :visible="visible"
    @close="closeDrawer"
    customTitle="生产记录"
    :spinning="spinning"
    :width="'90%'"
  >
    <template #header>
      <mw-button
        title="确定"
        @click="onSubmit"
        :loading="submitLoading"
      ></mw-button>
    </template>
    <div>
      <div class="flex items-center mb-3 gap-x-10">
        <div class="flex items-center">
          <div>生产计划总数:</div>
          <div>{{ detail.planQuantity }}</div>
        </div>
        <div class="flex items-center">
          <div>已报工数量:</div>
          <div>{{ detail.alreadyComplate }}</div>
        </div>
        <div class="flex items-center">
          <div>可报工数量:</div>
          <div>{{ detail.canComplate }}</div>
        </div>
      </div>
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="horizontal"
        :colon="false"
      >
        <a-form-item name="completedQuantity">
          <template #label>
            <div class="w-30 text-left text-primary-text">本次报工数</div>
          </template>
          <a-input-number
            class="w-60"
            v-model:value="formData.completedQuantity"
            :step="1"
            @change="changeCompletedQuantity"
            :parser="
              (value) => value.replace(/^\D*(\d*(?:\.\d{0,8})?).*$/g, '$1')
            "
            :stringMode="true"
          />
          <!-- :formatter="formatter8" -->
        </a-form-item>
        <div>
          <div class="mb-3 text-primary-text">物料消耗</div>
          <div class="p-4 bg-background rounded-lg space-y-4">
            <a-table
              bordered
              :data-source="data"
              :columns="columns"
              :scroll="{ x: 'max-content' }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'materialType'">
                  {{ getMaterialType(record.materialVO.type) }}
                </template>
                <template v-if="column.dataIndex === 'materialNo'">
                  {{ record.materialVO.materialNo }}
                </template>

                <template v-if="column.dataIndex === 'materialName'">
                  {{ record.materialVO.materialName }}
                </template>
                <template v-if="column.dataIndex === 'specification'">
                  {{ record.materialVO.specification }}
                </template>
                <template v-if="column.dataIndex === 'residueQuantity'">
                  {{ record.residueQuantity }}
                </template>
                <template v-if="column.dataIndex === 'consumeQuantity'">
                  <span class="text-title"
                    >{{
                      Number(record?.materialVO.quantity || 0).toFixed(2)
                    }} </span
                  >个
                </template>
                <template v-if="column.dataIndex === 'productionConsumption'">
                  {{
                    Number(
                      record?.materialVO.quantity * formData.completedQuantity
                    ).toFixed(2)
                  }}
                </template>
                <template v-if="column.dataIndex === 'lossQuantity'">
                  <div class="flex items-center">
                    <a-input-number
                      v-model:value="record.materialVO.lossQuantity"
                      placeholder="数值"
                      @change="
                        changeLossQuantity(
                          record,
                          record?.materialVO.lossQuantity
                        )
                      "
                      :min="0"
                      :stringMode="true"
                    />
                    <div class="w-20 text-center">
                      <span
                        class="text-red-price leading-8 text-xs"
                        v-if="record.showTips"
                        >物料数量不足</span
                      >
                    </div>
                  </div>
                </template>
              </template>
            </a-table>
          </div>
        </div>
        <a-form-item name="remark" class="mt-5">
          <template #label>
            <div class="text-left text-primary-text">备注</div>
          </template>
          <a-input v-model:value="formData.remark" />
        </a-form-item>
        <a-form-item>
          <template #label>
            <div class="text-left text-primary-text">附件</div>
          </template>
          <div class="overflow" v-if="formData.file?.fileVisitUrl">
            <i
              class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
              style="color: #959ec3"
            ></i>
            <a
              :href="formData.file?.fileVisitUrl"
              :title="formData.file?.fileName"
              target="_blank"
              class="underline"
              style="color: #959ec3"
              >{{ formData.file?.fileName }}
            </a>
          </div>
          <form-upload
            v-else
            v-model:value="formData.file"
            sence="delivery"
            :fileTypes="[]"
            :fileSize="100"
            hasDownLoad
          ></form-upload>
        </a-form-item>
      </a-form>
    </div>
  </mw-drawer>
</template>

<script setup>
import {
  reactive,
  ref,
  defineEmits,
  onBeforeMount,
  getCurrentInstance,
  defineProps,
  watch,
  toRaw,
} from "vue";
import { getBom, addRecords } from "@/api/plan.js";
import { useRoute, useRouter } from "vue-router";
import FormUpload from "@/components/form-upload.vue";
import { formatter8 } from "@/common/validate.js";
import { getViewportSize } from "@/common/reg";
import { materialTypes } from "@/common/constant";
import Decimal from "decimal.js";
const { proxy } = getCurrentInstance();
const router = useRouter(),
  route = useRoute(),
  data = ref([]),
  detail = ref({}),
  formData = reactive({
    completedQuantity: 0,
  }),
  rules = ref({
    completedQuantity: [
      {
        required: true,
        message: "请输入数量",
        trigger: "blur",
      },
    ],
  }),
  formRef = ref(),
  submitLoading = ref(false),
  spinning = ref(false),
  columns = [
    {
      title: "物料类型",
      dataIndex: "materialType",
      key: "materialType",
      // customRender: ({ record }) => {
      //   return record.materialType;
      // },
    },
    {
      title: "物料编码",
      dataIndex: "materialNo",
      key: "materialNo",
    },
    {
      title: "物料名称",
      dataIndex: "materialName",
      key: "materialName",
    },
    {
      title: "物料规格",
      dataIndex: "specification",
      key: "specification",
    },
    {
      title: "生产计划剩余数量",
      dataIndex: "residueQuantity",
      key: "residueQuantity",
    },
    {
      title: "单台消耗数",
      dataIndex: "consumeQuantity",
      key: "consumeQuantity",
    },
    {
      title: "生产消耗",
      dataIndex: "productionConsumption",
      key: "productionConsumption",
    },
    {
      title: "物料损耗",
      dataIndex: "lossQuantity",
      key: "lossQuantity",
    },
  ];
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  planNo: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["update:visible", "finish"]);
const closeDrawer = () => {
  formRef.value.resetFields();
  emit("update:visible", false);
  formData.file = [];
};
const getBomDetail = async () => {
  spinning.value = true;
  let res = await getBom({ planNo: props.planNo });
  detail.value = res.data;
  data.value = res.data.planMaterialWithStockVOS.map((item) => {
    return {
      ...item,
      materialVO: {
        ...item.materialVO,
        lossQuantity: 0,
      },
    };
  });
  // data.value.forEach((item) => {
  //   item.materialVO.lossQuantity = 0;
  //   item.materialVO.lossQuantity = 0;
  // });
  spinning.value = false;
};
watch(
  () => props.visible,
  (val) => {
    if (val) {
      getBomDetail();
    }
  }
);
const changeLossQuantity = (item, val) => {
  item.materialVO.lossQuantity = val || 0;
  item.materialVO.consumeQuantity = new Decimal(item.materialVO.quantity)
    .times(formData.completedQuantity)
    .plus(val)
    .toNumber();
  if (item?.materialVO && formData.completedQuantity !== undefined) {
    const quantity = new Decimal(item.materialVO.quantity || 0);
    const completedQuantity = new Decimal(formData.completedQuantity);
    const lossQuantity = new Decimal(item.materialVO.lossQuantity || 0);
    const residueQuantity = new Decimal(item.residueQuantity || 0);
    const totalQuantity = quantity.times(completedQuantity).plus(lossQuantity);
    if (totalQuantity.greaterThan(residueQuantity)) {
      item.showTips = true;
    } else {
      item.showTips = false;
    }
  } else {
    item.showTips = false;
  }
};
const changeCompletedQuantity = (e) => {
  data.value.forEach((item) => {
    if (item?.materialVO && formData.completedQuantity !== undefined) {
      const quantity = new Decimal(item.materialVO.quantity || 0);
      const completedQuantity = new Decimal(formData.completedQuantity);
      const lossQuantity = new Decimal(item.materialVO.lossQuantity || 0);
      const residueQuantity = new Decimal(item.residueQuantity || 0);

      const totalQuantity = quantity
        .times(completedQuantity)
        .plus(lossQuantity);

      if (totalQuantity.greaterThan(residueQuantity)) {
        item.showTips = true;
      } else {
        item.showTips = false;
      }
    } else {
      item.showTips = false;
    }
    const num = numMulti(
      item.materialVO.quantity,
      formData.completedQuantity,
      item.materialVO.lossQuantity
    );
    item.materialVO.consumeQuantity = addNum(
      num,
      item.materialVO.lossQuantity || 0
    );
  });
};
function numMulti(num1, num2) {
  var baseNum = 0;
  try {
    baseNum += num1.toString().split(".")[1].length;
  } catch (e) {}
  try {
    baseNum += num2.toString().split(".")[1].length;
  } catch (e) {}
  return (
    (Number(num1.toString().replace(".", "")) *
      Number(num2.toString().replace(".", ""))) /
    Math.pow(10, baseNum)
  );
}
//加法
const addNum = (num1, num2) => {
  var sq1, sq2, m;
  try {
    sq1 = num1.toString().split(".")[1].length;
  } catch (e) {
    sq1 = 0;
  }
  try {
    sq2 = num2.toString().split(".")[1].length;
  } catch (e) {
    sq2 = 0;
  }
  m = Math.pow(10, Math.max(sq1, sq2));
  return (num1 * m + num2 * m) / m;
};
const onSubmit = () => {
  const hasError = data.value.some((item) => {
    return item.showTips;
  });
  if (hasError) {
    proxy.$message.error("物料数量不足");
    return;
  }
  formRef.value.validate().then(async () => {
    submitLoading.value = true;
    let costMaterialList = data.value.map((item) => {
      return {
        consumeQuantity: numMulti(
          item.materialVO.quantity,
          formData.completedQuantity
        ),
        lossQuantity: item.materialVO.lossQuantity,
        materialId: item.materialVO.materialId,
      };
    });

    let param = {
      ...toRaw(formData),
      costMaterialList: costMaterialList,
      planNo: props.planNo,
      file: formData.file && formData.file[0],
    };
    try {
      let res = await addRecords(param);
      if (res.code == 200) {
        proxy.$message.success("添加成功");
      }
      // }
      submitLoading.value = false;
      closeDrawer();
      emit("finish");
    } catch (error) {
      submitLoading.value = false;
    }
  });
};
const getMaterialType = (val) => {
  const res = materialTypes.find((item) => item.value == val);
  return res ? res.label : "初始bom物料";
};
</script>

<style lang="scss" scoped></style>
