<template>
  <mw-drawer
    :visible="visible"
    @close="closeDrawer"
    customTitle="生产质检"
    :spinning="spinning"
    :width="getViewportSize() > 800 ? '35%' : '100%'"
  >
    <template #header>
      <mw-button @click="onSubmit" :loading="submitLoading">确定</mw-button>
    </template>
    <div>
      <div>
        <a-descriptions
          :colon="false"
          :column="1"
          :labelStyle="{ width: '68px', color: 'rgba(31,31,31,.65)' }"
          :contentStyle="{ color: 'rgba(31,31,31,.65)' }"
        >
          <a-descriptions-item label="产品名称">{{
            data.materialName
          }}</a-descriptions-item>
          <a-descriptions-item label="产品编码">{{
            data.materialNo
          }}</a-descriptions-item>
          <a-descriptions-item label="产品规格">{{
            data.materialSpecification
          }}</a-descriptions-item>
          <a-descriptions-item label="已生产">
            {{ data.totalQuantity }}</a-descriptions-item
          >
          <a-descriptions-item label="待质检">
            {{ data.uninspectedQuantity }}</a-descriptions-item
          >
          <a-descriptions-item label="生产线">
            {{ data.lineName }}
          </a-descriptions-item>
        </a-descriptions>
        <a-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          layout="horizontal"
          :colon="false"
          :labelStyle="{ width: '68px', color: 'rgba(31,31,31,.65)' }"
          :contentStyle="{ color: 'rgba(31,31,31,.65)' }"
        >
          <a-form-item name="quantity">
            <template #label>
              <div class="text-left text-primary-text">送检总数</div>
            </template>
            <a-input-number
              v-model:value="formData.quantity"
              :stringMode="true"
            />
          </a-form-item>
        </a-form>
      </div>
    </div>
  </mw-drawer>
</template>

<script setup>
import {
  reactive,
  ref,
  defineEmits,
  onBeforeMount,
  getCurrentInstance,
  defineProps,
  watch,
  toRaw,
} from "vue";
import { getBom, planDetail, productTestAllot } from "@/api/plan.js";
import { useRoute, useRouter } from "vue-router";
import { getViewportSize } from "@/common/reg";
const { proxy } = getCurrentInstance();
const router = useRouter(),
  route = useRoute(),
  data = ref({}),
  formRef = ref(),
  rules = ref({
    quantity: [
      {
        required: true,
        message: "请输入数量",
        trigger: "change",
      },
    ],
  }),
  formData = reactive({
    quantity: 0,
  }),
  max = ref(0),
  submitLoading = ref(false),
  spinning = ref(false);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  planNo: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["update:visible", "finish"]);
const closeDrawer = () => {
  emit("update:visible", false);
};
const getBomDetail = async () => {
  spinning.value = true;
  let res = await planDetail({ planNo: props.planNo });
  data.value = res.data;
  formData.quantity = res.data.maxQualityQuantity || 0;
  max.value = res.data.maxQualityQuantity;
  spinning.value = false;
};
const onSubmit = () => {
  // formData.quantity = parseInt(formData.quantity);

  formRef.value.validate().then(async () => {
    submitLoading.value = true;

    if (formData.quantity == 0) {
      proxy.$message.error("质检数量不能为0！");
      submitLoading.value = false;
      return;
    }
    try {
      let res = await productTestAllot({
        ...toRaw(formData),
        planNo: props.planNo,
      });
      if (res.code == 200) {
        proxy.$message.success("添加成功");
      }
      // }
      submitLoading.value = false;
      closeDrawer();
      emit("finish");
    } catch (error) {
      submitLoading.value = false;
    }
  });
};
watch(
  () => props.visible,
  (val) => {
    if (val) {
      getBomDetail();
    }
  }
);
watch(
  () => formData.quantity,
  (newValue) => {}
);
</script>

<style lang="less" scoped>
:deep(.ant-form-item-label > label::before) {
  content: "" !important;
  margin-right: 0 !important;
}
</style>
