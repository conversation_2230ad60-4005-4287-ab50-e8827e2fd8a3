<template>
  <mw-drawer
    :visible="visible"
    @close="closeDrawer"
    :customTitle="planNo ? '更新生产计划' : '新建生产计划'"
    :spinning="spinning"
    destroyOnClose="true"
    :width="getViewportSize() > 800 ? '35%' : '100%'"
  >
    <template #header>
      <mw-button
        title="确定"
        @click="onSubmit"
        :loading="submitLoading"
      ></mw-button>
    </template>
    <div>
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="horizontal"
        :colon="false"
      >
        <!-- 生产计划名称 -->

        <a-form-item name="planName">
          <template #label>
            <div class="w-30 text-left text-primary-text">生产计划名称</div>
          </template>
          <a-input
            v-model:value="formData.planName"
            placeholder="请输入生产计划名称"
          />
        </a-form-item>
        <a-form-item name="schedulingOrderId" required>
          <template #label>
            <div class="w-30 text-left text-primary-text">关联-排产计划</div>
          </template>
          <a-select
            v-if="!planNo"
            v-model:value="formData.schedulingOrderId"
            show-search
            placeholder="请选择排产计划"
            style="width: 100%"
            :default-active-first-option="false"
            :options="schedulingOrderdata"
            optionFilterProp="schedulingName"
            :fieldNames="{
              label: 'schedulingName',
              value: 'id',
            }"
            @change="handleChangeProduct"
          >
            ></a-select
          >
          <span v-else>{{ formData.planName }}</span>
        </a-form-item>
        <!-- <a-form-item name="materialNo">
          <template #label>
            <div class="w-30 text-left text-primary-text">销售订单编码</div>
          </template>
          <div>{{ formData.marketOrderNo }}</div>
        </a-form-item> -->
        <a-form-item name="productNoData">
          <template #label>
            <div class="w-32 text-left text-primary-text">销售订单编码</div>
          </template>
          <a-input v-model:value="formData.marketOrderNo" disabled />
        </a-form-item>
        <a-form-item name="materialNo">
          <template #label>
            <div class="w-30 text-left text-primary-text">关联产品</div>
          </template>
          <a-select
            v-if="!planNo"
            v-model:value="formData.materialNo"
            show-search
            placeholder="请选择关联产品"
            style="width: 100%"
            :default-active-first-option="false"
            :options="allProductList"
            optionFilterProp="materialName"
            :fieldNames="{
              label: 'materialName',
              value: 'materialNo',
            }"
            @change="handleChangeName($event, record)"
          ></a-select>
        </a-form-item>
        <!-- <a-form-item name="productNoData">
          <template #label>
            <div class="w-32 text-left text-primary-text">产品编号</div>
          </template>
          <a-input v-model:value="formData.productNoData" disabled />
        </a-form-item> -->
        <a-form-item name="responsibleUserId">
          <template #label>
            <div class="w-30 text-left text-primary-text">负责人</div>
          </template>
          <a-select
            v-if="!planNo"
            v-model:value="formData.responsibleUserId"
            show-search
            placeholder="请选择负责人"
            style="width: 100%"
            :default-active-first-option="false"
            :options="allUserList"
            optionFilterProp="nickName"
            :fieldNames="{
              label: 'nickName',
              value: 'userId',
            }"
            @change="handleChangeUser"
          ></a-select>
          <span v-else>{{ formData.responsibleUser }}</span>
        </a-form-item>
        <a-form-item name="lineNo">
          <template #label>
            <div class="w-30 text-left text-primary-text">生产线</div>
          </template>
          <!-- @search="handleSearchLine" -->

          <a-select
            v-if="!planNo"
            v-model:value="formData.lineNo"
            show-search
            placeholder="请选择生产线"
            style="width: 100%"
            :default-active-first-option="false"
            :options="allLineList"
            optionFilterProp="lineName"
            :fieldNames="{
              label: 'lineName',
              value: 'lineNo',
            }"
            @change="handleChangeLine"
          ></a-select>
          <span v-else>{{ formData.lineName }}</span>
        </a-form-item>
        <!-- <a-form-item name="requiredQuantity">
          <template #label>
            <div class="w-30 text-left text-primary-text">需求数量</div>
          </template>
          <a-input-number
            v-if="!planNo"
            v-model:value="formData.requiredQuantity"
          />
          <span v-else>{{ formData.requiredQuantity }}</span>
        </a-form-item> -->
        <a-form-item name="planName">
          <template #label>
            <div class="w-30 text-left text-primary-text">待生产数量</div>
          </template>
          <a-input
            v-model:value="schedulingTotal"
            placeholder="待排产数量"
            disabled
          />
        </a-form-item>
        <a-form-item>
          <template #label>
            <div class="w-30 text-left text-primary-text">生产计划明细</div>
          </template>
          <mw-button title="新增" @click="handleAdd"></mw-button>
        </a-form-item>
        <mw-table
          bordered
          :data-source="formData.productionPlanDetailParams"
          :columns="columnsProductionPlanDetailParams"
          :scroll="{ x: max - content }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'productionTime'">
              <a-range-picker
                v-if="!planNo"
                v-model:value="record.productionTime"
                style="width: 100%"
                valueFormat="YYYY-MM-DD"
                :disabled-date="disabledDateNoConThenBefore"
              />
              <span v-else>{{ record.productionTime }}</span>
            </template>
            <template v-if="column.dataIndex === 'productionQuantity'">
              <a-input-number
                style="width: 100%"
                id="inputNumber"
                v-model:value="record.productionQuantity"
                placeholder="请输入"
                :stringMode="true"
              />
            </template>
            <template v-if="column.dataIndex === 'operation'">
              <a-popconfirm title="确定是否删除" @confirm="onDelete(record)">
                <mw-button title="删除" danger></mw-button>
              </a-popconfirm>
            </template>
          </template>
        </mw-table>
      </a-form>
    </div>
    <schedulOrderDrawer
      ref="schedulOrderRef"
      @relatedSchedulingOrderList="ondulingOrderList"
    >
    </schedulOrderDrawer>
  </mw-drawer>
</template>

<script setup>
import {
  reactive,
  ref,
  defineEmits,
  onBeforeMount,
  getCurrentInstance,
  defineProps,
  watch,
  toRaw,
  onMounted,
} from "vue";
import { addNew, postNewAdd } from "@/api/plan.js";
import { list as getAllLineList } from "@/api/basicData/productionLine.js";
import { disabledDateNoConThenBefore } from "@/utils/util.js";
import { getSchedulingOrderPage } from "@/api/proSchedul/scheduling.js";
import { list as getAllUserList } from "@/api/system/user.js";
import {
  list as getAllProductList,
  listBySchedulingOrderId,
} from "@/api/basicData/product.js";
import { planDetail } from "../../api/plan";
import Dayjs from "dayjs";
import schedulOrderDrawer from "./schedulOrderDrawer.vue";
import { getViewportSize } from "@/common/reg";
// components = {
//     OnlineModal
// }
const { proxy } = getCurrentInstance();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  planNo: {
    type: String,
    default: "",
  },
});
const columnsProduc = [
  { title: "订单编号", dataIndex: "schedulingOrderNumber" },
  { title: "订单数量", dataIndex: "quantity" },
  { title: "订单状态", dataIndex: "status" },
];
const columnsProductionPlanDetailParams = [
  {
    title: "生产日期",
    dataIndex: "productionTime",
    width: "300px",
  },
  {
    title: "计划生产数",
    dataIndex: "productionQuantity",
    width: "200px",
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: "150px",
  },
];
const formData = reactive({
    status: 0,
    planName: undefined,
    lineNo: undefined,
    responsibleUserId: undefined,
    materialNo: undefined,
    // requiredQuantity: 1,
    productionTime: undefined,
    lineName: undefined,
    responsibleUser: undefined,
    productName: undefined,
    productionPlanDetailParams: [],
  }),
  rules = ref({
    planName: [
      {
        required: true,
        message: "请填写生产计划名称",
        trigger: "blur",
      },
    ],
    lineNo: [
      {
        required: true,
        message: "请选择生产线",
        trigger: "blur",
      },
    ],
    responsibleUserId: [
      {
        required: true,
        message: "请选择负责人",
        trigger: "blur",
      },
    ],
    materialNo: [
      {
        required: true,
        message: "请选择关联产品",
        trigger: "blur",
      },
    ],
    // requiredQuantity: [
    //   {
    //     required: true,
    //     message: "请输入需求数量",
    //     trigger: "blur",
    //   },
    // ],
    productionTime: [
      {
        required: true,
        message: "请选择完成日期",
        trigger: "blur",
      },
    ],
  }),
  formRef = ref(),
  schedulOrderRef = ref(),
  allLineList = ref([]),
  selectedLine = ref({}),
  allUserList = ref([]),
  selectedUser = ref({}),
  allProductList = ref([]),
  selectedProduct = ref({}),
  schedulingOrderdata = ref([]),
  submitLoading = ref(false),
  scheduOrderid = ref(),
  productSwitch = ref(false),
  spinning = ref(false);

const emit = defineEmits(["update:visible", "update:planNo"]);
const schedulingTotal = ref(undefined);

const closeDrawer = () => {
  formRef.value.resetFields();
  props.planNo = undefined;
  schedulingTotal.value = undefined;
  allLineList.value = [];
  allUserList.value = [];
  allProductList.value = [];
  formData.productionPlanDetailParams = [];
  (productSwitch.value = false), emit("update:visible", false);
  emit("update:planNo", "");
  formData.marketOrderNo = "";
};

// 获取产线列表
async function getAllLine(keyword) {
  let result = await getAllLineList({
    keyword: keyword,
    ignoreCancel: true,
    onlyUnUsed: false,
  });
  let { data } = result;
  allLineList.value = data;
}
// 搜索产线
// let lineTimer;
// const handleSearchLine = (value) => {
//   if (value.length) {
//     clearTimeout(lineTimer);
//     lineTimer = setTimeout(() => {
//       getAllLine(value);
//     }, 500);
//   } else {
//     setTimeout(() => {
//       allLineList.value = [];
//     }, 500);
//   }
// };
const onDelete = (val) => {
  formData.productionPlanDetailParams.forEach((item, index) => {
    if (val.id == item.id) {
      formData.productionPlanDetailParams.splice(index, 1);
    }
  });
  // 列表操作
};
// 选中产线
const handleChangeLine = async (value) => {
  selectedLine.value = allLineList.value.filter(
    (item) => item.value == value
  )[0];
  // 获取选中的bom信息
  // let res = await getBomMaterialList({ bomNo: value });
};

//  获取用户列表
async function getAllUser(keyword) {
  let result = await getAllUserList({
    pageNum: 1,
    pageSize: 10000,
    sortType: "create_time",
    sortOrder: "desc",
    userName: keyword,
  });
  let { data } = result;
  allUserList.value = data;
}
//  ---------
// 搜索用户
// let UserTimer;
// const handleSearchUser = (value) => {
//   if (value.length) {
//     clearTimeout(UserTimer);
//     UserTimer = setTimeout(() => {
//       getAllUser(value);
//     }, 500);
//   } else {
//     setTimeout(() => {
//       allUserList.value = [];
//     }, 500);
//   }
// };
// 选择用户
const handleChangeUser = async (value) => {
  selectedUser.value = allUserList.value.filter(
    (item) => item.value == value
  )[0];
  // 获取选中的bom信息
  // let res = await getBomMaterialList({ bomNo: value });
};
//  ---------
//  获取产品列表
async function getAllProduct(val) {
  let result = await listBySchedulingOrderId({
    schedulingOrderId: val,
  });
  let { data } = result;
  allProductList.value = data.map((item, index) => {
    item.materialName =
      item.materialNo +
      " / " +
      item.materialName +
      " / " +
      item.materialSpecification;
    return item;
  });
}
// 搜索产品
// let ProductTimer;
// const handleSearchProduct = (value) => {
//   if (value.length) {
//     clearTimeout(ProductTimer);
//     ProductTimer = setTimeout(() => {
//       getAllProduct(value);
//     }, 500);
//   } else {
//     setTimeout(() => {
//       allProductList.value = [];
//     }, 500);
//   }
// };
const handleChangeName = async (e) => {
  allProductList.value.forEach((item, index) => {
    if (item.materialNo == e) {
      formData.productName = item.productName;
      schedulingTotal.value = item.waitingProductionSchedulingQuantity;
      formData.productNoData = item.materialNo;
    }
  });
};

// 选择排产计划
const handleChangeProduct = async (value, item) => {
  formData.materialNo = undefined;
  schedulingTotal.value = undefined;
  getAllProduct(value);
  selectedProduct.value = allUserList.value.filter(
    (item) => item.value == value
  )[0];
  formData.marketOrderNo = item.marketOrderNo;
  // let data = schedulingOrderdata.value.find((item) => value == item.id);

  productSwitch.value = true;
};
const onSubmit = () => {
  formRef.value.validate().then(async () => {
    if (!formData.productionPlanDetailParams.length) {
      proxy.$message.error("生产计划明细不能为空!");
      return;
    }
    const hasEmptyName = Array.from(formData.productionPlanDetailParams).every(
      (obj) => obj.productionQuantity != "" && obj.productionTime != ""
    );
    if (!hasEmptyName) {
      proxy.$message.error("请完整填写明细信息!");
      return;
    }
    formData.productionPlanDetailParams.forEach((item, index) => {
      // delete item.id;
      item.productionStartTime = item?.productionTime
        ? item.productionTime[0]
        : void 0;
      item.productionEndTime = item.productionTime
        ? item.productionTime[1]
        : void 0;
    });

    const productionPlanDetailParams =
      formData.productionPlanDetailParams.filter((item) => {
        return item?.productionStartTime || item?.productionEndTime;
      });

    const params = { ...formData, productionPlanDetailParams };
    try {
      submitLoading.value = true;
      let res = await postNewAdd(params);
      if (res.code == 200) {
        proxy.$message.success("添加成功");
        closeDrawer();
        emit("finish");
      }
      submitLoading.value = false;
      formData.relatedSchedulingOrderList = [];
      productSwitch.value = false;
    } catch (error) {
      submitLoading.value = false;
    }
  });
};

// 获取plan详情
const getPlanDetail = async () => {
  spinning.value = true;
  let res = await planDetail({ planNo: props.planNo });
  for (let key in formData) {
    formData[key] = res.data[key];
  }
  getSchedulingOrderPage;
  spinning.value = false;
};

// 排产
const getschedulingOrder = async (valId) => {
  let listData = await getSchedulingOrderPage({
    pageNum: 1,
    pageSize: 9999999,
    ignoreCancel: true,
    status: 0,
  });
  schedulingOrderdata.value = listData.data;
};

// const onAddOrder = () =>{
//   formData.relatedSchedulingOrderList.push({
//         "requiredQuantity": undefined,
//         "schedulingOrderId": undefined,
//         "schedulingOrderInfoId": undefined
//   })
// }

const onSchedulingOrder = (orderId, item) => {
  let data = schedulingOrderdata.value.find(
    (item) => item.schedulingOrderInfoId === orderId
  );
  item.schedulingOrderId = data.id;
};
const deleteOrder = (val) => {
  formData.relatedSchedulingOrderList.forEach((item, index) => {
    if (item.schedulingOrderInfoId == val) {
      formData.relatedSchedulingOrderList.splice(index, 1);
    }
  });
};
// 添加排产订单按钮schedulOrderDrawer
const addSchedulOrder = () => {
  schedulOrderRef.value.init(scheduOrderid);
};
const ondulingOrderList = async (val) => {
  formData.relatedSchedulingOrderList = schedulingOrderdata.value
    .filter((item) => val.indexOf(item.schedulingOrderNumber) > -1)
    .map((item) => ({
      customerName: item.customerName,
      schedulingOrderNumber: item.schedulingOrderNumber,
      quantity: item.quantity,
      status: item.status == 0 ? "未使用" : "已使用",
      requiredQuantity: item.price,
      schedulingOrderId: item.productId,
      schedulingOrderInfoId: item.schedulingOrderInfoId,
    }));
};
const handleAdd = () => {
  const newData = {
    id: Date.now(),
  };
  formData.productionPlanDetailParams.push(newData);
};
watch(
  () => props.visible,
  async (val) => {
    if (val && props.planNo) {
      getPlanDetail();
    }
  }
);
watch(
  () => props.visible,
  (val) => {
    if (val) {
      getAllLine();
      getAllUser();
      getschedulingOrder();
    }
  }
);
onMounted(() => {});
</script>

<style lang="less" scoped>
:deep(.ant-form-item) {
  margin-bottom: 16px;
}
.colcentbg {
  background: #eee;
}
.colcent {
  margin-bottom: 4%;
}
.colcent .ant-col-12 {
  text-align: center;
  width: 50%;
}
.addpro {
  display: flex;
  margin: auto;
  margin-top: 30px;
}
.heidle {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
</style>
