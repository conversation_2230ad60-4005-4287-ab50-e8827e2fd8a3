<template style="width: 155%">
  <div class="codeDataBody" style="">
    <!-- v-if="platformSide" -->
    <div v-if="platformSide">
      <div v-if="route.name == 'warehouseArea'">
        <a-flex class="mb-4 mt-2"
          ><b> 当前所在企业：{{ data?.tenantName || "" }} </b>
        </a-flex>
        <a-flex class="mb-4">
          <b>仓库：{{ data.warehouseName || "-" }}</b>
          <b class="ml-10">库位：{{ data.warehouseArea || "-" }}</b>
        </a-flex>
        <b> 物料列表</b>
      </div>
      <div v-if="route.name == 'materialQrcode'">
        <a-flex class="mb-4 mt-2"
          ><b> 当前所在企业：{{ data?.tenantName || "" }} </b>
        </a-flex>
        <div class="flex flex-col">
          <b>物料编码：{{ data?.materialNo || "" }}</b>
          <b>物料名称：{{ data?.materialName || "" }}</b>
          <b>物料规格：{{ data?.specification || "" }}</b>
        </div>
      </div>

      <mw-table
        class="mt-4"
        :columns="route.name == 'warehouseArea' ? columns : columnsMaterial"
        :data-source="
          route.name == 'warehouseArea'
            ? data?.materialList
            : data?.warehouseList
        "
        :loading="loading"
        :rowKey="(record) => record.id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'inventory'">
            {{ formatter2(record.inventory ? record.inventory : 0) }}
            {{ record.unitName }}
          </template>

          <template v-if="column.dataIndex == 'materialName'">
            <div class="text-sm">{{ record.materialNo }}</div>
            <div class="text-sm">{{ record.materialName }}</div>
            <div class="text-sm">{{ record.specification }}</div>
          </template>

          <template v-if="column.dataIndex == 'operation'">
            <div style="width: 100%" class="flex flex-wrap">
              <span
                v-permission="'wms:inventory:add'"
                type="text"
                @click="onModal(record, 'inventory', data)"
                class="text-primary cursor-pointer p-1"
                >盘点</span
              >
              <span
                v-permission="'wms:allocation:add'"
                type="text"
                @click="onModal(record, 'allocation', data)"
                class="text-primary cursor-pointer p-1"
                >调拨</span
              >
            </div>
          </template>
        </template>
      </mw-table>
    </div>

    <a-modal
      width="90%"
      :closable="false"
      :mask-closable="false"
      :destroyOnClose="true"
      :centered="false"
      v-model:open="modal2Visible"
      @ok="modal2Visible = false"
      :title="`新增${textTitle}`"
    >
      <div style="height: 60vh; overflow-y: scroll">
        <a-flex v-if="textTitle == '盘点'" class="mb-4">
          <span style="width: 100px">最近盘点时间：</span>
          {{ formState.inventoryTime }}</a-flex
        >
        <a-flex class="mb-4">
          <span style="width: 100px"> 物料：</span>
          <div class="">
            <div>{{ formState.materialNo }}</div>
            <div>{{ formState.materialName }}</div>
            <div>{{ formState.specification }}</div>
          </div>
        </a-flex>
        <a-flex class="mb-4">
          <span style="width: 100px"> 仓库：</span>
          {{ formState.warehouseName }}
        </a-flex>
        <a-flex class="mb-4">
          <span style="width: 100px"> 仓库：</span>
          {{ formState.warehouseArea }}
        </a-flex>
        <a-flex class="mb-4">
          <span style="width: 100px">当前库存：</span>
          {{ (formState.oldNum = formState.inventory || 0)
          }}{{ formState.unitName }}
        </a-flex>
        <a-flex v-if="textTitle == '调拨'" class="mb-4">
          <span style="width: 100px"> 调出数量：</span>
          <a-input-number
            v-model:value="formState.inventoryNum"
            class="ml-2"
            style="width: 70%"
            :formatter="formatter8"
            :stringMode="true"
          />
          <span class="ml-1 mt-1">{{ formState.unitName }}</span>
        </a-flex>
        <a-flex v-if="textTitle == '盘点'" class="mb-4">
          <span style="width: 100px"> 盘点数量：</span>
          <a-input-number
            v-model:value="formState.num"
            class="ml-2"
            :formatter="formatter8"
            style="width: 70%"
            :stringMode="true"
          />
          <span class="ml-1 mt-1"> {{ formState.unitName }}</span>
        </a-flex>
        <a-flex
          class="mb-4"
          v-if="textTitle == '盘点' && formState.num >= formState.inventory"
        >
          <span style="width: 100px"> 盘盈：</span>
          <span v-if="formState.inventory && formState.num">
            {{ roundNumFun(Math.abs(formState.inventory - formState.num), 8)
            }}{{ formState.unitName }}</span
          >
        </a-flex>

        <a-flex
          class="mb-4"
          v-if="textTitle == '盘点' && formState.num < formState.inventory"
        >
          <span style="width: 100px"> 盘亏：</span>
          <span>
            {{
              roundNumFun(
                formState.num == 0
                  ? formState.oldNum
                  : Math.abs(formState.inventory - formState.num),
                8
              )
            }}{{ formState.unitName }}</span
          >
        </a-flex>
        <a-flex v-if="textTitle == '调拨'" class="mb-4">
          <span style="width: 100px"> 调入仓库：</span>

          <!--optionFilterProp="warehouseName"   show-search
        -->
          <a-select
            class="mb-2 ml-2"
            style="width: 70%"
            placeholder="请选择调入仓位"
            :options="warehouseList"
            v-model:value="formState.inWarehouseId"
            @change="choiceWarehouse"
          ></a-select>
        </a-flex>

        <a-flex v-if="textTitle == '调拨'" class="mb-4">
          <span style="width: 100px"> 仓库库位：</span>
          <!--    show-searchoptionFilterProp="warehouseArea" -->
          <a-select
            class="mb-2 ml-2"
            style="width: 70%"
            placeholder="请选择调入仓位"
            :options="warehouseLocationList"
            v-model:value="formState.inWarehouseAreaId"
            :field-names="{
              label: 'warehouseArea',
              value: 'id',
            }"
            @change="inWarehouseAreaChange"
          ></a-select>
        </a-flex>

        <a-flex v-if="textTitle == '调拨'" class="mb-4">
          <span style="width: 100px"> 调入实际库存：</span>
          {{ formState.inPhysicalInventory }}
          <span v-if="formState.inPhysicalInventory">{{
            formState.unitName
          }}</span>
        </a-flex>

        <a-flex class="mb-2">
          <span style="width: 100px"> {{ textTitle }}备注</span>
          <a-textarea
            class="ml-2"
            style="width: 70%"
            v-model:value="formState.remark"
          ></a-textarea
        ></a-flex>
      </div>
      <template #footer>
        <div class="text-center">
          <mw-button
            shape="round"
            key="back"
            @click="handleCancel"
            class="text-center"
            >取消</mw-button
          >
          <mw-button key="submit" :loading="loading" @click="handleOk"
            >确定</mw-button
          >
        </div>
      </template>
    </a-modal>
  </div>
</template>
<script setup>
import { ref, onBeforeMount, getCurrentInstance } from "vue";
import { useRoute } from "vue-router";
import { warehouse, queryMaterial } from "@/api/basicData/repository.js";
import { getToken } from "@/utils/auth.js";
import { env } from "dingtalk-jsapi";
import { useUserStore } from "@/stores/user.js";
import { roundNumFun } from "@/common/validate.js";
import {
  warehouseInventory,
  warehouseAllocation,
  listByMaterialIgnoreNoStock,
} from "@/api/basicData/repository.js";
import { menuListCollect } from "@/api/inventoryAllocation";
import { page } from "@/api/warehouse";
import { AllList, getInfoRepository } from "@/api/basicData/material";
import { formatter8, formatter2 } from "@/common/validate.js";
const columns = [
  {
    title: "物料",
    dataIndex: "materialName",
    width: 200,
  },
  {
    title: "数量",
    dataIndex: "inventory",
    width: 200,
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 250,
  },
];
const columnsMaterial = [
  {
    title: "仓库",
    dataIndex: "warehouseName",
    width: 200,
  },
  {
    title: "库位",
    dataIndex: "warehouseArea",
    width: 200,
  },
  {
    title: "数量",
    dataIndex: "inventory",
    width: 200,
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 250,
  },
];
const { proxy } = getCurrentInstance();
const data = ref([]);
const loading = ref(false);
const id = ref("");
const platformSide = ref();
const modal2Visible = ref(false);
const token = getToken();
const route = useRoute();
const referName = ref("");
const textTitle = ref("");
const formState = ref({
  menuId: "",
  areaId: "",
  inventoryNum: 0,
});
const userStore = useUserStore();
const warehouseList = ref();
const warehouseLocationList = ref();
const textValue = ref();
const choiceWarehouse = async (e, record) => {
  formState.value.inWarehouseAreaId = void 0;
  formState.value.inPhysicalInventory = void 0;
  const { data, code } = await getInfoRepository(e);
  if (code == 200) {
    warehouseLocationList.value = data?.warehouseMenuDetailVoList || [];
  }

  // warehouseLocationList.value = record.warehouseMenuDetailVoList;
};

const handleOk = async () => {
  console.log(formState.value, " formState.value");
  let res;
  formState.value.type = "material";
  if (textValue.value == "inventory") {
    if (!JSON.stringify(formState.value.num)) {
      proxy.$message.error("盘点数量不可为空");
    } else {
      res = await warehouseInventory(formState.value);
    }
  } else {
    if (formState.value.inventoryNum > formState.value.oldNum) {
      proxy.$message.error("调出数量不可大于当前库存");
      return;
    }
    formState.value.inventory = formState.value.inventoryNum;

    if (formState.value.inWarehouseId && formState.value.inWarehouseAreaId) {
      delete formState.value.inventoryTime;
      res = await warehouseAllocation(formState.value);
    } else {
      proxy.$message.error("调拨库存信息不可为空");
    }
    // formState.value.inventoryTime = moment(formState.value.inventoryTime).format(
  }
  console.log(res, "res");
  if (res && res.code == 200) {
    getList();
    proxy.$message.success("新增成功");
    modal2Visible.value = false;
    canScroll();
  }
  console.log(res, "res");
};
const onModal = (record, modalType, dataList) => {
  // 窗口标题
  stopScroll();
  textTitle.value = modalType
    ? { inventory: "盘点", allocation: "调拨" }[modalType]
    : "";
  textValue.value = modalType;

  formState.value.materialId = dataList.materialId;
  console.log(formState.value.materialId);
  // formState数据
  console.log(dataList, "dataList");
  formState.value = { ...record, inventory: record.inventory };

  if (dataList.unitName) {
    formState.value.unitName = dataList.unitName;
  }
  if (route.name == "warehouseArea") {
    formState.value.warehouseName = dataList.warehouseName;
    formState.value.warehouseArea = dataList.warehouseArea;
  } else {
    formState.value.warehouseName = record.warehouseName;
    formState.value.warehouseArea = record.warehouseArea;
    formState.value.specification = dataList.specification;
    formState.value.materialName = dataList.materialName;
    formState.value.materialNo = dataList.materialNo;
    formState.value.materialId = dataList.materialId;
  }

  // 打开弹窗

  if (modalType == "allocation") {
    formState.value.outWarehouseId = Number(
      route.name == "warehouseArea" ? dataList.menuId : record.menuId
    );
    formState.value.outWarehouseAreaId = Number(
      route.name == "warehouseArea" ? dataList.areaId : record.areaId
    );
    console.log(route.query.materialId, " route.query.materialId");

    // listByMaterialData({
    //   // materialId:
    //   //   route.name == "warehouseArea" ? record.materialId : dataList.materialId,获取所有仓库,不需要传id
    //   warehouseType: "RAWMATERIAL",
    //   materialId: formState.value.materialId,
    // });
    menuListCollectList();
  } else {
    // formState.value.type = "material";
    formState.value.warehouseId = Number(
      route.name == "warehouseArea" ? dataList.menuId : record.menuId
    );
    formState.value.warehouseAreaId = Number(
      route.name == "warehouseArea" ? dataList.areaId : record.areaId
    );
  }
  modal2Visible.value = true;
};
const handleCancel = () => {
  modal2Visible.value = false;
  canScroll();
};
// 仓库库存
const listByMaterialData = async (prams) => {
  let res = await listByMaterialIgnoreNoStock(prams);
  console.log(res, "res");
  warehouseList.value = res.data;
};

//获取出入库仓库 获取所有
const menuListCollectList = async (type) => {
  //FINISHED 产品
  //RAWMATERIAL 物料
  const { data, code } = await menuListCollect({
    warehouseType: type,
  });
  if (code === 200) {
    // inWarehouseOption.value =
    //   data?.map((item) => {
    //     return {
    //       label: item.warehouseName,
    //       value: item.id,
    //     };
    //   }) || [];
    warehouseList.value =
      data?.map((item) => {
        return {
          label: item.warehouseName,
          value: item.id,
        };
      }) || [];
  }
};

const inWarehouseAreaChange = async (value) => {
  console.log(formState, "..");
  const { data, code } = await page(
    { pageNum: 1, pageSize: 10000 },
    {
      classifyType: formState.type == "product" ? "product" : "material",
      warehouseId: formState.value.inWarehouseId,
      warehouseArea: value,
      materialIdOrProductId: formState.value.materialId,
    }
  );
  if (code === 200) {
    // inPhysicalInventory.value = data[0]?.inventory || 0;
    formState.value.inPhysicalInventory = data[0]?.inventory || 0;
  }
};

onBeforeMount(async () => {
  // getList();
  // 不等于notInDingTalk的时候就是钉钉登录，等于的时候就是其他平台登录
  if (env.platform == "notInDingTalk") {
    proxy.$message.warning("请使用钉钉扫码");
    platformSide.value = false;
    return;
  }
  platformSide.value = true;
  getList();
});
const getList = async () => {
  console.log("userStore", userStore, userStore.user.tenantId);
  loading.value = true;
  let warehouseData;
  if (route.name == "warehouseArea") {
    const formData = {
      type: "area",
      referId: route.query.itemId,
      referName: referName.value,
      userId: userStore.user.userId,
      tenantId: userStore.user.tenantId,
    };
    warehouseData = await warehouse(formData);
    if (warehouseData.code == 200) {
      platformSide.value = true;
    } else {
      platformSide.value = false;
    }
    console.log(warehouseData, "warehouseData");
    data.value = warehouseData.data;
  } else {
    console.log(route.query);
    let prams = {
      userId: userStore.user.userId,
      tenantId: userStore.user.tenantId,
      materialId: route.query.materialId,
    };
    let res = await queryMaterial(prams);
    if (res.code == 200) {
      platformSide.value = true;
    } else {
      platformSide.value = false;
    }
    data.value = res ? res?.data : [];
    console.log(res, "res");
  }
  loading.value = false;
};

//禁止滚动
const stopScroll = () => {
  var mo = function (e) {
    e.preventDefault();
  };
  document.body.style.overflow = "hidden";
  document.addEventListener("touchmove", mo, false); //禁止页面滑动
};
/***取消滑动限制***/
const canScroll = () => {
  var mo = function (e) {
    e.preventDefault();
  };
  document.body.style.overflow = ""; //出现滚动条
  document.removeEventListener("touchmove", mo, false);
};
</script>

<style lang="less" scoped>
.codeDataBody {
  width: 100vw;
  background: #ffffff;
  height: 100vh;
  padding: 5%;
}
::deep(.ant-modal-footer) {
  text-align: center;
}
input {
  margin-left: 0;
  margin-bottom: 2%;
  width: 200px;
  margin-right: 2%;
}
</style>
