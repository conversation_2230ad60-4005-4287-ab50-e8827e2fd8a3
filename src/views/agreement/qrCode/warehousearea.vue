<template style="width: 155%">
  <div class="codeDataBody" style="">
    <div style="display: flex">
      <a-input v-model:value="referName" placeholder="请输入物料/产品" />
      <a-button shape="round" type="primary" @click="getList()">搜索</a-button>
    </div>
    <mw-table
      style="width: 155%; background: #fff"
      :columns="columns"
      :data-source="data"
      :loading="loading"
      :rowKey="(record) => record.id"
      hasPage
      @change="onTableChange"
      :pageConfig="paginationProps"
      :customRow="rowClick"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'type'">
          <div class="">
            {{
              record.type == FINISHED
                ? "成品仓"
                : record.createBy == RAWMATERIAL
                ? "物料仓"
                : record.createBy == WASTE_FINISHED
                ? "废品仓"
                : record.createBy == WASTE
                ? "废料仓"
                : "--"
            }}
          </div>
        </template>
      </template>
    </mw-table>
  </div>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
} from "vue";
import { page, exportRepository } from "@/api/basicData/repository.js";
import { getDicByType } from "@/utils/util.js";
import { usePagenation } from "@/common/setup";
import Search from "@/components/search/index.vue";
const referName = ref("");
import { useRoute, useRouter } from "vue-router";
import { warehouse, noWarehouse } from "@/api/basicData/repository.js";
const route = useRoute();
import { getToken } from "@/utils/auth.js";
import { Alert } from "ant-design-vue";
const token = getToken();
// config.headers.Authorization = `Bearer ${token}`;
const columns = [
  ,
  {
    title: "物料名称",
    dataIndex: "materialName",
  },
  {
    title: "物料材料",
    dataIndex: "specification",
  },
  {
    title: "物料编号",
    dataIndex: "materialNo",
  },
  {
    title: "仓库名称",
    dataIndex: "warehouseName",
  },
  {
    title: "仓库地址",
    dataIndex: "warehouseAddress",
  },
  {
    title: "仓库类型",
    dataIndex: "type",
  },
  {
    title: "仓库区域名称",
    dataIndex: "warehouseArea",
  },
];
const { proxy } = getCurrentInstance();
const data = ref([]),
  loading = ref(false),
  addVisible = ref(false),
  id = ref(""),
  warehouseTypes = ref([]),
  exportLoading = ref(false);
const searchData = ref({
  operationButtons: [],
  fields: {
    keyword: {
      type: "a-input-search",
      placeholder: "输入仓库名称",
      width: "240px",
      allowClear: true,
    },
    warehouseType: {
      name: "仓库类型",
      type: "a-select",
      options: [],
      placeholder: "仓库类型",
      value: "",
      width: "120px",
      allowClear: true,
    },
  },
});
const getList = async () => {
  loading.value = true;
  let searchParam = {};
  //搜索信息
  let warehouseData = [];
  const formData = {
    referId: route.query.itemId,
    type: "area",
    referName: referName.value,
  };
  if (route.query.iteme == 1) {
    warehouseData = await noWarehouse(formData, route.query.token);
    data.value = warehouseData.data;
    paginationProps.value.total = warehouseData.total;
    loading.value = false;
  } else {
    warehouseData = await warehouse(formData, route.query.token);
    data.value = warehouseData.data;
    paginationProps.value.total = warehouseData.total;
    loading.value = false;
  }
  // dataSource.value=warehouseData.data
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  // let result = await page(pageParam.value, searchParam);
  // data.value = result.data;
  // paginationProps.value.total = result.total;
  loading.value = false;
};
const getWarehouseTypeOption = async () => {
  let { dics, dicFilters } = await getDicByType("warehouse_type", "仓库");
  warehouseTypes.value = dics;
  searchData.value.fields.warehouseType.options = dicFilters;
};
onBeforeMount(async () => {
  await getList();
  // await getWarehouseTypeOption();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
const rowClick = (record) => {
  return {
    onClick: (event) => {
      id.value = record.id.toString();
      addVisible.value = true;
    },
  };
};
</script>

<style lang="less" scoped>
.codeDataBody {
  width: 155%;
  background: #ffffff;
  height: 100vh;
  padding: 5%;
}
body {
  background: #fff !important;
  width: 155%;
}
.ant-layout {
  width: 155% !important;
  background: #fff;
}
:deep(.ant-layout) {
  width: 155% !important;
  background: #fff !important;
}
:deep(.ant-table-cell) {
  vertical-align: top;
}

:deep(.ant-layout) {
  background: #f5f5f5;
  display: flex;
  justify-content: center;
}
.detailsTitle {
  display: flex;
  justify-content: space-around;
  height: 50px;
  align-items: center;
}
input {
  margin-left: 0;
  margin-bottom: 2%;
  width: 200px;
  margin-right: 2%;
}
</style>
