<template>
	<div class="mb-4 text-right">
		<search :searchData="searchData" @search="refresh">
			<mw-button @click="onOpen('add')">退库申请</mw-button>
		</search>
	</div>
	<mw-table :scroll="{ x: 'max-content' }" :columns="columns" :data-source="data" :loading="loading"
		:rowKey="(record) => record.id" hasPage @change="onTableChange" :pageConfig="paginationProps">
		<template #bodyCell="{ column, record }">
			<!-- 嘿嘿嘿 -->
			<template v-if="column.dataIndex == 'type'"> {{ getType(record.type) }} </template>
			<template v-if="column.dataIndex == 'createBy'">
				{{ record.createBy }}
			</template>

			<template v-if="column.dataIndex == 'status'">
				<dictionary :statusOptions="listStatus" :value="record.status" :statusExtend="record.statusExtend" />
			</template>
			<template v-if="column.dataIndex == 'remark'">
				<a-tooltip>
					<div class="w-20 truncate">{{ record.remark }}</div>
					<template #title>{{ record.remark }}</template>
				</a-tooltip>
			</template>

			<template v-if="column.dataIndex == 'operate'">
				<mw-button @click="onDet('det', record)">详情</mw-button>
			</template>
		</template>
	</mw-table>
	<materialRequisitionDrawer @finish="getList" v-model:visible="visibleAdd" v-model:type="visibleType"
		v-model:id="visibleId"></materialRequisitionDrawer>
</template>
<script setup>
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import Search from "@/components/search/index.vue";
import { columns, materialRequisitionStatus, listStatus } from "./colUnm.js";
import { usePagenation } from "@/common/setup";
import materialRequisitionDrawer from "./applyForWarehouseDrawer.vue";
import { listApplyForWarehouse } from "@/api/applyForWarehouse/index.js";
const { proxy } = getCurrentInstance();
const data = ref([{}]);
const loading = ref(false);
const visibleAdd = ref(false);
const visibleType = ref("");
const visibleId = ref("");

const searchData = ref({
	searchButtons: [],
	operationButtons: [],
	fields: {
		status: {
			name: "状态",
			type: "a-select",
			options: materialRequisitionStatus,
			placeholder: "状态",
			width: "120px",
			value: "",
			allowClear: true,
		},
		keyword: {
			type: "a-input-search",
			placeholder: "请输入物料名称/物料编码/物料规格",
			width: "280px",
			allowClear: true,
		},
	},
});

const getList = async () => {
	loading.value = true;
	//分页信息
	let searchParam = {};
	//搜索信息
	for (const key in searchData.value.fields) {
		searchParam[key] = searchData.value.fields[key].value;
	}
	let result = await listApplyForWarehouse(searchParam, {
		...pageParam.value,
	});
	// let result = await getContractReviewList(pageParam.value, param);
	data.value = result.data;
	paginationProps.value.total = result.total;
	loading.value = false;
};
const { paginationProps, onTableChange, refresh, pageParam } = usePagenation(getList);
const onOpen = (type, value) => {
	visibleId.value = void 0;
	visibleType.value = type;
	visibleAdd.value = true;
};
const onDet = (type, value) => {
	visibleId.value = value.id;
	visibleType.value = type;
	visibleAdd.value = true;
};
const receiveList = ref([
	{
		label: "经济物料领用",
		value: 1,
	},
	{
		label: "其他领用",
		value: 2,
	},
	{
		label: "劳保用品领用",
		value: 3,
	},
	{
		label: "售后物品领用",
		value: 4,
	},
]);
const getType = (val) => {
	if (val == 1) {
		return "售后物料退库";
	} else if (val == 2) {
		return "普通退货（退款）";
	} else if (val == 3) {
		return "普通退货（不退款)";
	} else {
		return "";
	}
};
onBeforeMount(async () => {
	await getList();
});
</script>
