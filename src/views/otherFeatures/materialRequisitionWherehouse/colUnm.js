export const columns = [
  {
    title: "申请单号",
    dataIndex: "applyNo",
  },
  {
    title: "申请时间",
    dataIndex: "applyTime",
  },
  {
    title: "申请人",
    dataIndex: "createBy",
  },
  {
    title: "领用类型",
    dataIndex: "type",
  },
  {
    title: "状态",
    dataIndex: "status",
  },
  {
    title: "物料种类",
    dataIndex: "materialType",
  },
  {
    title: "物料数量",
    dataIndex: "materialQuantity",
  },
  {
    title: "备注",
    dataIndex: "remark",
  },
  {
    title: "操作",
    dataIndex: "operate",
    fixed: "right",
  },
];
export const columnsDrawer = [{
  title: "物料",
  key: "materialId",
  width: '300px',
}, {
  title: "数量",
  key: "quantity",
}, {
  title: "经济物料",
  dataIndex: "isEconomicMaterial",
  key: "isEconomicMaterial",
  customRender: ({ record }) => {
    return record.isEconomicMaterial == 1 ? "是" : record.isEconomicMaterial == 0 ? "否" : '';
  },
}, {
  title: "实际库存",
  key: "currentStock",
}, {
  title: "可用库存",
  key: "availableStock",
}, {
  title: "操作",
  key: "operate",
  fixed: "right",
},]
export const columnsDrawerDetail = [{
  title: "物料",
  key: "materialName",
}, {
  title: "经济物料",
  dataIndex: "isEconomicMaterial",
  key: "isEconomicMaterial",
  customRender: ({ record }) => {
    return record.isEconomicMaterial == 1 ? "是" : record.isEconomicMaterial == 0 ? "否" : '';
  },
},
{
  title: "数量",
  key: "quantity",
},]

export const materialRequisitionStatus = [
  {
    label: '全部',
    value: ''
  }, {
    label: '待审批',
    value: 7
  }, {
    label: '审批通过',
    value: 8
  }, {
    label: '审批不通过',
    value: 9
  },]
export const listStatus = [
  {
    label: '全部',
    value: ''
  }, {
    label: '待审批',
    value: 7
  }, {
    label: '审批通过',
    value: 8
  }, {
    label: '审批不通过',
    value: 9
  }, {
    label: '已撤销',
    value: 10
  },]

