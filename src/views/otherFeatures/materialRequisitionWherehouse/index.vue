<template>
  <div class="mb-4 text-right">
    <search :searchData="searchData" @search="refresh">
      <mw-button @click="onOpen('add')">领料申请</mw-button>
    </search>
  </div>
  <mw-table
    :scroll="{ x: 'max-content' }"
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <!-- 嘿嘿嘿 -->
      <template v-if="column.dataIndex == 'type'">
        {{ getType(record.type) }}
      </template>
      <template v-if="column.dataIndex == 'createBy'">
        {{ record.createBy }}
      </template>

      <template v-if="column.dataIndex == 'status'">
        <dictionary
          :statusOptions="listStatus"
          :value="record.status"
          :statusExtend="record.statusExtend"
        />
      </template>
      <template v-if="column.dataIndex == 'remark'">
        <a-tooltip>
          <div class="w-20 truncate">{{ record.remark }}</div>
          <template #title>{{ record.remark }}</template>
        </a-tooltip>
      </template>

      <template v-if="column.dataIndex == 'operate'">
        <mw-button @click="onDet('det', record)">详情</mw-button>
      </template>
    </template>
  </mw-table>
  <materialRequisitionDrawer
    @finish="getList"
    v-model:visible="visibleAdd"
    v-model:type="visibleType"
    v-model:id="visibleId"
  ></materialRequisitionDrawer>
</template>
<script setup>
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import Search from "@/components/search/index.vue";
import { columns, materialRequisitionStatus, listStatus } from "./colUnm.js";
import { usePagenation } from "@/common/setup";
import materialRequisitionDrawer from "./materialRequisitionDrawer.vue";
import { listMaterialRequisition } from "@/api/materialRequisition/index.js";
const { proxy } = getCurrentInstance();
const data = ref([{}]);
const loading = ref(false);
const visibleAdd = ref(false);
const visibleType = ref("");
const visibleId = ref("");

const searchData = ref({
  searchButtons: [],
  operationButtons: [],
  fields: {
    status: {
      name: "状态",
      type: "a-select",
      options: materialRequisitionStatus,
      placeholder: "状态",
      width: "120px",
      value: "",
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: "请输入物料名称/物料编码/物料规格",
      width: "280px",
      allowClear: true,
    },
  },
});

const getList = async () => {
  loading.value = true;
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  searchParam.type = "2";
  let result = await listMaterialRequisition(searchParam, {
    ...pageParam.value,
  });
  // let result = await getContractReviewList(pageParam.value, param);
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
const onOpen = (type, value) => {
  visibleId.value = void 0;
  visibleType.value = type;
  visibleAdd.value = true;
};
const onDet = (type, value) => {
  visibleId.value = value.id;
  visibleType.value = type;
  visibleAdd.value = true;
};

const getType = (val) => {
  if (val == 1) {
    return "经济物料领用";
  } else if (val == 2) {
    return "其他领用";
  } else if (val == 3) {
    return "劳保用品领用";
  } else if (val == 4) {
    return "售后物品领用";
  } else if (val == 5) {
    return "设备易耗品";
  } else if (val == 6) {
    return "研发领用";
  } else if (val == 7) {
    return "PACK线边库领料";
  } else if (val == 8) {
    return "电芯线边库领料";
  } else {
    return "";
  }
};
onBeforeMount(async () => {
  await getList();
});
</script>
