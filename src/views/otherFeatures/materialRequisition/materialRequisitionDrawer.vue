<template>
  <mw-drawer
    :custom-title="title"
    :visible="visible"
    @close="onClose"
    closeText="取消"
    :spinning="spinning"
    :width="getViewportSize() > 800 ? '80%' : '100%'"
    destroyOnClose="true"
  >
    <template v-slot:header>
      <mw-button @click="submitForm" :loading="submitLoading" v-if="!props.id"
        >确定</mw-button
      >
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
    >
      <a-form-item label="领用类型" name="type" required>
        <a-select
          show-search
          style="width: 100%"
          placeholder="请选择领用类型"
          :options="typeOptions"
          v-model:value="formData.type"
          :fieldNames="{ label: 'dictLabel', value: 'dictSort' }"
          :disabled="props.type == 'det'"
          @change="changeType"
        >
        </a-select>
      </a-form-item>
      <a-form-item
        label=""
        name="materialReceiveDetails"
        v-if="props.type == 'add'"
      >
        <mw-button @click="addMaterial" class="mb-2"> 新增 </mw-button>

        <!-- hasPage -->
        <mw-table
          :scroll="{ x: 'max-content' }"
          :columns="columnsDrawer"
          :data-source="formData.materialReceiveDetails"
          :loading="loading"
          :rowKey="(record) => record.id"
        >
          <template #bodyCell="{ column, index, record }">
            <template v-if="column.key == 'materialId'">
              <a-select
                show-search
                placeholder="请选择领用物料"
                :options="materialData"
                v-model:value="record.materialId"
                optionFilterProp="materialName"
                :field-names="{
                  label: 'materialName',
                  value: 'id',
                }"
                @change="changeMaterialData($event, record)"
                @search="searchMaterial"
              >
              </a-select>
            </template>
            <template v-if="column.key == 'isEconomicMaterial'">
              <span v-if="record.isEconomicMaterial == 1">是</span>
              <span v-if="record.isEconomicMaterial == 0">否</span>
            </template>

            <template v-if="column.key == 'quantity'">
              <div class="flex">
                <a-input-number
                  placeholder="数量"
                  @change="onChange($event, record)"
                  v-model:value="record.quantity"
                  :stringMode="true"
                  :min="0"
                ></a-input-number>
                <div class="ml-2 mt-1">
                  {{ record.unitName }}
                </div>
              </div>
            </template>
            <template v-if="column.key == 'currentStock'">
              {{ record.currentStock }}
            </template>
            <template v-if="column.key == 'availableStock'">
              {{ record.availableStock }}</template
            >
            <template v-if="column.key == 'operate'">
              <mw-button danger @click="onDelete(index)"> 删除 </mw-button>
            </template>
          </template></mw-table
        >
      </a-form-item>

      <a-form-item v-if="props.type == 'det'">
        <mw-table
          :scroll="{ x: 'max-content' }"
          :columns="columnsDrawerDetail"
          :data-source="formData.materialReceiveDetails"
          :loading="loading"
          :rowKey="(record) => record.id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key == 'materialName'">
              {{ record.materialName }}/{{ record.specification }}/{{
                record.materialNo
              }}</template
            >
            <template v-if="column.key == 'quantity'">
              {{ record.quantity }}{{ record.unitName }}</template
            >
          </template>
        </mw-table>
      </a-form-item>
      <a-form-item label="备注" name="remark" required>
        <a-textarea
          :disabled="props.type == 'det'"
          :maxlength="300"
          show-count
          v-model:value="formData.remark"
        ></a-textarea>
      </a-form-item>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  computed,
  defineEmits,
  reactive,
} from "vue";
const { proxy } = getCurrentInstance();
import {
  columnsDrawer,
  materialRequisitionStatus,
  columnsDrawerDetail,
} from "./colUnm.js";
import { roundNumFun } from "@/common/validate.js";
import _cloneDeep from "lodash/cloneDeep";
import { mobile, email, password, money, getViewportSize } from "@/common/reg";
import {
  saveMaterialRequisition,
  dictionary,
  detailMaterialReceive,
} from "@/api/materialRequisition/index.js";
import { listMaterial } from "@/api/applyForWarehouse/index.js";
import { debounce } from "lodash";
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
  type: String,
});
const emit = defineEmits(["update:visible", "finish"]);
const title = computed(() => {
  return props.id ? "领料明细" : "领料申请";
});
const typeOptions = ref([]);
const loading = ref(false);
const formRef = ref(null);
const submitLoading = ref(false);
const spinning = ref(false);
const materialData = ref([]);
const formData = reactive({
  materialReceiveDetails: [],
  remark: void 0,
  type: void 0,
});
const rules = ref({
  // roleName: [
  //   {
  //     required: true,
  //     message: "请输入角色名称",
  //     trigger: "blur",
  //   },
  // ],
  // dataScope: [
  //   {
  //     required: true,
  //     message: "请选择上级角色",
  //     trigger: "change",
  //   },
  // ],
});
// 数量
const onChange = async (e, val) => {
  val.quantity = await roundNumFun(e, 4);
};
// 关闭
const onClose = async () => {
  props.id = void 0;
  formRef.value.resetFields();
  formData.materialReceiveDetails = [];
  formData.remark = void 0;
  formData.type = void 0;
  emit("update:visible", false);
};

// 新增
const addMaterial = () => {
  formData.materialReceiveDetails.push({});
};

// 确定
const submitForm = () => {
  formRef.value
    .validate()
    .then(async () => {
      submitLoading.value = true;
      let res = await saveMaterialRequisition(formData);
      if (res.code == 200) {
        onClose();
        emit("finish");
        proxy.$message.success("添加成功");
      }
      submitLoading.value = false;
    })
    .catch((error) => {
      submitLoading.value = false;
    });
};
// 字典
const dictionaryData = async () => {
  let res = await dictionary("store_material_receive_type");
  typeOptions.value = res.data;
};
const listMaterialData = async (e, keyword) => {
  let param = {
    ignoreCancel: true,
    ignoreStop: true,
    isEconomicMaterial: e == 1 ? 1 : e == 2 ? 0 : void 0, // 根据index判断不合适 后续改成根据值判断
    classificationName:
      e == 3
        ? typeOptions.value.find((item) => item.dictSort == e).dictLabel
        : undefined,
    size: 99999999,
    keyword,
    isLineEdgeLibrary: [4, 6].includes(formData.type) ? undefined : 0,
  };
  let res = await listMaterial(param);

  materialData.value = res.data.map((item) => {
    item.materialName =
      item.materialNo + "/" + item.materialName + "/" + item.specification;
    return item;
  });
};
const changeMaterialData = (e, val) => {
  materialData.value.forEach((item, index) => {
    if (item.id == e) {
      val.availableStock = item.availableStock;
      val.currentStock = item.currentStock;
      val.unitName = item.unitName;
      val.isEconomicMaterial = item.isEconomicMaterial;
    }
  });
};
// 详情
const detailMaterialReceiveData = async (val) => {
  let res = await detailMaterialReceive(val);
  for (let key in formData) {
    formData[key] = res.data[key];
  }
};
// 删除
const onDelete = (index) => {
  formData.materialReceiveDetails.forEach((item, i) => {
    if (i == index) {
      formData.materialReceiveDetails.splice(i, 1);
    }
  });
};
const viewportWidth = ref();
const changeType = async (e) => {
  materialData.value = [];
  formData.materialReceiveDetails = [];
  await listMaterialData(e, "");
};
const debouncedSearch = debounce(async (e) => {
  console.log("[ e ] >", e);
  await listMaterialData(formData.type, e);
  //
}, 300);
const searchMaterial = async (e) => {
  debouncedSearch(e);
};
watch(
  () => props.visible,
  async (val) => {
    viewportWidth.value = getViewportSize();

    if (val) {
      if (props.id && props.type == "det") {
        await detailMaterialReceiveData(props.id);
      }
      if (!props.id) {
        // await listMaterialData();
      }
      await dictionaryData();
    }
  }
);
</script>
<style lang="less" scoped>
:deep(.mw-table .ant-table-tbody > tr > td) {
  padding: 8px 12px;
}
</style>
