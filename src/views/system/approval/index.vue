<template>
  <div>
    <div class="mb-4">全部审批流程[审批流数量总计19]</div>
    <div>
      <div class="flex flex-wrap gap-2 ul">
        <template v-for="(item, index) in data" :key="index">
          <div
            class="border-border border-2 li flex justify-between items-center px-2 py-4 rounded"
          >
            <div>{{ item.text }}</div>
            <div class="text-primary">
              <i class="iconfont icon-xianxing-14 align-middle"></i>
              <span class="align-middle ml-1">已关联</span>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const data = ref([
  {
    text: "新建生产计划审批流",
    status: 0,
  },
  {
    text: "取消生产计划审批流",
    status: 0,
  },
  {
    text: "生产调拨审批流",
    status: 0,
  },
  {
    text: "物料到货审批流",
    status: 0,
  },
  {
    text: "取消采购审批流",
    status: 0,
  },
  {
    text: "采购完成审批流",
    status: 0,
  },
  {
    text: "采购结算审批流",
    status: 0,
  },
  {
    text: "采购申请审批流",
    status: 0,
  },
  {
    text: "预付款结算审批流",
    status: 0,
  },
  {
    text: "物料质检审批流",
    status: 0,
  },
  {
    text: "成品质检审批流",
    status: 0,
  },
  {
    text: "新增产品审批流",
    status: 0,
  },
  {
    text: "取消产品审批流",
    status: 0,
  },
  {
    text: "新增BOM审批流",
    status: 0,
  },
  {
    text: "取消BOM审批流",
    status: 0,
  },
  {
    text: "新增物料审批流",
    status: 0,
  },
  {
    text: "取消物料审批流",
    status: 0,
  },
  {
    text: "新增供应商审批流",
    status: 0,
  },
  {
    text: "编辑供应商审批流",
    status: 0,
  },
]);
</script>

<style lang="less" scoped>
.ul {
  row-gap: 20px;
  column-gap: 40px;
  .li {
    width: calc(25% - 30px);
  }
}
</style>
