<template>
  <div v-if="store.$state.cacheObject">
    <search :searchData="searchData" @search="refresh">
      <mw-button @click="onOpen" :font="'iconfont icon-xianxing-121'"
        >新增</mw-button
      >
    </search>
    <mw-table
      :columns="columns"
      :data-source="data"
      :loading="loading"
      :rowKey="(record) => record.roleId"
      hasPage
      @change="onTableChange"
      :pageConfig="paginationProps"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key == 'status'">
          <dictionary
            :statusOptions="departmentStatus"
            :value="record.status"
            isBackgroundColor
          />
        </template>
        <template v-if="column.key == 'button'">
          <mw-button @click="rowClick(record)" v-if="record.roleSort != 1"
            >编辑</mw-button
          >
        </template>
      </template>
    </mw-table>
  </div>
  <empty name="Role" v-else />
  <create-drawer
    v-model:visible="addVisible"
    type="material"
    @finish="getList"
    :id="id"
  />
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
} from "vue";
import { list } from "@/api/system/role.js";
import createDrawer from "./createDrawer.vue";
import { departmentStatus } from "@/common/constant.js";
import Search from "@/components/search/index.vue";
import { usePagenation } from "@/common/setup";
import { useUserStore } from "@/stores/user.js";
import { env } from "dingtalk-jsapi";

const columns = ref([
  {
    title: "角色名称",
    dataIndex: "roleName",
    width: "420px",
  },
  {
    title: "状态",
    key: "status",
    align: "left",
    width: "320px",
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    width: "160px",
  },
  {
    title: "操作",
    dataIndex: "button",
    key: "button",
    width: "100px",
    fixed: "right",
  },
]);
const data = ref([]),
  addVisible = ref(false),
  loading = ref(false),
  id = ref(""),
  store = useUserStore();
const searchData = ref({
  fields: {
    roleName: {
      type: "a-input-search",
      placeholder: "输入角色名称",
      width: "240px",
      allowClear: true,
    },
  },
});
// 使用计算属性来根据条件筛选需要展示的列
// const visibleColumns = computed(() => {
//   if (env.platform == "notInDingTalk") {
//     return columns.value; // 如果满足条件，返回所有列
//   } else {
//     return columns.value.filter((column) => column.dataIndex !== "button");
//     // 如果不满足条件，返回除了列1和列2以外的其他列
//   }
// });
const getList = async () => {
  loading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let result = await list({ ...pageParam.value, ...searchParam });
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
const onOpen = (val) => {
  id.value = "";
  addVisible.value = true;
};
const rowClick = (record) => {
  id.value = record.roleId.toString();
  addVisible.value = true;
};
</script>
