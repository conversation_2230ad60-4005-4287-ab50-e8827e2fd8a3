<template>
  <mw-drawer
    :custom-title="title"
    :visible="visible"
    @close="onClose"
    closeText="取消"
    :spinning="spinning"
  >
    <template v-slot:header>
      <mw-button @click="submitForm" :loading="submitLoading">确定</mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :label-col="{ span: 4 }"
      :colon="false"
    >
      <a-form-item label="角色名称" name="roleName">
        <a-input
          v-model:value="formData.roleName"
          placeholder="请输入角色名称"
          :maxLength="12"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item label="数据权限" name="dataScope">
        <a-select
          v-model:value="formData.dataScope"
          :options="roleUserPermissions"
          placeholder="请选择数据权限"
        ></a-select>
      </a-form-item>
    </a-form>
    <div>
      <!-- {{ formData.menuIds }} -->
      <div>菜单权限</div>
      <a-tree
        v-model:checkedKeys="formData.menuIds"
        checkable
        :tree-data="treeData"
        :fieldNames="{ title: 'label', key: 'id' }"
        @check="handleCheck"
      >
        <template #title="{ label }"> {{ label }} </template>
      </a-tree>
    </div>
  </mw-drawer>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  computed,
  defineEmits,
} from "vue";
import { add, getRole, updateRole, list } from "@/api/system/role.js";
import { treeselect, roleMenuTreeselect } from "@/api/system/menu.js";
const { proxy } = getCurrentInstance();
import { getDicByType } from "@/utils/util.js";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
  type: String,
});
const emit = defineEmits(["update:visible", "finish"]);
const title = computed(() => {
  return props.id ? "修改角色" : "新增角色";
});
const wholeArr = ref();
const formRef = ref(),
  submitLoading = ref(false),
  spinning = ref(false),
  treeData = ref([]),
  detail = ref({}),
  formData = ref({
    dataScope: undefined,
    roleName: undefined,
    roleSort: undefined,
    menuIds: [],
  }),
  roleUserPermissions = ref([]),
  rules = ref({
    roleName: [
      {
        required: true,
        message: "请输入角色名称",
        trigger: "blur",
      },
    ],
    dataScope: [
      {
        required: true,
        message: "请选择上级角色",
        trigger: "change",
      },
    ],
  });
const onClose = () => {
  formRef.value.resetFields();
  formData.value.menuIds = [];
  emit("update:visible", false);
};
const submitForm = () => {
  formRef.value
    .validate()
    .then(async () => {
      formData.value.childNodeList = formData.value.menuIds.join(",");
      formData.value.menuIds = wholeArr.value
        ? wholeArr.value
        : formData.value.menuIds;
      submitLoading.value = true;
      let newDetail;
      if (props.id) {
        newDetail = await updateRole({ ...detail.value, ...formData.value });
        if (newDetail.code == 200) {
          proxy.$message.success("修改成功");
          onClose();
        }
      } else {
        newDetail = await add({
          ...formData.value,
          roleKey: formData.value.roleName,
          roleSort: 999,
        });
        if (newDetail.code == 200) {
          proxy.$message.success("添加成功");
          onClose();
        }
      }
      //保存数据权限
      // await dataScope(
      //   { ...newDetail },
      //   { dataScope: formData.value.dataScope }
      // );
      submitLoading.value = false;
      emit("finish");
    })
    .catch((error) => {
      submitLoading.value = false;
    });
};
const getTreeselect = async () => {
  let result = await treeselect();
  treeData.value = result.data;
};
const getRoleMenuTreeselect = async (roleId) => {
  return roleMenuTreeselect(roleId).then((response) => {
    treeData.value = response.menus;
    formData.value.menuIds = response?.childNodeList
      ? response.childNodeList.split(",").map(Number)
      : [];

    const newArr = [];
    formData.value.menuIds.forEach((item) => {
      // item是已有的权限id，将已有的权限id与全部的id进行匹配，找到它的父id并删除
      checked(item, treeData.value, newArr);
    });
    formData.value.menuIds = newArr; // 渲染

    // bbb.selectedKeys = newArr;

    return response;
  });
};

// 检测id是否是父级id，并将子级id过滤，运用递归
const checked = (id, data, newArr) => {
  console.log(id, data, newArr);
  data.forEach((item) => {
    // 在数组中查找属于这个id的item
    if (item.id == id) {
      // 如果item没有子级或者暂时为空，说明它不是父节点或者暂时不是
      if (item.children == undefined || item.children.length < 1) {
        newArr.push(item.id);
      }
    } else {
      // 如果有子级，继续往下查找，检查这个id是否是子节点id
      if (item.children != undefined && item.children.length != 0) {
        checked(id, item.children, newArr);
      }
    }
  });
};

const getRoleUserPermissions = async () => {
  let { dics } = await getDicByType("role_user_permissions");
  roleUserPermissions.value = dics;
};
const handleCheck = (checkedKeys, info) => {
  wholeArr.value = [...checkedKeys, ...info.halfCheckedKeys];
};

watch(
  () => props.visible,
  async (val) => {
    if (val) {
      spinning.value = true;
      await getRoleUserPermissions();
      if (props.id) {
        spinning.value = true;
        let result = await getRole(props.id);
        detail.value = result.data;
        for (const key in formData.value) {
          formData.value[key] = result.data[key];
        }
        await getRoleMenuTreeselect(props.id);
        spinning.value = false;
      } else {
        await getTreeselect();
        spinning.value = false;
      }
    }
  }
);
</script>
