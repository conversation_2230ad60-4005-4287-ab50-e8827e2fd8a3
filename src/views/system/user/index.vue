<template>
  <a-row :gutter="12">
    <a-col :span="4">
      <a-tree
        :tree-data="deptTreeData"
        :fieldNames="{ title: 'label', key: 'id' }"
        v-if="deptTreeData?.length"
        defaultExpandAll
        @select="handleSelectDept"
      >
        <template #title="{ label }">
          {{ label }}
        </template>
      </a-tree>
    </a-col>
    <a-col :span="20">
      <search :searchData="searchData" @search="refresh">
        <mw-button
          @click="onOpen"
          v-if="env.platform == 'notInDingTalk'"
          :font="'iconfont icon-xianxing-121'"
          >新增</mw-button
        >
      </search>
      <mw-table
        :columns="visibleColumns"
        :data-source="data"
        :loading="loading"
        :rowKey="(record) => record.userId"
        hasPage
        @change="onTableChange"
        :pageConfig="paginationProps"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'status'">
            <dictionary
              :statusOptions="departmentStatus"
              :value="record.status"
              isBackgroundColor
            />
          </template>
          <template v-if="column.dataIndex == 'roleName'">
            <div v-for="(item, index) in record.roles" :key="index">
              {{ item.roleName }}
            </div>
          </template>

          <template v-if="column.key == 'button'">
            <mw-button @click="rowClick(record)">编辑</mw-button>
          </template>
        </template>
      </mw-table>
    </a-col>
  </a-row>
  <create-drawer
    v-model:visible="addVisible"
    type="material"
    @finish="getList"
    :id="id"
    :treeDataid="treeDataid"
  />
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  reactive,
  toRaw,
  getCurrentInstance,
  onBeforeUnmount,
  computed,
} from "vue";
import { list, deptTreeSelect } from "@/api/system/user.js";
import createDrawer from "./createDrawer.vue";
import { departmentStatus } from "@/common/constant.js";
import { usePagenation } from "@/common/setup";
import Search from "@/components/search/index.vue";
import { env } from "dingtalk-jsapi";
const columns = ref([
  {
    title: "员工姓名",
    dataIndex: "nickName",
  },
  {
    title: "手机号码",
    dataIndex: "phonenumber",
  },
  {
    title: "岗位职务",
    dataIndex: "postName",
  },
  {
    title: "员工状态",
    key: "status",
    dataIndex: "status",
    align: "center",
  },
  {
    title: "角色",
    dataIndex: "roleName",
    align: "center",
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    width: "180px",
  },
  {
    title: "操作",
    dataIndex: "button",
    key: "button",
    width: "100px",
    fixed: "right",
  },
]);
// 使用计算属性来根据条件筛选需要展示的列
const visibleColumns = computed(() => {
  if (env.platform == "notInDingTalk") {
    return columns.value; // 如果满足条件，返回所有列
  } else {
    return columns.value.filter((column) => column.dataIndex !== "button");
    // 如果不满足条件，返回除了列1和列2以外的其他列
  }
});
const data = ref([]),
  addVisible = ref(false),
  loading = ref(false),
  id = ref(""),
  deptTreeData = ref([]);
let treeDataid = ref();
const searchData = ref({
  fields: {
    userName: {
      type: "a-input-search",
      placeholder: "输入员工名称",
      width: "240px",
      allowClear: true,
    },
  },
});
sessionStorage.removeItem("treeDataid");
const handleSelectDept = async (value) => {
  sessionStorage.removeItem("treeDataid");
  await getList(value[0], 1);
  treeDataid = value[0];
  sessionStorage.setItem("treeDataid", treeDataid);
};
const getList = async (deptId, current) => {
  if (current && paginationProps.value.current !== 1) {
    paginationProps.value.current = current;
  }
  loading.value = true;
  let searchParam = {};
  console.log("[ deptId ] >", deptId);
  if (
    sessionStorage.getItem("treeDataid") &&
    sessionStorage.getItem("treeDataid") !== "undefined"
  ) {
    searchParam.deptId = sessionStorage.getItem("treeDataid");
  } else if (deptId && deptId !== "undefined") {
    searchParam.deptId = deptId;
  }

  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }

  let result = await list({ ...pageParam.value, ...searchParam });
  data.value = result.data;

  paginationProps.value.total = result.total;
  loading.value = false;
};
const getDeptTree = async () => {
  let result = await deptTreeSelect();
  deptTreeData.value = result.data;
};
onBeforeMount(async () => {
  getDeptTree();
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } = usePagenation(
  getList,
  10
);

const rowClick = (record) => {
  id.value = record.userId.toString();
  addVisible.value = true;
};

const onOpen = (val) => {
  id.value = "";
  addVisible.value = true;
};
</script>

<style lang="less" scoped>
:deep(.ant-tree-switcher) {
  width: 14px !important;
}
:deep(.ant-tree-indent-unit) {
  width: 14px !important;
}
</style>
