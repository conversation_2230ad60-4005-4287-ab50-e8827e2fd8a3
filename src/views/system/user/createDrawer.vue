<template>
  <mw-drawer
    :custom-title="title"
    :visible="visible"
    @close="onClose"
    closeText="取消"
    :spinning="spinning"
    destroyOnClose="true"
  >
    <template v-slot:header>
      <mw-button @click="submitForm" :loading="submitLoading">确定</mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      :label-col="{ span: 4 }"
    >
      <a-form-item label="员工姓名" name="nickName">
        <a-input
          v-model:value="formData.nickName"
          placeholder="请输入员工姓名"
          :maxLength="12"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item label="手机号码">
        <a-input
          v-model:value="formData.phonenumber"
          placeholder="请输入手机号码"
          :maxLength="32"
          allow-clear
        >
        </a-input>
      </a-form-item>
      <a-form-item label="岗位职务">
        <a-input
          v-model:value="formData.postName"
          placeholder="请输入岗位职务"
          :maxLength="20"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item label="所属部门" name="deptId">
        <a-tree-select
          v-model:value="formData.deptId"
          :tree-data="treeData"
          :field-names="{
            value: 'id',
          }"
          placeholder="请选择所属部门"
          dropdown-matc-select-width
          allow-clear
          tree-default-expand-all
          tree-node-filter-prop="label"
          show-search
        />
      </a-form-item>
      <a-form-item label="角色" name="roleIds">
        <a-select
          v-model:value="formData.roleIds"
          mode="multiple"
          :options="roleOptions"
          placeholder="请选择角色"
          optionFilterProp="roleName"
          :field-names="{ label: 'roleName', value: 'roleId' }"
        ></a-select>
      </a-form-item>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  computed,
  defineEmits,
  nextTick,
} from "vue";
import {
  addUser,
  getUser,
  updateUser,
  deptTreeSelect,
} from "@/api/system/user.js";
const { proxy } = getCurrentInstance();
import { handleTree } from "@/utils/util.js";
import { getDicByType } from "@/utils/util.js";
import { checkPhone } from "@/common/validate";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
  type: String,
  treeDataid: String,
});
const emit = defineEmits(["update:visible", "finish"]);
const title = computed(() => {
  return props.id ? "修改员工" : "新增员工";
});
const formRef = ref(),
  submitLoading = ref(false),
  spinning = ref(false),
  treeData = ref([]),
  detail = ref({}),
  formData = ref({
    roleIds: undefined,
    nickName: undefined,
    phonenumber: undefined,
    postName: undefined,
    deptId: undefined,
  }),
  roleOptions = ref([]),
  rules = ref({
    nickName: [
      {
        required: true,
        message: "请输入员工姓名",
        trigger: "blur",
      },
    ],
    // phonenumber: [
    //   {
    //     required: true,
    //     message: "请输入手机号码",
    //     trigger: "blur",
    //   },
    //   {
    //     validator: checkPhone,
    //     trigger: "blur",
    //   },
    // ],
    // postName: [
    //   {
    //     required: true,
    //     message: "请输入岗位职务",
    //     trigger: "blur",
    //   },
    // ],
    deptId: [
      {
        required: true,
        message: "请选择所属部门",
        trigger: "change",
      },
    ],
    roleIds: [
      {
        required: true,
        message: "请选择角色",
        type: "array",
        trigger: "change",
      },
    ],
  });
const onClose = () => {
  formRef.value.resetFields();
  formData.value.roleIds = [];
  formData.value.phonenumber = undefined;
  emit("update:visible", false);
};
const submitForm = () => {
  formRef.value
    .validate()
    .then(async () => {
      submitLoading.value = true;
      if (props.id) {
        let res = await updateUser({
          ...detail.value,
          ...formData.value,
        });
        if (res.code == 200) {
          proxy.$message.success("修改成功");
          onClose();
        }
      } else {
        let res = await addUser({
          ...formData.value,
          password: "123456",
          userName: formData.value.nickName,
        });
        if (res.code == 200) {
          proxy.$message.success("添加成功");
          onClose();
        }
      }
      submitLoading.value = false;
      emit("finish", props.treeDataid);
    })
    .catch((error) => {
      submitLoading.value = false;
    });
};
const getTreeselect = async () => {
  let result = await deptTreeSelect();
  treeData.value = result.data;
};
const getRoleOptions = async () => {
  let { roles } = await getUser();
  roleOptions.value = roles;
};
const getDetail = async (userId) => {
  spinning.value = true;
  let { roles, data, roleIds } = await getUser(userId);
  roleOptions.value = roles;
  detail.value = data;
  for (const key in formData.value) {
    formData.value[key] = data[key];
  }
  formData.value.roleIds = roleIds;
  spinning.value = false;
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      spinning.value = true;
      await getTreeselect();
      if (props.id) {
        await getDetail(props.id);
      } else {
        await getRoleOptions();
      }
      spinning.value = false;
    }
  }
);
</script>
