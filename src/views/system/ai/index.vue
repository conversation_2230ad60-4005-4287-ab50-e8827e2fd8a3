<!-- views/IframePage.vue -->
<template>
  <div class="iframe-container">
    <iframe
      src="http://localhost/chat/share?shared_id=a1b0b05a232411f0b34a926f45193253&from=agent&auth=ZhYjE4M2Y4MjMwYzExZjBiYzFhOTI2Zj"
      style="width: 100%; height: 100%; min-height: 600px"
      frameborder="0"
      @load="handleIframeLoad"
    >
    </iframe>
    <div v-if="loading" class="loading">Loading...</div>
  </div>
</template>

<script>
export default {
  props: {
    url: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: true,
    };
  },
  methods: {
    handleIframeLoad() {
      this.loading = false;
    },
  },
};
</script>

<style scoped>
.iframe-container {
  position: relative;
  width: 100%;
  height: 100vh; /* 根据实际布局调整 */
}

.responsive-iframe {
  width: 100%;
  height: 100%;
  min-height: 600px;
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>