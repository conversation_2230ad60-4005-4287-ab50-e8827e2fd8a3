<template>
  <search :searchData="searchData" @search="getList">
    <mw-button
      @click="onOpen"
      v-if="env.platform == 'notInDingTalk'"
      :font="'iconfont icon-xianxing-121'"
      >新增</mw-button
    >
  </search>
  <mw-table
    v-if="data.length > 0"
    :columns="visibleColumns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.deptId"
    :rowExpandable="(record) => record.children.length"
    defaultExpandAllRows
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'status'">
        <dictionary
          :statusOptions="departmentStatus"
          :value="record.status"
          isBackgroundColor
        />
      </template>
      <template v-if="column.key == 'button'">
        <mw-button @click="rowClick(record)">编辑</mw-button>
      </template>
    </template>
  </mw-table>
  <create-drawer
    v-model:visible="addVisible"
    type="material"
    :hasParent="hasParent"
    @finish="getList"
    :id="id"
  />
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
} from "vue";
import { list } from "@/api/system/dept.js";
import createDrawer from "./createDrawer.vue";
import { handleTree } from "@/utils/util.js";
import { departmentStatus } from "@/common/constant.js";
import Search from "@/components/search/index.vue";
import { env } from "dingtalk-jsapi";
const columns = ref([
  {
    title: "部门名称",
    dataIndex: "deptName",
    width: "420px",
  },
  {
    title: "状态",
    key: "status",
    width: "320px",
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    width: "160px",
  },
  {
    title: "操作",
    dataIndex: "button",
    key: "button",
    width: "100px",
    fixed: "right",
  },
]);
// 使用计算属性来根据条件筛选需要展示的列
const visibleColumns = computed(() => {
  // if (env.platform == "notInDingTalk") {
  //   return columns.value; // 如果满足条件，返回所有列
  // } else {
  //   return columns.value.filter((column) => column.dataIndex !== "button");
  //   // 如果不满足条件，返回除了列1和列2以外的其他列
  // }
  return columns.value; // 如果满足条件，返回所有列
});
const data = ref([]),
  addVisible = ref(false),
  loading = ref(false),
  hasParent = ref(false),
  id = ref("");
const searchData = ref({
  fields: {
    deptName: {
      type: "a-input-search",
      placeholder: "输入部门名称",
      width: "240px",
      allowClear: true,
    },
  },
});
const getList = async () => {
  loading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let result = await list(searchParam);
  data.value = handleTree(result.data, "deptId");
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const onOpen = () => {
  id.value = "";
  addVisible.value = true;
};
const rowClick = (record) => {
  id.value = record.deptId.toString();
  addVisible.value = true;
};
</script>
