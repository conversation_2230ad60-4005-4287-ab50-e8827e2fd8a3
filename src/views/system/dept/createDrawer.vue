<template>
  <mw-drawer
    :custom-title="title"
    :visible="visible"
    @close="onClose"
    closeText="取消"
    :spinning="spinning"
  >
    <template v-slot:header>
      <mw-button @click="submitForm" :loading="submitLoading">确定</mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :label-col="{ span: 4 }"
      :colon="false"
    >
      <a-form-item label="上级部门" name="parentId" v-if="!id">
        <a-tree-select
          v-model:value="formData.parentId"
          :tree-data="parentIdTreeData"
          :field-names="parentIdReplaceFields"
          placeholder="请选择上级部门"
          dropdown-matc-select-width
          allow-clear
          tree-default-expand-all
          tree-node-filter-prop="deptName"
          show-search
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
        />
      </a-form-item>
      <a-form-item label="部门名称" name="deptName">
        <a-input
          v-model:value="formData.deptName"
          placeholder="请输入部门名称"
          :maxLength="12"
          allow-clear
        ></a-input>
      </a-form-item>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  computed,
  defineEmits,
} from "vue";
import { add, getInfo, update, list } from "@/api/system/dept.js";
const { proxy } = getCurrentInstance();
import { handleTree } from "@/utils/util.js";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
  type: String,
});
const emit = defineEmits(["update:visible", "finish"]);
const title = computed(() => {
  return props.id ? "修改部门" : "新增部门";
});
const formRef = ref(),
  submitLoading = ref(false),
  spinning = ref(false),
  formData = ref({
    parentId: undefined,
    deptName: undefined,
    orderNum: undefined,
  }),
  parentIdTreeData = ref([]),
  parentIdReplaceFields = ref({
    children: "children",
    label: "deptName",
    value: "deptId",
  }),
  rules = ref({
    parentId: [
      {
        required: true,
        message: "请选择上级部门",
        trigger: "change",
      },
    ],
    deptName: [
      {
        required: true,
        message: "请输入部门名称",
        trigger: "blur",
      },
    ],
  });
const onClose = () => {
  formRef.value.resetFields();
  formData.value.parentId = undefined;
  emit("update:visible", false);
};
const submitForm = () => {
  formRef.value
    .validate()
    .then(async () => {
      submitLoading.value = true;
      let res;
      if (props.id) {
        res = await update({ ...formData.value, deptId: props.id });
        if (res.code == 200) {
          proxy.$message.success("修改成功");
          onClose();
        }
      } else {
        res = await add({ ...formData.value, orderNum: 999 });
        if (res.code == 200) {
          proxy.$message.success("添加成功");
          onClose();
        }
      }
      submitLoading.value = false;
      emit("finish");
    })
    .catch((error) => {
      submitLoading.value = false;
    });
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      spinning.value = true;
      let result = await list();
      parentIdTreeData.value = handleTree(result.data, "deptId");
      spinning.value = false;
      if (props.id) {
        spinning.value = true;
        let result = await getInfo(props.id);
        for (const key in formData.value) {
          formData.value[key] = result.data[key];
        }
        spinning.value = false;
      }
    }
  }
);
</script>
