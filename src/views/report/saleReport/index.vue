<template>
  <div>
    <div class="">
      <a-select
        v-model:value="selectRoute"
        show-search
        placeholder="站内内容搜索"
        style="width: 32.5%"
        :options="menuOptions"
        :filterOption="filterOption"
        :loading="searchLoading"
        @select="handleSelect"
        @search="onSearch"
        size="large"
      ></a-select>
    </div>
    <div class="report-content">
      <div class="flex items-center justify-between gap-4 flex-wrap">
        <template v-for="item in reports" :key="item.key">
          <div
            class="rounded-lg btn-box text-white"
            @click="handleReportSelect(item)"
          >
            {{ item.name }}
          </div>
        </template>
      </div>
      <div class="iframe" @click="handleIframeClick">
        <iframe
          :src="currentReport.path"
          style="width: 100%; height: 1080px; pointer-events: none"
          frameborder="0"
          @load="handleIframeLoad"
        >
        </iframe>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { getRouters } from "@/api/menu";
import { useRouter } from "vue-router";

const router = useRouter();
const keyword = ref();
const menuOptions = ref([]);
const selectRoute = ref();
const searchLoading = ref(false);
const reports = [
  {
    name: "合同报表（日）",
    key: "day_cantract_report",
    path: "https://bi.ssnj.com/index.html#/aj/DZNILiyE",
  },
  {
    name: "合同报表（月）",
    key: "month_cantract_report",
    path: "https://bi.ssnj.com/index.html#/aj/HsIvvFCr",
  },
  {
    name: "合同报表（年）",
    key: "year_cantract_report",
    path: "https://bi.ssnj.com/index.html#/aj/iuKmIoLM",
  },
  {
    name: "销售回款报表（日）",
    key: "day_receipt_report",
    path: "https://bi.ssnj.com/index.html#/aj/kQmibONJ",
  },
  {
    name: "销售回款报表（月）",
    key: "month_receipt_report",
    path: "https://bi.ssnj.com/index.html#/aj/1EP1NtyQ",
  },

  {
    name: "销售回款报表（年）",
    key: "year_receipt_report",
    path: "https://bi.ssnj.com/index.html#/aj/J6rPOANn",
  },
  {
    name: "采购报表（日）",
    key: "day_purchase_report",
    path: "https://bi.ssnj.com/index.html#/aj/v1Oz4g7o",
  },
  {
    name: "采购报表（月）",
    key: "month_purchase_report",
    path: "https://bi.ssnj.com/index.html#/aj/ikA9dRIH",
  },

  {
    name: "采购报表（年）",
    key: "year_purchase_report",
    path: "https://bi.ssnj.com/index.html#/aj/ucHLzCU4",
  },
  {
    name: "采购付款（日）",
    key: "day_payment_report",
    path: "https://bi.ssnj.com/index.html#/aj/GXVxRzDc",
  },
  {
    name: "采购付款（月）",
    key: "month_payment_report",
    path: "https://bi.ssnj.com/index.html#/aj/wyIreyuh",
  },

  {
    name: "采购付款（年）",
    key: "year_payment_report",
    path: "https://bi.ssnj.com/index.html#/aj/HmmpYoCj",
  },
];
const currentReport = ref(reports[0]);
const handleReportSelect = (item) => {
  currentReport.value = item;
};
const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
// 将嵌套路由转换为扁平化结构
const flattenRoutes = (routes, parentPath = "") => {
  const flatRoutes = [];

  routes.forEach((route) => {
    // 如果有子路由，递归处理
    if (route.children && route.children.length > 0) {
      route.children.forEach((child) => {
        // 构建完整路径：父路径 + '/' + 子路径
        const fullPath = parentPath + route.path + "/" + child.path;

        flatRoutes.push({
          title: child.meta?.title || child.name,
          path: fullPath,
          name: child.name,
          originalData: child,
        });
      });
    }
    // 如果没有子路由但是是菜单项，也添加到列表中
    else if (route.menuType === "C") {
      const fullPath = parentPath + route.path;
      flatRoutes.push({
        title: route.meta?.title || route.name,
        path: fullPath,
        name: route.name,
        originalData: route,
      });
    }
  });

  return flatRoutes;
};

const getAllRoutes = async (searchKeyword = "") => {
  try {
    searchLoading.value = true;
    let res = await getRouters();
    // 将嵌套路由转换为扁平化结构
    const flatRoutes = flattenRoutes(res.data);
    // 根据搜索关键词过滤
    let filteredRoutes = flatRoutes;
    if (searchKeyword && searchKeyword.trim()) {
      filteredRoutes = flatRoutes.filter((route) =>
        route.title.toLowerCase().includes(searchKeyword.toLowerCase())
      );
    }
    // 转换为 ant-design-vue select 组件需要的格式
    menuOptions.value = filteredRoutes.map((route) => {
      return {
        label: route.title,
        value: route.path,
        name: route.name,
        originalData: route.originalData,
      };
    });
  } catch (error) {
    //
  } finally {
    searchLoading.value = false;
  }
};

// 搜索函数
const onSearch = (searchValue) => {
  keyword.value = searchValue;
  getAllRoutes(searchValue);
};
const handleSelect = (value) => {
  console.log("选中的路径:", value);

  try {
    // 直接使用路径进行跳转
    router.push(value);
    console.log("路由跳转成功!");
  } catch (error) {
    console.error("路由跳转失败:", error);
    alert(`页面跳转失败: ${value}\n请检查路由配置或联系管理员`);
  }
};

const handleIframeClick = () => {
  // 获取当前选中的报表链接
  const currentPath = currentReport.value?.path;
  console.log(currentPath, "currentPath");

  if (currentPath) {
    // 在新窗口中打开链接
    window.open(currentPath, "_blank");
  }
};

onMounted(() => {
  console.log(window);

  // 初始化时加载路由数据
  getAllRoutes();

  // 添加一个测试函数到全局，方便调试
  window.testFlattenRoutes = () => {
    const testData = [
      {
        name: "Aiasstenant",
        path: "/aiasstenant",
        menuType: "M",
        hidden: false,
        redirect: "noRedirect",
        component: "Layout",
        alwaysShow: true,
        meta: {
          title: "智能助理",
          icon: "client",
          noCache: false,
          link: null,
        },
        children: [
          {
            name: "AiNotice",
            path: "aiNotice",
            menuType: "C",
            hidden: false,
            component: "aiNotice/aiNotice/index",
            meta: {
              title: "Ai子菜单",
              icon: "#",
              noCache: false,
              link: null,
            },
          },
        ],
      },
      {
        name: "Report",
        path: "/report",
        menuType: "M",
        hidden: false,
        redirect: "noRedirect",
        component: "Layout",
        alwaysShow: true,
        meta: {
          title: "报表",
          icon: "#",
          noCache: false,
          link: null,
        },
        children: [
          {
            name: "SaleReport",
            path: "saleReport",
            menuType: "C",
            hidden: false,
            component: "report/saleReport/index",
            meta: {
              title: "销售报表",
              icon: "#",
              noCache: false,
              link: null,
            },
          },
        ],
      },
    ];

    const result = flattenRoutes(testData);
    console.log("测试扁平化结果:", result);
    return result;
  };

  getAllRoutes();
});
</script>

<style lang="less" scoped>
.iframe {
  width: 100%;
  height: 1080px;
  // height: calc(100vh-180px); /* 根据实际布局调整 */
  margin-top: 30px;
}
.report-content {
  margin-top: 16px;
  // padding-right: 378px;
  width: 100%;
}
.btn-box {
  width: calc(33.3333% - 18px);
  height: 60px;
  background: #1890ff;
  text-align: center;
  line-height: 60px;
  cursor: pointer;
  user-select: none;
}
</style>
