<template>
  <search :searchData="searchData" @search="refresh">
    <a-dropdown
      :trigger="['click']"
      v-permission="'button:common:qualityTest:export'"
    >
      <template #overlay>
        <a-menu>
          <a-menu-item key="1" @click="exportOrg">导出质检结果</a-menu-item>
          <a-menu-item key="2" @click="exportRecord">导出质检记录</a-menu-item>
        </a-menu>
      </template>
      <mw-button
        title="导出"
        v-permission="'button:common:qualityTest:export'"
      ></mw-button>
    </a-dropdown>
  </search>

  <mw-table
    :scroll="{ x: 'max-content' }"
    :align="center"
    :columns="visibleColumns"
    :data-source="data"
    class="tables"
    :rowKey="(record) => record.id"
    childrenColumnName="record.detailList"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'source'">
        <dictionary
          :statusOptions="applicantTypeList"
          :value="record.applicantType"
          :showBadge="false"
        />
        /
        <dictionary
          :statusOptions="qualitySource"
          :value="record.source"
          :showBadge="false"
        />
      </template>
      <template v-if="column.dataIndex == 'parentApplicantNo'">
        <span
          v-if="record.applicantType == 'purchase'"
          class="text-primary cursor-pointer"
          @click="
            onParentApplicantNoRemarks(
              '质检备注',
              'qualityInspectionRemarks',
              record
            )
          "
        >
          {{ record.parentApplicantNo }}</span
        >
      </template>

      <template v-if="column.dataIndex == 'applicantNo'">
        <span
          v-if="record.applicantType == 'purchase'"
          class="text-primary cursor-pointer"
          @click="
            onParentApplicantNoRemarks(
              '质检备注',
              'qualityInspectionRemarks',
              record
            )
          "
        >
          {{ record.applicantNo }}</span
        >
      </template>
      <template v-if="column.key == 'status'">
        <dictionary
          :statusOptions="qualityStatus"
          :value="record.status"
          isBackgroundColor
        />
      </template>

      <template v-if="column.key == 'operation'">
        <mw-button @click="showCreateDrawer('detail', record)" class="mr-2">
          详情
        </mw-button>
        <mw-button @click="showCreateDrawer('rec', record)" class="mr-2">
          记录
        </mw-button>
        <mw-button
          v-if="record.status !== 1"
          @click="showCreateDrawer('qualityTesting', record)"
          class="mr-2"
        >
          质检
        </mw-button>
      </template>
    </template>
    <template #expandedRowRender="{ record }">
      <mw-table
        :columns="columns.childrenColumns"
        :data-source="record.detailList"
        :pagination="{ pageSize: 5 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'disqualifiedPercent'">
            <span v-if="record.disqualifiedPercent">
              {{ _cloneDeep(record.disqualifiedPercent) }}%</span
            >
          </template>

          <template v-if="column.key == 'bizNo'">
            <mw-button
              type="link"
              v-if="record.bizNo"
              @click="
                $router.push({
                  name: 'ProductAndMaterDetail',
                  query: {
                    code: record.bizNo,
                    bizType: record.bizType,
                    id: record.bizId,
                  },
                })
              "
            >
              {{ record.bizNo }}</mw-button
            >
          </template>

          <template v-if="column.key == 'disqualifiedQuantity'">
            <div>
              {{ _cloneDeep(record.disqualifiedQuantity) }}
            </div>
            <div>
              <a
                v-if="record.remarkTime"
                class="cursor-pointer"
                style="color: #959ec3"
                href="javascript:void(0);"
                @click.stop="showDetailDrawer(record)"
                >查看备注</a
              >
            </div>
          </template>
        </template>
      </mw-table>
    </template>
  </mw-table>
  <material-quality-drawer
    :title="title"
    v-model:visible="addVisible"
    :record="currendRecord"
    @finish="getList"
    :applicantType="applicantType"
    :privateId="privateId"
    :sourceDetail="sourceDetail"
    :openType="openType"
  />
  <remark-drawer
    v-model:visible="remarkDrawerVisible"
    :detail="currentDetail"
  />
  <PublicRemarks
    v-model:visible="publicRemarksVisible"
    :publicRemarksTitle="publicRemarksTitle"
    :remarkRecord="remarkRecord"
    @finish="getList"
    :remarkType="remarkType"
  />
  <detectionRecordDrawer
    v-model:visible="detectionRecordDrawerVisible"
    :openType="openType"
    :record="currendRecord"
    :sourceDetail="sourceDetail"
  ></detectionRecordDrawer>
  <detailDrawer
    v-model:visible="detailDrawerVisible"
    :openType="openType"
    :record="currendRecord"
    :sourceDetail="sourceDetail"
  ></detailDrawer>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
} from "vue";
import _cloneDeep from "lodash/cloneDeep";
import { page, qualityTestExport, qualityTestExportV1 } from "@/api/quality.js";
import { usePagenation } from "@/common/setup";
import Search from "@/components/search/index.vue";
import materialQualityDrawer from "./materialQualityDrawer.vue";
import remarkDrawer from "./remarkDrawer.vue";
import {
  qualityStatus,
  qualityStatusFilters,
  qualitySource,
  applicantTypeList,
} from "@/common/constant.js";
import PublicRemarks from "@/components/publicRemarks.vue";
import detectionRecordDrawer from "./detectionRecordDrawer.vue";
import detailDrawer from "./detailDrawer.vue";
import { exportExecl } from "@/utils/util.js";
import { formattedToday } from "@/common/validate.js";
const props = defineProps({
  type: {
    type: String,
    default: "material",
  },
});

const { proxy } = getCurrentInstance();
const title = computed(() => {
  return props.type == "product" ? "产品" : "物料";
});
const applicantType = ref();
const privateId = ref();
const data = ref([]),
  loading = ref(false),
  addVisible = ref(false),
  currendRecord = ref({}),
  currentDetail = ref({}),
  remarkDrawerVisible = ref(false);
const publicRemarksTitle = ref();
const publicRemarksVisible = ref(false);
const remarkRecord = ref({});
const exportLoading = ref(false);
const remarkType = ref("");
const sourceDetail = ref();
const openType = ref();
const detailDrawerVisible = ref(false);
const detectionRecordDrawerVisible = ref(false);
const searchData = reactive({
  fields: {
    status: {
      name: "质检状态",
      type: "a-select",
      options: qualityStatusFilters,
      placeholder: "选择状态",
      width: "120px",
      value: 0,
      allowClear: true,
    },
    type: {
      name: "质检类型",
      type: "a-select",
      options: qualitySource,
      placeholder: "选择类型",
      width: "120px",
      value: "",
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: `输入${title.value}编码/名称/关联信息/供应商名称/供应商编码`,
      width: "390px",
      allowClear: true,
    },
    rangeDate: {
      type: "a-range-picker",
      valueFormat: "YYYY-MM-DD",
      value: [],
      width: "240px",
    },
  },
});
const columns = ref({
  fatColumns: [
    {
      title: "质检来源",
      dataIndex: "source",
      key: "source",
    },
    {
      // title: title.value == "物料" ? "采购订单号" : "销售订单号",
      title: "父关联编码",
      dataIndex: "parentApplicantNo",
      key: "parentApplicantNo",
      width: "150px",
    },
    {
      title: "关联编码",
      dataIndex: "applicantNo",
      key: "applicantNo",
    },
    {
      title: "供应商 / 客户",
      dataIndex: "supplierName",
      key: "supplierName",
      customRender: ({ record }) => {
        return record.supplierName + " / " + record.customerName
          ? record.customerName
          : "";
      },
    },
    {
      title: "质检总数",
      dataIndex: "changeQuantity",
      key: "changeQuantity",
    },

    {
      title: "待检总数",
      dataIndex: "waitQuantity",
      key: "waitQuantity",
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
    },

    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
    },
  ],
  childrenColumns: [
    {
      title: `${title.value}` + "编码",
      dataIndex: "bizNo",
      key: "bizNo",
      width: "150px",
    },
    {
      title: `${title.value}` + "信息",
      dataIndex: "bizName",
      key: "bizName",
      width: "150px",
    },

    {
      title: "规格",
      dataIndex: "specification",
      key: "specification",
      width: "150px",
    },
    {
      title: "质检总数",
      dataIndex: "changeQuantity",
      key: "changeQuantity",
      width: "150px",
    },

    {
      title: "合格" + `${title.value}`,
      dataIndex: "passQuantity",
      key: "passQuantity",
      width: "150px",
    },
    {
      title: "不良率",
      dataIndex: "disqualifiedPercent",
      key: "disqualifiedPercent",
      width: "150px",
    },
    {
      title: "不合格",
      dataIndex: "disqualifiedQuantity",
      key: "disqualifiedQuantity",
      width: "150px",
    },
  ],
});
// 使用计算属性来根据条件筛选需要展示的列
const visibleColumns = computed(() => {
  if (props.type !== "product") {
    return columns.value.fatColumns; // 如果满足条件，返回所有列
  } else {
    return columns.value.fatColumns.filter(
      (column) => column.dataIndex !== "supplierName"
    ); // 如果不满足条件，返回除了列1和列2以外的其他列
  }
});
// 导出
const exportOrg = async () => {
  try {
    exportLoading.value = true;
    let searchParam = {};
    //搜索信息
    for (const key in searchData.fields) {
      searchParam[key] = searchData.fields[key].value;
    }
    let { rangeDate } = toRaw(searchParam);
    if (rangeDate && rangeDate.length > 0) {
      searchParam.startTime = rangeDate[0] + " 00:00:00";
      searchParam.endTime = rangeDate[1] + " 23:59:59";
    }
    let result = await qualityTestExport({
      ...searchParam,
      bizType: props.type,
    });

    const fileName = "物料质检结果_" + formattedToday + ".xlsx";
    exportExecl(fileName, result);
    exportLoading.value = false;
  } catch (error) {
    exportLoading.value = false;
  }
};
const exportRecord = async () => {
  //
  try {
    exportLoading.value = true;
    let searchParam = {};
    //搜索信息
    for (const key in searchData.fields) {
      searchParam[key] = searchData.fields[key].value;
    }
    let { rangeDate } = toRaw(searchParam);
    if (rangeDate && rangeDate.length > 0) {
      searchParam.startTime = rangeDate[0] + " 00:00:00";
      searchParam.endTime = rangeDate[1] + " 23:59:59";
    }
    proxy.$message.info("正在导出，请稍等");
    let result = await qualityTestExportV1({
      ...searchParam,
      bizType: props.type,
    });
    const fileName = "物料质检记录_" + formattedToday + ".xlsx";
    exportExecl(fileName, result);
    proxy.$message.success("导出成功");
    exportLoading.value = false;
  } catch (error) {
    exportLoading.value = false;
  }
};
const getList = async () => {
  loading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await page(pageParam.value, {
    ...searchParam,
    bizType: props.type,
  });
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
const showCreateDrawer = (type, record) => {
  if (type == "rec") {
    detectionRecordDrawerVisible.value = true;
  }
  if (type == "detail") {
    detailDrawerVisible.value = true;
  }
  if (type == "qualityTesting") {
    addVisible.value = true;
  }
  console.log(record, "record");
  // if (record.status != 1) {
  openType.value = type;
  currendRecord.value = record;

  applicantType.value = record.applicantType;
  privateId.value = record.id;
  sourceDetail.value = record.source;
  // }
};
const showDetailDrawer = (record) => {
  currentDetail.value = record;
  remarkDrawerVisible.value = true;
};
const onParentApplicantNoRemarks = (title, type, val) => {
  publicRemarksTitle.value = title;
  publicRemarksVisible.value = true;
  remarkRecord.value = val;
  remarkType.value = type;
};
</script>
<style lang="less" scoped>
.list-header {
  background: theme("colors.background");
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  color: theme("colors.primary-text");
}

:deep(.ant-list-item) {
  // background: theme("colors.background");
  border: 1px solid theme("colors.dividers") !important;
  padding: 0 16px !important;
  border-radius: 8px;
}

:deep(.ant-pagination) {
  text-align: center;
}
</style>
