<template>
  <mw-drawer custom-title="备注详情" :visible="visible" @close="onClose">
    <div class="space-y-1 pt-2">
      <p>{{ detail.bizName }}</p>
      <p class="text-secondar-text">备注时间：{{ detail.remarkTime }}</p>
      <!-- <p class="text-secondar-text">编码：{{ detail.bizNo }}</p>
      <p class="text-secondar-text">规格：{{ detail.specification }}</p>
      <p class="text-secondar-text">质检总数：{{ detail.changeQuantity }}</p>
      <p class="text-secondar-text">合格总数：{{ detail.passQuantity }}</p> -->
      <p class="text-secondar-text">
        不合格数：{{ detail.disqualifiedQuantity }}
      </p>
      <p class="text-secondar-text">
        不良率：{{ detail.disqualifiedPercent || "-" }}
      </p>
      <p class="text-secondar-text flex" v-if="detail.remark">
        <span>备注内容：</span>{{ detail.remark }}
      </p>
      <p class="text-secondar-text flex" v-if="detail.remarkFile">
        <span class="mr-3">添加附件</span>
        <form-upload
          :value="detail.remarkFile ? [detail.remarkFile] : []"
          sence="quality"
          readonly
        ></form-upload>
      </p>
    </div>
  </mw-drawer>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  onBeforeMount,
  defineEmits,
  reactive,
} from "vue";
import { check } from "@/api/quality.js";
const { proxy } = getCurrentInstance();
import FormUpload from "@/components/form-upload.vue";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  detail: { type: Object, default: () => ({}) },
});
const emit = defineEmits(["update:visible", "finish"]);

const onClose = () => {
  emit("update:visible", false);
};
</script>
<style lang="less" scoped>
.bg {
  height: 40px;
  line-height: 40px;
  background: rgba(34, 34, 34, 0.08);
  border-radius: 8px;
  text-align: center;
  margin-bottom: 24px;
}
</style>
