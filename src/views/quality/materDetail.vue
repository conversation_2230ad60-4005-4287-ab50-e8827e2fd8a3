<template>
  <mw-drawer
    :custom-title="'物料明细'"
    :visible="visible"
    @close="onClose"
    :spinning="spinning"
    width="80%"
  >
    <a-spin :spinning="spinning">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="inline"
        :colon="false"
      >
        <p class="text-base mb-4">物料信息</p>
        <a-row style="width: 100%">
          <a-col :span="6" class="mb-3">
            <a-form-item :label="`物料编号:`">
              <span>{{ formData.materialNo || "-" }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6" class="mb-3">
            <a-form-item :label="`物料名称:`">
              <span>{{ formData.materialName || "-" }}</span>
            </a-form-item>
          </a-col>

          <a-col :span="6" class="mb-3">
            <a-form-item :label="`物料规格:`">
              <span>{{ formData.specification || "-" }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6" class="mb-3">
            <a-form-item label="单位名称:">
              <span>{{
                formData.unitName ? formData.unitName : formData.unit || "-"
              }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6" class="mb-3">
            <a-form-item label="分类:">
              <span>{{ formData.classificationName || "-" }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6" class="mb-3">
            <a-form-item label="物料标签:">
              <span v-if="formData.isHomemade == 1">自制 ，</span>
              <span v-if="formData.isExpense == 1">耗用，</span>
              <span v-if="formData.isEconomicMaterial == 1">经济物料，</span>
              <span v-if="formData.isMarket == 1">销售，</span>
              <span v-if="formData.isPurchase == 1">采购，</span>
              <span
                v-if="
                  formData.isHomemade == 0 &&
                  formData.isExpense == 0 &&
                  formData.isEconomicMaterial == 0 &&
                  formData.isMarket == 0 &&
                  formData.isPurchase == 0
                "
                >无</span
              >
            </a-form-item>
          </a-col>
          <a-col :span="6" class="mb-3">
            <a-form-item label="备注:">
              <span>{{ formData.remark || "-" }}</span>
            </a-form-item>
          </a-col>
        </a-row>

        <div style="width: 100%">
          <a-row>
            <a-col
              :span="6"
              class="mb-3 mt-4"
              style="display: flex; flex-direction: column"
            >
              <div>备注附件:</div>
              <div
                v-for="(item, index) in formData.remarkFileList"
                :key="index"
                @click.stop="openUrl(item.fileVisitUrl)"
                :href="item.fileVisitUrl"
                :title="item.fileName"
                class="cursor-pointer inline-block"
                style="color: #959ec3"
              >
                <i
                  v-if="item.fileName"
                  class="iconfont icon-jichu-lianjie text-xs -middle mr-1"
                  style="color: #959ec3"
                ></i
                ><span class="underline"> {{ item.fileName }}</span>
              </div>
            </a-col>
            <a-col
              :span="6"
              class="mb-3 mt-4"
              style="display: flex; flex-direction: column"
            >
              <div>图片:</div>

              <div
                v-for="(item, index) in formData.materialImage"
                :key="index"
                @click.stop="openUrl(item.fileVisitUrl)"
                :href="item.fileVisitUrl"
                :title="item.fileName"
                class="cursor-pointer inline-block"
                style="color: #959ec3"
              >
                <i
                  v-if="item.fileName"
                  class="iconfont icon-jichu-lianjie text-xs -middle mr-1"
                  style="color: #959ec3"
                ></i
                ><span class="underline"> {{ item.fileName }}</span>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-spin>
  </mw-drawer>
</template>

<script setup>
import { defineProps, defineEmits, ref, watch } from "vue";
import { getInfo } from "@/api/basicData/material.js";
const emit = defineEmits(["update:visible"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  openItem: {
    type: Object,
    default: {},
  },
});
const spinning = ref(false);

const formData = ref({
  bomName: undefined,
  fileDetailModel: [],
  productNo: undefined,
});

const openUrl = (url) => {
  window.open(url, "_blacnk");
};
const onClose = () => {
  emit("update:visible", false);
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      console.log(props.openItem, "props");
      let res = await getInfo(props.openItem.bizId);
      formData.value = res.data;
    }
  }
);
</script>

<style></style>
