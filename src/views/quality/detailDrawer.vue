<template>
  <mw-drawer
    :custom-title="'质检详情'"
    :visible="visible"
    @close="onClose"
    :spinning="spinning"
    width="90%"
  >
    <div class="w-full divide-y divide-border space-y-2">
      <div v-for="(item, index) in detail" :key="index" class="space-y-1 flex">
        <div class="w-4/5">
          <div class="mt-2">
            <mw-button
              shape="round"
              style="padding: 0"
              type="link"
              @click="openDetail(item)"
            >
              {{ item.bizNo }}</mw-button
            >
            <span class="ml-5"> {{ item.bizName }}</span>
            <span class="ml-5"> {{ item.specification }}</span>
          </div>
          <div class="mt-3" v-if="props.openType !== 'rec'">
            <span>
              物料总数：<span class="ml-3">{{ item.changeQuantity }}</span>
            </span>

            <span class="ml-5">
              已质检数：
              <span class="ml-3">
                {{ item.alreadyQuantity }}
              </span>
            </span>
          </div>
          <div v-for="(ite, ind) in item.recordList" :key="ind">
            <div class="flex w-full">
              <div class="mt-3" style="width: 19%">
                时间：{{ ite.createTime }}
              </div>
              <div class="ml-8" style="width: 80%">
                <a-row class="mt-3">
                  <div class="" style="width: 16%">
                    入库总数:
                    <span v-if="sourceDetail == 'purchase'">{{
                      ite.passQuantity || 0
                    }}</span>
                    <span v-else>{{ (ite.passQuantity = ite.changeQ) }}</span>
                  </div>

                  <div class="ml-2" style="width: 16%">
                    合格入库:
                    <span>{{ ite.changeQ || 0 }}</span>
                  </div>

                  <div
                    v-if="sourceDetail == 'purchase'"
                    class="ml-2"
                    style="width: 16%"
                  >
                    让步接收:
                    <span>{{ ite.concessionAcceptance || 0 }}</span>
                  </div>

                  <div
                    v-if="sourceDetail == 'purchase'"
                    class="ml-2"
                    style="width: 16%"
                  >
                    挑选使用:
                    <span>{{ ite.selectAndUse || 0 }}</span>
                  </div>

                  <div
                    v-if="sourceDetail == 'purchase'"
                    class="ml-2"
                    style="width: 16%"
                  >
                    返修合格:

                    <span>{{ ite.qualifiedRepair || 0 }}</span>
                  </div>
                </a-row>
                <a-row class="mt-3">
                  <span class="" style="width: 16%">
                    退回总数:
                    <span>
                      {{
                        (ite.disqualifiedQuantity = ite.noDisqualifiedQuantity
                          ? ite.noDisqualifiedQuantity
                          : 0)
                      }}
                    </span>
                  </span>
                  <span class="ml-2" style="width: 16%">
                    不合格数:
                    <span>{{ ite.noDisqualifiedQuantity || 0 }}</span>
                  </span>
                </a-row>
              </div>
            </div>
          </div>
        </div>
        <div
          class="ml-5 pl-3"
          style="border-left: 1px solid #ccc"
          v-if="item?.orderInfos && item.orderInfos.length"
        >
          关联订单
          <div v-for="(itemInfos, index) in item.orderInfos" :key="index">
            <mw-button
              style="padding: 0"
              type="link"
              @click="poCorrelationOrder('qualityInspection', itemInfos)"
            >
              {{ itemInfos.orderNo }}
            </mw-button>
            <!-- <span @click="poCorrelationOrder">{{ item.orderNo }}</span> -->
            <span
              class="ml-5"
              v-if="
                props.record.applicantType !== 'plan' &&
                props.record.applicantType !== 'market'
              "
              >数量 {{ itemInfos.purchaseCount }}</span
            >
          </div>
        </div>
      </div>
      <!-- <div class="pl-4" style="border-left: 1px solid #ccc">
        关联采购需求
        <div v-for="(item, index) in detail.orderInfos" :key="index">
          <span @click="poCorrelationOrder">{{ item.orderNo }}</span>
          <span class="ml-5">数量 {{ item.purchaseCount }}</span>
        </div>
      </div> -->
    </div>
  </mw-drawer>
  <materDetail
    ref="materDetailRef"
    v-model:visible="materDetailVisible"
    :openItem="openItem"
  ></materDetail>
  <bom-drawer
    v-model:visible="bomDrawerVisible"
    :planNo="props.record.id"
    :orderItem="orderItem"
    :orderType="orderType"
  ></bom-drawer>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  onBeforeMount,
  defineEmits,
  reactive,
  computed,
} from "vue";
import {
  check,
  reject,
  postWholeReturn,
  qualityTestGetInfo,
} from "@/api/quality.js";
const { proxy } = getCurrentInstance();
import FormUpload from "@/components/form-upload.vue";
import _cloneDeep from "lodash/cloneDeep";
import { useRoute, useRouter } from "vue-router";
import { formatter8 } from "@/common/validate.js";
import materDetail from "./materDetail.vue";
// BomDrawer
import BomDrawer from "@/views/production/BomDrawer.vue";
import { downFile } from "@/common/setup/index.js";
const bomDrawerVisible = ref(false);
const router = useRouter();
const route = useRoute();
const orderItem = ref();
const orderType = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  record: { type: Object, default: () => ({}) },
  title: {
    type: String,
    default: "",
  },
  privateId: {
    type: String,
    default: "",
  },
  sourceDetail: {
    type: String,
    default: "",
  },
  openType: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["update:visible", "finish"]);
const materDetailVisible = ref();
const submitLoading = ref(false);
const spinning = ref(false);
const detail = ref({});
const openItem = ref();
const openDetail = (val) => {
  materDetailVisible.value = true;
  openItem.value = val;
};

const onClose = () => {
  emit("update:visible", false);
};
const poCorrelationOrder = (type, item) => {
  bomDrawerVisible.value = true;
  orderItem.value = item;
  orderType.value = type;
};
// 记录接口
const qualityTestGetRecordInfoData = async (val) => {
  let res = await qualityTestGetInfo({ testId: val });
  res.data.detailList.forEach((element) => {
    element.recordList.forEach((ite, ind) => {
      console.log(ite, "ite");
      const typesToFind = [1, 2, 3, 4, 5];

      typesToFind.forEach((type) => {
        const foundItem = ite.checkQuantityList.find(
          (item) => item.type == type
        );
        ite[
          `${
            type == 1
              ? "changeQ"
              : type == 2
              ? "concessionAcceptance"
              : type == 3
              ? "selectAndUse"
              : type == 4
              ? "qualifiedRepair"
              : type == 5
              ? "noDisqualifiedQuantity"
              : ""
          }`
        ] = foundItem?.quantity;
      });
    });
  });

  detail.value = res.data.detailList;
  detail.value.orderInfos = res.data.orderInfos;

  console.log(detail.value, "res");
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      qualityTestGetRecordInfoData(props.record.id);
    }
  }
);
</script>
<style lang="less" scoped>
.bg {
  height: 40px;
  line-height: 40px;
  background: rgba(34, 34, 34, 0.08);
  border-radius: 8px;
  text-align: center;
  margin-bottom: 24px;
}
</style>
