<template>
  <mw-drawer
    :custom-title="'质检记录'"
    :visible="visible"
    @close="onClose"
    :spinning="spinning"
    width="90%"
  >
    <div class="w-full divide-y divide-border space-y-2">
      <div v-for="item in detail" :key="item.bizNo">
        <div class="flex mt-5">
          <div :class="props.openType !== 'rec' ? 'w-10/12' : 'w-full'">
            <div v-if="props.openType == 'rec'">
              <span> 质检时间：{{ item.createTime }} </span>
              <span class="ml-5"> 操作人：{{ item.createName }} </span>
            </div>
            <div v-for="ite in item.detailList" :key="ite.bizId" class="w-full">
              <div class="flex w-full">
                <div
                  v-if="props.openType == 'rec'"
                  class="mt-2"
                  style="width: 30%"
                >
                  <mw-button
                    style="padding: 0"
                    type="link"
                    @click="openDetail(ite)"
                  >
                    {{ ite.bizNo }}</mw-button
                  >
                  <span class="ml-5"> {{ ite.bizName }}</span>
                  <span class="ml-5"> {{ ite.specification }}</span>
                </div>
                <div :class="'ml-4'" style="width: 70%">
                  <a-flex class="mt-3">
                    <div style="width: 20%">
                      入库总数:
                      <span v-if="sourceDetail == 'purchase'">{{
                        ite.passQuantity
                      }}</span>
                      <span v-else>{{ (ite.passQuantity = ite.changeQ) }}</span>
                    </div>
                    <div class="ml-1" style="width: 20%">
                      合格入库:
                      <span>{{ ite.changeQ || 0 }}</span>
                    </div>
                    <div
                      v-if="sourceDetail == 'purchase'"
                      class="ml-1"
                      style="width: 20%"
                    >
                      让步接收:
                      <span>{{ ite.concessionAcceptance || 0 }}</span>
                    </div>

                    <div
                      v-if="sourceDetail == 'purchase'"
                      class="ml-1"
                      style="width: 20%"
                    >
                      挑选使用:
                      <span>{{ ite.selectAndUse }}</span>
                    </div>
                    <div
                      v-if="sourceDetail == 'purchase'"
                      class="ml-1"
                      style="width: 20%"
                    >
                      返修合格:

                      <span>{{ ite.qualifiedRepair || 0 }}</span>
                    </div>
                  </a-flex>
                  <a-flex class="mt-5">
                    <div class="" style="width: 20%">
                      退回总数:
                      <span>
                        {{ ite.disqualifiedQuantity || 0 }}
                      </span>
                    </div>
                    <div class="ml-1" style="width: 20%">
                      不合格数:

                      <span>{{ ite.noDisqualifiedQuantity || 0 }}</span>
                    </div>
                  </a-flex>
                </div>
              </div>

              <div class="mt-5 flex">
                <div class="flex w-1/2">
                  <div>物料备注：</div>
                  <a-textarea
                    class="flex-1"
                    v-model:value="ite.remark"
                    placeholder="请输入物料备注信息"
                    :rows="3"
                    :maxlength="200"
                    v-if="props.openType == 'qualityTesting'"
                  />
                  <span v-else>{{ ite.remark }}</span>
                </div>
                <div class="flex ml-30">
                  <span class="mr-3">附件</span>
                  <div style="display: flex; flex-direction: column">
                    <span v-for="(item, index) in ite.remarkFile" :key="index">
                      <i
                        class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                        style="color: #959ec3"
                      ></i>
                      <a
                        @click="downFile(item.fileVisitUrl, item.fileName)"
                        :title="item.fileName"
                        target="_blank"
                        class="underline"
                        style="color: #959ec3"
                        >{{ item.fileName }}
                      </a>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- <div
            class="ml-5 pl-3"
            style="border-left: 1px solid #ccc"
            v-if="props.openType !== 'rec'"
            @click="poCorrelationOrder"
          >
            关联订单
          </div> -->
        </div>
      </div>
    </div>
  </mw-drawer>
  <materDetail
    ref="materDetailRef"
    v-model:visible="materDetailVisible"
    :openItem="openItem"
  ></materDetail>
  <BomDrawer
    v-model:visible="bomDrawerVisible"
    :planNo="props.record.id"
  ></BomDrawer>
</template>
<script setup>
import { defineProps, ref, getCurrentInstance, watch, defineEmits } from "vue";
import { qualityTestGetRecordInfo } from "@/api/quality.js";
import _cloneDeep from "lodash/cloneDeep";
import materDetail from "./materDetail.vue";
import BomDrawer from "@/views/production/BomDrawer.vue";
import { downFile } from "@/common/setup/index.js";
const bomDrawerVisible = ref(false);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  record: { type: Object, default: () => ({}) },
  title: {
    type: String,
    default: "",
  },
  privateId: {
    type: String,
    default: "",
  },
  sourceDetail: {
    type: String,
    default: "",
  },
  openType: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["update:visible", "finish"]);
const materDetailVisible = ref();
const submitLoading = ref(false);
const spinning = ref(false);
const detail = ref({});
const openItem = ref();
const openDetail = (val) => {
  materDetailVisible.value = true;
  openItem.value = val;
};
//加法
const addNum = (num1, num2, num3, num4) => {
  console.log(num1, num2, num3, num4, "num1num2num3num4");
  // console.log(num1, num2, "num2num2num2num2");
  var sq1, sq2, sq3, sq4, m;
  try {
    sq1 = num1.toString().split(".")[1].length;
  } catch (e) {
    sq1 = 0;
  }
  try {
    sq2 = num2.toString().split(".")[1].length;
  } catch (e) {
    sq2 = 0;
  }
  try {
    sq3 = num3.toString().split(".")[1].length;
  } catch (e) {
    sq3 = 0;
  }
  try {
    sq4 = num4.toString().split(".")[1].length;
  } catch (e) {
    sq4 = 0;
  }
  m = Math.pow(10, Math.max(sq1, sq2, sq3, sq4));
  return parseFloat((num1 * m + num2 * m + num3 * m + num4 * m) / m);
};
const onClose = () => {
  emit("update:visible", false);
};
const poCorrelationOrder = () => {
  bomDrawerVisible.value = true;
};
// 记录接口
const qualityTestGetRecordInfoData = async (val) => {
  let res = await qualityTestGetRecordInfo({ testId: val });
  res.data.forEach((element) => {
    console.log(element.detailList, "element");
    element.detailList.forEach((ite, ind) => {
      console.log(ite.checkQuantityList, "ite");
      // let changeQ = ite.checkQuantityList.find((item) => item.type == 1);
      // let concessionAcceptance = ite.checkQuantityList.find(
      //   (item) => item.type == 2
      // );
      // let selectAndUse = ite.checkQuantityList.find((item) => item.type == 3);
      // let qualifiedRepair = ite.checkQuantityList.find(
      //   (item) => item.type == 4
      // );
      // let noDisqualifiedQuantity = ite.checkQuantityList.find(
      //   (item) => item.type == 5
      // );
      // ite.changeQ = changeQ?.quantity;
      // ite.concessionAcceptance = concessionAcceptance?.quantity;
      // ite.selectAndUse = selectAndUse?.quantity;
      // ite.qualifiedRepair = qualifiedRepair?.quantity;
      // ite.noDisqualifiedQuantity = noDisqualifiedQuantity?.quantity;
      const typesToFind = [1, 2, 3, 4, 5];

      typesToFind.forEach((type) => {
        const foundItem = ite.checkQuantityList.find(
          (item) => item.type == type
        );
        ite[
          `${
            type === 1
              ? "changeQ"
              : type === 2
              ? "concessionAcceptance"
              : type === 3
              ? "selectAndUse"
              : type === 4
              ? "qualifiedRepair"
              : "noDisqualifiedQuantity"
          }`
        ] = foundItem?.quantity;
      });
    });
  });
  detail.value = res.data;
  console.log(res, "res");
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      console.log(props, "props------");

      if (props.openType == "rec") {
        qualityTestGetRecordInfoData(props.record.id);
      }
    }
  }
);
</script>
<style lang="less" scoped>
.bg {
  height: 40px;
  line-height: 40px;
  background: rgba(34, 34, 34, 0.08);
  border-radius: 8px;
  text-align: center;
  margin-bottom: 24px;
}
</style>
