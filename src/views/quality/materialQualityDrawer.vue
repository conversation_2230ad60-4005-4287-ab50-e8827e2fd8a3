<template>
  <mw-drawer
    :custom-title="`物料质检`"
    :visible="visible"
    @close="onClose"
    :spinning="spinning"
    width="90%"
  >
    <template #header>
      <!-- <a-popconfirm
        title="确认是否全部退回！"
        @confirm="wholeReturn(detail.detailList)"
        placement="bottom"
      >
        <a-button class="mt-2" type="primary" :loading="submitLoading"
          >全部退回</a-button
        >
      </a-popconfirm> -->
      <a-popconfirm
        v-permission="'common:quality:reject'"
        title="请确认是否全部驳回"
        ok-text="确定"
        cancel-text="取消"
        @confirm="onWholeReject"
        placement="bottom"
        @cancel="cancel"
      >
        <!-- @click="submitForm" -->
        <mw-button
          :loading="submitLoading"
          v-permission="'common:quality:reject'"
          >全部驳回
        </mw-button>
      </a-popconfirm>

      <a-popconfirm
        title="请确认质检无误"
        ok-text="确定"
        cancel-text="取消"
        @confirm="submitForm"
        placement="bottom"
        @cancel="cancel"
      >
        <mw-button :loading="submitLoading">批量质检</mw-button>
      </a-popconfirm>
    </template>
    <div class="divide-y divide-border space-y-2">
      <div
        v-for="item in detail.detailList"
        :key="item.bizNo"
        class="space-y-1"
      >
        <div class="flex">
          <div class="w-4/5">
            <div>
              <mw-button
                style="padding: 0"
                type="link"
                @click="openDetail(item)"
              >
                {{ item.bizNo }}</mw-button
              >
              <span class="ml-5"> {{ item.bizName }}</span>
              <span class="ml-5"> {{ item.specification }}</span>
            </div>

            <div class="flex">
              <div class="ml-0">
                <div class="mt-3 flex">
                  <div style="width: 150px">
                    物料总数：
                    <span>{{ item.changeQuantitys }}</span>
                  </div>
                  <div class="ml-5" style="width: 150px">
                    待质检数：<span class="ml-3">{{ item.waitQuantity }}</span>
                  </div>
                  <div>
                    本次质检总数：
                    <span class="ml-3">
                      <!-- 因为是4位数相加，不想在写一遍，所以继续使用这个，传参为0 -->
                      {{
                        (item.changeQuantity = addNum(
                          (item.passQuantity = addNum(
                            item.changeQ,
                            item.concessionAcceptance,
                            item.selectAndUse,
                            item.qualifiedRepair,
                            2
                          )),
                          item.disqualifiedQuantity,
                          0,
                          0,
                          2
                        )
                          .toFixed(8)
                          .replace(/(\.[\d]+?)0+$/, "$1")
                          .replace(/\.0+$/, ""))
                      }}
                    </span>
                  </div>
                </div>
                <div class="mt-3 flex">
                  <div class="flex" style="width: 150px">
                    <div>入库总数：</div>

                    <div v-if="sourceDetail == 'purchase'">
                      {{
                        (item.passQuantity = addNum(
                          item.changeQ,
                          item.concessionAcceptance,
                          item.selectAndUse,
                          item.qualifiedRepair,
                          2
                        )
                          .toFixed(8)
                          .replace(/(\.[\d]+?)0+$/, "$1")
                          .replace(/\.0+$/, ""))
                      }}
                    </div>
                    <div v-else>{{ item.changeQ }}</div>
                  </div>
                  <span class="ml-5">
                    合格入库：<a-input-number
                      :stringMode="true"
                      class="w-28"
                      id="inputNumber"
                      v-model:value="item.changeQ"
                      :formatter="formatter8"
                      @change="
                        (val) => {
                          item.changeQ = val;
                          item.changeQuantity = addNum(
                            item.waitQuantity,
                            item.concessionAcceptance,
                            item.selectAndUse,
                            item.qualifiedRepair,
                            2
                          );
                        }
                      "
                    />
                  </span>
                  <span v-if="sourceDetail == 'purchase'" class="ml-5"
                    >让步接收：<a-input-number
                      :stringMode="true"
                      class="w-28"
                      v-model:value="item.concessionAcceptance"
                      :formatter="formatter8"
                      @change="
                        (val) => {
                          item.changeQ = subtract(
                            item.waitQuantity,
                            item.concessionAcceptance,
                            item.selectAndUse,
                            item.qualifiedRepair,
                            item.noDisqualifiedQuantity
                          );
                          item.concessionAcceptance = val;
                          item.changeQuantity = addNum(
                            item.waitQuantity,
                            item.concessionAcceptance,
                            item.selectAndUse,
                            item.qualifiedRepair,
                            2
                          );
                        }
                      "
                    />
                  </span>

                  <span v-if="sourceDetail == 'purchase'" class="ml-5"
                    >挑选使用：<a-input-number
                      :stringMode="true"
                      class="w-28"
                      v-model:value="item.selectAndUse"
                      :formatter="formatter8"
                      @change="
                        (val) => {
                          item.selectAndUse = val;
                          item.changeQ = subtract(
                            item.waitQuantity,
                            item.concessionAcceptance,
                            item.selectAndUse,
                            item.qualifiedRepair,
                            item.noDisqualifiedQuantity
                          );
                        }
                      "
                    />
                  </span>
                  <span v-if="sourceDetail == 'purchase'" class="ml-5"
                    >返修合格：
                    <a-input-number
                      :stringMode="true"
                      class="w-28"
                      v-model:value="item.qualifiedRepair"
                      :formatter="formatter8"
                      @change="
                        (val) => {
                          item.qualifiedRepair = val;
                          item.changeQ = subtract(
                            item.waitQuantity,
                            item.concessionAcceptance,
                            item.selectAndUse,
                            item.qualifiedRepair,
                            item.noDisqualifiedQuantity
                          );
                        }
                      "
                    />
                  </span>
                </div>
                <div class="mt-5 flex">
                  <div class="" style="width: 150px">
                    退回总数：
                    <span>
                      {{
                        (item.disqualifiedQuantity = item.noDisqualifiedQuantity
                          ? item.noDisqualifiedQuantity
                          : 0)
                      }}
                    </span>
                  </div>
                  <div class="ml-5">
                    不合格数：
                    <a-input-number
                      :stringMode="true"
                      class="w-28"
                      id="inputNumber"
                      v-model:value="item.noDisqualifiedQuantity"
                      :formatter="formatter8"
                      @change="
                        (e, val) => {
                          item.changeQ = subtract(
                            item.waitQuantity,
                            item.concessionAcceptance,
                            item.selectAndUse,
                            item.qualifiedRepair,
                            item.noDisqualifiedQuantity
                          );
                        }
                      "
                    />
                  </div>
                </div>
              </div>
            </div>

            <div class="mt-5 flex">
              <div class="flex w-1/2">
                <div>物料备注：</div>
                <a-textarea
                  class="flex-1"
                  v-model:value="item.remark"
                  placeholder="请输入物料备注信息"
                  :rows="3"
                  :maxlength="200"
                />
              </div>
              <div class="flex ml-30">
                <span class="mr-3">附件</span>
                <!-- <form-upload
                  hasDownLoad
                  :showDeletdPopup="true"
                  :value="item.remarkFile"
                  sence="quality"
                  :fileTypes="[]"
                  :fileSize="100"
                  @done="
                    (files) => {
                      item.remarkFile = files;
                    }
                  "
                ></form-upload> -->

                <form-upload-contract
                  v-model:value="item.remarkFile"
                  sence="article"
                  :fileTypes="[]"
                  :fileSize="100"
                  :readonly="butType == 'detail'"
                  hasDownLoad
                  :fileLName="true"
                  :fileLimit="9999"
                  :detailType="'1'"
                  @del="delFile"
                  :delShow="true"
                >
                </form-upload-contract>
              </div>
            </div>
          </div>
          <div
            class="ml-5 pl-3"
            style="border-left: 1px solid #ccc"
            v-if="item?.orderInfos && item.orderInfos.length"
          >
            关联订单
            <div v-for="(itemInfos, index) in item.orderInfos" :key="index">
              <mw-button
                style="padding: 0"
                type="link"
                @click="poCorrelationOrder('qualityInspection', itemInfos)"
              >
                {{ itemInfos.orderNo }}</mw-button
              >
              <!-- <span @click="poCorrelationOrder">{{ item.orderNo }}</span> -->
              <span
                class="ml-5"
                v-if="
                  props.record.applicantType !== 'plan' &&
                  props.record.applicantType !== 'market'
                "
                >数量{{ itemInfos.purchaseCount }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </mw-drawer>
  <materDetail
    ref="materDetailRef"
    v-model:visible="materDetailVisible"
    :openItem="openItem"
  ></materDetail>
  <bom-drawer
    v-model:visible="bomDrawerVisible"
    :planNo="props.record.id"
    :orderItem="orderItem"
    :orderType="orderType"
  ></bom-drawer>
</template>
<script setup>
import { defineProps, ref, getCurrentInstance, watch, defineEmits } from "vue";
import {
  check,
  reject,
  postWholeReturn,
  qualityTestGetInfo,
} from "@/api/quality.js";
import { subtract } from "@/common/calculationAlgorithm.js";
const { proxy } = getCurrentInstance();
import FormUpload from "@/components/form-upload.vue";
import _cloneDeep from "lodash/cloneDeep";
import { useRoute, useRouter } from "vue-router";
import { formatter8 } from "@/common/validate.js";
// BomDrawer
import BomDrawer from "@/views/production/BomDrawer.vue";
import materDetail from "./materDetail.vue";
import FormUploadContract from "@/components/form-upload-contract.vue";
const bomDrawerVisible = ref(false);
const router = useRouter();
const route = useRoute();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  record: { type: Object, default: () => ({}) },
  title: {
    type: String,
    default: "",
  },
  privateId: {
    type: String,
    default: "",
  },
  sourceDetail: {
    type: String,
    default: "",
  },
  openType: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["update:visible", "finish"]);
const materDetailVisible = ref();
const submitLoading = ref(false);
const spinning = ref(false);
const detail = ref({});
const openItem = ref();
const orderItem = ref();
const orderType = ref();
const openDetail = (val) => {
  materDetailVisible.value = true;
  openItem.value = val;
};
//加法
const addNum = (num1, num2, num3, num4) => {
  var sq1, sq2, sq3, sq4, m;
  try {
    sq1 = num1.toString().split(".")[1].length;
  } catch (e) {
    sq1 = 0;
  }
  try {
    sq2 = num2.toString().split(".")[1].length;
  } catch (e) {
    sq2 = 0;
  }
  try {
    sq3 = num3.toString().split(".")[1].length;
  } catch (e) {
    sq3 = 0;
  }
  try {
    sq4 = num4.toString().split(".")[1].length;
  } catch (e) {
    sq4 = 0;
  }
  m = Math.pow(10, Math.max(sq1, sq2, sq3, sq4));
  return parseFloat((num1 * m + num2 * m + num3 * m + num4 * m) / m);
};
const onClose = () => {
  emit("update:visible", false);
};
const poCorrelationOrder = (type, item) => {
  bomDrawerVisible.value = true;
  orderItem.value = item;
  orderType.value = type;
};
const submitForm = async () => {
  submitLoading.value = true;
  try {
    // 模拟一些异步操作更新数据
    const bb = detail.value;
    bb.detailList?.forEach((item) => {
      if (!item.disqualifiedQuantity) {
        item.disqualifiedQuantity = 0;
      }
      item.passQuantity = item.changeQ ? item.changeQ : 0;
      item.checkQuantityList = [
        {
          type: "1",
          quantity: item.changeQ ? item.changeQ : 0,
        },
        {
          type: "2",
          quantity: item.concessionAcceptance ? item.concessionAcceptance : 0,
        },
        {
          type: "3",
          quantity: item.selectAndUse ? item.selectAndUse : 0,
        },
        {
          type: "4",
          quantity: item.qualifiedRepair ? item.qualifiedRepair : 0,
        },
        {
          type: "5",
          quantity: item.noDisqualifiedQuantity
            ? item.noDisqualifiedQuantity
            : 0,
        },
      ];
    });
    // let { testNo, detailList } = bb;
    let res = await check({
      testNo: bb.testNo,
      detail: bb.detailList,
      applicantType: props.record.applicantType,
      id: props.record.id,
    });
    if (res.code == 200) {
      proxy.$message.success("添加成功");
      onClose();
    }
    submitLoading.value = false;
    emit("finish");
  } catch (error) {
    error.message && proxy.$message.warning(error.message);
    submitLoading.value = false;
  }
};
// 全部驳回
const onWholeReject = async () => {
  let res = await reject({ testNo: props.record.testNo });
  if (res.code == 200) {
    proxy.$message.success("成功驳回");
    onClose();
  }
  emit("finish");
  submitLoading.value = false;

  // reject
};
// 全部退回
// const wholeReturn = async (val) => {
//   try {
//     submitLoading.value = true;
//     let res = await postWholeReturn({
//       detail: val,
//       testNo: detail.value.testNo,
//     });

//     if (res.code == 200) {
//       proxy.$message.success("退回成功");
//       onClose();
//     } else {
//       proxy.$message.error(res.msg);
//     }
//     submitLoading.value = false;
//     emit("finish");
//   } catch (error) {
//     submitLoading.value = false;
//   }
// };
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      // qualityTestGetRecordInfoData(props.record.id);
      props.record.detailList.forEach((item, index) => {
        item.disqualifiedQuantity = 0;
      });
      detail.value = _cloneDeep(props.record);
      detail.value.detailList = detail.value.detailList.filter(
        (item) => item.waitQuantity > 0
      );
      detail.value.detailList.forEach((item, index) => {
        item.changeQuantitys = item.changeQuantity;
        item.waitQuantity = item.waitQuantity;
        (item.remark =
          props.record.source == "plan_residue"
            ? item.remark
            : item.materialRemark),
          // 质检总数
          (item.changeQ = item.waitQuantity);
        item.concessionAcceptance = 0;
        item.selectAndUse = 0;
        item.qualifiedRepair = 0;
        item.disqualifiedQuantity = 0;
        item.noDisqualifiedQuantity = 0;

        // item.remarkFile = item.remarkFile ? [item.remarkFile] : [];
        item.remarkFile = [];
      });
    }
  }
);
</script>
<style lang="less" scoped>
.bg {
  height: 40px;
  line-height: 40px;
  background: rgba(34, 34, 34, 0.08);
  border-radius: 8px;
  text-align: center;
  margin-bottom: 24px;
}
</style>
