<template>
  <div class="mb-4 text-right">
    <search :searchData="searchData" @search="refresh">
      <mw-button @click="onOpen()"> 工作通知订阅 </mw-button>
    </search>
  </div>
  <mw-table
    :scroll="{ x: 'max-content' }"
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'operateType'">
        {{
          operateTypeList.find((item) => item.value == record.operateType)
            ?.label || ""
        }}
      </template>
      <template v-if="column.key == 'operation'">
        <mw-button @click="onSubscriptionDetails(record)">详情 </mw-button>
      </template>
    </template>
  </mw-table>
  <!-- </div> -->
  <subscriptionNew
    v-model:visible="subscriptionNewVisible"
    @finish="getList()"
    @finishType="noticeGeNoticeTypeList()"
  ></subscriptionNew>
  <subscriptionDetails
    v-model:visible="subscriptionDetailsVisible"
    :recordDetail="recordDetail"
  ></subscriptionDetails>
</template>
<script setup>
import Search from "@/components/search/index.vue";
import { usePagenation } from "@/common/setup";
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import subscriptionDetails from "./subscriptionDetails.vue";
import subscriptionNew from "./subscriptionNew.vue";
import { operateTypeList } from "@/common/constant.js";
import {
  noticePage,
  noticeGetUserNoticeType,
} from "@/api/workbench/subscriptionNotice.js";
const { proxy } = getCurrentInstance();
const loading = ref(false);
const data = ref([]);
const recordDetail = ref();
const noticeGeNoticeTypeData = ref();
const subscriptionNewVisible = ref(false);
const subscriptionDetailsVisible = ref(false);
const showTable = ref();

const searchData = ref({
  searchButtons: [],
  operationButtons: [],
  fields: {
    noticeTypeId: {
      name: "全部",
      type: "a-select",
      options: noticeGeNoticeTypeData,
      placeholder: "全部",
      width: "220px",
      fieldNames: {
        label: "noticeType",
        value: "id",
      },
      value: "",
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: "请输入父关联编码/关联编号",
      width: "250px",
      allowClear: true,
    },
  },
});
const columns = ref([
  {
    title: "工作通知类型",
    key: "noticeType",
    dataIndex: "noticeType",
  },
  {
    title: "事件类型",
    key: "operateType",
    dataIndex: "operateType",
  },
  {
    title: "父关联编码",
    key: "parentApplicationNo",
    dataIndex: "parentApplicationNo",
    customRender: ({ record }) => {
      return record.parentApplicationNo || "-";
    },
  },
  {
    title: "关联编码",
    key: "applicationNo",
    dataIndex: "applicationNo",
    customRender: ({ record }) => {
      return record.applicationNo || "-";
    },
  },
  {
    title: "概述",
    key: "modifyTitle",
    dataIndex: "modifyTitle",
  },
  {
    title: "操作人",
    key: "createByName",
    dataIndex: "createByName",
  },
  {
    title: "操作时间",
    key: "createTime",
    dataIndex: "createTime",
  },
  {
    title: "操作",
    key: "operation",
    fixed: "right",
  },
]);
// g
const noticeGeNoticeTypeList = async () => {
  let res = await noticeGetUserNoticeType();
  console.log(res, "类型");
  noticeGeNoticeTypeData.value = [{ noticeType: "全部", id: "" }, ...res?.data];
};
// 列表
const getList = async () => {
  loading.value = true;
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let result = await noticePage({ ...pageParam.value }, searchParam);
  console.log(result, "result");
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
onBeforeMount(async () => {
  paginationProps.value.pageSize = 10;
  // const btnList = JSON.parse(localStorage.getItem("perms")) || [];
  // let data = "work:notice:page";
  // showTable.value = btnList.includes(data);
  await noticeGeNoticeTypeList();
  await getList();
});
// 工作通知订阅
const onOpen = () => {
  subscriptionNewVisible.value = true;
};
//详情
const onSubscriptionDetails = (val) => {
  recordDetail.value = val;
  subscriptionDetailsVisible.value = true;
};
</script>

<style lang="scss" scoped></style>
