<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="30%"
    :customTitle="'工作通知订阅'"
  >
    <template #header>
      <mw-button @click="formSubmit" :loading="loading">提交</mw-button>
    </template>
    <a-checkbox-group
      class="flex flex-col justify-center"
      v-model:value="checkedList"
      :options="noticeGeNoticeTypeData"
    />
  </mw-drawer>
</template>
<script setup>
import { ref, getCurrentInstance, defineProps, defineEmits, watch } from "vue";
import _cloneDeep from "lodash/cloneDeep";
const emit = defineEmits([
  "update:visible",
  "finish",
  "update:value",
  "finishType",
]);
import {
  noticeSubscription,
  noticeGeNoticeType,
  noticeGetUserNoticeType,
} from "@/api/workbench/subscriptionNotice.js";
const checkedList = ref();
const noticeGeNoticeTypeData = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const { proxy } = getCurrentInstance();

const loading = ref(false);
// 支持订阅的全部数据列表
const noticeGeNoticeTypeList = async () => {
  let res = await noticeGeNoticeType();
  noticeGeNoticeTypeData.value = res.data.map((item) => {
    return { label: item.noticeType, value: item.id };
  });
};

// 订阅过的数据列表
const noticeGetUserNoticeTypeList = async () => {
  let res = await noticeGetUserNoticeType();
  checkedList.value = res.data.map((item) => item.id);
};
// 确认新增
const formSubmit = async () => {
  try {
    console.log(checkedList.value, "新增的参数");
    let res = await noticeSubscription({ noticeTypeId: checkedList.value });
    if (res.code == 200) {
      onClose();
      proxy.$message.success("订阅成功");
      emit("finish");
      emit("finishType");
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
  }
};
function onClose() {
  checkedList.value = [];
  emit("update:visible", false);
}

watch(
  () => props.visible,
  async (val) => {
    if (val) {
      await noticeGeNoticeTypeList();
      await noticeGetUserNoticeTypeList();
      if (props.id) {
        getDetail(props.id);
      }
    }
  }
);
</script>
<style lang="less" scoped></style>
