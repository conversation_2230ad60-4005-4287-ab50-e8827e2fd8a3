<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="30%"
    :customTitle="'订阅详情'"
  >
    <a-descriptions
      :colon="false"
      :column="1"
      :labelStyle="{ width: '110px', color: 'rgba(31,31,31,.65)' }"
      :contentStyle="{ color: 'rgba(31,31,31,.65)' }"
    >
      <a-descriptions-item label="工作通知类型：">{{
        formData?.noticeType
      }}</a-descriptions-item>
      <a-descriptions-item label="事件类型：">
        {{
          operateTypeList.find((item) => item.value == formData.operateType)
            ?.label || ""
        }}
      </a-descriptions-item>
      <a-descriptions-item label="父关联编码">
        {{ formData.parentApplicationNo || "-" }}
      </a-descriptions-item>
      <a-descriptions-item label="关联编码">
        {{ formData.applicationNo || "-" }}
      </a-descriptions-item>
      <a-descriptions-item label="操作人：">{{
        formData?.createByName
      }}</a-descriptions-item>
      <a-descriptions-item label="操作时间：">{{
        formData?.createTime
      }}</a-descriptions-item>
      <a-descriptions-item label="概述：">{{
        formData?.modifyTitle
      }}</a-descriptions-item>
      <a-descriptions-item label="详情：" v-if="recordDetail.operateType !== 2">
        <span v-if="formData.operateType == 4 || formData.operateType == 1">
          <div v-for="line in formData?.modifyAfter?.split('\n')" :key="line">
            {{ line }}
          </div>
        </span>
        <span v-else>{{ formData?.modifyBefore }}</span>
      </a-descriptions-item>

      <a-descriptions-item
        label="修改前："
        v-if="recordDetail.operateType == 2"
      >
        <!-- {{ recordDetail.operateType }} -->

        {{ formData?.modifyBefore }}
      </a-descriptions-item>
      <a-descriptions-item
        label="修改后："
        v-if="recordDetail.operateType == 2"
      >
        {{ formData?.modifyAfter }}
      </a-descriptions-item>
    </a-descriptions>
  </mw-drawer>
</template>
<script setup>
import { ref, defineProps, defineEmits, watch } from "vue";
import _cloneDeep from "lodash/cloneDeep";
import { operateTypeList } from "@/common/constant.js";
const emit = defineEmits(["update:visible", "finish", "update:value"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  recordDetail: {
    type: Object,
    default: {},
  },
});
const loading = ref(false);
const formData = ref({});
function onClose() {
  emit("update:visible", false);
}
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      formData.value = props.recordDetail;
    }
  }
);
</script>
<style lang="less" scoped></style>
