<template>
  <div class="video-box">
    <img class="video-background" :src="$appConstant.login.background" alt="" />
  </div>
  <div
    class="w-full h-screen flex flex-col justify-between items-center box absolute p-8"
    style="min-width: 1200px"
  >
    <div class="w-full login-box flex">
      <div class="">
        <div
          class="px-8 py-15 rounded-2xl bg-ff"
          style="width: 384px; height: 540px"
        >
          <div
            v-if="!isInDingtalk || thirdUserIdentify"
            class="body-inner h-full relative my-0 mx-auto"
          >
            <span v-if="thirdUserIdentify"> 绑定手机号</span>
            <a-radio-group v-else v-model:value="loginType">
              <a-radio-button value="phone">账号登录</a-radio-button>
              <a-radio-button value="qrcode">二维码登录</a-radio-button>
            </a-radio-group>
            <phone-login
              v-if="loginType == 'phone'"
              :thirdUserIdentify="thirdUserIdentify"
              :corpId="corpId"
              :isInDingtalk="isInDingtalk"
            />
            <div v-show="loginType == 'qrcode'" id="dingTalkLogin"></div>
          </div>
          <div v-else class="h-full flex items-center justify-center">
            <span v-if="loginError">{{ loginError }}</span>
            <a-spin v-else tip="正在登录，请稍候..." size="large" />
          </div>
        </div>
      </div>
      <div class="flex-1 px-16 py-15" :class="$appConstant.login.textColor">
        <div class="text-2xl mb-2 font-medium pl-1">
          {{ $appConstant.login.title1 }}
        </div>
        <div class="text-5xl font-bold pl-1">
          {{ $appConstant.login.title2 }}
        </div>
        <div
          class="mt-6 text-base"
          :class="$appConstant.login.textColor"
          style="line-height: 34px"
        >
          <p>{{ $appConstant.login.p1 }}</p>
          <p>{{ $appConstant.login.p2 }}</p>
          <p>{{ $appConstant.login.p3 }}</p>
          <p>{{ $appConstant.login.p4 }}</p>
        </div>
      </div>
    </div>
    <div
      class="footer pt-5 space-x-2 text-center text-59"
      :class="$appConstant.login.textColor"
    >
      <span>{{ $appConstant.copyRight }}</span>
      <span
        >版权所有 |
        <a
          href="https://beian.miit.gov.cn/#/Integrated/index"
          target="_blank"
          >{{ $appConstant.beian }}</a
        ></span
      >
    </div>
  </div>
</template>
<script>
import PhoneLogin from "./phoneLogin.vue";
import { domain } from "@/config/env";
import {
  reactive,
  toRefs,
  computed,
  watch,
  onMounted,
  onBeforeMount,
  getCurrentInstance,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import { env, runtime } from "dingtalk-jsapi";
import { dingTalkLoginApi } from "@/utils/util.js";
import { DDFreeAuthenLogin, DDFreeAuthenLoginNoCropId } from "@/api/login.js";
import { useUserStore } from "@/stores/user.js";
import { getToken } from "@/utils/auth.js";
export default {
  components: { PhoneLogin },
  setup() {
    const store = useUserStore();
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    const router = useRouter();
    const isInDingtalk = computed(() => {
      return env.platform != "notInDingTalk";
    });
    const state = reactive({
      loginType: isInDingtalk.value ? "qrcode" : "phone",
      suitekey: "suiteshuuw3ihmyyf1oyn",
      dingTalkRedirectUrl: encodeURIComponent(
        import.meta.env.VITE_DOMAIN + "/dingTalkCallback"
      ),
      dingTalkIdentify: "",
      corpId: "",
      loginError: "",
    });
    const thirdUserIdentify = computed(() => {
      return route.query.thirdUserIdentify || state.dingTalkIdentify;
    });
    watch(
      // () => route.query.thirdUserIdentify,
      thirdUserIdentify,
      (val) => {
        if (val) {
          state.loginType = "phone";
        }
      },
      { immediate: true }
    );
    const dingTalkLoginInit = () => {
      console.log("事件走进来了");
      dingTalkLoginApi().then((DTFrameLogin) => {
        console.log(DTFrameLogin, "参数");
        DTFrameLogin(
          {
            id: "dingTalkLogin", //这里需要你在自己的页面定义一个HTML标签并设置id
            width: "300",
            height: "400",
          },
          {
            redirect_uri: state.dingTalkRedirectUrl,
            client_id: state.suitekey,
            scope: "openid",
            response_type: "code",
            state: "dddd",
            prompt: "consent",
          },
          async (loginResult) => {
            console.log(loginResult, "登录结果");
            // debugger;
            // 这里一般需要展示登录成功后的页面
            // 这里一般需要展示登录成功后的页面
            const { redirectUrl, authCode, state } = loginResult;
            // debugger;
            let result = await DDFreeAuthenLoginNoCropId(authCode);
            store.setLoginData(result);
            // 这里可以直接进行重定向
            // window.location.href = redirectUrl;
            // 也可以在不跳转页面的情况下，使用code进行授权
          },
          (errorMsg) => {
            // 这里一般需要展示登录失败的具体原因
            alert(`Login Error: ${errorMsg}`);
          }
        );
      });
    };
    onBeforeMount(async () => {
      if (isInDingtalk.value) {
        await dingTalkLogin();
      }
    });
    onMounted(() => {
      if (!isInDingtalk.value) {
        dingTalkLoginInit();
      }
    });

    const userStore = useUserStore();
    const dingTalkLogin = async () => {
      return new Promise((resolve, reject) => {
        let corpId;
        let appId;
        let type;
        let dingtalkCorpId = localStorage.getItem("dingtalkCorpId");
        let params = dingtalkCorpId.split("&");
        corpId = params[0];
        appId = params[1].split("=")[1];
        type = params[2].split("=")[1];
        runtime.permission.requestAuthCode({
          corpId,
          appId,
          type,
          onSuccess: async function (res) {
            try {
              let result = await DDFreeAuthenLogin(
                res.code,
                corpId,
                appId,
                type
              );
              console.log(result, "result测试钉钉登录返回的什么");
              let { code, dingUserId } = result.data;
              if (code == "6666") {
                state.dingTalkIdentify = dingUserId;
                state.corpId = corpId;
              } else {
                const { redirect = "" } = route.query;
                store.setLoginData(result, redirect);
              }
              //
            } catch (error) {
              console.log("测试是否走了失败");
              alert("test01");
              state.loginError = "登录异常，请联系管理员";
            }
          },
          onFail: function (err) {
            console.log("测试是否走了失败");

            state.loginError = "登录异常，请联系管理员";
            console.log(err);
            reject(err);
          },
        });
      });
    };
    return { ...toRefs(state), domain, thirdUserIdentify, isInDingtalk };
  },
};
</script>
<style lang="less" scoped>
.header {
  // margin-bottom: 30px;
  div {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  span {
    color: #595959;
  }
}
.wxlogin-container {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
}
#dingTalkLogin {
  width: 300px;
  height: 300px;
}
.login-box {
  width: 1200px;
  // height: calc(~"100vh - 160px");
  // height: 540px;
  margin: auto;
  // overflow: hidden;
  border-radius: 8px;
}

.video-box {
  position: relative;
  height: 100vh;
  /*进行视频裁剪*/
  overflow: hidden;
  min-width: 1200px;
  min-height: 760px;
}

.video-box .video-background {
  position: absolute;
  left: 50%;
  top: 50%;
  /*保证视频内容始终居中*/
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  /*保证视频充满屏幕*/
  object-fit: cover;
  min-height: 800px;
}
</style>
