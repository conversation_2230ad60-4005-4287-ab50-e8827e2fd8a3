<template>
  <!-- <div class="text-center">
    <img :src="logoUrl" alt="" srcset="" style="width: 220px" />
  </div> -->
  <a-form
    :model="formState"
    ref="formRef"
    :rules="rules"
    class="mw-form"
    :colon="false"
  >
    <a-form-item name="phone" :validateFirst="true">
      <a-input
        size="large"
        autocomplete="off"
        v-model:value="formState.phone"
        placeholder="手机号"
        @keyup.enter="onSubmit"
      >
        <template #prefix>
          <i class="iconfont icon-ej-10"></i>
        </template>
      </a-input>
    </a-form-item>
    <a-form-item
      name="type"
      :validateFirst="true"
      v-if="optionsType.length > 1 && !isInDingtalk"
    >
      <a-select
        v-model:value="formState.smsType"
        placeholder="请选择企业"
        allow-clear
        optionFilterProp="tenantName"
        :fieldNames="{
          label: 'tenantName',
          value: 'index',
        }"
        :options="optionsType"
        showSearch
        @change="onChangeType"
      >
      </a-select>
    </a-form-item>
    <a-form-item name="smsCode" :validateFirst="true">
      <sms-code
        :phone="formState.phone"
        v-model:value="formState.smsCode"
        @onChangeCode="onChangeCode"
        @keyup.enter="onSubmit"
        smsVerifyType="login"
        size="large"
        :thirdUserIdentify="thirdUserIdentify"
        :type="formState.smsType"
        :agentIdVal="agentIdVal"
        :isInDingtalk="isInDingtalk"
        ><i class="iconfont icon-ej-11"></i
      ></sms-code>
      <!-- :optionsLength="optionsType.length" -->
    </a-form-item>
  </a-form>
  <p
    style="color: #ff4d4f; margin-top: 0; font-size: 12px; margin-left: 12px"
    v-show="agreePolicyTip"
  >
    您还未接受协议
  </p>
  <div class="login-bar">
    <a-button
      shape="round"
      type="primary"
      @click="onSubmit"
      :loading="loading"
      size="large"
      style="width: 100%; margin-bottom: 20px"
      >登录</a-button
    >
    <div class="login-policy">
      <a-checkbox
        @change="handleAgreePolicyChange"
        v-model:checked="agreePolicy"
        size="small"
        style="margin-right: 4px"
      ></a-checkbox
      ><span class="policy-text">同意 </span>
      <router-link
        target="_blank"
        to="/agreement-service"
        tag="a"
        class="content-clause-color"
        >《服务条款》</router-link
      >
      <span class="policy-text">和</span
      ><router-link
        target="_blank"
        to="/agreement-privacy"
        tag="a"
        class="content-clause-color"
        >《隐私政策》</router-link
      >

      <span class="policy-text">完成注册/登录</span>
    </div>
  </div>
</template>
<script>
import {
  defineComponent,
  reactive,
  toRaw,
  ref,
  toRefs,
  computed,
  getCurrentInstance,
} from "vue";
import { DDUpdateMobile } from "@/api/login.js";
// import graphCode from "./graphCode";
import smsCode from "@/components/smsCode.vue";
import { useRouter, useRoute } from "vue-router";
import { checkPhone, checkPassword } from "@/common/validate.js";
// import { env } from 'dingtalk-jsapi'
import { useUserStore } from "@/stores/user.js";
import logoUrl from "@/assets/logo.svg";
export default defineComponent({
  components: {
    // graphCode
    smsCode,
  },
  props: {
    thirdUserIdentify: String,
    corpId: String,
    isInDingtalk: String,
  },
  setup(props) {
    const { proxy } = getCurrentInstance();
    const { thirdUserIdentify, corpId } = toRefs(props);
    const router = useRouter();
    const route = useRoute();
    const formRef = ref();
    const optionsType = ref([]);
    const agentIdVal = ref();
    const agentId = ref();
    const isInDingtalk = computed(() => {
      return env.platform != "notInDingTalk";
    });
    const state = reactive({
      agreePolicyTip: false,
      loading: false,
      agreePolicy: false,
    });
    const tenantId = ref();
    const formState = reactive({
      loginType: 2,
      phone: undefined,
      smsCode: undefined,
      smsType: undefined,
      //   isSave: true,
      //   saveTime: 15,
    });
    const graphCodeRef = ref(null);
    const store = useUserStore();

    const onChangeType = (e) => {
      // optionsType.value.forEach((item, index) => {
      //   if (item.tenantId == e) {
      //     agentIdVal.value = item.agentId;
      //   }
      // });
    };
    const searchfun = (e) => {};
    const onSubmit = () => {
      formRef.value
        .validate()
        .then(async () => {
          if (!state.agreePolicy) {
            state.agreePolicyTip = true;
          } else {
            state.loading = true;
            // try {
            let identify = thirdUserIdentify.value;
            if (identify) {
              //扫码登录绑定手机号
              let param = {
                mobile: formState.phone,
                dingUserId: identify,
                corpId: corpId.value, //不知道为啥要corpId
                smsCode: formState.smsCode,
              };
              let result = await DDUpdateMobile(param);
              //TODO这边应该是可以拿到登录信息，需要测试
              store.setLoginData(result);
              state.loading = false;
            } else {
              // 手机验证码登录
              await store.login(
                {
                  username: formState.phone,
                  password: formState.smsCode,
                  agentId: agentId.value,
                },
                // formState.smsType
                tenantId.value
              );
              state.loading = false;
            }
            // } catch (error) {
            //   //表单提交错误要刷新图片验证码
            //   // graphCodeRef.value.onChangeCode();
            //   state.loading = false;
            // }
          }
        })
        .catch((error) => {
          state.loading = false;
          state.agreePolicyTip = false;
        });
    };
    const resetForm = () => {
      formRef.value.resetFields();
    };

    const rules = {
      phone: [
        {
          required: true,
          message: "请填写手机号码",
          trigger: "blur",
        },
        {
          validator: checkPhone,
          trigger: "blur",
        },
      ],
      smsCode: [
        {
          required: true,
          message: "请填写短信验证码",
          trigger: "blur",
        },
      ],
      // password: [
      //   {
      //     validator: checkPassword,
      //     trigger: "blur",
      //   },
      // ],
      // graphCode: [
      //   {
      //     required: true,
      //     message: "请填写验证码",
      //     trigger: "blur",
      //   },
      // ],
    };
    const onChangeCode = async (callback, id, agentIdVal, tenantIdVal) => {
      if (id) {
        //代表选择企业的id
        formState.smsType = id;
        agentId.value = agentIdVal; //
      }
      tenantId.value = tenantIdVal; //企业
      await formRef.value.validate("phone");

      optionsType.value = callback.data;
      // callback();
    };
    const handleAgreePolicyChange = (e) => {
      if (e.target.checked) {
        state.agreePolicyTip = false;
      }
    };
    return {
      ...toRefs(state),
      formRef,
      formState,
      onSubmit,
      resetForm,
      rules,
      onChangeCode,
      graphCodeRef,
      logoUrl,
      handleAgreePolicyChange,
      optionsType,
      onChangeType,
    };
  },
});
</script>
<style lang="less" scoped>
.mw-form {
  margin-top: 60px;
}
</style>
