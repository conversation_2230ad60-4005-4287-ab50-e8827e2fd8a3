<template>
  <div class="mb-4 text-right">
    <search :searchData="searchData" @search="refresh">
      <mw-button
        @click="exportOrder"
        :loading="exportLoading"
        :disabled="!data?.length"
      >
        导出Excel
      </mw-button>
    </search>
  </div>
  <a-table
    :scroll="{ x: 'max-content' }"
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :row-key="(record) => record.bomNo"
    @change="onTableChange"
    :pagination="{ ...paginationProps, position: ['bottomCenter'] }"
    :expandedRowKeys="expandedRowKeys"
    @expand="onExpand"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex == 'materialTypeCount'">
        <div class="">{{ record.materialTypeCount || 0 }}种</div>
      </template>

      <template v-if="column.key == 'button'">
        <mw-button
          title="详情"
          @click="onOpenDetail(record.bomNo)"
          v-permission="'production:bomMarketOrder:detail'"
        ></mw-button>
      </template>
    </template>
    <template #expandedRowRender="{ record }">
      <div>
        <div v-if="record.loading">
          <a-spin />
        </div>
        <a-table
          v-else
          :columns="innerColumns"
          :data-source="record.childrenTable || []"
          :pagination="false"
          :row-key="(r) => r.materialNo"
          :locale="{ emptyText: '关联产品列表为空。' }"
        />
      </div>
    </template>
  </a-table>
  <!-- 新增 -->
  <add-contract-review-drawer
    ref="refAddContractReviewDrawer"
    v-model:visible="visibleAddContractReviewDrawer"
    @finish="getList"
    v-model:id="contractReviewId"
  />
  <detail-contract-review-drawer
    ref="refVisibleDetail"
    v-model:visible="visibleDetail"
    v-model:id="contractReviewId"
  />
</template>
<script setup>
import Search from "@/components/search/index.vue";
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import { usePagenation } from "@/common/setup";
import addContractReviewDrawer from "./addContractReviewDrawer.vue";
import detailContractReviewDrawer from "./detailContractReviewDrawer.vue";
import {
  getBomMarketOrderPage,
  exportOrderBom,
  getBomMarketOrderDetail,
} from "@/api/basicData/orderBomChange.js";
import { useRouter } from "vue-router";
import {
  orderBOMType,
  wholeType,
  orderTypeList,
  orderBomIsLockedList,
  productMarketClassificationList,
} from "@/common/constant.js";
import { exportExecl } from "@/utils/util.js";
// import { Table } from "ant-design-vue";

const router = useRouter();

const expandedRowKeys = ref([]);

const innerColumns = [
  { title: "产品编码", dataIndex: "materialNo", key: "materialNo", width: 200 },
  {
    title: "产品名称",
    dataIndex: "materialName",
    key: "materialName",
    width: 200,
  },
  {
    title: "产品规格",
    dataIndex: "materialSpecification",
    key: "materialSpecification",
    width: 200,
  },
  {
    title: "订单台数",
    dataIndex: "orderQuantity",
    key: "orderQuantity",
    width: 200,
  },
];

const onExpand = async (expanded, record) => {
  const key = record.bomNo;
  const isCurrentlyExpanded = expandedRowKeys.value.includes(key);

  // Fetch data only when expanding a row for the first time
  if (!isCurrentlyExpanded && record.childrenTable === undefined) {
    record.loading = true;
    try {
      const res = await getBomMarketOrderDetail({ bomNo: record.bomNo });
      let bomProductList = res.data?.bomProductList || [];
      if (bomProductList.length > 0) {
        // 过滤掉 bomName 为"整单数"或"配件数"的项目
        bomProductList = bomProductList.filter(
          (item) => item.bomName !== "整单数" && item.bomName !== "配件数"
        );
      }
      record.childrenTable = bomProductList;
    } catch (error) {
      console.error("Failed to fetch BOM details", error);
      record.childrenTable = []; // set to empty on error
    } finally {
      record.loading = false;
    }
  }
  if (isCurrentlyExpanded) {
    // If it's already expanded, collapse it by removing its key.
    expandedRowKeys.value = expandedRowKeys.value.filter((k) => k !== key);
  } else {
    // If it's not expanded, add its key to the list of expanded keys.
    expandedRowKeys.value.push(key);
  }
};

const columns = ref([
  // Table.EXPAND_COLUMN,
  {
    title: "BOM名称",
    dataIndex: "bomName",
    key: "bomName",
  },
  {
    title: "关联订单编号",
    dataIndex: "marketOrderNo",
    key: "marketOrderNo",
  },
  {
    title: "锁定状态",
    dataIndex: "isLocked",
    key: "isLocked",
    customRender: ({ record }) => {
      if (record.isLocked == 0) {
        return "未锁定";
      } else if (record.isLocked == 1) {
        return record.lockTime
          ? "已锁定" + " ( " + record.lockTime + " ) "
          : "已锁定";
      }
    },
  },
  {
    title: "状态",
    dataIndex: "statusType",
    key: "statusType",
  },
  {
    title: "物料种类",
    dataIndex: "materialTypeCount",
    key: "materialTypeCount",
  },
  {
    title: "业务所属人",
    dataIndex: "bizBelongUserName",
    key: "bizBelongUserName",
  },
  {
    title: "客户代码",
    dataIndex: "customerNo",
    key: "customerNo",
  },

  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
  },
  {
    title: "备注",
    dataIndex: "remark",
    key: "remark",
  },
  {
    title: "操作",
    dataIndex: "button",
    key: "button",
    fixed: "right",
  },
]);

const searchData = ref({
  searchButtons: [],
  operationButtons: [],
  fields: {
    status: {
      name: "产品分类",
      type: "a-select",
      options: [...wholeType, ...orderBOMType],
      placeholder: "选择状态",
      width: "120px",
      allowClear: true,
    },
    isLocked: {
      name: "锁定状态",
      type: "a-select",
      options: [...wholeType, ...orderBomIsLockedList],
      placeholder: "锁定状态",
      width: "120px",

      allowClear: true,
    },
    orderType: {
      name: "订单类型",
      type: "a-select",
      options: [
        ...wholeType,
        ...orderTypeList,
        {
          label: "配件订单",
          value: "ACCESSORY_ORDER",
        },
      ],
      placeholder: "订单类型",
      width: "120px",
      // value: "",
      allowClear: true,
    },
    orderNo: {
      type: "a-input-search",
      placeholder: "输入订单号",
      width: "200px",
      allowClear: true,
    },
    rangeDate: {
      type: "a-range-picker",
      valueFormat: "YYYY-MM-DD",
      value: [],
      width: "240px",
    },
    bizBelongUser: {
      type: "a-input-search",
      placeholder: "业务归属人",
      width: "200px",
      allowClear: true,
    },
    customer: {
      type: "a-input-search",
      placeholder: "客户名称/编码",
      width: "200px",
      allowClear: true,
    },
    productMarketClass: {
      name: "产品市场分类",
      type: "a-select",
      options: [...wholeType, ...productMarketClassificationList],
      placeholder: "产品市场分类",
      width: "200px",

      allowClear: true,
    },
    materialNo: {
      type: "a-input-search",
      placeholder: "产品编码",
      width: "200px",
      allowClear: true,
    },
    materialName: {
      type: "a-input-search",
      placeholder: "产品名称",
      width: "200px",
      allowClear: true,
    },
    specification: {
      type: "a-input-search",
      placeholder: "产品规格",
      width: "200px",
      allowClear: true,
    },
    fileNumber: {
      type: "a-input-search",
      placeholder: "技术规格书",
      width: "200px",
      allowClear: true,
    },
  },
});

const { proxy } = getCurrentInstance();
const visibleDetail = ref(false);
const visibleAddContractReviewDrawer = ref(false);

const contractReviewId = ref("");
const data = ref([]),
  loading = ref(false);
const refAddContractReviewDrawer = ref(null);
const getList = async () => {
  loading.value = true;

  // 刷新时关闭所有展开的行并清除缓存数据
  expandedRowKeys.value = [];

  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await getBomMarketOrderPage({ ...pageParam.value }, searchParam);

  // 清除每个记录的展开缓存数据
  data.value = result.data.map((record) => {
    // 移除可能存在的展开相关属性
    const { childrenTable, loading: recordLoading, ...cleanRecord } = record;
    return cleanRecord;
  });

  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

const onOpen = (valId) => {
  contractReviewId.value = valId;
  visibleAddContractReviewDrawer.value = true;
};

const onOpenDetail = (valId) => {
  router.push({ name: "OrderBomDetail", query: { id: valId } });
  // contractReviewId.value = valId;
  // visibleDetail.value = true;
};

async function onConfirm(record) {
  getList();
}
const onCancel = (record) => {
  // message.error('Click on No');
};
const exportLoading = ref(false);
const exportOrder = async () => {
  exportLoading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
    searchParam.rangeDate = undefined;
  } else {
    searchParam.rangeDate = undefined;
  }
  console.log("[ searchParam ] >", searchParam);
  let result = await exportOrderBom({
    ...searchParam,
  });
  const fileName = "订单物料详情导出.xlsx";
  exportExecl(fileName, result);
  exportLoading.value = false;
};
</script>

<style scoped>
.inner-product-list {
  padding: 16px;
  background-color: #fafafa;
}
.inner-product-header,
.inner-product-row {
  display: flex;
  padding: 8px;
  border-bottom: 1px solid #e8e8e8;
}
.inner-product-header {
  font-weight: bold;
}
.inner-product-row:last-child {
  border-bottom: none;
}
.inner-product-cell {
  flex: 1;
  padding: 0 8px;
  word-break: break-word;
}
</style>
