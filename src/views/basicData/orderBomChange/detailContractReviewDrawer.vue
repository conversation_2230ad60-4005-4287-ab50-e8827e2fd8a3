<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="60%"
    :customTitle="'订单BOM变更详情'"
  >
    <template #header> </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      labelAlign="left"
      :label-col="{ span: 7, offset: 1 }"
    >
      <a-row>
        <a-col :span="12">
          <a-form-item
            label="单据编号"
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
          >
            <a-input v-model:value="formData.recordNo" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
            label="申请人"
            name="applicant"
            required
          >
            <a-select
              disabled
              v-model:value="formData.applicant"
              show-search
              placeholder="请选择负责人"
              style="width: 100%"
              :default-active-first-option="false"
              :options="allUserList"
              optionFilterProp="nickName"
              :fieldNames="{
                label: 'nickName',
                value: 'userId',
              }"
              @change="handleChangeUser"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="申请日期"
            name="applicationDate"
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
            required
          >
            <a-date-picker
              disabled
              v-if="!planNo"
              v-model:value="formData.applicationDate"
              style="width: 100%"
              valueFormat="YYYY-MM-DD"
            />
            <span v-else>{{ formData.applicationDate }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
            label="关联-销售订单"
            name="orderId"
            required
          >
            <a-select
              disabled
              placeholder="请选择关联-销售订单"
              :options="postNoContractReviewListOptions"
              v-model:value="formData.orderId"
              optionFilterProp="name"
              @change="handleChange"
              :field-names="{
                label: 'orderName',
                value: 'id',
              }"
              show-search
            >
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item
            label="是否是BOM增料"
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
            required
            name="insertFlag"
            placeholder="请选择是否是BOM增料"
          >
            <a-radio-group v-model:value="formData.insertFlag" disabled>
              <a-radio :value="0">否</a-radio>
              <a-radio :value="1">是</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="是否BOM删料"
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
            required
            name="removeFlag"
            placeholder="请选择是否BOM删料"
          >
            <a-radio-group v-model:value="formData.removeFlag" disabled>
              <a-radio :value="0">否</a-radio>
              <a-radio :value="1">是</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item
            label="变更说明"
            :label-col="{ span: 0, offset: 1 }"
            required
            name="remark"
          >
            <a-textarea
              disabled
              style="width: 100%"
              v-model:value="formData.remark"
              placeholder="请输入变更说明"
              allow-clear
            />
          </a-form-item>
        </a-col>
        <!--增料订单产品BOM明细 formData.insertFlag -->
        <a-col :span="24" v-if="formData.insertFlag == 1">
          <div>
            <a-form-item
              label="增料订单产品BOM明细 "
              :label-col="{ span: -20, offset: -10 }"
              :required="formData.insertFlag == 1 ? true : false"
              name="insertParams"
            >
            </a-form-item>
          </div>
          <a-table
            bordered
            :data-source="formData.insertParams"
            :columns="columnsInsertParams"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'productId'">
                <a-select
                  disabled
                  show-search
                  style="width: 100%"
                  placeholder="请选择产品分类"
                  :options="insertParamsListDataListData"
                  v-model:value="record.productId"
                  :field-names="{
                    label: 'productName',
                    value: 'id',
                  }"
                >
                </a-select>
              </template>
              <template v-if="column.dataIndex === 'unitPrice'">
                <a-input-number
                  disabled
                  style="width: 100%"
                  id="inputNumber"
                  v-model:value="record.unitPrice"
                  :stringMode="true"
                />
              </template>
              <template v-if="column.dataIndex === 'quantity'">
                <a-input-number
                  disabled
                  style="width: 100%"
                  id="inputNumber"
                  v-model:value="record.quantity"
                  placeholder="请输入"
                  :stringMode="true"
                />
              </template>
              <template v-if="column.dataIndex === 'amount'">
                <div v-if="record.unitPrice && record.quantity">
                  {{ (record.amount = record.unitPrice * record.quantity) }}
                </div>
                <div v-else></div>
              </template>
              <template v-if="column.dataIndex === 'isNeedDebugging'">
                <a-textarea
                  disabled
                  style="width: 100%; height: 10px"
                  v-model:value="record.isNeedDebugging"
                  placeholder="请输入"
                  allow-clear
                />
              </template>
              <template v-if="column.dataIndex === 'productUse'">
                <a-textarea
                  disabled
                  style="width: 100%; height: 10px"
                  v-model:value="record.productUse"
                  placeholder="请输入"
                  allow-clear
                />
              </template>
              <template v-if="column.dataIndex === 'operation'">
                <a-popconfirm title="确定是否删除" @confirm="onDelete(record)">
                  <mw-button
                    title="删除"
                    danger
                    v-if="formData.insertParams.length > 1"
                  ></mw-button>
                </a-popconfirm>
              </template>
            </template>
          </a-table>
        </a-col>
        <!-- 删除订单产品BOM明细 -->
        <a-col
          :span="24"
          v-if="formData.removeFlag == 1 || formData.removeFlag == '1'"
        >
          <div>
            <a-form-item
              label="删除订单产品BOM明细"
              :label-col="{ span: -20, offset: -10 }"
              :required="formData.removeFlag == 1 ? true : false"
              name="removeParams"
            >
            </a-form-item>
          </div>
          <a-table
            bordered
            :data-source="formData.removeParams"
            :columns="columnsRemoveParams"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex == 'materialId'">
                <a-select
                  disabled
                  show-search
                  style="width: 100%"
                  placeholder="请选择产品分类"
                  :options="removeParamsListData"
                  v-model:value="record.materialId"
                  @change="onRemoveParamsChange($event, record)"
                  :field-names="{
                    label: 'materialName',
                    value: 'materialId',
                  }"
                >
                </a-select>
              </template>
              <template v-if="column.dataIndex === 'requiresMaterialQuantity'">
                <a-input-number
                  disabled
                  style="width: 100%"
                  id="inputNumber"
                  v-model:value="record.requiresMaterialQuantity"
                  placeholder="请输入"
                  :stringMode="true"
                />
              </template>
              <template v-if="column.dataIndex === 'operation'">
                <a-popconfirm title="确定是否删除" @confirm="onDelete(record)">
                  <mw-button
                    title=""
                    danger
                    v-if="formData.insertParams.length > 1"
                    disabled
                  ></mw-button>
                </a-popconfirm>
              </template>
            </template>
          </a-table>
        </a-col>
        <a-col :span="24">
          <a-form-item label="其他备注" :label-col="{ span: 0, offset: 1 }">
            <a-textarea
              disabled
              style="width: 100%"
              v-model:value="formData.otherRemark"
              placeholder="请输入其他备注"
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
  defineProps,
  defineEmits,
  watch,
} from "vue";
import { postCustomerPage, productList } from "@/api/proSchedul/scheduling.js"; //客户信息
import {
  getOrderModifyRecordInfo,
  getAddMaterialList,
  getRemovelList,
} from "@/api/basicData/orderBomChange.js";
import FormUpload from "@/components/form-upload.vue";
import {
  postNoContractReviewList,
  getSelectUnshippedlist,
} from "@/api/sales/index.js";
import { list as getAllUserList } from "@/api/system/user.js";
import { message } from "ant-design-vue";
const deliveryOrderFiles = ref([]);
// const productListData = ref([]);
const postNoContractReviewListOptions = ref([]);
const allUserList = ref([]);
const emit = defineEmits(["update:visible", "finish"]);

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
});
const { proxy } = getCurrentInstance();
const formRef = ref();
const customerPageList = ref([]);
const submitLoading = ref(false);
const formData = reactive({});

const rules = reactive({
  contractClassify: [
    {
      required: true,
      message: "请选择",
      trigger: "blur",
    },
  ],
});
const columnsRemoveParams = ref([
  {
    title: "选择-删除物料",
    dataIndex: "materialId",
    width: "150px",
  },
  {
    title: "物料编码",
    dataIndex: "materialNo",
    width: "150px",
  },
  {
    title: "物料名称",
    dataIndex: "materialName",
    width: "150px",
  },
  {
    title: "规格型号",
    dataIndex: "unitName",
    width: "150px",
  },
  {
    title: "本次-删除数量",
    dataIndex: "requiresMaterialQuantity",
    width: "150px",
  },
]);

const changeContract = (e) => {};
function onClose() {
  emit("update:visible", false);
  formRef.value.resetFields();
}
//  申请人-获取用户列表
async function getAllUser() {
  let result = await getAllUserList({
    pageNum: 1,
    pageSize: 10000,
    sortType: "create_time",
    sortOrder: "desc",
    // userName: keyword,
  });
  let { data } = result;
  allUserList.value = data;
}
// 客户
const postCustomerPageList = async () => {
  let res = await postCustomerPage();
  customerPageList.value = res.data;
};
// // 产品名称productList
// const postproductList = async () => {
//   let res = await productList();
//   productListData.value = res.data;
// };
// 关联-销售合同
const postNoContractReview = async () => {
  // let param = {
  //   statusList: [0, 1],
  // };
  let res = await getSelectUnshippedlist();
  postNoContractReviewListOptions.value = res.data;
};

// 编辑详情
// contractReviewId
const getDetail = async (val) => {
  let res = await getOrderModifyRecordInfo({ recordNo: val });
  // addMaterialList(res.data.orderId);
  // removelList(res.data.orderId);
  formData.recordNo = res.data.recordNo;
  formData.orderId = String(res.data.orderId);
  formData.orderName = res.data.orderName;
  formData.otherRemark = res.data.otherRemark;
  formData.applicant = Number(res.data.applicant);
  formData.applicantName = res.data.applicantName;
  formData.applicationDate = res.data.applicationDate;
  formData.remark = res.data.remark;
  formData.insertFlag = res.data.insertFlag;
  formData.removeFlag = res.data.removeFlag;
  formData.productId = res.data.productId;
  formData.materialId = res.data.materialId;
  formData.removeParams = res.data.removeParams;

  // deliveryOrderFiles.value = res.data.file;
};

// 产品明细添加
const handleAdd = () => {
  const newData = {
    id: Date.now(),
    productId: "",
    unitPrice: 0.0,
    quantity: "",
    amount: 0.0,
    isNeedDebugging: "",
    productUse: "",
  };
  formData.relationProductList.push(newData);
};
// 产品删除
const onDelete = (val) => {
  formData.relationProductList.forEach((element, index) => {
    if (element.id == val.id) {
      formData.relationProductList.splice(index, 1);
    }
  });
};
// getAddMaterialList

const addMaterialList = async (val) => {
  let res = await getAddMaterialList({ orderId: val });
  formData.insertParams = res.data;
};
const removelList = async (val) => {
  let res = await getRemovelList({ orderId: val });
  formData.removeParams = res.data;
};

watch(
  () => props.visible,
  async (val) => {
    if (val) {
      if (props.id) {
        getDetail(props.id);
      }
      postCustomerPageList();
      // postproductList();

      getAllUser();
      postNoContractReview();
    }
  }
);
</script>
<style lang="less" scoped></style>
