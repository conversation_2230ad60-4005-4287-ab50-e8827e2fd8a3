<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    customTitle="引用BOM"
    width="70%"
  >
    <template #header>
      <mw-button
        title="确定"
        @click="quoteBOMOnSubmitOnSubmit"
        :loading="submitLoading"
      ></mw-button>
    </template>
    <div>
      <div>
        <a-form-item>
          <template #label>
            <div class="w-15 text-left text-primary-text">BOM类型</div>
          </template>
          <a-select
            v-model:value="bomType"
            show-search
            placeholder="请选择BOM"
            :default-active-first-option="false"
            :not-found-content="null"
            :options="bomTypeList"
            optionFilterProp="label"
            :fieldNames="{
              label: 'label',
              value: 'value',
            }"
            @change="bomTypeChange"
          ></a-select>
        </a-form-item>
        <a-form-item v-if="bomType == 'orderBom'">
          <template #label>
            <div class="w-15 text-left text-primary-text">关联订单</div>
          </template>
          <a-select
            v-model:value="orderId"
            show-search
            placeholder="请选择关联订单"
            :default-active-first-option="false"
            :not-found-content="null"
            :options="orderData"
            :filter-option="filterOption"
            :fieldNames="{
              label: 'marketOrderName',
              value: 'marketOrderNo',
            }"
            @change="orderBomOnChange"
            @search="onSearchOrder"
          ></a-select>
        </a-form-item>
        <a-form-item>
          <template #label>
            <div class="w-15 text-left text-primary-text">BOM名称</div>
          </template>
          <a-select
            v-model:value="bomSearchName"
            show-search
            placeholder="请选择BOM"
            style="width: 100%"
            :default-active-first-option="false"
            :not-found-content="null"
            :filter-option="filterOptionBom"
            :options="allBomList"
            :fieldNames="{
              label: 'bomName',
              value: 'bomNo',
            }"
            @change="handleChangBom"
          ></a-select>
        </a-form-item>
        <a-form-item>
          <template #label>
            <div class="w-15 text-left text-primary-text">BOM编码</div>
          </template>
          <div class="underline">
            {{ selectedBom?.bomNo || "-" }}
            <span></span>
          </div>
        </a-form-item>

        <div v-if="bomType == 'orderBom'" class="leading-8">
          <div class="flex">
            <div class="w-15 text-left text-primary-text">公共附件</div>
            <form-upload-contract
              readonly
              class="mr-2"
              :value="selectedBom?.publicFile"
              :fileSize="10"
              sence="orderContract"
              :hasDownLoad="true"
              :fileTypes="[]"
              :multiple="true"
            ></form-upload-contract>
          </div>
          <div class="flex">
            <div class="w-15 text-left text-primary-text">机密附件</div>
            <form-upload-contract
              readonly
              class="mr-2"
              :value="selectedBom?.confidentialFile"
              :fileSize="10"
              sence="orderContract"
              :hasDownLoad="true"
              :fileTypes="[]"
              :multiple="true"
            ></form-upload-contract>
          </div>
        </div>
      </div>
      <mw-table
        :dataSource="bomRelatedMaterialList"
        :columns="columns"
        :pagination="false"
        :row-key="(r) => r.index"
        :scroll="{ x: 'max-content' }"
      >
        <template #bodyCell="{ column, index }">
          <template v-if="column.dataIndex == 'operate'">
            <mw-button title="删除" danger @click="onDelete(index)"></mw-button>
          </template>
        </template>
      </mw-table>
      <!-- <div class="">
        <div class="px-4 bg-background rounded-lg divide-y divide-border">
          <div
            class="text-primary-text py-4"
            v-for="item in bomRelatedMaterialList"
            :key="item.id"
          >
            <div class="flex justify-between items-center leading-5.5 mb-1">
              <div class="flex-1 text-title overflow">
                {{ item.materialName }} ({{ item.materialNo || "--" }})
              </div>
            </div>
            <div class="flex justify-between items-center leading-5.5">
              <div class="space-y-1">
                <div class="">{{ item.specification }}</div>
              </div>
              <div class="">x {{ item.quantity }} {{ item.unit }}</div>
            </div>
          </div>
        </div>
      </div> -->
    </div>
  </mw-drawer>
</template>

<script setup>
import FormUploadContract from "@/components/form-upload-contract.vue";
import { getAllBomList, getBomMaterialList } from "@/api/basicData/bom.js";
import { uniqueArr, exportExecl } from "@/utils/util.js";
import { getCurrentInstance } from "vue";
import { ref, watch, defineEmits, defineProps, reactive } from "vue";
import { bomTypeList } from "@/common/constant.js";
import { orderListV1, orderBomList } from "@/api/basicData/orderBomChange.js";
import { debounce } from "lodash";
const emit = defineEmits(["update:visible", "finish", "newArr"]);
const { proxy } = getCurrentInstance();
const bomSearchName = ref();
const bomRelatedMaterialList = ref([]);
const allBomList = ref([]);
const materialList = ref([]);
const selectedBom = ref();
const submitLoading = ref(false);
const bomType = ref(null);
const orderData = ref([]);
const orderId = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  bomNoData: {
    type: String,
    default: "",
  },
  loading: {
    type: Boolean,
    default: false,
  },
});
const columns = ref([
  {
    title: "物料编号",
    dataIndex: "materialNo",
    key: "materialNo",
  },
  {
    title: "物料名称",
    dataIndex: "materialName",
    key: "materialName",
  },
  {
    title: "物料生产线",
    dataIndex: "lineName",
    key: "lineName",
  },
  {
    title: "物料规格",
    dataIndex: "specification",
    key: "specification",
    width: 100,
  },
  {
    title: "经济物料",
    dataIndex: "isEconomicMaterial",
    key: "isEconomicMaterial",
    customRender: ({ record }) => {
      return record.isEconomicMaterial == 1
        ? "是"
        : record.isEconomicMaterial == 0
        ? "否"
        : "";
    },
  },
  {
    title: "物料单台数量",
    dataIndex: "quantity",
    key: "quantity",
  },
  {
    title: "单位",
    dataIndex: "unit",
    key: "unit",
  },
  {
    title: "排序",
    dataIndex: "sortNumber",
    key: "sortNumber",
  },
  {
    title: "备注",
    dataIndex: "remark",
    key: "remark",
    width: 100,
  },
  {
    title: "操作",
    dataIndex: "operate",
    key: "operate",
    fixed: "right",
  },
]);
//确认引入bom
const quoteBOMOnSubmitOnSubmit = () => {
  submitLoading.value = true;
  let newArr = bomRelatedMaterialList.value.map((item) => {
    return {
      ...item,
      id: item?.materialId,
      quantity: item?.quantity || 0, // 把数量加进去
    };
  });

  newArr.forEach((item, index) => {
    materialList.value.forEach((ite, ind) => {
      if (item.materialId == ite.materialId) {
        ite.quantity = item.quantity + ite.quantity;
      }
    });
  });

  // 合并数组
  const resultArr = uniqueArr(materialList.value, newArr);
  materialList.value = resultArr;
  submitLoading.value = false;
  emit("newArr", newArr, props.bomNoData, selectedBom.value);
  onClose();
};

function onClose() {
  emit("update:visible", false);
  bomSearchName.value = [];
  selectedBom.value = {};
  bomRelatedMaterialList.value = [];
  bomType.value = void 0;
  allBomList.value = [];
}
//删除
const onDelete = (ind) => {
  bomRelatedMaterialList.value.forEach((item, index) => {
    if (index == ind) {
      bomRelatedMaterialList.value.splice(ind, 1);
    }
  });
};
// 获取基础bom列表
async function getAllBom(keyword) {
  let result = await getAllBomList({
    ignoreStatus: true,
    keyword: keyword,
  });
  let { data } = result;
  allBomList.value = data;
}
// 获取订单列表
const apiOrderList = async (keyword) => {
  let result = await orderListV1({ ignoreStatus: true, keyword: keyword });
  orderData.value = result.data.map((item, index) => {
    if (item.marketOrderNo == "全部") {
      item.marketOrderName = item.marketOrderNo;
    } else {
      item.marketOrderName = item.marketOrderNo + "/" + item.marketOrderName;
    }

    return item;
  });
};
const onSearchOrder = debounce((value) => {
  apiOrderList(value);
}, 300);
// 获取订单物料列表
const apiOrderBomList = async (bomNo) => {
  let result = await orderBomList({ bomNo: bomNo });
  bomRelatedMaterialList.value = result.data;
};

// bom类型change事件
const bomTypeChange = (e) => {
  allBomList.value = [];
  orderId.value = void 0;
  bomSearchName.value = void 0;
  bomRelatedMaterialList.value = [];
  selectedBom.value = void 0;
  if (e == "foundationBom") {
    getAllBom("");
  } else if (e == "orderBom") {
    apiOrderList();
  }
};
// 订单事件
const orderBomOnChange = async (e, option) => {
  bomRelatedMaterialList.value = [];
  selectedBom.value = void 0;
  bomSearchName.value = void 0;
  allBomList.value = option.bomProductList.map((item, index) => {
    if (item.materialFileNumber) {
      item.bomName = item.bomName + "/" + item.materialFileNumber;
    }
    return item;
  });
};
// bom事件
const handleChangBom = async (value) => {
  selectedBom.value = allBomList.value.filter((item) => item.bomNo == value)[0];
  if (bomType.value == "foundationBom") {
    // 获取选中的bom信息
    let res = await getBomMaterialList({ bomNo: value });
    bomRelatedMaterialList.value = res.data;
  } else if (bomType.value == "orderBom") {
    apiOrderBomList(value);
  }
};

const filterOption = (input, option) => {
  // 根据名称和ID进行查询
  return (
    option.marketOrderName.toLowerCase().includes(input.toLowerCase()) ||
    option.marketOrderNo.toLowerCase().includes(input.toLowerCase())
  );
};
// const filterOptionBom = (input, option) => {
//   // 根据名称和ID进行查询
//   if (option?.materialFileNumber) {
//     return (
//       option?.materialFileNumber.toLowerCase().includes(input.toLowerCase()) ||
//       option.bomName.toLowerCase().includes(input.toLowerCase()) ||
//       option.bomNo.toLowerCase().includes(input.toLowerCase())
//     );
//   } else {
//     return (
//       option.bomName.toLowerCase().includes(input.toLowerCase()) ||
//       option.bomNo.toLowerCase().includes(input.toLowerCase())
//     );
//   }
// };
const filterOptionBom = (input, option) => {
  // 根据名称和ID进行查询
  const inputLowerCase = input.toLowerCase();
  const bomNameLowerCase = option.bomName.toLowerCase();
  const bomNoLowerCase = option.bomNo.toLowerCase();

  if (option?.materialFileNumber) {
    const materialFileNumberLowerCase = option.materialFileNumber.toLowerCase();
    return (
      materialFileNumberLowerCase.includes(inputLowerCase) ||
      bomNameLowerCase.includes(inputLowerCase) ||
      bomNoLowerCase.includes(inputLowerCase)
    );
  } else {
    return (
      bomNameLowerCase.includes(inputLowerCase) ||
      bomNoLowerCase.includes(inputLowerCase)
    );
  }
};
</script>

<style></style>
