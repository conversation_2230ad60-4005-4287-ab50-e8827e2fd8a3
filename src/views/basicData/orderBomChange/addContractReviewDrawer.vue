<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="80%"
    customTitle="订单BOM变更"
  >
    <template #header>
      <mw-button
        title="确定"
        @click="formSubmit"
        :loading="loading"
      ></mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      labelAlign="left"
      :label-col="{ span: 7, offset: 1 }"
    >
      <a-row>
        <!-- <a-col :span="12">
          <a-form-item
            label="单据编号"
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
          >
            <a-input v-model:value="formData.customerNo" disabled />
          </a-form-item>
        </a-col> -->
        <a-col :span="12">
          <a-form-item
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
            label="申请人"
            name="applicant"
            required
          >
            <a-select
              v-model:value="formData.applicant"
              show-search
              placeholder="请选择负责人"
              style="width: 100%"
              :default-active-first-option="false"
              :options="allUserList"
              optionFilterProp="nickName"
              :fieldNames="{
                label: 'nickName',
                value: 'userId',
              }"
              @change="handleChangeUser"
              disabled
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="申请日期"
            name="applicationDate"
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
            required
          >
            <a-date-picker
              disabled
              v-if="!planNo"
              v-model:value="formData.applicationDate"
              style="width: 100%"
              valueFormat="YYYY-MM-DD"
            />
            <span v-else>{{ formData.applicationDate }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
            label="关联-销售订单"
            name="orderId"
            required
          >
            <a-select
              disabled
              placeholder="请选择关联-销售订单"
              :options="postNoContractReviewListOptions"
              v-model:value="formData.orderId"
              optionFilterProp="name"
              @change="handleChange"
              :field-names="{
                label: 'orderName',
                value: 'id',
              }"
              show-search
            >
            </a-select>
          </a-form-item>
        </a-col>
        <!--  -->
        <a-col :span="12">
          <a-form-item
            label="产品数量"
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
          >
            {{ props.productQuantity }}
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="是否是BOM增料"
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
            required
            name="insertFlag"
          >
            <!-- <a-radio-group v-model:value="formData.insertFlag">
              <a-radio :value="0">否</a-radio>
              <a-radio :value="1">是</a-radio>
            </a-radio-group> -->
            <radioGroup
              v-model:value="formData.insertFlag"
              :options="autoGenerationCodeFlagList"
            ></radioGroup>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item
            label="是否BOM删料"
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
            required
            name="removeFlag"
          >
            <!-- <a-radio-group v-model:value="formData.removeFlag">
              <a-radio :value="0">否</a-radio>
              <a-radio :value="1">是</a-radio>
            </a-radio-group> -->
            <radioGroup
              v-model:value="formData.removeFlag"
              :options="autoGenerationCodeFlagList"
            ></radioGroup>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="变更类型"
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
            required
            name="alterationType"
          >
            <a-select
              v-model:value="formData.alterationType"
              show-search
              placeholder="请选择变更类型"
              :default-active-first-option="false"
              :options="orderAlterationType"
              optionFilterProp="label"
            ></a-select>
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item
            label="变更说明"
            :label-col="{ span: 4, offset: 1 }"
            required
            name="remark"
          >
            <a-textarea
              style="width: 100%"
              v-model:value="formData.remark"
              placeholder="请输入变更说明"
              allow-clear
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item
            name="fileDetailModel"
            label="订单BOM公共附件"
            :label-col="{ span: 4, offset: 1 }"
          >
            <form-upload-contract
              :multiple="true"
              class="mr-2"
              v-model:value="formData.file"
              :fileSize="50"
              sence="orderContract"
              :hasDownLoad="true"
              :fileTypes="[]"
              :fileLName="'上传订单BOM公共附件'"
              :fileLimit="9999999999"
              :delShow="true"
              :flexT="true"
            ></form-upload-contract>
            <!-- <div
              v-if="props.id"
              @click.stop="openUrl(formData?.file[0]?.fileVisitUrl)"
              :href="formData?.file[0]?.fileVisitUrl"
              :title="formData?.file[0]?.fileName"
              class="cursor-pointer inline-block"
              style="color: #959ec3"
            >
              <i
                class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                style="color: #959ec3"
              ></i
              ><span class="underline">{{ formData.file[0]?.fileName }}</span>
            </div>
            <form-upload
              v-else
              v-model:value="formData.file"
              sence="article"
              :fileTypes="[]"
              :fileSize="100"
            ></form-upload> -->
          </a-form-item>
        </a-col>
        <!--增料订单产品BOM明细 formData.insertFlag -->
        <a-col :span="24" v-if="formData.insertFlag == 1">
          <div>
            <a-form-item
              label="增料订单产品BOM明细 "
              :label-col="{ span: -20, offset: -10 }"
              :required="formData.insertFlag == 1 ? true : false"
              name="insertParams"
            >
              <mw-button
                title="新增"
                :font="'iconfont icon-xianxing-121'"
                @click="handleAdd('add')"
              ></mw-button>
            </a-form-item>
          </div>
          <a-table
            bordered
            :data-source="formData.insertParams"
            :columns="columnsInsertParams"
            :scroll="{ x: 'max-content' }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'materialId'">
                <a-select
                  show-search
                  placeholder="请选择"
                  style="width: 100%"
                  :options="insertParamsListDataListData"
                  v-model:value="record.materialId"
                  optionFilterProp="materialName"
                  :field-names="{
                    label: 'materialName',
                    value: 'materialId',
                  }"
                  @change="addMatPro(record)"
                >
                </a-select>
              </template>

              <template v-if="column.dataIndex === 'lineId'">
                <a-select
                  v-model:value="record.lineId"
                  show-search
                  allowClear
                  placeholder="请选择生产线"
                  style="width: 220px"
                  :default-active-first-option="false"
                  :options="allLineList"
                  optionFilterProp="lineName"
                  :fieldNames="{
                    label: 'lineName',
                    value: 'id',
                  }"
                ></a-select
              ></template>

              <template v-if="column.dataIndex === 'requiresMaterialQuantity'">
                <a-input-number
                  style="width: 100%"
                  id="inputNumber"
                  v-model:value="record.requiresMaterialQuantity"
                  placeholder="请输入"
                  :stringMode="true"
                />
              </template>
              <template v-if="column.dataIndex === 'remark'">
                <a-input
                  placeholder="请输入"
                  style="width: 100%"
                  id="inputNumber"
                  v-model:value="record.remark"
                />
              </template>

              <template v-if="column.dataIndex === 'amount'">
                <div v-if="record.unitPrice && record.quantity">
                  {{ (record.amount = record.unitPrice * record.quantity) }}
                </div>
                <div v-else></div>
              </template>

              <template v-if="column.dataIndex == 'operation'">
                <a-popconfirm
                  title="确定是否删除？"
                  ok-text="是"
                  cancel-text="否"
                  @confirm="onDelete(record)"
                >
                  <mw-button title="删除" danger></mw-button>
                </a-popconfirm>
              </template>
            </template>
          </a-table>
        </a-col>
        <!-- 删除订单产品BOM明细 -->
        <a-col :span="24" v-if="formData.removeFlag == 1">
          <div>
            <a-form-item
              label="删除订单产品BOM明细"
              :label-col="{ span: -20, offset: -10 }"
              :required="formData.removeFlag == 1 ? true : false"
              name="removeParams"
            >
              <mw-button
                title="新增"
                :font="'iconfont icon-xianxing-121'"
                @click="handleAdd('delete')"
              ></mw-button>
            </a-form-item>
          </div>
          <a-table
            bordered
            :data-source="formData.removeParams"
            :columns="columnsRemoveParams"
            :scroll="{ x: 'max-content' }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex == 'materialId'">
                <a-select
                  show-search
                  style="width: 100%"
                  placeholder="请选择"
                  :options="removeParamsListData"
                  v-model:value="record.materialId"
                  @change="onRemoveParamsChange($event, record)"
                  optionFilterProp="materialName"
                  :field-names="{
                    label: 'materialName',
                    value: 'materialId',
                  }"
                >
                </a-select>
              </template>
              <!-- requiresMaterialQuantity -->
              <!-- <template v-if="column.dataIndex === 'lineId'">
                <a-select
                  v-model:value="record.lineId"
                  show-search
                  allowClear
                  placeholder="请选择生产线"
                  style="width: 220px"
                  :default-active-first-option="false"
                  :options="allLineList"
                  optionFilterProp="lineName"
                  :fieldNames="{
                    label: 'lineName',
                    value: 'id',
                  }"
                ></a-select
              ></template> -->

              <template v-if="column.dataIndex === 'requiresMaterialQuantity'">
                <a-input-number
                  style="width: 100%"
                  id="inputNumber"
                  v-model:value="record.requiresMaterialQuantity"
                  placeholder="请输入"
                  :stringMode="true"
                />
              </template>
              <template v-if="column.dataIndex === 'remark'">
                <a-input
                  placeholder="请输入"
                  style="width: 100%"
                  id="inputNumber"
                  v-model:value="record.remark"
                />
              </template>
              <template v-if="column.dataIndex === 'operation'">
                <a-popconfirm
                  title="确定是否删除？"
                  ok-text="是"
                  cancel-text="否"
                  @confirm="onDeleteRemoveParams(record)"
                >
                  <mw-button title="删除" danger></mw-button>
                </a-popconfirm>
              </template>
            </template>
          </a-table>
        </a-col>
        <a-col :span="24" class="mt-5">
          <a-form-item label="其他备注" :label-col="{ span: 0, offset: 1 }">
            <a-textarea
              style="width: 100%"
              v-model:value="formData.otherRemark"
              placeholder="请输入其他备注"
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
  defineProps,
  defineEmits,
  watch,
} from "vue";
import { postCustomerPage, productList } from "@/api/proSchedul/scheduling.js"; //客户信息
import { list as getAllUserList } from "@/api/system/user.js";
import { getUserInfo } from "@/api/login.js";
import FormUploadContract from "@/components/form-upload-contract.vue";
import {
  getOrderModifyRecordUpdate,
  getRemoveMaterialList,
  getAddMaterialList,
} from "@/api/basicData/orderBomChange.js";
import {
  getContractReviewDetail,
  postContractReviewUpdate,
  postNoSaleOrderList,
} from "@/api/sales/index.js";
import { orderAlterationType } from "@/common/constant.js";
import { list as getAllLineList } from "@/api/basicData/productionLine.js";
import { autoGenerationCodeFlagList } from "@/common/constant.js";
import _cloneDeep from "lodash/cloneDeep";
const today = new Date(); // 获取当前日期
const emit = defineEmits(["update:visible", "finish"]);
const postNoContractReviewListOptions = ref([]);
const insertParamsListDataListData = ref([]);
const removeParamsListData = ref([]);
const allUserList = ref([]);
const allLineList = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
  marketOrderId: {
    type: String,
    required: true,
  },
  productQuantity: {
    type: String,
    required: true,
  },
  BomData: {
    type: String,
    required: true,
  },
  detailsVal: {
    type: Object,
    default: {},
  },
});
const { proxy } = getCurrentInstance();
const formRef = ref();
const customerPageList = ref([]);
const loading = ref(false);
const formData = reactive({
  applicant: undefined,
  applicationDate: `${today.getFullYear()}-${(
    "0" +
    (today.getMonth() + 1)
  ).slice(-2)}-${("0" + today.getDate()).slice(-2)}`,
  insertFlag: 0,
  insertParams: [],
  orderId: undefined,
  otherRemark: undefined,
  remark: undefined,
  removeFlag: 0,
  removeParams: [],
  otherRemark: undefined,
  file: [],
  alterationType: void 0,
});

const columnsInsertParams = ref([
  {
    title: "新增物料",
    dataIndex: "materialId",
    width: "350px",
  },
  // {
  //   title: "物料编码",
  //   dataIndex: "materialNo",
  //   width: "150px",
  // },
  {
    title: "生产线",
    dataIndex: "lineId",
    width: "150px",
  },
  {
    title: "可用单台数",
    dataIndex: "availableStock",
    width: "150px",
  },

  {
    title: "新增数量",
    dataIndex: "requiresMaterialQuantity",
    width: "150px",
  },

  {
    title: "备注",
    dataIndex: "remark",
    width: "150px",
  },
  {
    title: "删除",
    dataIndex: "operation",
    width: "100px",
  },
]);

const columnsRemoveParams = ref([
  {
    title: "删除物料",
    dataIndex: "materialId",
    width: "350px",
  },
  // {
  //   title: "物料编码",
  //   dataIndex: "materialNo",
  //   width: "150px",
  // },
  // {
  //   title: "物料名称",
  //   dataIndex: "materialName",
  //   width: "150px",
  // },
  {
    title: "生产线",
    dataIndex: "lineName",
    width: "150px",
  },
  {
    title: "原单台数",
    dataIndex: "quantity",
    width: "150px",
  },
  // {
  //   title: "可用库存",
  //   dataIndex: "availableStock",
  //   width: "150px",
  // },
  {
    title: "删除数量",
    dataIndex: "requiresMaterialQuantity",
    width: "150px",
  },
  {
    title: "备注",
    dataIndex: "remark",
    width: "150px",
  },
  {
    title: "删除",
    dataIndex: "operation",
    width: "100px",
  },
]);
const rules = reactive({
  materialIdOrProductId: [
    {
      required: true,
      message: "请选择" + props.name,
      trigger: "blur",
    },
  ],
  remark: [
    {
      required: true,
      message: "请输入变更说明",
      trigger: "blur",
    },
  ],
});
// 获取产线列表
async function getAllLine() {
  let result = await getAllLineList({
    ignoreCancel: true,
    onlyUnUsed: false,
  });
  let { data } = result;
  allLineList.value = [{ lineName: "无", id: null }, ...data];
}
// 用户人信息
const userInfo = async () => {
  let res = await getUserInfo();
  formData.applicant = res.user.userId;
};

const addMatPro = (val) => {
  insertParamsListDataListData.value.forEach((item, index) => {
    if (val.materialId == item.materialId) {
      val.materialId = item.materialId;
      val.materialNo = item.materialNo;
      val.oldMaterialNo = item.oldMaterialNo;
      val.isNeedDebugging = item.isNeedDebugging;
      val.productUse = item.productUse;
      val.unitName = item.unitName;
      val.materialName = item.materialName;
      val.requiresProductQuantity = item.requiresProductQuantity;
      val.totalQuantity = item.totalQuantity;
      val.purchaseQuantity = item.purchaseQuantity;
      val.availableStock = item.availableStock;
      val.lineId = item.lineId;
      val.remark = item.remark;
    }
  });
  // formData.applicant = e;
};

//  获取用户列表
async function getAllUser() {
  let result = await getAllUserList({
    pageNum: 1,
    pageSize: 10000,
    sortType: "create_time",
    sortOrder: "desc",
    // userName: keyword,
  });
  let { data } = result;
  allUserList.value = data;
}

const apiGetAddMaterialList = async (val) => {
  let res = await getAddMaterialList({ orderId: val });
  insertParamsListDataListData.value = res.data.map((item, index) => {
    item.materialName =
      item.materialNo + "/" + item.materialName + "/" + item.specification;
    return item;
  });
};
const apiGetRemoveMaterialList = async (val) => {
  let res = await getRemoveMaterialList({
    orderId: val,
    bomNo: props.BomData,
  });
  // res.data.forEach((item) => {
  //   // item.materialName=
  //   return;
  // });
  removeParamsListData.value = res.data.map((item, index) => {
    item.materialId = item.id;
    item.materialName =
      item.materialNo + "/" + item.materialName + "/" + item.specification;
    return item;
  });
};
function onClose() {
  emit("update:visible", false);
  formRef.value.resetFields();
  formData.otherRemark = undefined;
  formData.file = [];
  formData.remark = void 0;
}
// 客户
const postCustomerPageList = async () => {
  let res = await postCustomerPage();
  customerPageList.value = res.data;
};
// 产品名称productList
// const postproductList = async () => {
//   let res = await productList(); //
//   productListData.value = res.data;
// };

// 关联-销售订单
const postNoContractReview = async () => {
  let res = await postNoSaleOrderList({});
  postNoContractReviewListOptions.value = res.data;
};

// 编辑详情
// contractReviewId
const getDetail = async (val) => {
  let res = await getContractReviewDetail({ id: val });
};

// 产品明细添加
const handleAdd = (val) => {
  const newData = {
    id: Date.now(),
  };
  if (val == "delete") {
    formData.removeParams.push(newData);
  } else {
    formData.insertParams.push(newData);
  }
};
// 产品删除
const onDelete = (val) => {
  formData.insertParams.forEach((element, index) => {
    if (props.id) {
      if (element.materialId == val.materialId) {
        formData.insertParams.splice(index, 1);
      }
    } else {
      if (element.id == val.id) {
        formData.insertParams.splice(index, 1);
      }
    }
  });
};
// 删除订单产品BOM明细
const onDeleteRemoveParams = (val) => {
  formData.removeParams.forEach((element, index) => {
    if (props.id) {
      if (element.materialId == val.materialId) {
        formData.removeParams.splice(index, 1);
      }
    } else {
      if (element.id == val.id) {
        formData.removeParams.splice(index, 1);
      }
    }
  });
};
const handleChange = (e) => {
  apiGetRemoveMaterialList(e);
  apiGetAddMaterialList(e);
};
const onRemoveParamsChange = (e, val) => {
  removeParamsListData.value.forEach((item, index) => {
    if (item.materialId == e) {
      val.materialId = item.materialId;
      val.materialName = item.materialName;
      val.materialNo = item.materialNo;
      val.unitName = item.unitName;
      val.availableStock = item.availableStock;
      val.totalQuantity = item.totalQuantity;
      val.quantity = item.quantity;
      val.lineName = item.lineName ? item.lineName : "无";
    }
  });

  // formData.removeParams
};
const formSubmit = async () => {
  formRef.value.validate().then(async () => {
    if (formData.insertFlag == 0) {
      formData.insertParams = [];
    } else {
      const hasEmptyName = Array.from(formData.insertParams).every(
        (obj) =>
          obj.requiresMaterialQuantity != undefined &&
          obj.materialId !== undefined
      );
      if (!hasEmptyName) {
        proxy.$message.error("请完整填写产品信息!");
        return;
      }
      const uniqueUsersSet = new Set(
        formData.insertParams.map((item) => item.materialId)
      );
      if (formData.insertParams.length > uniqueUsersSet.size) {
        proxy.$message.error("物料不可重复添加!");
        return;
      }
    }
    if (formData.removeFlag == 0) {
      formData.removeParams = [];
    } else {
      formData.removeParams.forEach((item, index) => {
        delete item.id;
      });
      const hasEmptyName = Array.from(formData.removeParams).every(
        (obj) =>
          obj.requiresMaterialQuantity != undefined &&
          obj.materialId !== undefined
      );
      if (!hasEmptyName) {
        proxy.$message.error("请完整填写产品信息!");
        return;
      }
      const uniqueUsersSet = new Set(
        formData.removeParams.map((item) => item.materialId)
      );
      if (formData.removeParams.length > uniqueUsersSet.size) {
        proxy.$message.error("物料不可重复添加!");
        return;
      }
    }
    const hasEmptyName = Array.from(formData.insertParams).every(
      (obj) => obj.lineId && obj.lineId != undefined && obj.lineId != null
    );
    if (!hasEmptyName) {
      proxy.$message.error("生产线不得为空");
      return;
    }
    let param = {
      ...formData,
      bomNo: props.BomData,
    };
    // return;
    if (props.id) {
      param.id = props.id;
      loading.value = true;
      let res = await postContractReviewUpdate(param);
      if (res.code == 200) {
        proxy.$message.success("操作成功");
        onClose();
      }
      loading.value = false;
      emit("finish");
    } else {
      loading.value = true;
      let res = await getOrderModifyRecordUpdate(param);
      if (res.code == 200) {
        proxy.$message.success("操作成功");
        onClose();
      }
      loading.value = false;
      emit("finish");
    }
  });
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      userInfo();
      if (props.id) {
        getDetail(props.id);
      }
      if (props.marketOrderId) {
        let dataFile = _cloneDeep(props.detailsVal.publicFile);
        formData.file = dataFile;
        console.log(formData.file, " formData.file ");
        formData.orderId = props.marketOrderId;
        apiGetRemoveMaterialList(props.marketOrderId);
        apiGetAddMaterialList(props.marketOrderId);
      }
      postCustomerPageList();
      // postproductList();
      postNoContractReview();
      getAllUser();
      getAllLine();
    }
  }
);
</script>
<style lang="less" scoped></style>
