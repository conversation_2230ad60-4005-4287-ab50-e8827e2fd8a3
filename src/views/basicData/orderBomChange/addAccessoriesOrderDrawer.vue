<template>
  <mw-drawer
    custom-title="新增配件订单"
    :visible="visible"
    @close="onClose"
    closeText="取消"
    :spinning="spinning"
    :width="getViewportSize() > 800 ? '80%' : '100%'"
    destroyOnClose="true"
  >
    <template v-slot:header>
      <mw-button @click="submitForm" :loading="submitLoading" v-if="!props.id"
        >确定</mw-button
      >
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
    >
      <a-form-item label="" name="materialReceiveDetails">
        <mw-button @click="addMaterial" class="mb-2"> 新增 </mw-button>
        <mw-table
          :scroll="{ x: 'max-content' }"
          :columns="columnsDrawer"
          :data-source="formData.materialReceiveDetails"
          :loading="loading"
          :rowKey="(record) => record.id"
        >
          <template #bodyCell="{ column, index, record }">
            <template v-if="column.key == 'materialId'">
              <a-select
                show-search
                placeholder="请选择物料"
                :options="materialData"
                v-model:value="record.materialId"
                optionFilterProp="materialName"
                :field-names="{
                  label: 'materialName',
                  value: 'id',
                }"
                @change="changeMaterialData($event, record, index)"
                @search="searchMaterial"
              >
              </a-select>
            </template>
            <template v-if="column.key == 'isEconomicMaterial'">
              <span v-if="record.isEconomicMaterial == 1">是</span>
              <span v-if="record.isEconomicMaterial == 0">否</span>
            </template>

            <template v-if="column.key == 'requiresMaterialQuantity'">
              <div class="flex">
                <a-input-number
                  placeholder="数量"
                  @change="onChange($event, record)"
                  v-model:value="record.requiresMaterialQuantity"
                  :stringMode="true"
                  :min="0"
                ></a-input-number>
                <div class="ml-2 mt-1">
                  {{ record.unitName }}
                </div>
              </div>
            </template>
            <template v-if="column.key == 'currentStock'">
              {{ record.currentStock }}
            </template>
            <template v-if="column.key == 'availableStock'">
              {{ record.availableStock }}</template
            >
            <template v-if="column.key == 'operate'">
              <mw-button danger @click="onDelete(index)"> 删除 </mw-button>
            </template>
          </template></mw-table
        >
      </a-form-item>

      <!-- <a-form-item v-if="props.type == 'det'">
        <mw-table
          :scroll="{ x: 'max-content' }"
          :columns="columnsDrawerDetail"
          :data-source="formData.materialReceiveDetails"
          :loading="loading"
          :rowKey="(record) => record.id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key == 'materialName'">
              {{ record.materialName }}/{{ record.specification }}/{{
                record.materialNo
              }}</template
            >
            <template v-if="column.key == 'requiresMaterialQuantity'">
              {{ record.requiresMaterialQuantity }}{{ record.unitName }}</template
            >
          </template>
        </mw-table>
      </a-form-item> -->
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  computed,
  defineEmits,
  reactive,
} from "vue";
const { proxy } = getCurrentInstance();

import { roundNumFun } from "@/common/validate.js";
import _cloneDeep from "lodash/cloneDeep";
import { mobile, email, password, money, getViewportSize } from "@/common/reg";
import {
  saveMaterialRequisition,
  dictionary,
  detailMaterialReceive,
} from "@/api/materialRequisition/index.js";
import { addMarketOrderV1 } from "@/api/basicData/orderBomChange.js";
import { listMaterial } from "@/api/applyForWarehouse/index.js";
import { debounce } from "lodash";
import { message } from "ant-design-vue";
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
  type: String,
  marketOrderId: String,
});
const emit = defineEmits(["update:visible", "finish"]);
const title = computed(() => {
  return props.id ? "领料明细" : "领料申请";
});
const typeOptions = ref([]);
const loading = ref(false);
const formRef = ref(null);
const submitLoading = ref(false);
const spinning = ref(false);
const materialData = ref([]);
const formData = reactive({
  materialReceiveDetails: [],
});
const rules = ref({
  // roleName: [
  //   {
  //     required: true,
  //     message: "请输入角色名称",
  //     trigger: "blur",
  //   },
  // ],
  // dataScope: [
  //   {
  //     required: true,
  //     message: "请选择上级角色",
  //     trigger: "change",
  //   },
  // ],
});
// 数量
const onChange = async (e, val) => {
  const roundedQuantity = await roundNumFun(e, 4);
  val.requiresMaterialQuantity = roundedQuantity;

  // 确保数量变化时，数据也同步更新到数组中
  const index = formData.materialReceiveDetails.findIndex(
    (item) => item === val
  );
  if (index !== -1) {
    formData.materialReceiveDetails[index].requiresMaterialQuantity =
      roundedQuantity;
  }
};
// 关闭
const onClose = async () => {
  formRef.value.resetFields();
  formData.materialReceiveDetails = [];
  emit("update:visible", false);
};

// 新增
const addMaterial = () => {
  formData.materialReceiveDetails.push({});
};

// 确定
const submitForm = () => {
  formRef.value
    .validate()
    .then(async () => {
      submitLoading.value = true;

      // 验证 materialReceiveDetails 数组
      if (
        !formData.materialReceiveDetails ||
        formData.materialReceiveDetails.length === 0
      ) {
        message.error("请至少添加一个物料");
        submitLoading.value = false;
        return;
      }

      // 验证每个物料的数据
      const errors = [];
      formData.materialReceiveDetails.forEach((item, index) => {
        if (!item.materialId) {
          errors.push(`第${index + 1}行：请选择物料`);
        }
        if (
          !item.requiresMaterialQuantity ||
          Number(item.requiresMaterialQuantity) <= 0
        ) {
          errors.push(`第${index + 1}行：数量必须大于0`);
        }
      });

      if (errors.length > 0) {
        message.error(errors.join("\n"));
        submitLoading.value = false;
        return;
      }

      try {
        // 调用API保存数据
        // const res = await saveMaterialRequisition(formData);
        let materialList = formData.materialReceiveDetails.map((item) => {
          return {};
        });
        const res = await addMarketOrderV1({
          insertParams: formData.materialReceiveDetails,
          //   insertParams:
          type: "ACCESSORY_ORDER",
          othersMarketOrderId: props.marketOrderId,
        });
        if (res.code === 200) {
          message.success("保存成功");
          emit("finish");
          onClose();
        } else {
          //   message.error(res.msg || "保存失败");
        }
      } catch (error) {
        message.error("保存失败，请重试");
        console.error("保存失败:", error);
      } finally {
        submitLoading.value = false;
      }
    })
    .catch((error) => {
      submitLoading.value = false;
    });
};
// 字典
const dictionaryData = async () => {
  let res = await dictionary("store_material_receive_type");
  typeOptions.value = res.data;
};
const listMaterialData = async (e, keyword) => {
  let param = {
    ignoreCancel: true, // 是否过滤取消物料
    ignoreStop: true, //是否过滤停用物料
    // isEconomicMaterial: e == 1 ? 1 : e == 2 ? 0 : void 0, // 根据index判断不合适 后续改成根据值判断
    // classificationName:
    //   e == 3
    //     ? typeOptions.value.find((item) => item.dictSort == e).dictLabel
    //     : undefined,
    size: 10,
    keyword,
    isExpense: 1, // 只能选取标记了“耗用”的物料
  };
  let res = await listMaterial(param, { pageNum: 1, pageSize: 50 });

  materialData.value = res.data.map((item) => {
    item.materialName =
      item.materialNo + "/" + item.materialName + "/" + item.specification;
    return item;
  });
};
const changeMaterialData = (materialId, record, index) => {
  // 查找完整的物料数据
  const fullMaterialData = materialData.value.find(
    (item) => item.id === materialId
  );

  if (fullMaterialData) {
    // 直接更新 record 对象的属性，保持对象引用不变
    Object.assign(record, fullMaterialData);
    // 保持用户已输入的数量
    if (record.requiresMaterialQuantity === undefined) {
      record.requiresMaterialQuantity = 0;
    }
  }
};
// 详情
const detailMaterialReceiveData = async (val) => {
  let res = await detailMaterialReceive(val);
  for (let key in formData) {
    formData[key] = res.data[key];
  }
};
// 删除
const onDelete = (index) => {
  formData.materialReceiveDetails.forEach((item, i) => {
    if (i == index) {
      formData.materialReceiveDetails.splice(i, 1);
    }
  });
};
const viewportWidth = ref();
const debouncedSearch = debounce(async (e) => {
  console.log("[ e ] >", e);
  await listMaterialData(undefined, e);
  //
}, 300);
const searchMaterial = async (e) => {
  debouncedSearch(e);
};
watch(
  () => props.visible,
  async (val) => {
    viewportWidth.value = getViewportSize();

    if (val) {
      await listMaterialData("", "");
      if (props.id && props.type == "det") {
        await detailMaterialReceiveData(props.id);
      }
      if (!props.id) {
        // await listMaterialData();
      }
      await dictionaryData();
    }
  }
);
const columnsDrawer = ref([
  {
    title: "物料",
    key: "materialId",
    width: "300px",
  },
  {
    title: "数量",
    key: "requiresMaterialQuantity",
  },
  {
    title: "经济物料",
    dataIndex: "isEconomicMaterial",
    key: "isEconomicMaterial",
    customRender: ({ record }) => {
      return record.isEconomicMaterial == 1
        ? "是"
        : record.isEconomicMaterial == 0
        ? "否"
        : "";
    },
  },
  {
    title: "实际库存",
    key: "currentStock",
  },
  {
    title: "可用库存",
    key: "availableStock",
  },
  {
    title: "操作",
    key: "operate",
    fixed: "right",
  },
]);
const columnsDrawerDetail = ref([
  {
    title: "物料",
    key: "materialName",
  },
  {
    title: "经济物料",
    dataIndex: "isEconomicMaterial",
    key: "isEconomicMaterial",
    customRender: ({ record }) => {
      return record.isEconomicMaterial == 1
        ? "是"
        : record.isEconomicMaterial == 0
        ? "否"
        : "";
    },
  },
  {
    title: "数量",
    key: "requiresMaterialQuantity",
  },
]);
</script>
<style lang="less" scoped>
:deep(.mw-table .ant-table-tbody > tr > td) {
  padding: 8px 12px;
}
</style>
