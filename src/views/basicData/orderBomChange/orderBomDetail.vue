<template>
  <a-spin :spinning="spinning">
    <div class="relative">
      <div class="flex justify-end mb-3" v-if="!hasACCESSORY_ORDER">
        <mw-button
          title="新增配件订单"
          class="mr-1"
          @click="NewAccessoriesOrder"
          v-if="formData.canCreateOthers === 0"
        ></mw-button>
      </div>
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="inline"
        :colon="false"
      >
        <a-collapse class="w-full mb-4">
          <a-collapse-panel :key="1">
            <template v-slot:header><b>修改信息</b></template>
            <mw-table
              :columns="modifyInformationColumns"
              :dataSource="formData.bomModifyList"
              :pagination="false"
              :row-key="(r) => r.index"
              :scroll="{ x: 'max-content' }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex == 'modifyDetail'">
                  <div
                    v-for="line in record.modifyDetail.info?.split('\n')"
                    :key="line"
                  >
                    {{ line }}
                  </div>
                  <div>附件：</div>
                  <div
                    v-for="items in record.modifyDetail.files"
                    :key="items.fileId"
                  >
                    <div>
                      <i
                        class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                        style="color: #959ec3"
                      ></i>
                      <a
                        :title="items?.fileName"
                        @click="downFile(items.fileVisitUrl, items.fileName)"
                        target="_blank"
                        class="underline"
                        style="color: #959ec3"
                        >{{ items?.fileName }}
                      </a>
                    </div>
                  </div>
                </template>
              </template>
            </mw-table>
          </a-collapse-panel>
        </a-collapse>
        <a-collapse
          bordered="false"
          class="mb-4 w-full"
          collapsible="icon"
          destroyInactivePanel="true"
        >
          <a-collapse-panel :key="1">
            <template v-slot:header>
              <div>
                <b>合同信息</b>
                <span class="ml-7"
                  >业务归属人：{{ formData.bizBelongUserName }}</span
                >
              </div>
            </template>

            <a-row style="width: 100%">
              <a-col class="mb-2" :span="6"
                >合同名称：{{ formData.marketContractName }}</a-col
              ><a-col class="mb-2" :span="6"
                >合同类型：

                <dictionary
                  :statusOptions="businessTypes"
                  :value="formData.marketContractType"
                />
              </a-col>
              <a-col class="mb-2" :span="6">
                <span> 合同附件： </span>
                <div style="display: flex; flex-direction: column">
                  <span
                    v-for="(item, index) in formData.marketContractFile"
                    :key="index"
                  >
                    <i
                      class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                      style="color: #959ec3"
                    ></i>
                    <a
                      :href="item.fileVisitUrl"
                      :title="item.fileName"
                      target="_blank"
                      class="underline"
                      style="color: #959ec3"
                      >{{ item.fileName }}
                    </a>
                  </span>
                </div> </a-col
              ><a-col class="mb-2" :span="6"
                >交货日期：{{ formData.marketContractDeliveryTime }}</a-col
              >
              <a-col class="mb-2" :span="6"
                >合同备注：{{ formData.marketContractRemark }}</a-col
              ></a-row
            >
          </a-collapse-panel></a-collapse
        >

        <a-collapse
          bordered="false"
          class="mb-4 w-full"
          collapsible="icon"
          destroyInactivePanel="true"
        >
          <a-collapse-panel>
            <template v-slot:header>
              <div>
                <b>订单信息</b>
                <span class="ml-7">订单编号：{{ formData.marketOrderNo }}</span>
                <span class="ml-7"
                  >总台数：{{ formData.materialQuantity }}</span
                >
              </div>
            </template>
            <a-row style="width: 100%" class="mb-4">
              <a-col :span="8">
                <a-form-item label="订单名称：">
                  <span>{{ formData.marketOrderName || "-" }}</span>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="使用国家：">
                  <span
                    >{{
                      formData.marketUseCountry == 1 ? "国外" : "国内" || "-"
                    }}
                    <span v-if="formData.marketUseCountry == 1"
                      >({{ formData.marketForeignCountry }})</span
                    >
                  </span>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="订单备注：">
                  <span>{{ formData.marketOrderRemark || "-" }}</span>
                </a-form-item>
              </a-col>
              <!-- <a-col :span="6">
                <a-form-item label="订单要求：">
                  <span>{{ formData.productQuantity || "-" }}</span>
                </a-form-item>
              </a-col> -->
            </a-row>
            <a-row style="display: flex; justify-content: space-between">
              <a-col class="mb-4" :span="11">
                单体标识：
                {{
                  getLabelList(
                    orderConfigList.monomerIdentification,
                    formData.orderConfigVO.monomerIdentification
                  )
                }}
              </a-col>
              <a-col class="mb-4" :span="11"
                >包装要求：{{
                  getLabelList(
                    orderConfigList.packingRequirement,
                    formData.orderConfigVO.packingRequirement
                  )
                }}</a-col
              >
              <a-col class="mb-4" :span="11"
                >随货文件：{{
                  getLabelList(
                    orderConfigList.accompanyingDocument,
                    formData.orderConfigVO.accompanyingDocument
                  )
                }}</a-col
              >
              <a-col class="mb-4" :span="11"
                >1.机箱标识(电源)：{{
                  getLabelList(
                    orderConfigList.chassisIdentification,
                    formData.orderConfigVO.chassisIdentification
                  )
                }}</a-col
              >
              <a-col class="mb-4" :span="11"
                >2.说明书(电源)：{{
                  getLabelList(
                    orderConfigList.specification,
                    formData.orderConfigVO.specification
                  )
                }}</a-col
              >
              <a-col class="mb-4" :span="11"
                >3.铭牌(电源)：{{
                  getLabelList(
                    orderConfigList.dogtag,
                    formData.orderConfigVO.dogtag
                  )
                }}</a-col
              >
              <a-col class="mb-4" :span="11"
                >4.充电机铭牌(电源)：{{
                  getLabelList(
                    orderConfigList.chargerNameplate,
                    formData.orderConfigVO.chargerNameplate
                  )
                }}</a-col
              >
              <a-col class="mb-4" :span="11"
                >5.充电机显示屏(电源)：{{
                  getLabelList(
                    orderConfigList.chargerDisplay,
                    formData.orderConfigVO.chargerDisplay
                  )
                }}</a-col
              >
              <a-col class="mb-4" :span="11"
                >6.充电机说明书(电源) ：{{
                  getLabelList(
                    orderConfigList.chargerManual,
                    formData.orderConfigVO.chargerManual
                  )
                }}</a-col
              >
              <a-col class="mb-4" :span="11"
                >特殊要求：{{
                  formData.orderConfigVO.specialRequirements
                }}</a-col
              >
            </a-row>
          </a-collapse-panel>
        </a-collapse>
        <a-collapse
          bordered="false"
          class="mb-4 w-full"
          collapsible="icon"
          destroyInactivePanel="true"
        >
          <a-collapse-panel>
            <template v-slot:header>
              <div class="flex" style="justify-content: space-between">
                <div>
                  <b>订单BOM</b>
                  <span class="ml-5"
                    >公共附件 {{ formData.publicFileLength || "--" }} 个</span
                  >
                  <span class="ml-5"
                    >机密附件
                    {{ formData.confidentialFileLength || "--" }} 个</span
                  >
                </div>
                <div>
                  <mw-button
                    title="导出BOM（财务）"
                    @click="onFinanceExportBom(formData)"
                    v-permission="
                      'production:bomMarketOrder:exportExcelFinancial'
                    "
                    class="mr-1"
                  ></mw-button>
                  <mw-button
                    title="导出BOM"
                    @click="onExportBom(formData)"
                    v-permission="'production:bomMarketOrder:exportExcel'"
                    class="mr-1"
                  ></mw-button>
                  <mw-button
                    title="下载附件"
                    @click="onExportAccessoryZip(formData)"
                    v-permission="
                      'production:bomMarketOrder:exportAccessoryZip'
                    "
                    class="mr-1"
                  ></mw-button>
                </div>
              </div>
            </template>
            <div class="flex">
              <div class="w-1/2">
                订单BOM公共附件上传
                <div class="mt-5">
                  <form-upload-contract
                    :multiple="true"
                    class="mr-2"
                    v-model:value="formData.publicFile"
                    :fileSize="50"
                    sence="orderContract"
                    :hasDownLoad="true"
                    :fileTypes="[]"
                    @done="
                      upload(
                        formData.publicFile,
                        formData.bomOrderOtherId,
                        1,
                        'publicFile'
                      )
                    "
                    :fileLName="'上传附件'"
                    :fileLimit="9999999999"
                    :delShow="true"
                    @del="
                      (index, value) =>
                        del(
                          index,
                          value,
                          formData.bomOrderOtherId,
                          1,
                          'publicFile'
                        )
                    "
                    :flexT="true"
                  ></form-upload-contract>
                  <!-- <a-button type="primary" @click="saveAnnex">保存</a-button> -->
                </div>
              </div>
              <div class="w-1/2">
                订单BOM私密附件上传
                <div class="mt-5">
                  <form-upload-contract
                    :multiple="true"
                    class="mr-2"
                    v-model:value="formData.confidentialFile"
                    :fileSize="50"
                    sence="orderContract"
                    :hasDownLoad="true"
                    :fileTypes="[]"
                    @done="
                      upload(
                        formData.confidentialFile,
                        formData.bomOrderOtherId,
                        1,
                        'confidentialFile'
                      )
                    "
                    :fileLName="'上传附件'"
                    :fileLimit="9999999999"
                    :delShow="true"
                    :flexT="true"
                    @del="
                      (index, value) =>
                        del(
                          index,
                          value,
                          formData.bomOrderOtherId,
                          1,
                          'confidentialFile'
                        )
                    "
                  ></form-upload-contract>
                </div>
              </div>
            </div>
          </a-collapse-panel>
        </a-collapse>
        <div
          v-for="item in formData.bomProductList"
          :key="item.bomNo"
          class="w-full"
        >
          <a-collapse
            v-model:activeKey="collapseActiveKey"
            bordered="false"
            class="mb-4"
            collapsible="icon"
            destroyInactivePanel="true"
            @change="changeCollapse"
          >
            <a-collapse-panel :key="item.index">
              <template v-slot:header>
                <div class="flex justify-between">
                  <div>
                    <b>名称：{{ item.bomName || "-" }}</b>
                    <span class="ml-7"
                      >关联产品：{{ item.materialName || "-" }}</span
                    >
                    <span class="ml-7"
                      >关联编码：{{ item.materialNo || "-" }}</span
                    >

                    <span class="ml-7"
                      >订单台数：{{ item.orderQuantity || "-" }}</span
                    >
                    <span class="ml-7"
                      >产品规格：{{ item.materialSpecification || "-" }}</span
                    >
                  </div>

                  <div v-if="item.bomName != '配件数'">
                    <mw-button
                      title="导出BOM（财务）"
                      @click="onFinanceExportBom(formData, item)"
                      v-if="!item.showSearch"
                      v-permission="
                        'production:bomMarketOrder:exportExcelFinancial'
                      "
                      class="mr-1"
                    ></mw-button>
                    <mw-button
                      title="导出BOM"
                      @click="onExportBom(formData, item)"
                      v-if="!item.showSearch"
                      v-permission="'production:bomMarketOrder:exportExcel'"
                      class="mr-1"
                    ></mw-button>
                    <mw-button
                      title="下载附件"
                      @click="onExportAccessoryZip(formData, item)"
                      v-if="!item.showSearch"
                      v-permission="
                        'production:bomMarketOrder:exportAccessoryZip'
                      "
                      class="mr-1"
                    ></mw-button>

                    <mw-button
                      title="编辑"
                      @click="onEdit(item)"
                      v-if="!item.showSearch && formData.isLocked == 0"
                      class="mr-1"
                    ></mw-button>

                    <a-popconfirm
                      placement="bottom"
                      ok-text="确定"
                      cancel-text="取消"
                      @confirm="newAdd(item)"
                      icon=""
                    >
                      <template #title>
                        <div class="flex">
                          <p class="mt-1">新增条数</p>
                          <a-input-number
                            class="ml-2"
                            v-model:value="inputValue"
                            :stringMode="true"
                          ></a-input-number>
                        </div>
                      </template>

                      <mw-button
                        title="新增"
                        v-if="item.showSearch"
                        :loading="item.tabLoading"
                        class="mr-1"
                      ></mw-button>
                    </a-popconfirm>
                    <!-- <mw-button
                    title="新增"
                    v-if="item.showSearch"
                    :loading="item.tabLoading"
                    class="mr-1"
                    @click="addOne(item)"
                  ></mw-button> -->
                    <mw-button
                      title="引用BOM"
                      v-if="item.showSearch"
                      @click="onQuoteBom(item)"
                      :loading="item.loading || item.tabLoading"
                      class="mr-1"
                    ></mw-button>
                    <mw-button
                      title="保存"
                      v-if="item.showSearch"
                      @click="saveSubmission(item)"
                      :loading="item.loading || item.tabLoading"
                      class="mr-1"
                    ></mw-button>

                    <a-popconfirm
                      title="提示：确定取消本次操作"
                      ok-text="确定"
                      cancel-text="取消"
                      @confirm="onCancellation(item)"
                    >
                      <mw-button title="取消" v-if="item.showSearch">
                      </mw-button>
                    </a-popconfirm>

                    <mw-button
                      title="修改BOM"
                      v-if="!item.showSearch && formData.isLocked == 1"
                      @click="onChangeBomForm(item)"
                      :loading="submitLoading"
                      class="mr-1"
                    ></mw-button>
                  </div>
                </div>
                <div class="w-full">
                  <div class="mt-3">
                    <span class=""
                      >物料种类：{{ item.materialTypeCount || "-" }}</span
                    >
                    <span class="ml-7"
                      >单台物料总数：{{ item.materialCount || "-" }}</span
                    >
                    <span class="ml-7"
                      >产品物料总数：{{ item.materialTotalCount || "-" }}</span
                    >
                    <span class="ml-7"
                      >规格书/技术协议编号：{{
                        item.materialFileNumber || "-"
                      }}</span
                    >
                  </div>

                  <div class="mt-5">
                    <span
                      >产品规格书/技术协议：

                      <span
                        v-if="item.materialFile && item.materialFile.length > 0"
                      >
                        <div
                          v-for="items in item.materialFile"
                          :key="items.fileId"
                          style="display: inline-block"
                        >
                          <i
                            class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                            style="color: #959ec3"
                          ></i>
                          <a
                            :title="items?.fileName"
                            @click="
                              downFile(items.fileVisitUrl, items.fileName)
                            "
                            target="_blank"
                            class="underline"
                            style="color: #959ec3"
                            >{{ items?.fileName }}
                          </a>
                        </div>
                      </span>
                      <span v-else>-</span>
                    </span>
                  </div>
                  <div class="mt-5">
                    <span>订单产品备注： </span>
                    <span class=" ">{{ item.productRemark || "-" }}</span>
                  </div>
                  <div class="mt-5">
                    <span>产品BOM备注： </span>
                    <span
                      class="text-primary cursor-pointer"
                      @click="
                        onDeliveryRemark('修改产品BOM备注', 'bomRemark', item)
                      "
                      >{{ item.bomRemark || "这是产品备注" }}</span
                    >
                  </div>
                  <div class="flex w-full mt-5">
                    <div class="w-1/2">
                      <div class="mb-3">订单BOM公共附件上传</div>

                      <!-- <div v-for="(itex,indx) item.publicFile" :key="indx">
                    </div> -->
                      <div class="mt-5" v-if="formData.orderStatus !== 0">
                        <form-upload-contract
                          :multiple="true"
                          class="mr-2"
                          v-model:value="item.publicFile"
                          :fileSize="50"
                          sence="orderContract"
                          :hasDownLoad="true"
                          :fileTypes="[]"
                          @done="
                            upload(
                              item.publicFile,
                              item.bomOrderId,
                              2,
                              'publicFile'
                            )
                          "
                          :fileLName="'上传附件'"
                          :fileLimit="9999999999"
                          :delShow="true"
                          :flexT="true"
                          @del="
                            (index, value) =>
                              del(
                                index,
                                value,
                                item.bomOrderId,
                                2,
                                'publicFile'
                              )
                          "
                        ></form-upload-contract>
                      </div>
                      <div v-else class="flex flex-col">
                        <div
                          v-for="(ite, ind) in item.publicFile"
                          :key="ind"
                          style="display: inline-block"
                        >
                          <i
                            class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                            style="color: #959ec3"
                          ></i>
                          <a
                            :title="ite?.fileName"
                            @click="downFile(ite.fileVisitUrl, ite.fileName)"
                            target="_blank"
                            class="underline"
                            style="color: #959ec3"
                            >{{ ite?.fileName }}
                          </a>
                        </div>
                      </div>
                      <!-- <div
                      v-else
                      v-for="(ite, ind) in item.publicFile"
                      :key="ind"
                    >
                      <a>{{ ite.fileName }}</a>
                    </div> -->
                    </div>
                    <div class="w-1/2">
                      订单BOM机密附件上传
                      <div class="mt-5">
                        <form-upload-contract
                          class="mr-2"
                          v-model:value="item.confidentialFile"
                          :fileSize="50"
                          sence="orderContract"
                          :hasDownLoad="true"
                          :fileTypes="[]"
                          :multiple="true"
                          @done="
                            upload(
                              item.confidentialFile,
                              item.bomOrderId,
                              2,
                              'confidentialFile'
                            )
                          "
                          :fileLName="'上传附件'"
                          :fileLimit="9999999999"
                          :flexT="true"
                          :delShow="true"
                          @del="
                            (index, value) =>
                              del(
                                index,
                                value,
                                item.bomOrderId,
                                2,
                                'confidentialFile'
                              )
                          "
                        ></form-upload-contract>
                      </div>
                    </div>
                  </div>
                </div>
              </template>

              <mw-table
                v-if="!item.showSearch"
                :columns="columnsList"
                :dataSource="item.materialRelationList"
                :pagination="false"
                :row-key="(r) => r.index"
                :loading="item.tabLoading"
                :scroll="{ x: 'max-content' }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex == 'materialNo'"
                    >{{ record.materialNo }}
                    <span v-if="record.isStop == 1">(已停用) </span></template
                  ></template
                >
              </mw-table>
              <mw-table
                v-if="item.showSearch"
                :dataSource="item.materialRelationList"
                :columns="columns"
                :pagination="false"
                :row-key="(r) => r.index"
                :loading="item.tabLoading"
                :scroll="{ x: 'max-content' }"
              >
                <template #expandColumnTitle="{}">
                  <p style="margin: 0"></p>
                </template>
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.dataIndex == 'materialId'">
                    <div v-if="record.add">
                      <a-select
                        v-if="record.add"
                        v-model:value="record.materialId"
                        show-search
                        placeholder="请选择关联产品"
                        style="width: 100%"
                        :default-active-first-option="false"
                        :options="AllMaterialList"
                        optionFilterProp="materialName"
                        :fieldNames="{
                          label: 'materialName',
                          value: 'materialId',
                        }"
                        @change="changeMaterial($event, index, item, record)"
                        @search="onSearch"
                      ></a-select>
                      <!-- (已停用) -->
                      <span v-else
                        >{{ record.materialNo }}/{{ record.materialName }} /
                        {{ record.specification }}</span
                      >
                    </div>
                    <div v-else>
                      <a-select
                        v-if="item.showSearch"
                        v-model:value="record.materialId"
                        show-search
                        placeholder="请选择关联产品"
                        style="width: 100%"
                        :default-active-first-option="false"
                        :options="AllMaterialList"
                        optionFilterProp="materialName"
                        :fieldNames="{
                          label: 'materialName',
                          value: 'materialId',
                        }"
                        @change="changeMaterial($event, index, item, record)"
                        @search="onSearch"
                      ></a-select>
                      <span v-else
                        >{{ record.materialNo }}/{{ record.materialName }} /{{
                          record.specification
                        }}</span
                      >
                    </div>
                    <!-- <span v-else>{{ record.materialName }}(已停用)</span> -->
                  </template>
                  <template v-if="column.dataIndex == 'quantity'">
                    <a-input-number
                      v-model:value="record.quantity"
                      v-if="item.showSearch"
                      :formatter="formatter4"
                      :stringMode="true"
                    />
                    <span v-else>{{ record.quantity }}</span>
                    {{ record.unit }}
                  </template>
                  <template v-if="column.dataIndex == 'totalQuantity'">
                    <span v-if="record.quantity && item.orderQuantity">
                      {{
                        (record.totalQuantity = (
                          record.quantity * item.orderQuantity
                        ).toFixed(2))
                      }}{{ record.unit }}</span
                    >
                  </template>
                  <template v-if="column.dataIndex == 'availableStock'">
                    {{ record.availableStock }}
                    {{ record.unit }}
                  </template>

                  <template v-if="column.dataIndex == 'lineName'">
                    <a-select
                      class="w-full"
                      show-search
                      placeholder="请选择生产线"
                      optionFilterProp="lineName"
                      :options="lineAllListOptions"
                      :not-found-content="null"
                      v-model:value="record.lineId"
                      :default-active-first-option="false"
                      :fieldNames="{
                        label: 'lineName',
                        value: 'id',
                      }"
                    ></a-select>
                  </template>

                  <template v-if="column.dataIndex == 'sortNumber'">
                    <a-input-number
                      v-if="item.showSearch"
                      v-model:value="record.sortNumber"
                      maxlength="5"
                      :stringMode="true"
                    />
                    <div v-else>{{ record.sortNumber }}</div>
                  </template>
                  <template v-if="column.dataIndex == 'remark'">
                    <a-input
                      v-if="item.showSearch"
                      v-model:value="record.remark"
                    ></a-input>
                    <div v-else>{{ record.remark }}</div>
                  </template>

                  <template v-if="column.dataIndex == 'operate'">
                    <mw-button
                      title="删除"
                      v-if="item.showSearch"
                      @click="onDelete(record, item, index)"
                      danger
                    ></mw-button>
                  </template>
                </template>
              </mw-table>
            </a-collapse-panel>
          </a-collapse>

          <!-- </div>
        </div> -->
          <!-- <div style="width: 49%">
          <div class="mb-5 mt-5">
            <b>物料合计</b>
          </div>
          <mw-table
            :dataSource="formData.materialRelationList"
            :columns="columns"
            :pagination="false"
          />
        </div> -->
        </div>
      </a-form>

      <add-contract-review-drawer
        ref="refAddContractReviewDrawer"
        v-model:visible="visibleAddContractReviewDrawer"
        :marketOrderId="marketOrderIdVal"
        :productQuantity="productQuantity"
        :BomData="BomData"
        :detailsVal="detailsVal"
        @finish="bomMarketOrderDetail(route.query.id)"
      />
      <quoteBom
        v-model:visible="quoteBomVisible"
        :bomNoData="bomNoData"
        @newArr="onNewArrList"
      ></quoteBom>
      <PublicRemarks
        v-model:visible="publicRemarksVisible"
        :publicRemarksTitle="publicRemarksTitle"
        :remarkType="remarkType"
        :remarkRecord="remarkRecord"
        @finish="bomMarketOrderDetail(route.query.id)"
      />
      <addAccessoriesOrderDrawer
        v-model:visible="addAccessoriesOrderDrawerVisible"
        :marketOrderId="formData.marketOrderId"
        @finish="bomMarketOrderDetail(route.query.id)"
      />
    </div>
  </a-spin>
</template>

<script setup>
import FormUploadContract from "@/components/form-upload-contract.vue";
import {
  defineProps,
  defineEmits,
  ref,
  getCurrentInstance,
  watch,
  onBeforeMount,
  nextTick,
  computed,
} from "vue";
import PublicRemarks from "@/components/publicRemarks.vue";
import _cloneDeep from "lodash/cloneDeep";
import { formatter4 } from "@/common/validate.js";
import addContractReviewDrawer from "./addContractReviewDrawer.vue";
import {
  updateV1,
  updateFile,
  apiExportBom,
  apiFinanceExportBom,
  getBomMarketOrderDetail,
  apiExportAccessoryZip,
} from "@/api/basicData/orderBomChange.js";
import { AllList, pageV1 } from "@/api/basicData/material.js";
import { useRouter, useRoute } from "vue-router";
import quoteBom from "./quoteBom.vue";
import { orderConfigList, businessTypes } from "@/common/constant.js";
import { dateFormat } from "@/utils/util.js";
import { detail } from "../../../api/purchase/requistion";
import { exportExecl } from "@/utils/util.js";
import { downFile } from "@/common/setup/index.js";
import { list as getLineAllList } from "@/api/basicData/productionLine.js";
import { roundNumFun } from "@/common/validate.js";
import addAccessoriesOrderDrawer from "./addAccessoriesOrderDrawer.vue";
import { debounce } from "lodash";
const contractActiveKey = ref([]);
const confidentialFile = ref([]);
const publicFile = ref([]);
const orderActiveKey = ref([]);
const annexKey = ref();
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const spinning = ref(false);
const productQuantity = ref(undefined);
const dataSource = ref([]);
const marketOrderIdVal = ref();
const visibleAddContractReviewDrawer = ref(false);
const BomData = ref();
const editShow = ref(true);
const otherShow = ref(false);
const AllMaterialList = ref();
const quoteBomVisible = ref(false);
const bomNoData = ref();
const tabLoading = ref(false);
const collapseActiveKey = ref();
const exportLoading = ref(false);
const inputValue = ref();
const lineAllListOptions = ref([]);
const detailsVal = ref();
const publicRemarksVisible = ref(false);
const publicRemarksTitle = ref();
const remarkType = ref();
const remarkRecord = ref();
const formData = ref({
  confidentialFile: [],
  bomName: undefined,
  fileDetailModel: [],
  productNo: undefined,
  materialRelationList: [],
});
const modifyInformationColumns = ref([
  { title: "操作人", dataIndex: "createBy", key: "createBy" },
  { title: "操作时间", dataIndex: "createTime", key: "createTime" },
  { title: "订单号", dataIndex: "orderNo", key: "orderNo" },
  { title: "产品编码", dataIndex: "bomNo", key: "bomNo" },
  { title: "详情", dataIndex: "modifyDetail", key: "modifyDetail" },
]);
const columnsList = ref([
  {
    title: "物料编号",
    dataIndex: "materialNo",
    key: "materialNo",
    width: "180px",
  },
  {
    title: "物料名称",
    dataIndex: "materialName",
    key: "materialName",
    width: "180px",
  },
  {
    title: "物料规格",
    dataIndex: "specification",
    key: "specification",
    width: "180px",
  },

  {
    title: "经济物料",
    dataIndex: "isEconomicMaterial",
    key: "isEconomicMaterial",
    customRender: ({ record }) => {
      return record.isEconomicMaterial == 1
        ? "是"
        : record.isEconomicMaterial == 0
        ? "否"
        : "";
    },
  },
  {
    title: "生产线",
    dataIndex: "lineName",
    key: "lineName",
    customRender: ({ record }) => {
      return record.lineName ? record.lineName : "无";
    },
  },

  // {
  //   title: "单位",
  //   dataIndex: "unit",
  //   key: "unit",
  // },
  {
    title: "物料单台数量",
    dataIndex: "quantity",
    key: "quantity",
    customRender: ({ record }) => {
      return record.quantity + record.unit;
    },
  },

  {
    title: "物料合计",
    dataIndex: "totalQuantity",
    key: "totalQuantity",
    customRender: ({ record }) => {
      return roundNumFun(record.totalQuantity, 2) + record.unit;
    },
  },

  {
    title: "库存数量",
    dataIndex: "availableStock",
    key: "availableStock",
    customRender: ({ record }) => {
      return record.availableStock + record.unit;
    },
  },
  {
    title: "排序",
    dataIndex: "sortNumber",
    key: "sortNumber",
  },
  {
    title: "备注",
    dataIndex: "remark",
    key: "remark",
  },
  {
    title: "操作",
    dataIndex: "operate",
    key: "operate",
    fixed: "right",
  },
]);

const columns = ref([
  {
    title: "物料编码/名称/规格",
    dataIndex: "materialId",
    key: "materialId",
  },
  {
    title: "经济物料",
    dataIndex: "isEconomicMaterial",
    key: "isEconomicMaterial",
    customRender: ({ record }) => {
      return record.isEconomicMaterial == 1
        ? "是"
        : record.isEconomicMaterial == 0
        ? "否"
        : "";
    },
  },
  {
    title: "生产线",
    dataIndex: "lineName",
    key: "lineName",
    width: "180px",
  },

  // {
  //   title: "单位",
  //   dataIndex: "unit",
  //   key: "unit",
  // },
  {
    title: "物料单台数量",
    dataIndex: "quantity",
    key: "quantity",
  },

  {
    title: "物料合计",
    dataIndex: "totalQuantity",
    key: "totalQuantity",
  },

  {
    title: "库存数量",
    dataIndex: "availableStock",
    key: "availableStock",
  },
  {
    title: "排序",
    dataIndex: "sortNumber",
    key: "sortNumber",
  },
  {
    title: "备注",
    dataIndex: "remark",
    key: "remark",
  },
  {
    title: "操作",
    dataIndex: "operate",
    key: "operate",
    fixed: "right",
  },
]);
const debouncedSearch = debounce(async (e) => {
  console.log("[ e ] >", e);
  AllMaterial(e);
  //
}, 500);
const onSearch = async (e) => {
  debouncedSearch(e);
  //
};
onBeforeMount(async () => {
  await bomMarketOrderDetail(route.query.id);
  let ids = formData.value.materialRelationList.map((item) => {
    return item.materialId;
  });
  await AllMaterial(undefined, ids);
  await lineAllList();
});
// 使用计算属性来根据条件筛选需要展示的列
// let visibleColumns = computed(() => {
//   return columns.value.filter((column) => column.dataIndex !== "materialId"); // 如果不
// });
const getLabelList = (config, valueList) => {
  const labelList = valueList.map((value) => {
    const item = config.find((item) => item.value === value);
    return item ? item.label : "";
  });
  return labelList.join(" ， ");
};
const del = async (index, value, id, type, fileType) => {
  let param = {
    id: id,
    type: type,
    fileType: fileType == "publicFile" ? 1 : 2,
  };
  if (fileType == "confidentialFile") {
    param.confidentialFile = value;
  } else {
    param.publicFile = value;
  }
  let res = await updateFile(param);
  if (res.code == 200) {
    bomMarketOrderDetail(route.query.id);
    proxy.$message.success("删除成功");
  }
};
//  publicFile,'publicFile',formData.bomOrderOtherId,1
const upload = async (val, id, type, fileType) => {
  let param = {
    id: id,
    type: type,
    fileType: fileType == "publicFile" ? 1 : 2,
  };
  if (fileType == "confidentialFile") {
    param.confidentialFile = val;
  } else {
    param.publicFile = val;
  }
  let res = await updateFile(param);
  if (res.code == 200) {
    bomMarketOrderDetail(route.query.id);
    // proxy.$message.success("上传成功");
  }
};
// const saveAnnex = async () => {
//   await updateFile();
// };
// 预览
const openUrl = (url) => {
  window.open(url, "_blacnk");
};
const onDeliveryRemark = (title, type, val) => {
  console.log(val, "val");
  publicRemarksVisible.value = true;
  publicRemarksTitle.value = title;
  remarkType.value = type;
  remarkRecord.value = val;
};
const onCancellation = (item) => {
  bomMarketOrderDetail(route.query.id);
  formData.value.bomProductList.forEach((element, index) => {
    if (element.bomNo == item.bomNo) {
      element.showSearch = false;
    } else {
      element.showSearch = false;
    }
  });
  collapseActiveKey.value = [];
};

const changeCollapse = (e) => {
  collapseActiveKey.value = e.index;
};
const bomMarketOrderDetail = async (val) => {
  let res = await getBomMarketOrderDetail({ bomNo: val });
  res.data.confidentialFile = res.data.confidentialFile ?? [];
  res.data.publicFile = res.data.publicFile ?? [];
  res.data.confidentialFileLength = res.data.confidentialFile.length ?? 0;
  res.data.publicFileLength = res.data.publicFile.length ?? 0;
  // res.data.bomProductList.forEach((item) => {
  //   item.otherShow = false;
  //   item.editShow = true;
  //   item.loading = false;
  //   item.confidentialFile = item.confidentialFile ?? [];
  //   item.publicFile = item.publicFile ?? [];
  // });
  // res.data.bomModifyList.map((item, index) => {
  //   item.modifyDetail = JSON.parse(item.modifyDetail);
  //   return item;
  // });
  formData.value = res.data;
  formData.value.bomProductList = res.data.bomProductList.map((item) => {
    return {
      ...item,
      itemotherShow: false,
      itemeditShow: true,
      itemloading: false,
      itemconfidentialFile: item.confidentialFile ?? [],
      publicFile: item.publicFile ?? [],
      showSearch: false,
    };
  });
};
const onChangeBomForm = (val) => {
  visibleAddContractReviewDrawer.value = true;
  // marketOrderId
  productQuantity.value = val.orderQuantity;
  BomData.value = val.bomNo;
  marketOrderIdVal.value = formData.value.marketOrderId;
  console.log(val, "val");
  detailsVal.value = val;
};
const AllMaterial = async (keyword, ids) => {
  let res = await pageV1(
    {
      pageSize: keyword ? 50 : ids.length < 50 ? 50 : undefined,
    },
    {
      ignoreStop: true, //是否过滤停用物料
      keyword,
      pageNum: 1,
      // pageSize: 99999,
      materialIds: ids,
    }
  );
  AllMaterialList.value =
    res.data &&
    res.data.map((item, index) => {
      // item.materialId = String(item.id);
      // item.materialName =
      //   item.materialNo + "/" + item.materialName + "/" + item.specification;
      // return item;
      return {
        materialName:
          item.materialNo + "/" + item.materialName + "/" + item.specification,
        materialId: String(item.id),
        lineName: item.lineName,
        isEconomicMaterial: item.isEconomicMaterial,
        specification: item.specification,
        materialNo: item.materialNo,
        quantity: item.quantity,
        totalQuantity: item.totalQuantity,
        availableStock: item.availableStock,
        sortNumber: item.sortNumber,
        remark: item.remark,
        unit: item.unit,
        unitName: item.unitName,
        lineId: item.lineId,
      };
    });
};
const onEdit = (item) => {
  collapseActiveKey.value = [item.index];
  formData.value.bomProductList.forEach((itemD) => {
    if (itemD.index == item.index) {
      itemD.showSearch = true;
    } else {
      itemD.showSearch = false;
    }
  });
  if (item.materialRelationList.length > 0) {
    item.tabLoading = true;
    const data = _cloneDeep(item);
    item.materialRelationList = [];
    data.materialRelationList.forEach((ite) => {
      setTimeout(() => {
        ite.materialId = String(ite.materialId);
        ite.materialName =
          ite.materialNo + "/" + ite.materialName + "/" + ite.specification;
        item.materialRelationList.push(ite);
        if (
          item.materialRelationList.length >= data.materialRelationList.length
        ) {
          item.tabLoading = false;
        }
      });
    });
  } else {
    item.showSearch = true;
  }
};

const addOne = (val) => {
  val.tabLoading = true;
  formData.value.bomProductList.forEach((element, index) => {
    if (element.bomNo == val.bomNo) {
      if (!element.materialRelationList) {
        element.materialRelationList = [];
      }
      setTimeout(() => {
        const sortNumbers = element.materialRelationList.map((item) =>
          Number(item.sortNumber)
        );
        const maxSortNumber =
          sortNumbers.length > 0 ? Math.max(...sortNumbers) : 0; // 获取最大值

        element.materialRelationList.unshift({
          briefId: Date.now(),
          add: true,
          sortNumber: `${maxSortNumber + 1}`,
        });
        val.tabLoading = false;
      }, 100);
    }
  });
};
const newAdd = (val) => {
  val.tabLoading = true;
  formData.value.bomProductList.forEach((element, index) => {
    if (element.bomNo == val.bomNo) {
      if (!element.materialRelationList) {
        element.materialRelationList = [];
      }
      // element.materialRelationList.sort((a, b) => {
      //   // 首先尝试将 sortNumber 转换为数字类型进行比较
      //   const aSortNumber = Number(a.sortNumber);
      //   const bSortNumber = Number(b.sortNumber);

      //   // 如果转换成功，则按照数字大小进行排序
      //   if (!isNaN(aSortNumber) && !isNaN(bSortNumber)) {
      //     return aSortNumber - bSortNumber;
      //   }

      //   // 如果转换失败（即为字符串类型），则按照字符串比较规则进行排序
      //   return a.sortNumber.localeCompare(b.sortNumber);
      // }); // 对 arr 进行排序

      setTimeout(() => {
        for (let i = 0; i < inputValue.value; i++) {
          const sortNumbers = element.materialRelationList.map((item) =>
            Number(item.sortNumber)
          ); // 将 sortNumber 转换为数字类型的数组
          const maxSortNumber =
            sortNumbers.length > 0 ? Math.max(...sortNumbers) : 0; // 获取最大值
          element.materialRelationList.unshift({
            briefId: Date.now(),
            add: true,
            sortNumber: `${maxSortNumber + 1}`,
          });
          val.tabLoading = false;
        }
        inputValue.value = void 0;
      }, 100);
    }
  });
};
const changeMaterial = (e, ind, v, record) => {
  console.log(e, ind, v, record);
  const option = AllMaterialList.value.find((item) => item.materialId == e);
  record.materialNo = option?.materialNo;
  record.materialName = option.materialName;
  record.specification = option.specification;
  record.quantity = option.quantity;
  record.requiresMaterialQuantity = option.quantity;
  // record.totalQuantity = option.totalQuantity;
  record.lineId = option.lineId;
  record.availableStock = option.availableStock;
  record.remark = option.remark;
  record.unit = option.unitName ? option.unitName : "--";
  record.isEconomicMaterial = option.isEconomicMaterial;
};

const saveSubmission = async (value) => {
  try {
    const data = _cloneDeep(value.materialRelationList);
    value.materialRelationList.forEach((item, index) => {
      item.requiresMaterialQuantity = item.quantity;
    });
    // 使用 find() 方法查找第一个重复的值
    const hasEmptyName = Array.from(value.materialRelationList).every(
      (obj) => obj.materialId && obj.quantity
    );
    if (!hasEmptyName) {
      proxy.$message.error(
        "存在名称为空或物料单台数为0的数据，请修改后重新保存"
      );
      return;
    }
    const newArrListData = value.materialRelationList.map(
      (item) => item.materialId
    );
    // value.materialRelationList.forEach((item, index) => {
    //   delete item.relationId;
    // });

    const isRepeat = newArrListData.some(
      (item, index, arr) => arr.indexOf(item) != index
    );
    if (isRepeat) {
      proxy.$message.error("物料不可重复!");
      return;
    }
    let prams = {
      applicationDate: dateFormat(new Date(), "YYYY-MM-DD"),
      ...value,
      allParam: value.materialRelationList,
      remark: "11",
      orderId: formData.value.marketOrderId,
      applicant: formData.value.bizBelongUserName,
    };
    // delete prams.materialRelationList;
    value.tabLoading = true;
    let res = await updateV1(prams);
    if (res.code == 200) {
      proxy.$message.success("操作成功");
      await bomMarketOrderDetail(route.query.id);
      value.tabLoading = false;
    }
    value.loading = false;
    collapseActiveKey.value = [];
  } catch (error) {
    value.tabLoading = false;
  } finally {
    value.loading = false;
    value.tabLoading = false;
  }
};
const onQuoteBom = (item) => {
  quoteBomVisible.value = true;
  bomNoData.value = item.bomNo;
};

const addNum = (num1, num2) => {
  var sq1, sq2, m;
  try {
    sq1 = num1.toString().split(".")[1].length;
  } catch (e) {
    sq1 = 0;
  }
  try {
    sq2 = num2.toString().split(".")[1].length;
  } catch (e) {
    sq2 = 0;
  }
  m = Math.pow(10, Math.max(sq1, sq2));
  return (num1 * m + num2 * m) / m;
};

const onNewArrList = async (resultArr, bomNoData, selectedBom) => {
  // val.tabLoading = true;
  let formIds = formData.value.materialRelationList.map((item) => {
    return item.materialId;
  });
  let newIds = resultArr.map((item) => {
    return item.materialId;
  });
  const allIds = [...formIds, ...newIds];
  // 去重
  const set = new Set(allIds);
  const newArr = [...set];
  await AllMaterial(undefined, newArr);
  const mapArr = new Map();

  const filterArr = formData.value.bomProductList.filter(
    (item) => item.bomNo == bomNoData
  )[0];
  console.log("[ filterArr ] >", bomNoData, filterArr);
  const materialRelationList = _cloneDeep(
    filterArr?.materialRelationList || []
  );
  const maxSortNumberNum =
    materialRelationList.length > 0
      ? Math.max(
          ...(materialRelationList || []).map((ite) => {
            return ite.sortNumber;
          })
        )
      : 0;
  const le = maxSortNumberNum + Number(resultArr.length);
  for (let i = maxSortNumberNum + 1; i <= le; i++) {
    // 将 < 号修改为 <=
    resultArr.forEach((item) => (item.sortNumber = i++));
  }

  const newArrLists = _cloneDeep(resultArr || []);
  const filterArrIndex = formData.value.bomProductList.findIndex(
    (item) => item.bomNo == bomNoData
  );
  if (materialRelationList.length >= 0) {
    filterArr.tabLoading = true;
    if (filterArrIndex != -1) {
      formData.value.bomProductList[filterArrIndex].tabLoading = true;
    }

    filterArr.loading = true;

    const arr = [...materialRelationList, ...newArrLists];
    arr.forEach((item) => {
      if (mapArr.has(item.materialId)) {
        const obj = mapArr.get(item.materialId);
        obj.quantity = addNum(obj?.quantity || 0, item?.quantity || 0);
      } else {
        mapArr.set(item.materialId, item);
      }
    });

    const arrList = [];
    for (let arr of mapArr) {
      arrList.push(arr[1]);
    }
    filterArr.materialRelationList = [];

    arrList.forEach((items) => {
      setTimeout(() => {
        items.materialId = items.materialId ? String(items.materialId) : void 0;
        filterArr.materialRelationList.push(items);
        if (filterArr.materialRelationList.length >= arrList.length) {
          filterArr.loading = false;
          filterArr.tabLoading = false;
          if (filterArrIndex != -1) {
            formData.value.bomProductList[filterArrIndex].tabLoading = false;
          }
        }
      }, 10);
    });
  }

  if (selectedBom && selectedBom.publicFile) {
    filterArr.publicFile = filterArr.publicFile.concat(selectedBom.publicFile);
  }
  if (selectedBom && selectedBom.confidentialFile) {
    filterArr.confidentialFile = filterArr.confidentialFile.concat(
      selectedBom.confidentialFile
    );
  }
};
const onDelete = (val, data, index) => {
  console.log(val, data, index);
  const materialRelationList = data.materialRelationList;
  materialRelationList.splice(index, 1);
};

const filterData = (data, id) => {
  const boolean = data?.some((item) => item.id == id);
  return boolean;
};
const onFinanceExportBom = async (value, item) => {
  exportLoading.value = true;
  console.log(value, "value", item);
  let prams = {
    marketOrderId: formData.value.marketOrderId,
    bomOrderId: item ? item.bomOrderId : void 0,
  };
  let res = await apiFinanceExportBom(prams);

  const fileName = item
    ? value.marketOrderNo + "_" + item.materialNo + "_" + "bom.xlsx"
    : value.marketOrderNo + "_" + "bom.xlsx";
  exportExecl(fileName, res);
  exportLoading.value = false;
};

const onExportBom = async (value, item) => {
  console.log(value, "value");
  exportLoading.value = true;
  let prams = {
    marketOrderId: formData.value.marketOrderId,
    bomOrderId: item ? item.bomOrderId : void 0,
  };
  let res = await apiExportBom(prams);
  const fileName = item
    ? value.marketOrderNo + "_" + item.materialNo + "_" + "bom.xlsx"
    : value.marketOrderNo + "_" + "bom.xlsx";
  exportExecl(fileName, res);
  exportLoading.value = false;
};
const onExportAccessoryZip = async (value, item) => {
  try {
    // 输出 value 和 formData 的值
    console.log(value, "value");
    exportLoading.value = true;
    let prams = {
      marketOrderId: formData.value.marketOrderId,
      bomOrderId: item ? item.bomOrderId : void 0,
    };

    let res = await apiExportAccessoryZip(prams);
    const fileName = item
      ? value.marketOrderNo + "_" + item.materialNo + "_" + "附件.zip"
      : value.marketOrderNo + "_" + "附件.zip";
    exportExecl(fileName, res);
  } catch (error) {
    // 输出错误信息
  }
};
const lineAllList = async () => {
  const { code, data } = await getLineAllList({
    pageNum: 1,
    pageSize: 99999,
  });
  if (code == 200) {
    lineAllListOptions.value = [{ lineName: "无", id: null }, ...data];
  }
};

const addAccessoriesOrderDrawerVisible = ref(false);
const NewAccessoriesOrder = () => {
  //
  addAccessoriesOrderDrawerVisible.value = true;
};
const hasACCESSORY_ORDER = computed(() => {
  console.log(formData.value.bomProductList, 2, 2, 2);
  return (
    Array.isArray(formData.value.bomProductList) &&
    formData.value.bomProductList.some((item) => item.bomName === "配件数")
  );

  // if (
  //   !Array.isArray(formData.value.bomProductList) ||
  //   formData.value.bomProductList.length === 0
  // )
  //   return false;
  // return formData.value.bomProductList.some(
  //   (item) => item.bomName == '配件数"'
  // );
});
</script>

<style></style>
