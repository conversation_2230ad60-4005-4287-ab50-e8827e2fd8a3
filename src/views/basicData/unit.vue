<template>
  <div class="mb-4 text-right">
    <mw-button
      v-permission="'common:unit:add'"
      @click="onOpen"
      :font="'iconfont icon-xianxing-121'"
      >新增</mw-button
    >
  </div>
  <mw-table
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'button'">
        <a-popconfirm
          title="确定是否删除"
          ok-text="是"
          cancel-text="否"
          @confirm="onConfirm(record)"
          @cancel="onCancel(record)"
        >
          <mw-button
            danger
            v-permission="'common:unit:edit'"
            :disabled="record.tenantId != userStore.user.tenantId"
            >删除</mw-button
          >
        </a-popconfirm>
      </template>
    </template>
  </mw-table>
  <mw-drawer
    :custom-title="(formData.id ? '修改' : '新增') + '单位'"
    :visible="addUnitDrawer"
    @close="onClose"
    closeText="取消"
  >
    <template v-slot:header>
      <mw-button @click="submitForm" :loading="submitLoading">确定</mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
    >
      <a-form-item label="新增单位" name="name">
        <a-input
          v-model:value="formData.name"
          placeholder="请输入新增单位的名称，例如：个，千克等"
          :maxLength="8"
        ></a-input>
      </a-form-item>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import { message } from "ant-design-vue";
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import { page, add, update, unitDelete } from "@/api/basicData/unit.js";
import { usePagenation } from "@/common/setup";
import { useUserStore } from "@/stores/user.js";
const userStore = useUserStore();
const columns = ref([
  {
    title: "单位名称",
    dataIndex: "name",
    key: "production",
    width: "360px",
  },
  {
    title: "添加人员",
    dataIndex: "createName",
    key: "createName",
    width: "360px",
  },
  {
    title: "添加时间",
    dataIndex: "createTime",
    key: "createTime",
    width: "180px",
  },
  {
    title: "删除",
    dataIndex: "button",
    key: "button",
    width: "180px",
  },
]);
const { proxy } = getCurrentInstance();
const data = ref([]),
  addUnitDrawer = ref(false),
  loading = ref(false),
  formRef = ref(),
  submitLoading = ref(false),
  formData = reactive({
    name: undefined,
  }),
  rules = ref({
    name: [
      {
        required: true,
        message: "请输入新增单位的名称，例如：个，千克等",
        trigger: "blur",
      },
    ],
  });
const getList = async () => {
  loading.value = true;
  let result = await page(pageParam.value);
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
const onOpen = () => {
  addUnitDrawer.value = true;
};
const onClose = () => {
  formRef.value.resetFields();
  formData.id = undefined;
  addUnitDrawer.value = false;
};
const submitForm = () => {
  formRef.value
    .validate()
    .then(async () => {
      submitLoading.value = true;
      if (formData.id) {
        let res = await update(formData);
        if (res.code == 200) {
          proxy.$message.success("编辑成功");
          onClose();
        }
      } else {
        let res = await add(formData);
        if (res.code == 200) {
          proxy.$message.success("添加成功");
          onClose();
        }
      }
      submitLoading.value = false;
      await getList();
    })
    .catch((error) => {
      submitLoading.value = false;
    });
};

const rowClick = (record) => {
  return {
    onClick: (event) => {
      formData.name = record.name;
      formData.id = record.id;
      onOpen();
    },
  };
};

async function onConfirm(record) {
  let resultData = await unitDelete({ id: record.id });
  getList();
}
const onCancel = (record) => {
  // message.error('Click on No');
};
</script>
<style lang="less" scoped>
:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  // background: transparent;
}
:deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
  // background: transparent;
}
</style>
