<template>
  <div class="new-material">
    <div class="search-box"></div>

    <search :searchData="searchData" @search="refresh">
      <a-checkbox
        v-model:checked="onlyEconomicalMaterials"
        @change="changeOnlyEconomicalMaterials"
        >只看经济物料</a-checkbox
      >
      <mw-button
        @click="exportOrg"
        :loading="exportLoading"
        :disabled="!dataSource?.length"
        v-permission="'store:material:export'"
        >导出Excel</mw-button
      >
      <mw-button
        v-permission="'store:material:classification'"
        @click="
          $router.push({
            name: 'Category',
            query: { type: 'newmaterial' },
          })
        "
      >
        分类管理
      </mw-button>
      <mw-button @click="showVisible" :font="'iconfont icon-xianxing-121'"
        >新增</mw-button
      >
    </search>
    <mw-table
      :scroll="{ x: 'max-content' }"
      class="leading-5.5"
      :columns="columns"
      hasPage
      :data-source="dataSource"
      :rowKey="(record) => record.id"
      :loading="tableLoading"
      :pageConfig="paginationProps"
      @change="onTableChange"
    >
      <template #bodyCell="{ record, column }">
        <!-- <template v-if="column.key == 'file'">
          <div v-for="(item, index) in record.file" :key="index">
            <i
              class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
              style="color: #959ec3"
              download
            ></i>
            <div
              @click="downFile(item.fileVisitUrl, item.fileName)"
              :href="item.fileVisitUrl"
              :title="item.fileName"
              class="inline underline cursor-pointer break-all whitespace-nowrap align-middle max-w-full"
              style="color: #959ec3"
            >
              {{ item.fileName }}
            </div>
          </div>
        </template> -->

        <template v-if="column.key == 'remark'">
          <a-tooltip
            placement="topLeft"
            :title="record.remark ? record.remark : '无'"
          >
            <div
              class="w-20 truncate cursor-pointer"
              style="color: rgb(24, 144, 255)"
            >
              【查看】
            </div>
          </a-tooltip>
        </template>
        <template v-if="column.key == 'status'">
          <div
            v-if="record.isStop"
            style="
              font-size: 12px;
              background: rgba(34, 34, 34, 0.04);
              color: rgb(34, 34, 34);
              height: 22px;
              line-height: 22px;
              padding: 0 8px;
              display: inline-block;
            "
          >
            停用
          </div>
          <dictionary
            v-else
            :statusOptions="materialStatus"
            :value="record.status"
            :statusExtend="record.statusExtend"
            isBackgroundColor
          />
        </template>

        <template v-if="column.key == 'classificationName'">
          <span v-if="record.classificationName">{{
            record.classificationParentName
          }}</span>
          <span
            v-if="record.classificationName && record.classificationParentName"
            >/</span
          >
          <span v-if="record.classificationParentName">{{
            record.classificationName
          }}</span>
        </template>
        <template v-if="column.key == 'isExpense'">
          <span v-if="record.isExpense == 1">
            <p>是</p>
            <p v-if="record.isEconomicMaterial == 1">经济物料</p>
          </span>
          <span v-if="record.isExpense == 0">
            <p>否</p>
            <p v-if="record.isEconomicMaterial == 1">经济物料</p>
          </span>
        </template>
        <template v-if="column.key == 'action'">
          <!--   :disabled="record.tenantId != userStore.user.tenantId" -->
          <mw-button @click="editClick(record)">编辑</mw-button>
          <mw-button @click.stop="generateQRCode(record)" class="ml-2"
            >生成二维码</mw-button
          >
        </template>
      </template>
    </mw-table>

    <mw-drawer
      :visible="visible"
      @close="onClose()"
      width="50%"
      :custom-title="
        editId
          ? '修改物料' +
            `&nbsp &nbsp &nbsp 物料编码：${materialNo} &nbsp &nbsp状态： ${
              isStatus == 0 ? '可用' : '停用'
            }`
          : '新增物料'
      "
    >
      <template #header>
        <div v-if="isStop == 0">
          <a-popconfirm
            title="确认取消？"
            v-if="canEdit"
            @confirm="submitCancel"
            placement="bottomRight"
          >
            <mw-button :loading="cancelLoading">停用物料</mw-button>
          </a-popconfirm>
        </div>
        <mw-button @click="formSubmit" :loading="loading">保存</mw-button>
        <mw-button @click="reStart" :loading="loading" v-if="editId && isStop"
          >重新启用</mw-button
        >
      </template>
      <add-new-material ref="addMaterial" :clickData="clickData" :id="editId" />
      <a-modal
        v-model:visible="remarkVisible"
        title=""
        @ok="handleConfirm"
        :confirmLoading="confirmLoading"
      >
        <div style="padding: 20px">
          <div class="mb-3">物料重新启用备注：</div>
          <a-input
            v-model:value="remark"
            placeholder="输入备注"
            allow-clear
          ></a-input>
        </div>
      </a-modal>
    </mw-drawer>
  </div>
  <a-modal v-model:visible="qrVisible" title="" @ok="handleOk">
    <a-row>
      <!-- <a-col  :span="24" class="top center"> 
          <a-radio-group v-model:value="item.value" @change="handleOptionChange($event,item.id,item.id,item.e,item.value)" :options="plainOptions">
          </a-radio-group> 
        </a-col> -->
      <a-col :span="24" class="top center">
        <div v-if="qrCodeDataimg" class="erwm mt-6" id="qrCodeDataimg">
          <div
            ref="printContent"
            id="print-content"
            class="flex items-center gap-3 w-full"
          >
            <img id="qrCodeData" :src="qrCodeDataimg" alt="QR Code" />
            <div class="qrcodetitle flex-1" id="qrcodetitle">
              <p>物料编号：{{ selectMaterial.materialNo }}</p>
              <!-- <p>仓库地址：{{ warehouseAddress }}</p> -->
              <p>物料名称：{{ selectMaterial.materialName }}</p>
              <p>型号规格：{{ selectMaterial.specification }}</p>
              <p>单位：{{ selectMaterial.unitName }}</p>
              <!-- {{item.warehouseName}}  <span v-if="item.warehouseName&&item.warehouseArea">/</span> -->
            </div>
          </div>
        </div>
      </a-col>
      <a-col :span="24" class="top center mt-6" style="">
        <div class="flex items-center justify-center gap-x-4">
          <mw-button @click.stop="printDocument(qrCodeDataimg)"
            >打印二维码
          </mw-button>
          <mw-button @click.stop="downloadImage()">保存二维码</mw-button>
        </div>
      </a-col>
    </a-row>
  </a-modal>
</template>

<script setup>
import { downFile } from "@/common/setup/index.js";
import { ref, onBeforeMount, getCurrentInstance, computed } from "vue";
import {
  page,
  update as updateApi,
  exportMaterial,
  cancelMaterial,
  startMaterial,
  add,
  getInfo,
} from "@/api/basicData/material.js";
import { usePagenation } from "@/common/setup";
import addNewMaterial from "./bankComponents/addNewMaterial.vue";
import { materialStatus, rawMaterialStatus } from "@/common/constant.js";
import { list as getAllCategory } from "@/api/basicData/category.js";
import Search from "@/components/search/index.vue";
import { exportExecl } from "@/utils/util.js";
import { useUserStore } from "@/stores/user.js";
import printJS from "print-js";
import html2canvas from "html2canvas";

const { proxy } = getCurrentInstance();
const columns = [
  {
    title: "编码",
    dataIndex: "materialNo",
    key: "materialNo",
  },
  {
    title: "名称",
    dataIndex: "materialName",
    key: "materialName",
  },
  {
    title: "规格",
    dataIndex: "specification",
    key: "specification",
  },

  {
    title: "分类",
    dataIndex: "classificationName",
    key: "classificationName",
  },
  {
    title: "当前库存",
    dataIndex: "currentStock",
    key: "currentStock",
    customRender: ({ record }) => {
      return record.currentStock + record.unitName;
    },
  },
  {
    title: "可用库存",
    dataIndex: "availableStock",
    key: "availableStock",
    customRender: ({ record }) => {
      return record.availableStock + record.unitName;
    },
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status",
  },
  {
    title: "单位",
    dataIndex: "unitName",

    key: "unitName",
  },
  {
    title: "附件",
    dataIndex: "file",
    key: "file",
    customRender: ({ record }) => {
      return record.remarkFileList && record.remarkFileList.length > 0
        ? "有"
        : "无";
    },
  },
  {
    title: "耗用",
    dataIndex: "isExpense",
    key: "isExpense",
  },
  {
    title: "自制",
    dataIndex: "isHomemade",
    key: "isHomemade",
    customRender: ({ record }) => {
      return record.isHomemade == 1 ? "是" : record.isHomemade == 0 ? "否" : "";
    },
  },
  {
    title: "采购",
    dataIndex: "isPurchase",
    key: "isPurchase",
    customRender: ({ record }) => {
      return record.isPurchase == 1 ? "是" : record.isPurchase == 0 ? "否" : "";
    },
  },
  {
    title: "销售",
    dataIndex: "isMarket",
    key: "isMarket",
    customRender: ({ record }) => {
      return record.isMarket == 1 ? "是" : record.isMarket == 0 ? "否" : "";
    },
  },
  {
    title: "备注",
    dataIndex: "remark",
    key: "remark",
  },
  {
    title: "操作",
    dataIndex: "action",
    key: "action",
    fixed: "right",
  },
];
const onlyEconomicalMaterials = ref();
const cancelLoading = ref(false);
const exportLoading = ref(false);
const editId = ref("");
const dataSource = ref([]);
const tableLoading = ref(false);
const visible = ref(false);
const loading = ref(false);
const addMaterial = ref(null);
const clickData = ref(void 0);
const materialNo = ref("");
const isStatus = ref("");
const status = ref(0);
const isStop = ref(1);
const store = useUserStore();

const onClose = () => {
  visible.value = false;
  editId.value = void 0;
};
const changeOnlyEconomicalMaterials = () => {
  getList();
};
const addData = async (params) => {
  try {
    if (params.isHomemade == 1 && !params.bomNo && !params.fileNumber) {
      return proxy.$message.error("自制件必须填写BOM编号和文件编号");
    }
    loading.value = true;
    const { code, data, msg } = await add(params);
    if (code == 200) {
      proxy.$message.success("新增物料成功");
      onClose();
      getList();
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
  }
};
const updateClick = async (params) => {
  try {
    loading.value = true;
    const { code, data, msg } = await updateApi({
      ...params,
      id: editId.value,
    });
    if (code == 200) {
      proxy.$message.success("编辑物料成功");
      onClose();
      getList();
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
  }
};

const formSubmit = async () => {
  const res = await addMaterial.value.submit();
  if (res) {
    const formData = addMaterial.value.formData;
    formData.materialNo = formData.msgCode
      ? formData.msgCode
      : formData.materialNo;
    formData.isLineEdgeLibrary = formData.isLineEdgeLibrary ? 1 : 0;
    if (formData.isLineEdgeLibrary && !formData.lineEdgeLibraryType) {
      proxy.$message.error("请选择线边库物料类型！");
      return false;
    }
    // return;
    if (editId.value) {
      updateClick(formData);
    } else {
      if (clickData.value) {
      } else {
        addData(formData);
      }
    }
  }
};
const remarkVisible = ref(false);
const reStart = async () => {
  remarkVisible.value = true;
};
const remark = ref("");
const confirmLoading = ref(false);
const handleConfirm = async () => {
  confirmLoading.value = true;
  let res = await startMaterial(editId.value, remark.value);
  if (res.code == 200) {
    remarkVisible.value = false;
    remark.value = "";
    proxy.$message.success("重新启动申请已提交");
    onClose();
    getList();
  }
  confirmLoading.value = false;
};

const searchData = ref({
  operationButtons: [],
  fields: {
    classificationId: {
      type: "a-tree-select",
      placeholder: "选择物料分类",
      dropdownMatcSelectWidth: true,
      showSearch: true,
      treeNodeFilterProp: "name",
      fieldNames: { children: "children", label: "name", value: "id" },
      treeData: [],
      width: "140px",
      value: "",
      allowClear: true,
    },
    status: {
      name: "物料状态",
      type: "a-select",
      options: rawMaterialStatus,
      placeholder: "选择状态",
      width: "120px",
      value: "",
      allowClear: true,
    },
    // rawMaterialStatus

    keyword: {
      type: "a-input-search",
      placeholder: "名称/编码/规格",
      width: "240px",
      allowClear: true,
    },
  },
});
const getList = async () => {
  tableLoading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }

  if (store.$state.paramsBomNo) {
    searchData.value.fields.keyword.value = store.$state.paramsBomNo;
    searchParam.keyword = store.$state.paramsBomNo;
    store.setParamsBomNo(void 0);
  }
  searchParam.isEconomicMaterial =
    onlyEconomicalMaterials.value == true ? 1 : "";
  //单个搜索信息
  //单个搜索信息
  if (searchParam.status == "3") {
    searchParam["isStop"] = 1;
    searchParam.status = undefined;
  }
  try {
    let result = await page(pageParam.value, searchParam);
    dataSource.value = result?.data || [];
    paginationProps.value.total = result?.total || 0;
    if (result.code == 200) {
      tableLoading.value = false;
    }
  } catch (error) {
    tableLoading.value = false;
  }
};

const getClassificationIdTreeData = async () => {
  let result = await getAllCategory("material");
  searchData.value.fields.classificationId.treeData = result.data?.map(
    (item) => {
      return {
        ...item,
        selectable: !item?.children || item.children.length == 0,
      };
    }
  );
  searchData.value.fields.classificationId.treeData.unshift({
    name: "全部分类",
    id: "",
  });
};

onBeforeMount(async () => {
  getClassificationIdTreeData();
  await getList();
});

const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

const showVisible = () => {
  clickData.value = void 0;
  visible.value = true;
  editId.value = void 0;
};

const info = async (val) => {
  let res = await getInfo(val);
  clickData.value = res.data;
  status.value = res.data.status;
  isStop.value = res.data.isStop;
  materialNo.value = res.data.materialNo;
  isStatus.value = res.data.isStop;
  addMaterial.value.infodata(res.data);
};
const editClick = (row) => {
  visible.value = true;
  info(row.id);
  editId.value = row.id;
};
const exportOrg = async () => {
  exportLoading.value = true;
  pageParam.value.pageSize = 100000;
  let searchParam = {
    // ...pageParam.value,
  };
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  //单个搜索信息
  if (searchData.value.filterData) {
    searchParam[searchData.value.filterData.value.filterBy] =
      searchData.value.filterData.value.filterValue;
  }
  let result = await exportMaterial(searchParam, pageParam.value);
  const fileName = "物料.xlsx";
  exportExecl(fileName, result);
  exportLoading.value = false;
};
const canEdit = computed(() => {
  return editId.value && ![2, 7, 9, 10].includes(status.value);
});
const submitCancel = async () => {
  cancelLoading.value = true;
  try {
    let res = await cancelMaterial(editId.value);
    if (res.code == 200) {
      proxy.$message.success("操作完成");
      onClose();
      await getList();
    }
    emit("finish");
    cancelLoading.value = false;
  } catch (error) {
    cancelLoading.value = false;
  }
};

const selectMaterial = ref();
// 生成二维码
import qrcode from "qrcode";
import { warehouse } from "@/api/basicData/repository.js";
const qrVisible = ref(false);
const userStore = useUserStore();
const qrCodeDataimg = ref();
const generateQRCode = async (record) => {
  selectMaterial.value = record;
  // http://localhost:3005/basicData/repository
  const url =
    // 二维码链接地址，可以根据需要自行修改
    import.meta.env.VITE_DOMAIN + "/materialQrcode?materialId=" + record.id;
  console.log(url, "url");
  // 二维码链接地址，可以根据需要自行修改
  const options = {
    width: 200, // 二维码图片宽度
    height: 200, // 二维码图片高度
    margin: 1,
  };
  qrcode.toDataURL(url, options, (error, dataURL) => {
    if (error) {
      console.log("[ error ] >", error);
    } else {
      qrCodeDataimg.value = dataURL;
      console.log("[ dataURL ] >", dataURL);
    }
  });
  qrVisible.value = true;
};
// 关闭二维码
const handleOk = (e) => {
  visible.value = false;
};

const downloadImage = () => {
  // 获取要保存的 DOM 元素
  const elementToSave = document.querySelector("#qrCodeDataimg");

  // 使用 html2canvas 将 DOM 元素转换为图像
  html2canvas(elementToSave).then((canvas) => {
    // 将 canvas 转换为 base64 编码的图像数据字符串
    const imageData = canvas.toDataURL("image/png");
    // 创建下载链接
    const downloadLink = document.createElement("a");
    downloadLink.href = imageData;
    downloadLink.download = "image.png";
    // 通过模拟点击下载链接，触发图像保存到本地相册
    downloadLink.click();
  });
};

// 打印
const printDocument = (itemqrCodeData) => {
  // if(!itemvalue){
  //   message.error('请先选择是否加密');
  //   return
  // }else if(!itemqrCodeData){
  //   message.error('请先选择生成二维码');
  //   return
  // }
  if (!itemqrCodeData) {
    message.error("请先选择生成二维码");
    return;
  }
  var originalBodyStyles = window.getComputedStyle(document.body).cssText;
  var originalPageStyles = document.getElementById("qrCodeData").style.cssText;
  document.getElementById("qrCodeDataimg").style.width = "100%";
  document.getElementById("qrcodetitle").style.width = "100%";
  document.getElementById("qrcodetitle").style.fontSize = "16px";
  document.getElementById("qrcodetitle").style.marginLeft = "1%";
  document.getElementById("qrcodetitle").style.marginReft = "1%";
  printJS({
    printable: document.getElementById("qrCodeDataimg"),
    type: "html",
    style: `
    @media print {
      .print-area {
        page-break-inside: avoid;
        page-break-after: always;  /* 强制分页，保证最多只有1页 */
      }

    }
    @page {
      size: A4; /* 设置页面尺寸为A4 */
      margin: 0; /* 设置页边距为0 */
      page-break-after: always;  /* 强制分页，保证最多只有1页 */
    }
    body {
      position: absolute;
      top: 0%;
      left: 0%;
      transform: translate(-10%, 0%);
      color: #333; /* 设置文字颜色 */
      zoom:310%
    }

  `,
    scanstyle: false,
    // css: '/path/to/style.css',
    background: true,
    orientation: "portrait",
    documentTitle: "我的打印文件",
    targetStyles: ["*"], //添加样式
  });
  // 恢复原始样式
  document.body.style.cssText = originalBodyStyles;
  document.getElementById("qrCodeDataimg").style.cssText = originalPageStyles;
  document.getElementById("qrcodetitle").style.cssText = originalPageStyles;
};
</script>

<style scoped lang="less">
.new-material {
  // .search-box {
  //   display: flex;
  //   justify-content: flex-end;
  // }
}
.qrcodetitle {
  text-align: left;
  font-size: 16px;
  margin-left: 1%;
  margin-right: 1%;
}

.erwm {
  background: #fafafa;
  width: 472px;
  height: 236px;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.erwm img {
  width: 50%;
}
</style>
