<template>
  <a-spin :spinning="spinning">
    <a-space class="absolute right-0"
      ><mw-button
        @click="
          $router.push({
            name:
              route.query.bizType == 'material'
                ? 'Qualitymaterial'
                : 'Qualityfinishedproduct',
          })
        "
        >取消</mw-button
      >
    </a-space>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="inline"
      :colon="false"
    >
      <p class="text-base mb-4">物料信息</p>
      <a-row style="width: 100%">
        <a-col :span="6" class="mb-3">
          <a-form-item :label="`物料编号:`">
            <span>{{
              route.query.bizType == "product"
                ? formData.productNo
                : formData.materialNo || "-"
            }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3">
          <a-form-item :label="`物料名称:`">
            <span>{{
              route.query.bizType == "product"
                ? formData.productName
                : formData.materialName || "-"
            }}</span>
          </a-form-item>
        </a-col>

        <a-col :span="6" class="mb-3">
          <a-form-item :label="`物料规格:`">
            <span>{{ formData.specification || "-" }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3">
          <a-form-item label="单位名称:">
            <span>{{
              formData.unitName ? formData.unitName : formData.unit || "-"
            }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3">
          <a-form-item label="分类:">
            <span>{{ formData.classificationName || "-" }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3">
          <a-form-item label="物料标签:">
            <span v-if="formData.isHomemade == 1">自制 ，</span>
            <span v-if="formData.isExpense == 1">耗用，</span>
            <span v-if="formData.isEconomicMaterial == 1">经济物料，</span>
            <span v-if="formData.isMarket == 1">销售，</span>
            <span v-if="formData.isPurchase == 1">采购，</span>
            <span
              v-if="
                formData.isHomemade == 0 &&
                formData.isExpense == 0 &&
                formData.isEconomicMaterial == 0 &&
                formData.isMarket == 0 &&
                formData.isPurchase == 0
              "
              >无</span
            >
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3">
          <a-form-item label="备注:">
            <span>{{ formData.remark || "-" }}</span>
          </a-form-item>
        </a-col>
        <!-- <a-col :span="6" class="mb-3">
          <a-form-item label="技术协议:">
            <div class="overflow">
              <i
                class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                style="color: #959ec3"
              ></i>
              <a
                :href="formData.file?.fileVisitUrl"
                :title="formData.file?.fileVisitUrl"
                target="_blank"
                class="underline"
                style="color: #959ec3"
                >{{ formData.file?.fileName }}
              </a>
            </div>
          </a-form-item>
        </a-col>

        <a-col :span="6" class="mb-3" v-if="route.query.bizType == 'material'">
          <a-form-item label="最低库存:">
            <span>{{ formData.minimumStock || "-" }}</span>
          </a-form-item>
        </a-col>

        <a-col :span="6" class="mb-3" v-if="route.query.bizType == 'material'">
          <a-form-item label="补料中的数量:">
            <span>{{ formData.addingQuantity || "-" }}</span>
          </a-form-item>
        </a-col>

        <a-col :span="6" class="mb-3" v-if="route.query.bizType == 'material'">
          <a-form-item label="总库存:">
            <span>{{ formData.total || "-" }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3">
          <a-form-item label="品牌:">
            <span>{{ formData.brand || "-" }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3" v-if="route.query.bizType == 'material'">
          <a-form-item label="当前库存:">
            <span>{{ formData.currentStock || "-" }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3" v-if="route.query.bizType == 'material'">
          <a-form-item label="可用库存:">
            <span>{{ formData.availableStock || "-" }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3" v-if="route.query.bizType == 'product'">
          <a-form-item label="bom名称:">
            <span>{{ formData.bomName || "-" }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3" v-if="route.query.bizType == 'product'">
          <a-form-item label="bom编号:">
            <span>{{ formData.bomNo || "-" }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3" v-if="route.query.bizType == 'product'">
          <a-form-item label="默认所属仓库区域id:">
            <span>{{ formData.defaultWarehouseAreaId || "-" }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3" v-if="route.query.bizType == 'product'">
          <a-form-item label="是否需要调试:">
            <span>{{ formData.isNeedDebugging || "-" }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3" v-if="route.query.bizType == 'product'">
          <a-form-item label="合同编号:">
            <span>{{ formData.contractNumber || "-" }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3" v-if="route.query.bizType == 'product'">
          <a-form-item label="销售价格:">
            <span>{{ formData.sellPrice || "-" }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3" v-if="route.query.bizType == 'product'">
          <a-form-item label="产品用途:">
            <span>{{ formData.use || "-" }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mb-3" v-if="route.query.bizType == 'product'">
          <a-form-item label="备注:">
            <span>{{ formData.remark || "-" }}</span>
          </a-form-item>
        </a-col>

        <a-col :span="6" class="mb-3">
          <a-form-item label="结束时间:">
            <span>{{ formData.endTime || "-" }}</span>
          </a-form-item>
        </a-col> -->
      </a-row>

      <!-- <a-row style="width: 100%">
      
      </a-row> -->
      <div style="width: 100%" v-if="route.query.bizType == 'material'">
        <!-- <p class="text-base mb-4 mt-5">供应商明细</p> -->
        <a-row>
          <!-- <a-col
            :span="6"
            class="mb-3"
            v-for="(item, index) in formData.materialSupplierRelationList"
            :key="index"
          >
            <a-form-item label="供应商：">
              <span>{{ item.supplierName || "-" }}</span>
            </a-form-item>
          </a-col> -->
          <a-col
            :span="6"
            class="mb-3 mt-4"
            style="display: flex; flex-direction: column"
          >
            <!-- <div
                v-for="(item, index) in formData.remarkFileList"
                :key="index"
              >
                {{ item.fileName || "-" }}
              </div> -->
            <div>备注附件</div>
            <div
              v-for="(item, index) in formData.remarkFileList"
              :key="index"
              @click.stop="openUrl(item.fileVisitUrl)"
              :href="item.fileVisitUrl"
              :title="item.fileName"
              class="cursor-pointer inline-block"
              style="color: #959ec3"
            >
              <i
                v-if="item.fileName"
                class="iconfont icon-jichu-lianjie text-xs -middle mr-1"
                style="color: #959ec3"
              ></i
              ><span class="underline"> {{ item.fileName }}</span>
            </div>
          </a-col>
          <a-col
            :span="6"
            class="mb-3 mt-4"
            style="display: flex; flex-direction: column"
          >
            <!-- <a-form-item label="图片">
              <div v-for="(item, index) in formData.materialImage" :key="index">
                {{ item.fileName || "-" }}
              </div>
            </a-form-item> -->
            <div>图片</div>

            <div
              v-for="(item, index) in formData.materialImage"
              :key="index"
              @click.stop="openUrl(item.fileVisitUrl)"
              :href="item.fileVisitUrl"
              :title="item.fileName"
              class="cursor-pointer inline-block"
              style="color: #959ec3"
            >
              <i
                v-if="item.fileName"
                class="iconfont icon-jichu-lianjie text-xs -middle mr-1"
                style="color: #959ec3"
              ></i
              ><span class="underline"> {{ item.fileName }}</span>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </a-spin>
</template>

<script setup>
import {
  defineProps,
  defineEmits,
  ref,
  getCurrentInstance,
  watch,
  onBeforeMount,
} from "vue";
import { getBomMarketOrderDetail } from "@/api/basicData/orderBomChange.js";
import { useRouter, useRoute } from "vue-router";
import { getInfoByMaterialNo, productDetail } from "@/api/basicData/product.js";

import { getInfo } from "@/api/basicData/material.js";

const router = useRouter();
const route = useRoute();
const spinning = ref(false);
const productQuantity = ref(undefined);
const dataSource = ref([]);
const bizTypeVal = ref();

const visibleAddContractReviewDrawer = ref(false);
const formData = ref({
  bomName: undefined,
  fileDetailModel: [],
  productNo: undefined,
});
const columns = ref([
  {
    title: "物料名称",
    dataIndex: "materialName",
    key: "materialName",
    width: "180px",
  },
  {
    title: "物料编号",
    dataIndex: "materialNo",
    key: "materialNo",
    width: "180px",
  },
  {
    title: "物料规格",
    dataIndex: "specification",
    key: "specification",
    width: "180px",
  },
  {
    title: "物料单位",
    dataIndex: "unit",
    key: "unit",
    width: "180px",
  },
  {
    title: "物料台数",
    dataIndex: "quantity",
    key: "quantity",
    width: "180px",
  },
  {
    title: "用料合计数",
    dataIndex: "totalQuantity",
    key: "totalQuantity",
    width: "180px",
  },
]);

onBeforeMount(async () => {
  let res = await getInfo(route.query.id);
  formData.value = res.data;
  // if (route.query.bizType) {
  //   bizTypeVal.value = route.query.bizType == "product" ? "产品" : "物料";
  // }
  // if (route.query.bizType == "product") {
  //   onProductDetail(route.query.code);
  // } else {
  //   onInfoByMaterialNo(route.query.code);
  // }
});
// const onInfoByMaterialNo = async (val) => {
//   // bomNo
//   let res = await getInfoByMaterialNo({ materialNo: val });
//   formData.value = res.data;
// };
// const onProductDetail = async (val) => {
//   // bomNo
//   let res = await productDetail({ productNo: val });
//   formData.value = res.data;
// };
const openUrl = (url) => {
  window.open(url, "_blacnk");
};
</script>

<style></style>
