<template>
  <div class="mb-4 text-right">
    <a-space>
      <mw-button @click="onOpen(false)">新增一级分类</mw-button>
      <mw-button @click="onOpen(true)">新增二级分类</mw-button>
    </a-space>
  </div>
  <mw-table
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    :rowExpandable="(record) => record.children?.length"
    childrenColumnName="no"
  >
    <template #expandedRowRender="{ record }">
      <mw-table
        v-if="record.children"
        :columns="innerColumns"
        :data-source="record.children"
        :pagination="false"
        :customRow="rowClick"
        :rowKey="(item) => item.id"
        :rowExpandable="(item) => item.children?.length"
        childrenColumnName="no"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'status'">
            <dictionary
              :statusOptions="categoryStatus"
              :value="record.status"
              isBackgroundColor
            />
          </template>
        </template>
      </mw-table>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'status'">
        <dictionary
          :statusOptions="categoryStatus"
          :value="record.status"
          isBackgroundColor
        />
      </template>
      <template v-if="column.key == 'button'">
        <mw-button @click="parentRowClick(record)">编辑</mw-button>

        <!-- <a-popconfirm
          title="仓库下有区域，会同时删除区域，确定是否删除!"
          ok-text="是"
          cancel-text="否"
          @confirm="confirm(record)"
          @cancel="cancel(record)">
          <a-button  style="margin-left: 10px;" type="primary" danger>
            删除
          </a-button>
        </a-popconfirm> -->
      </template>
    </template>
  </mw-table>
  <category-create-drawer
    v-model:visible="addVisible"
    :type="'material'"
    :hasParent="hasParent"
    @finish="getList('material')"
    :id="id"
  />
</template>
<script setup>
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import { list } from "@/api/basicData/category.js";
import categoryCreateDrawer from "./components/categoryCreateDrawer.vue";
import { categoryStatus } from "@/common/constant.js";
import { useRoute } from "vue-router";
import { message } from "ant-design-vue";

const columns = ref([
  {
    title: "一级分类",
    dataIndex: "name",
  },
  {
    title: "编码",
    dataIndex: "code",
    align: "center",
  },
  {
    title: "状态",
    key: "status",
    align: "center",
  },
  {
    title: "添加人",
    dataIndex: "createName",
    align: "center",
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
  },
  {
    title: "操作",
    dataIndex: "button",
    key: "button",
  },
]);
const innerColumns = ref([
  {
    title: "二级分类",
    dataIndex: "name",
  },
  {
    title: "编码",
    dataIndex: "code",
    align: "center",
    width: "200px",
  },
  {
    title: "状态",
    key: "status",
    align: "center",
  },
  {
    title: "添加人",
    dataIndex: "createName",
    align: "center",
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
  },
]);
const data = ref([]),
  addVisible = ref(false),
  loading = ref(false),
  hasParent = ref(false),
  id = ref(""),
  route = useRoute();
const getList = async (type) => {
  loading.value = true;
  let result = await list(type);
  data.value = result.data;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList("material");
});
const onOpen = (val) => {
  id.value = "";
  hasParent.value = val;
  addVisible.value = true;
};
const parentRowClick = (record) => {
  // return {
  //   onClick: (event) => {

  hasParent.value = false;
  id.value = record.id;
  addVisible.value = true;
  //   },
  // };
};
const rowClick = (record) => {
  return {
    onClick: (event) => {
      hasParent.value = true;
      id.value = record.id;
      addVisible.value = true;
    },
  };
};

// const confirm = record => {
//       message.success('Click on Yes');
//     };
//     const cancel = record => {
//       message.error('Click on No');
//     };
</script>
