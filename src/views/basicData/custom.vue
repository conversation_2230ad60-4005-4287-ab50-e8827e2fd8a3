<template>
  <div v-if="store.$state.cacheObject">
    <search :searchData="searchData" @search="refresh">
      <mw-button
        title="新增"
        :font="'iconfont icon-xianxing-121'"
        @click="addLine"
      >
      </mw-button>
    </search>
    <mw-table
      class="leading-5.5"
      :scroll="{ x: 'max-content' }"
      :columns="columns"
      hasPage
      :data-source="data"
      :rowKey="(record) => record.id"
      :loading="tableLoading"
      :pageConfig="paginationProps"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key == 'customerNo'">
          {{ record.customerNo }}
        </template>
        <template v-if="column.key == 'customerName'">
          {{ record.customerName }}
        </template>

        <template v-if="column.key == 'level'">
          <div v-if="record.level == 'excellent'">优质</div>
          <div v-else-if="record.level == 'good'">良好</div>
          <div v-else-if="record.level == 'ordinary'">一般</div>
          <div v-else>-</div>
        </template>
        <template v-if="column.key == 'realName'">
          {{ record.realName }}
        </template>

        <template v-if="column.key == 'phone'">
          {{ record.phone }}
        </template>
        <template v-if="column.key == 'orderCount'">
          {{ record.orderCount }}
        </template>
        <template v-if="column.key == 'createTime'">
          {{ record.createTime }}
        </template>
        <template v-if="column.key == 'button'">
          <mw-button
            title="详情"
            @click="rowClick(record, 'detail')"
            class="mr-2"
          ></mw-button>
          <mw-button
            title="编辑"
            @click="rowClick(record, 'edit')"
            class="mr-2"
            :disabled="record.tenantId != userStore.user.tenantId"
          ></mw-button>

          <a-popconfirm
            title="确定是否删除"
            ok-text="是"
            cancel-text="否"
            @confirm="onDeleteCustom(record)"
            @cancel="onCancel(record)"
          >
            <mw-button
              title="删除"
              danger
              :disabled="record.tenantId != userStore.user.tenantId"
            ></mw-button>
          </a-popconfirm>
        </template>
      </template>
    </mw-table>
  </div>
  <empty name="ProductionLine" v-else />
  <mw-drawer
    width="40%"
    :visible="addLineVisible"
    @close="closeDrawer"
    :customTitle="
      typeClick == 'edit'
        ? '更新客户'
        : typeClick == 'detail'
        ? '详情'
        : '新增客户'
    "
    :spinning="spinning"
  >
    <template #header>
      <mw-button title="确定" @click="addCustomOnSubmit"></mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 7, offset: 1 }"
      layout="horizontal"
      :colon="false"
    >
      <a-form-item label="企业名称" name="customerName">
        <a-input
          v-model:value="formData.customerName"
          placeholder="请输入客户名称（2-80字）"
          :style="{ width: '100%' }"
          :maxlength="20"
          allow-clear
          :disabled="typeClick == 'detail'"
        >
        </a-input>
      </a-form-item>
      <a-form-item
        label="客户编码"
        name="customerName"
        v-if="typeClick == 'detail'"
      >
        <a-input
          v-model:value="formData.customerNo"
          placeholder="请输入客户编码"
          :style="{ width: '100%' }"
          :disabled="typeClick == 'detail'"
        >
        </a-input>
      </a-form-item>
      <a-form-item label="客户等级" name="level">
        <a-select
          v-model:value="formData.level"
          placeholder="请选择客户等级"
          :disabled="typeClick == 'detail'"
        >
          <a-select-option
            :value="item.value"
            v-for="(item, index) in customGrade"
            :key="index"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="联系人" name="realName">
        <a-input
          v-model:value="formData.realName"
          placeholder="输入联系人名称，如张安、张先生，1-24字"
          :style="{ width: '100%' }"
          :maxlength="24"
          allow-clear
          :disabled="typeClick == 'detail'"
        ></a-input>
      </a-form-item>
      <a-form-item label="联系方式">
        <a-input
          v-model:value="formData.phone"
          placeholder="输入联系人手机号"
          allow-clear
          :disabled="typeClick == 'detail'"
        ></a-input>
      </a-form-item>
      <a-form-item label="客户经理" name="customerManagerId">
        <a-select
          :disabled="typeClick == 'detail'"
          v-model:value="formData.customerManagerId"
          show-search
          placeholder="请选择客户经理"
          style="width: 100%"
          :options="allUserList"
          optionFilterProp="nickName"
          :fieldNames="{
            label: 'nickName',
            value: 'userId',
          }"
          @change="handleChangeUser"
        ></a-select>
        <!-- <span v-else>{{ formData.responsibleUser }}</span> -->
        <!-- <div v-if="typeClick == 'detail'">
          <div class="select-header prohibit" @click="toggleOptions">
            <span :class="customerManagerName ? '' : 'select-header-color'">{{
              customerManagerName ? customerManagerName : "请选择客户经理"
            }}</span>
            <i
              :class="{ 'arrow-up': showOptions, 'arrow-down': !showOptions }"
            ></i>
          </div>
        </div> -->
        <!-- <div class="custom-select" v-else>
          <div class="select-header" @click="toggleOptions">
            <span :class="customerManagerName ? '' : 'select-header-color'">{{
              customerManagerName ? customerManagerName : "请选择客户经理"
            }}</span>
            <i
              :class="{ 'arrow-up': showOptions, 'arrow-down': !showOptions }"
            ></i>
          </div>
          <div class="select-options" v-show="showOptions">
            <div style="border-right: 1px solid #e0e3e9">
              <a-tree
                v-if="deptTreeData.length > 0"
                :defaultExpandAll="true"
                :tree-data="deptTreeData"
                :fieldNames="{
                  children: 'children',
                  title: 'label',
                  key: 'id',
                }"
                v-model:selectedKeys="selectedKeys"
                @select="handleDeptChange"
              />
            </div>

            <div class="staffTree" style="" v-if="staffTreeData.length > 0">
              <div
                v-for="(item, index) in staffTreeData"
                @click="onSiteTreeLi(item.nickName, item.userId)"
              >
                <span
                  :class="
                    formData.customerManagerId == item.userId ? 'calm' : ''
                  "
                >
                  <a-icon component="UserOutlined" />
                  <UserOutlined />{{ item.nickName }}</span
                >
              </div>
            </div>
            <div v-else class="notHave">暂无数据</div>
          </div>
        </div> -->
      </a-form-item>
      <a-form-item label="所属部门" name="customerManagerBelongDeptName">
        <a-input
          v-model:value="formData.customerManagerBelongDeptName"
          placeholder="所属部门"
          allow-clear
          :disabled="true"
        ></a-input>
        <!-- <a-input
          v-else
          :value="nodeLabelSwitch ? formData.customerManagerBelongDeptName : ''"
          placeholder="所属部门"
          allow-clear
          :disabled="true"
        ></a-input> -->
      </a-form-item>
      <a-form-item label="公司名称">
        <a-input
          v-model:value="formData.companyName"
          placeholder="输入公司名称"
          allow-clear
          :disabled="typeClick == 'detail'"
        ></a-input>
      </a-form-item>
      <a-form-item label="纳税人识别号">
        <a-input
          v-model:value="formData.taxpayerIdentityNumber"
          placeholder="输入纳税人识别号"
          allow-clear
          :disabled="typeClick == 'detail'"
        ></a-input>
      </a-form-item>
      <a-form-item label="开户行信息">
        <a-input
          v-model:value="formData.openingBank"
          placeholder="输入开户行信息"
          allow-clear
          :disabled="typeClick == 'detail'"
        ></a-input>
      </a-form-item>
      <a-form-item label="银行账号">
        <a-input
          v-model:value="formData.bankAccount"
          placeholder="输入银行账号"
          allow-clear
          :disabled="typeClick == 'detail'"
        ></a-input>
      </a-form-item>
      <a-form-item label="公司电话">
        <a-input
          v-model:value="formData.companyPhone"
          placeholder="输入公司电话"
          allow-clear
          :disabled="typeClick == 'detail'"
          :maxlength="20"
        ></a-input>
      </a-form-item>
      <a-form-item label="公司地址">
        <a-input
          v-model:value="formData.companyAddress"
          placeholder="输入公司地址"
          allow-clear
          :disabled="typeClick == 'detail'"
        ></a-input>
      </a-form-item>
      <a-form-item label="备注">
        <a-input
          v-model:value="formData.remark"
          placeholder="输入备注"
          allow-clear
          :disabled="typeClick == 'detail'"
        ></a-input>
      </a-form-item>
      <a-form-item label="附件">
        <form-upload
          v-if="formData.file && !customId"
          v-model:value="formData.file"
          sence="article"
          :fileTypes="[]"
          :fileSize="100"
        ></form-upload>
        <div class="overflow" v-else>
          <i
            v-if="formData.file[0]?.fileVisitUrl"
            class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
            style="color: #959ec3"
          ></i>
          <a
            :href="formData.file[0]?.fileVisitUrl"
            :title="formData.file[0]?.fileName"
            target="_blank"
            class="underline"
            style="color: #959ec3"
            >{{ formData.file[0]?.fileName }}
          </a>
        </div>
      </a-form-item>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
} from "vue";
import Search from "@/components/search/index.vue";
import { customGrade } from "@/common/constant.js";
//导入
import { TreeSelect } from "ant-design-vue";
import {
  customPage,
  customAdd,
  customUpdate,
  getDetailInfo,
  deleteCustom,
} from "@/api/basicData/custom.js";
import { usePagenation } from "@/common/setup";
import { useUserStore } from "@/stores/user.js";
import { deptTreeSelect, list, alldeptTreeSelect } from "@/api/system/user.js";
import FormUpload from "@/components/form-upload.vue";
import { list as getAllUserList } from "@/api/system/user.js";
const userStore = useUserStore();
const columns = ref([
  {
    title: "客户编码",
    key: "customerNo",
  },
  {
    title: "客户信息",
    key: "customerName",
  },

  {
    title: "客户等级",
    key: "level",
  },
  {
    title: "联系人",
    key: "realName",
  },
  {
    title: "联系人手机",
    key: "phone",
    ellipsis: true,
  },
  {
    title: "客户订单数",
    key: "orderCount",
  },
  {
    title: "创建时间",
    key: "createTime",
  },
  {
    title: "操作",
    dataIndex: "button",
    key: "button",
    align: "center",
    fixed: "right",
  },
]);
const allUserList = ref([]);
const selectedKeys = ref([]);
const staffTreeData = ref([]);
const typeClick = ref("");
const data = ref([]);
const addLineVisible = ref(false);
const loading = ref(false);
const tableLoading = ref(false);
const { proxy } = getCurrentInstance();
const spinning = ref(false),
  exportLoading = ref(false),
  customId = ref("");
const searchData = ref({
  searchButtons: [],
  operationButtons: [],
  fields: {
    level: {
      name: "请选择客户等级",
      type: "a-select",
      options: customGrade,
      placeholder: "请选择客户等级",
      width: "150px",
      value: "",
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: "请输入客户名称/客户编码/纳税人识别号",
      width: "340px",
      allowClear: true,
    },
  },
});
const nodeLabelSwitch = ref(false);
const deptTreeData = ref([]);
const formRef = ref();
const removeLoading = ref(false),
  store = useUserStore(),
  selectedProductLine = ref({}),
  getDetailList = ref({});
//  获取用户列表
async function getAllUser(keyword) {
  let result = await getAllUserList({
    pageNum: 1,
    pageSize: 10000,
    sortType: "create_time",
    sortOrder: "desc",
    userName: keyword,
  });
  let { data } = result;
  allUserList.value = data;
}
const handleChangeUser = async (value) => {
  allUserList.value.forEach((item, index) => {
    if (item.userId == value) {
      formData.customerManagerBelongDeptName = item.dept.deptName;
    }
  });

  // selectedUser.value = allUserList.value.filter(
  //   (item) => item.value == value
  // )[0];
  // 获取选中的bom信息
  // let res = await getBomMaterialList({ bomNo: value });
};
// 列表
const getList = async () => {
  tableLoading.value = true;
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  //单个搜索信息
  // searchParam[searchData.filterData.value.filterBy] =
  //   searchData.filterData.value.filterValue;
  let result = await customPage(searchParam, { ...pageParam.value });

  data.value = result.data;
  // state.corpId = result.data.data.records[0].corpId;
  paginationProps.value.total = result.total;
  tableLoading.value = false;
};
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
// 初始化
const customerManagerName = ref("");
const formData = reactive({
  customerName: "",
  realName: "",
  phone: "",
  customerManagerId: undefined,
  file: [],
});
const rules = ref({
  customerName: [{ required: true }],
  realName: [{ required: true }],
  level: [{ required: true }],
  customerManagerId: [{ required: true }],
});
// 初始化前
onBeforeMount(() => {
  getList();
  getAllUser();
});
function closeDrawer() {
  formData.file = [];
  formRef.value.resetFields();
  addLineVisible.value = false;
  formData.customerName = "";
  formData.realName = "";
  formData.level = "";
  formData.phone = "";
  typeClick.value = "";
  formData.taxpayerIdentityNumber = "";
  formData.openingBank = "";
  formData.bankAccount = "";
  formData.companyPhone = "";
  formData.companyAddress = "";
  formData.companyName = "";
  formData.customerManagerId = "";
  customerManagerName.value = "";
  formData.customerManagerBelongDeptName = "";
  showOptions.value = false;
  formData.value = {};
  formData.remark = "";
}
// 编辑
const rowClick = async (record, type) => {
  spinning.value = true; //加载
  addLineVisible.value = true; //显示状态
  customId.value = record.id;
  typeClick.value = type;
  customList();
  let res = await getDetailInfo({ id: record.id });
  getDetailList.value = res.data;
  nodeLabelSwitch.value = true;
  formData.customerName = res.data.customerName;
  formData.customerNo = res.data.customerNo;
  formData.file = res.data?.file ? [res.data?.file] : [];
  formData.realName = res.data.realName;
  formData.level = res.data.level;
  formData.phone = res.data.phone;
  formData.taxpayerIdentityNumber = res.data.taxpayerIdentityNumber;
  formData.openingBank = res.data.openingBank;
  formData.bankAccount = res.data.bankAccount;
  formData.companyPhone = res.data.companyPhone;
  formData.companyAddress = res.data.companyAddress;
  formData.companyName = res.data.companyName;
  if (res.data.customerManagerId) {
    formData.customerManagerId = Number(res.data.customerManagerId);
  }

  customerManagerName.value = res.data.customerManagerName;
  formData.customerManagerBelongDeptName =
    res.data.customerManagerBelongDeptName;

  selectedKeys.value.push(Number(res.data.customerManagerBelongDeptId));
  formData.remark = res.data.remark;

  // defaultSelectedkeys.value.push(res.data.customerManagerBelongDeptId)

  //  login
  setTimeout(() => {
    spinning.value = false;
  }, 200);
};
// 客户
const customList = async () => {
  let result = await deptTreeSelect();
  deptTreeData.value = result.data;
};
const handleDeptChange = (value, info) => {
  nodeLabelSwitch.value = false;
  formData.customerManagerBelongDeptName = info.node.label;

  memberList(value);
};

const memberList = async (value) => {
  let params = {
    pageNum: 1,
    pageSize: 999999,
    sortOrder: "desc",
    deptId: value[0],
  };
  let res = await list({ ...params });
  staffTreeData.value = res.data;
};
const onSiteTreeLi = (val, userId) => {
  customerManagerName.value = val;
  formData.customerManagerId = userId;
  nodeLabelSwitch.value = true;
  showOptions.value = false;
};
// 新增
const addLine = () => {
  addLineVisible.value = true;
  customList();
  customId.value = void 0;
};

// 新增
async function addCustomOnSubmit() {
  formRef.value
    .validate()
    .then(async () => {
      loading.value = true;
      if (formData.customerName.length < 2) {
        proxy.$message.error("企业名称必须大于2个字符且小于80字符!");
        return;
      }
      // const regCN =
      //   /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
      // if (!regCN.test(formData.phone)) {
      //   proxy.$message.error("请正确输入手机号");
      //   return;
      // }

      let param = {
        ...formData,
        file: formData.file ? formData.file[0] : {},
      };
      if (typeClick.value == "edit") {
        param.id = getDetailList.value.id;
        param.contactsId = getDetailList.value.contactsId;
        let res = await customUpdate(toRaw(param));
        if (res.code == 200) {
          proxy.$message.success("更新成功");
        }
      } else {
        let res = await customAdd(toRaw(param));
        if (res.code == 200) {
          proxy.$message.success("添加成功");
          closeDrawer();
        }
      }
      loading.value = false;
      showOptions.value = false;

      getList();
    })
    .catch((error) => {
      loading.value = false;
    });
}
const handleCascaderChange = (value) => {};

const showOptions = ref(false);
const selectedOption = ref();
const toggleOptions = () => {
  showOptions.value = !showOptions.value;
  memberList(selectedKeys.value);
};
const selectOption = (option) => {
  selectedOption.value = option;
  showOptions.value = false;
};
// 删除
const onDeleteCustom = async (val) => {
  await deleteCustom({ id: val.id });
  getList();
};
</script>

<style lang="less" scoped>
.custom-select {
  position: relative;
  z-index: 1;
}
.prohibit {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  box-shadow: none;
  cursor: not-allowed;
  opacity: 1;
}
.select-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  border: 1px solid #ddd;
  cursor: pointer;
}
.select-header-color {
  color: #ccc;
  background-color: #fff;
}

.select-header span {
  margin-right: 8px;
}

.select-header i {
  font-size: 12px;
  transition: transform 0.3s;
}

.select-header .arrow-up {
  transform: rotate(-180deg);
}

.select-options {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  /* max-height: 200px; */
  height: 300px;
  /* overflow-y: auto; */
  background-color: #fff;
  border: 1px solid #ddd;
  display: flex;
  box-shadow: 0 0 16px #ddd;

  div {
    width: 50%;
    padding: 8px;
  }
}

.select-options .option {
  padding: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.select-options .option:hover {
  background-color: #f5f5f5;
}
.staffTree {
  overflow: auto;
  div {
    display: block;
    height: 35px;
    padding-left: 10%;
    width: 100%;
    line-height: 35px;
  }
  span:hover {
    background-color: #f0f0f0;
    padding: 5px 2px;
    transform: scale(1);
  }
}
.calm {
  background-color: #bae7ff;
  padding: 5px 2px;
}
.notHave {
  text-align: center;
}
</style>
