<template>
  <div class="bank_main">
    <div class="search-box">
      <mw-button @click="addShow" :font="'iconfont icon-xianxing-121'"
        >新增</mw-button
      >
    </div>
    <mw-table
      class="leading-5.5"
      :columns="columns"
      hasPage
      :data-source="bankData"
      :rowKey="(record) => record.id"
      :loading="tableLoading"
      :pageConfig="paginationProps"
      @change="onTableChange"
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.key == 'action'">
          <mw-button @click="edit(record)">编辑</mw-button>
        </template>
      </template>
    </mw-table>

    <mw-drawer
      :visible="visible"
      @close="onClose()"
      width="30%"
      :customTitle="clickData?.id ? '编辑收款行信息' : '新增收款行信息'"
    >
      <template #header>
        <mw-button @click="formSubmit" :loading="loading">保存</mw-button>
      </template>
      <addBank ref="bankRef" :data="clickData" />
    </mw-drawer>
  </div>
</template>

<script setup>
import { ref } from "vue";
import addBank from "./bankComponents/addBank.vue";
import { getPageList, addBanks, updateData } from "@/api/basicData/bank.js";
import { usePagenation } from "@/common/setup";
import { message } from "ant-design-vue";
const columns = [
  {
    title: "银行账户编码",
    dataIndex: "bankCode",
    key: "bankCode",
  },
  {
    title: "支行名称",
    dataIndex: "bankName",
    key: "bankName",
  },
  {
    title: "银行账户",
    dataIndex: "bankNo",
    key: "bankNo",
  },
  {
    title: "操作",
    dataIndex: "action",
    key: "action",
    width: 150,
  },
];
const bankData = ref([]);
const tableLoading = ref(false);
const visible = ref(false);
const bankRef = ref(null);
const loading = ref(false);
const clickData = ref(void 0);

const onClose = () => {
  visible.value = false;
};

const addShow = () => {
  clickData.value = void 0;
  visible.value = true;
};

const edit = (row) => {
  clickData.value = row;
  visible.value = true;
};

const getApi = () => {
  if (clickData.value) {
    return updateData;
  } else {
    return addBanks;
  }
};

const formSubmit = async () => {
  const res = await bankRef.value.submit();
  if (res) {
    const params = {
      ...res,
      id: clickData.value ? clickData.value.id : void 0,
    };
    try {
      loading.value = true;
      const promiseApi = getApi();
      const { code, msg } = await promiseApi(params);
      if (code == 200) {
        message.success("操作成功");
        refresh();
        onClose();
      } else {
        message.error(msg);
      }
      loading.value = false;
    } catch (error) {
      loading.value = false;
    }
  }
};

const getList = async () => {
  try {
    tableLoading.value = true;
    const { code, data, total } = await getPageList();
    if (code == 200) {
      paginationProps.value.total = total || 0;
      bankData.value = data || [];
    }
    tableLoading.value = false;
  } catch (error) {
    tableLoading.value = false;
  }
};

getList();

const { paginationProps, onTableChange, refresh } = usePagenation(getList);
</script>

<style scoped lang="less">
.bank_main {
  .search-box {
    display: flex;
    justify-content: flex-end;
    padding-bottom: 16px;
  }
}
</style>
