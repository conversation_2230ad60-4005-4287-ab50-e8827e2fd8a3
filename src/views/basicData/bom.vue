<template>
  <div v-if="store.$state.cacheObject">
    <search :searchData="searchData" @search="refresh">
      <mw-button
        @click="exportOrg"
        :loading="exportLoading"
        :disabled="!data?.length"
        v-permission="'production:bom:export'"
        >导出Excel</mw-button
      >
      <mw-button
        v-permission="'production:bom:add'"
        @click="addLine"
        :font="'iconfont icon-xianxing-121'"
        >新增</mw-button
      >
    </search>

    <mw-table
      class="leading-5.5"
      :columns="columns"
      hasPage
      :data-source="data"
      :rowKey="(record) => record.id"
      :loading="tableLoading"
      :pageConfig="paginationProps"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'bomInfo'">
          <div>
            <div class="overflow" :title="record.bomName">
              {{ record.bomName || " " }}
            </div>
            <div class="secondary-color">编码：{{ record.bomNo }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex == 'createByName'">
          <div class="">
            <div class="overflow" :title="record.createByName">
              {{ record.createByName }}
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex == 'materialTypeCount'">
          <div class="">{{ record.materialTypeCount }}种</div>
        </template>
        <template v-if="column.dataIndex == 'status'">
          <div class="">
            <dictionary
              :statusOptions="productionLineStatusFilter"
              :value="record.status"
              isBackgroundColor
              :statusExtend="record.statusExtend"
            />
          </div>
        </template>
        <template v-if="column.dataIndex == 'fileDetailModel'">
          <div
            class="overflow w-56 whitespace-nowrap"
            v-for="(item, index) in record.fileDetailModel"
            :key="index"
          >
            <i
              class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
              style="color: #959ec3"
              download
            ></i>
            <div
              @click="downFile(item.fileVisitUrl, item.fileName)"
              :href="item.fileVisitUrl"
              :title="item.fileName"
              class="inline underline cursor-pointer break-all whitespace-nowrap align-middle max-w-full"
              style="color: #959ec3"
            >
              {{ item.fileName }}
            </div>
          </div>
        </template>

        <template v-if="column.dataIndex == 'createTime'">
          <div class="">{{ record.createTime }}</div>
        </template>
        <template v-if="column.dataIndex == 'button'">
          <mw-button
            @click="rowClick(record, 'detail')"
            v-permission="'production:bom:add'"
            class="mr-2"
            >详情</mw-button
          >

          <!-- <a-popconfirm title="确定是否停用?" @confirm="onDeactivate(record)">
            <a-button type="primary" class="ml-2"> 停用 </a-button>
          </a-popconfirm> -->
          <mw-button
            v-if="record.status !== 3 && record.status !== 7"
            @click="rowClick(record, 'edit')"
            v-permission="'production:bom:edit'"
            :disabled="record.tenantId != userStore.user.tenantId"
            >编辑</mw-button
          >

          <!-- 暂时注释，可能后期会试用 -->
          <!-- <a-popconfirm
            v-if="record.status != 3"
            title="确认停用？"
            ok-text="是"
            cancel-text="否"
            @confirm="onDeactivate(record)"
            placement="bottomRight"
          >
            <a-button
              type="primary"
              ghost
              danger
              class="ml-2"
              :loading="removeLoading"
            >
              停用
            </a-button>
          </a-popconfirm> -->
        </template>
      </template>
    </mw-table>
  </div>

  <empty name="Bom" v-else />
  <mw-drawer
    :visible="addBomVisible"
    @close="closeDrawer"
    :customTitle="
      editOrDetail == 'edit'
        ? '编辑BOM'
        : editOrDetail == 'detail'
        ? 'BOM详情'
        : '新增BOM'
    "
    width="70%"
    :spinning="spinning"
  >
    <template #header>
      <a-popconfirm
        title="确认取消？取消后将无法恢复。"
        ok-text="是"
        cancel-text="否"
        @confirm="deleteBOM"
        placement="bottomRight"
        v-if="bomNo && !canceled && editOrDetail !== 'detail'"
      >
        <mw-button :loading="removeLoading">取消BOM</mw-button>
      </a-popconfirm>
      <!-- <a-button type="primary" @click="onSubmit" :loading="loading">{{
        bomNo ? "更新" : "提交"
      }}</a-button> -->

      <mw-button
        @click="onSubmit"
        :loading="loading"
        v-if="editOrDetail !== 'detail'"
        >提交</mw-button
      >
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      :hideRequiredMark="!!bomNo"
    >
      <a-form-item name="bomName">
        <template #label>
          <div class="w-15 text-left text-primary-text">BOM名称</div>
        </template>
        <a-input
          v-if="!bomNo"
          v-model:value="formData.bomName"
          placeholder="请填写BOM名称"
          :style="{ width: '100%' }"
          allow-clear
        >
        </a-input>
        <span v-else>{{ formData.bomName }}</span>
      </a-form-item>
      <a-form-item name="fileDetailModel">
        <template #label>
          <div class="w-15 text-left text-primary-text">技术协议</div>
        </template>

        <form-upload-contract
          v-model:value="formData.fileDetailModel"
          sence="article"
          :fileSize="100"
          :readonly="quotedBom"
          hasDownLoad
          :fileLName="true"
          :fileLimit="9999"
          :detailType="butType"
          :disabled="editOrDetail == 'detail' ? true : false"
        >
        </form-upload-contract>
      </a-form-item>

      <a-form-item>
        <div class="text-primary-text mb-3">物料明细</div>
        <div class="">
          <div class="flex pt-3 pb-4" v-if="editOrDetail != 'detail'">
            <a-space>
              <mw-button @click="addMaterial">新增物料</mw-button>
              <mw-button @click="quoteBOM">引用BOM</mw-button>
            </a-space>
          </div>
          <mw-table
            :dataSource="materialList"
            :columns="columnsBomList"
            :pagination="false"
            :row-key="(r) => r.index"
            :scroll="{ x: 'max-content' }"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex == 'quantity'">
                <a-input-number
                  v-model:value="record.quantity"
                  :formatter="formatter2"
                  v-if="editOrDetail != 'detail'"
                  :stringMode="true"
                />
                <span v-else>{{ record.quantity }}</span>
              </template>
              <template v-if="column.dataIndex == 'operate'">
                <mw-button
                  v-if="editOrDetail != 'detail'"
                  @click="materialList.splice(index, 1)"
                  >删除</mw-button
                >
              </template>
            </template></mw-table
          >
          <!-- <div
            class="text-primary-text py-4"
            v-for="(item, index) in materialList"
            :key="item.id"
          >
            <div class="flex justify-between items-center leading-5.5 mb-1">
              <div class="flex-1 text-title overflow">
                <div class="text-primary-text">
                  名称： {{ item.materialName || "-" }}
                </div>
                <div class="text-primary-text">
                  编号：{{ item.materialNo || "-" }}
                </div>
              </div>

              <div
                class=""
                @click="materialList.splice(index, 1)"
                v-if="editOrDetail != 'detail'"
              >
                <i class="iconfont icon-jichu-shanchu align-middle"></i>
                <span class="align-middle ml-1">移除</span>
              </div>
            </div>
            <div class="flex justify-between items-end leading-5.5 mb-1">
              <div class="space-y-1">
                <div class="">{{ item.specification }}</div>
                <div class="" v-if="!bomNo">物料数量</div>
              </div>
              <div class="">
                <span v-if="bomNo">x </span>
                <a-input-number
                  v-model:value="item.quantity"
                  :formatter="formatter2"
                  v-if="editOrDetail != 'detail'"
                />
                <span v-else>{{ item.quantity }}</span>

                {{ item.unit }}
              </div>
            </div>
          </div> -->
          <!-- 编辑可以新增 -->
        </div>
      </a-form-item>
    </a-form>
    <mw-drawer
      :visible="addMaterialVisible"
      @close="closeMaterialDrawer()"
      customTitle="新增物料"
    >
      <template #header>
        <mw-button @click="addMaterialOnSubmit">确定</mw-button>
      </template>
      <div>
        <div>
          <a-form
            ref="addMaterialFormRef"
            :model="formDataM"
            :rules="addMaterialRules"
            layout="horizontal"
            :colon="false"
          >
            <a-form-item name="materialId">
              <template #label>
                <div class="w-15 text-left text-primary-text">物料名称</div>
              </template>

              <MaterialSelect
                v-model:value="formDataM.materialId"
                @change="handleChangeMaterial"
                :materiaSelect="materiaSelect"
                :showSpecification="true"
              />
            </a-form-item>
            <a-form-item name="quantity">
              <template #label>
                <div class="w-15 text-left text-primary-text">单台数量</div>
              </template>
              <a-input-number
                v-model:value="formDataM.quantity"
                :formatter="formatter2"
                :stringMode="true"
              />
            </a-form-item>
            <a-form-item>
              <template #label>
                <div class="w-full text-left text-primary-text">
                  物料基本信息
                </div>
              </template>
            </a-form-item>
            <div class="p-4 bg-background rounded space-y-3">
              <div class="flex leading-5.5">
                <div class="w-17 text-primary-text">物料编码</div>
                <div class="flex-1 w-0">
                  {{ selectedMaterial.materialNo || "-" }}
                </div>
              </div>
              <div class="flex leading-5.5">
                <div class="w-17 text-primary-text">物料规格</div>
                <div class="flex-1 w-0">
                  {{ selectedMaterial.specification || "-" }}
                </div>
              </div>
              <div class="flex leading-5.5">
                <div class="w-17 text-primary-text">一级分类</div>
                <div class="flex-1 w-0">
                  {{ selectedMaterial.classificationParentName || "-" }}
                </div>
              </div>
              <div class="flex leading-5.5">
                <div class="w-17 text-primary-text">二级分类</div>
                <div class="flex-1 w-0">
                  {{ selectedMaterial.classificationName || "-" }}
                </div>
              </div>
              <div class="flex leading-5.5">
                <div class="w-17 text-primary-text">库存数量</div>
                <div class="flex-1 w-0">
                  {{ selectedMaterial.total }}
                </div>
              </div>
            </div>
          </a-form>
        </div>
      </div>
    </mw-drawer>
    <quoteBom
      v-model:visible="quoteBOMVisible"
      @newArr="quoteBOMOnSubmitOnSubmit"
      :bomNoData="bomNoData"
    ></quoteBom>
    <!-- <mw-drawer
      :visible="quoteBOMVisible"
      @close="quoteBOMVisible = false"
      customTitle="引用BOM"
    >
      <template #header>
        <a-button type="primary" @click="quoteBOMOnSubmitOnSubmit"
          >确定</a-button
        >
      </template>
      <div>
        <div>
          <a-form-item>
            <template #label>
              <div class="w-15 text-left text-primary-text">BOM名称</div>
            </template>
            <a-select
              v-model:value="bomSearchName"
              show-search
              placeholder="请选择BOM"
              style="width: 100%"
              :default-active-first-option="false"
              :not-found-content="null"
              :options="allBomList"
              optionFilterProp="bomName"
              :fieldNames="{
                label: 'bomName',
                value: 'bomNo',
              }"
              @search="handleSearchBom"
              @change="handleChangBom"
            ></a-select>
          </a-form-item>
          <a-form-item>
            <template #label>
              <div class="w-15 text-left text-primary-text">BOM编码</div>
            </template>
            <div class="underline">
              {{ selectedBom || "-" }}
              <span></span>
            </div>
          </a-form-item>
        </div>
        <div class="">
          <div class="px-4 bg-background rounded-lg divide-y divide-border">
            <div
              class="text-primary-text py-4"
              v-for="item in bomRelatedMaterialList"
              :key="item.id"
            >
              <div class="flex justify-between items-center leading-5.5 mb-1">
                <div class="flex-1 text-title overflow">
                  {{ item.materialName }} ({{ item.materialNo }})
                </div>
              </div>
              <div class="flex justify-between items-center leading-5.5">
                <div class="space-y-1">
                  <div class="">{{ item.specification }}</div>
                </div>
                <div class="">x {{ item.quantity }} {{ item.unit }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </mw-drawer> -->
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
} from "vue";
import Search from "@/components/search/index.vue";
import FormUploadContract from "@/components/form-upload-contract.vue";
import { productionLineStatusFilter } from "@/common/constant.js";
import {
  getPage,
  addNew,
  update,
  remove,
  cancel,
  getAllBomList,
  getBomMaterialList,
  bomDetail,
  exportBom,
  bomStop,
} from "@/api/basicData/bom.js";
import { downFile } from "@/common/setup/index.js";
import { AllList, getInfo } from "@/api/basicData/material.js";
import { usePagenation } from "@/common/setup/index.js";
import FormUpload from "@/components/form-upload.vue";
import { uniqueArr, exportExecl } from "@/utils/util.js";
import { useUserStore } from "@/stores/user.js";
import { useRoute, useRouter } from "vue-router";
import { formatter2 } from "@/common/validate.js";
import { listNoRelatedBom } from "@/api/basicData/bom.js";
import quoteBom from "./orderBomChange/quoteBom.vue";
import _cloneDeep from "lodash/cloneDeep";
const userStore = useUserStore();
// import { list as getAllProductList } from "@/api/basicData/product.js";
const materiaSelect = {
  ignoreCancel: true,
  isExpense: 1,
};
const router = useRouter(),
  route = useRoute();
const columns = ref([
  {
    title: "BOM信息",
    dataIndex: "bomInfo",
    key: "bomInfo",
  },
  {
    title: "创建者",
    dataIndex: "createByName",
    key: "createByName",
  },
  {
    title: "包含物料",
    dataIndex: "materialTypeCount",
    key: "materialTypeCount",
  },
  {
    title: "使用状态",
    dataIndex: "status",
    key: "status",
  },
  {
    title: "技术协议",
    dataIndex: "fileDetailModel",
    key: "fileDetailModel",
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
  },
  {
    title: "操作",
    dataIndex: "button",
    key: "button",
  },
]);
const data = ref([]),
  allProductList = ref([]),
  quantity = ref(0),
  quoteBOMVisible = ref(false),
  addMaterialVisible = ref(false),
  addBomVisible = ref(false),
  loading = ref(false),
  bomNo = ref(undefined),
  canceled = ref(false),
  formRef = ref(),
  addMaterialFormRef = ref(),
  removeLoading = ref(false),
  tableLoading = ref(false),
  rules = reactive({
    bomName: [
      {
        required: true,
        message: "请输入BOM名称",
        trigger: "blur",
      },
    ],
    fileDetailModel: [
      {
        required: true,
        message: "请上传技术协议",
        trigger: "blur",
      },
    ],
  }),
  addMaterialRules = reactive({
    materialId: [
      {
        required: true,
        message: "请选择物料",
        trigger: "blur",
      },
    ],
    quantity: [
      {
        required: true,
        message: "请输入需求数量",
        trigger: "blur",
      },
    ],
  }),
  formData = reactive({
    bomName: undefined,
    fileDetailModel: [],
    productNo: undefined,
  }),
  formDataM = reactive({
    materialId: undefined,
    quantity: 1,
  }),
  editOrDetail = ref(undefined),
  materialId = ref(null),
  materialList = ref([]),
  allMaterialList = ref([]),
  selectedMaterial = ref({}),
  allBomList = ref([]),
  selectedBom = ref(null),
  bomRelatedMaterialList = ref([]),
  spinning = ref(false),
  bomSearchName = ref(),
  store = useUserStore(),
  exportLoading = ref(false);
const { proxy } = getCurrentInstance();
const searchData = ref({
  operationButtons: [],
  searchButtons: [],
  fields: {
    status: {
      name: "生产状态",
      type: "a-select",
      options: productionLineStatusFilter,
      placeholder: "选择状态",
      width: "120px",
      value: "",
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: "输入BOM名称",
      width: "240px",
      allowClear: true,
    },
  },
});
const columnsBomList = ref([
  {
    title: "物料编号",
    dataIndex: "materialNo",
    key: "materialNo",
  },
  {
    title: "物料名称",
    dataIndex: "materialName",
    key: "materialName",
  },
  {
    title: "物料规格",
    dataIndex: "specification",
    key: "specification",
    width: 100,
  },
  {
    title: "经济物料",
    dataIndex: "isEconomicMaterial",
    key: "isEconomicMaterial",
    customRender: ({ record }) => {
      return record.isEconomicMaterial == 1
        ? "是"
        : record.isEconomicMaterial == 0
        ? "否"
        : "";
    },
  },
  {
    title: "物料单台数量",
    dataIndex: "quantity",
    key: "quantity",
  },
  {
    title: "单位",
    dataIndex: "unit",
    key: "unit",
  },
  {
    title: "备注",
    dataIndex: "remark",
    key: "remark",
    width: 100,
  },
  {
    title: "操作",
    dataIndex: "operate",
    key: "operate",
    fixed: "right",
  },
]);
const filterOption = (input, option) => {
  // 根据名称和ID进行查询
  return (
    option.productName.toLowerCase().includes(input.toLowerCase()) ||
    option.productNo.toLowerCase().includes(input.toLowerCase())
  );
};
//  获取产品列表
// async function getAllProduct(keyword) {
//   let result = await listNoRelatedBom({
//     keyword: keyword,
//     ignoreCancel: true,
//   });
//   let { data } = result;
//   allProductList.value = data;
// }
const getList = async () => {
  tableLoading.value = true;
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  if (store.$state.paramsBomNo) {
    searchData.value.fields.keyword.value = store.$state.paramsBomNo;
    searchParam.keyword = store.$state.paramsBomNo;
  }

  //单个搜索信息
  // searchParam[searchData.value.filterData.value.filterBy] =
  //   searchData.value.filterData.value.filterValue;
  try {
    let result = await getPage(searchParam, { ...pageParam.value });
    data.value = result.data;
    // state.corpId = result.data.data.records[0].corpId;
    paginationProps.value.total = result.total;
    store.setParamsBomNo();
    tableLoading.value = false;
  } catch (error) {
    tableLoading.value = false;
  }
};
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

onBeforeMount(() => {
  getList();
});
// 停用bom--暂时注释，可能后期会试用
// const onDeactivate = async (record) => {
//   await bomStop({ bomNo: record.bomNo });
//   getList();
// };
function closeDrawer() {
  addBomVisible.value = false;
  bomNo.value = undefined;
  formRef.value.resetFields();
  materialList.value = [];
  bomRelatedMaterialList.value = [];
  editOrDetail.value = undefined;
}

async function rowClick(record, type) {
  // spinning.value = true;
  editOrDetail.value = type;
  addBomVisible.value = true;
  bomNo.value = record.bomNo;
  canceled.value = [2, 7, 9, 10].includes(record.status);
  let res = await bomDetail({ bomNo: record.bomNo });
  // bomRelatedMaterialList.value = res.data;
  formData.bomName = res.data.bomName;
  if (res.data.fileDetailModel) {
    formData.fileDetailModel = res.data.fileDetailModel;
  }
  formData.productNo = res.data.productNo;
  let result = await getBomMaterialList({ bomNo: record.bomNo });
  // getAllProduct();
  materialList.value = result.data;
  formData.lineName = record.lineName;
  formData.region = record.region;
  formData.introduce = record.introduce;
  spinning.value = false;
}
function addLine() {
  // getAllProduct();
  addBomVisible.value = true;
  bomNo.value = undefined;
}
// 新增
async function onSubmit() {
  formData.bomName = formData.bomName.trim();
  formRef.value
    .validate()
    .then(async () => {
      if (materialList.value.length) {
        loading.value = true;
        if (bomNo.value) {
          let res = await update({
            bomNo: bomNo.value,
            ...toRaw(formData),
            materialList: materialList.value,
            materialList: materialList.value.map((item) => {
              return {
                materialId: item.id ? item.id : item.materialId,
                quantity: item.quantity,
              };
            }),
          });
          if (res.code == 200) {
            proxy.$message.success("更新成功");
            closeDrawer();
          }
        } else {
          let res = await addNew({
            ...toRaw(formData),
            materialList: materialList.value.map((item) => {
              return { materialId: item.id, quantity: item.quantity };
            }),
          });
          if (res.code == 200) {
            proxy.$message.success("添加成功");
            closeDrawer();
          }
        }
        loading.value = false;

        getList();
      } else {
        proxy.$message.error("物料不能为空");
      }
      editOrDetail.value = undefined;
    })
    .catch((error) => {
      loading.value = false;
    });
}
// 删除
async function deleteBOM() {
  removeLoading.value = true;
  try {
    let res = await cancel({ bomNo: bomNo.value });
    if (res.code == 200) {
      proxy.$message.success("操作成功");
    }
    closeDrawer();
    removeLoading.value = false;
    getList();
  } catch (error) {
    removeLoading.value = false;
  }
  editOrDetail.value = undefined;
}

// 新增物料
function addMaterial() {
  addMaterialVisible.value = true;
  // getMaterialList();
}
// 确认新增物料
function addMaterialOnSubmit() {
  addMaterialFormRef.value.validate().then(async () => {
    !materialList.value.some((item) => item.id == selectedMaterial.value.id) &&
      materialList.value.unshift({
        ...selectedMaterial.value,
        quantity: formDataM.quantity,
      });

    closeMaterialDrawer();
    formDataM.materialId = null;
  });
}
function closeMaterialDrawer() {
  addMaterialVisible.value = false;
  addMaterialFormRef.value.resetFields();
  allMaterialList.value = [];
  selectedMaterial.value = {};
}
// // 获取物料列表
// async function getMaterialList(materialName) {
//   let result = await AllList({
//     materialName: materialName,
//   });
//   let { data } = result;
//   allMaterialList.value = data.map((item) => {
//     return {
//       label: item.materialName,
//       value: item.id,
//     };
//   });
// }

// // 搜索物料
// let timer;
// const handleSearchMaterial = (value) => {
//   if (value.length) {
//     clearTimeout(timer);
//     timer = setTimeout(() => {
//       getMaterialList(value);
//     }, 500);
//   } else {
//     setTimeout(() => {
//       allMaterialList.value = [];
//     }, 500);
//   }
// };

//   选中物料
const handleChangeMaterial = async (value) => {
  // 获取选中的物料信息
  let res = await getInfo(value);
  selectedMaterial.value = res.data;
};

// 关闭引用bomt弹窗
const closeBomDrawer = () => {
  quoteBOMVisible.value = false;
  allBomList.value = [];
  selectedBom.value = {};
  bomRelatedMaterialList.value = [];
  bomSearchName.value = undefined;
};
// 引用bom
const quoteBOM = () => {
  quoteBOMVisible.value = true;
  getAllBom("");
};
// 获取bom列表
async function getAllBom(keyword) {
  let result = await getAllBomList({
    keyword: keyword,
  });
  let { data } = result;
  allBomList.value = data;
}
// // 搜索BOM
// let BomTimer;
// const handleSearchBom = (value) => {
//   if (value.length) {
//     clearTimeout(BomTimer);
//     BomTimer = setTimeout(() => {
//       getAllBom(value);
//     }, 500);
//   } else {
//     setTimeout(() => {
//       allBomList.value = [];
//     }, 500);
//   }
// };
// 选中bom

const handleChangBom = async (value) => {
  selectedBom.value = allBomList.value.filter(
    (item) => item.bomNo == value
  )[0].bomNo;
  // 获取选中的bom信息
  let res = await getBomMaterialList({ bomNo: value });
  bomRelatedMaterialList.value = res.data;
};

// // 预览
// const downFile = (url, fileName) => {
//   const x = new XMLHttpRequest();
//   x.open("GET", url, true);
//   x.responseType = "blob";
//   x.onload = function () {
//     const url = window.URL.createObjectURL(x.response);
//     const a = document.createElement("a");
//     a.href = url;
//     a.download = fileName;
//     a.click();
//   };
//   x.send();
// };

const addNum = (num1, num2) => {
  var sq1, sq2, m;
  try {
    sq1 = num1.toString().split(".")[1].length;
  } catch (e) {
    sq1 = 0;
  }
  try {
    sq2 = num2.toString().split(".")[1].length;
  } catch (e) {
    sq2 = 0;
  }
  m = Math.pow(10, Math.max(sq1, sq2));
  return (num1 * m + num2 * m) / m;
};
// 确认引入bom
const quoteBOMOnSubmitOnSubmit = (newArr, bomNoData) => {
  const mapArr = new Map();
  const newArrList = _cloneDeep(newArr || []);
  const materialRelationList = _cloneDeep(materialList.value || []);
  if (materialRelationList.length >= 0) {
    const arr = [...materialRelationList, ...newArrList];
    arr.forEach((item) => {
      if (mapArr.has(item.materialId)) {
        const obj = mapArr.get(item.materialId);
        obj.quantity = addNum(obj?.quantity || 0, item?.quantity || 0);
      } else {
        mapArr.set(item.materialId, item);
      }
    });
    const arrList = [];
    for (let arr of mapArr) {
      arrList.push(arr[1]);
    }
    materialList.value = [];
    arrList.forEach((items) => {
      items.materialId = String(items.materialId);
      if (items.quantity !== 0) {
        // 将不等于 0 的项添加到 materialList.value 中
        materialList.value.push(items);
      }
    });
  }
};
// const quoteBOMOnSubmitOnSubmit = (List, bomNoData) => {
//   const materialRelationList = _cloneDeep(materialList.value || []);
//   const ListData = _cloneDeep(List || []);
//   if (materialRelationList.length > 0) {
//     let newArr = List.map((item) => {
//       return {
//         ...item,
//         id: item?.materialId,
//         quantity: item?.quantity || 0, // 把数量加进去
//       };
//     });

//     newArr.forEach((item, index) => {
//       materialList.value.forEach((ite, ind) => {
//         if (item.materialId == ite.materialId) {
//           ite.quantity = item.quantity + ite.quantity;
//         }
//       });
//     });
//     // 合并数组
//     const resultArr = uniqueArr(newArr);
//     materialList.value = resultArr;
//   } else {
//     materialList.value = ListData;
//   }
// };
const exportOrg = async () => {
  exportLoading.value = true;
  pageParam.value.pageSize = 100000;
  let searchParam = {
    // ...pageParam.value,
  };
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  //单个搜索信息
  if (searchData.value.filterData) {
    searchParam[searchData.value.filterData.value.filterBy] =
      searchData.value.filterData.value.filterValue;
  }
  let result = await exportBom(searchParam, pageParam.value);
  const fileName = "Bom.xlsx";
  exportExecl(fileName, result);
  exportLoading.value = false;
};
</script>

<style lang="less" scoped>
:deep(.ant-table-cell) {
  vertical-align: top;
}
</style>
