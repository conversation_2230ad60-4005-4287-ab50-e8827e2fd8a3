<template>
  <div class="add-bank">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      labelAlign="right"
      :label-col="{ span: 6, offset: 0 }"
      :wrapper-col="{ span: 19, offset: 0 }"
      autocomplete="off"
    >
      <a-form-item label="银行账户编码" name="bankCode">
        <a-input
          v-model:value="formData.bankCode"
          :maxLength="10"
          placeholder="银行账户编码"
          @change="(val) => allChange(val, 'bankCode', 1)"
        />
      </a-form-item>
      <a-form-item label="支行名称" name="bankName">
        <a-input
          v-model:value="formData.bankName"
          :maxLength="20"
          placeholder="支行名称"
        />
      </a-form-item>
      <a-form-item label="银行账号" name="bankNo">
        <a-input
          v-model:value="formData.bankNo"
          :maxLength="30"
          placeholder="银行账号"
          @change="(val) => allChange(val, 'bankNo', 3)"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { reactive, ref, watch } from "vue";
const props = defineProps({ data: { type: Object } });
const formData = reactive({
  bankNO: void 0,
  bankName: void 0,
  bankCode: void 0,
});
const rules = {
  bankCode: [
    { required: true, message: "请输入银行账户编码", trigger: "blur" },
  ],
  bankName: [{ required: true, message: "请输入支行名称", trigger: "blur" }],
  bankNo: [{ required: false }],
};

const formRef = ref(null);

const submit = () => {
  return formRef.value.validateFields();
};

const clearValidate = () => {
  formRef.value.clearValidate();
};

const resetFields = () => {
  formRef.value.resetFields();
};

const changeType = {
  1: /[^\d]/g,
  2: /[^\u4e00-\u9fa5\d]/g,
  3: /[^\d]/g,
};

const allChange = (e, key, type) => {
  const regex = changeType[type];
  const val = e.target.value;
  formData[key] = val.replace(regex, "");
};

watch(
  () => props.data,
  (val) => {
    if (val) {
      Object.keys(val).forEach((key) => {
        formData[key] = val[key];
      });
    }
  },
  { immediate: true }
);

defineExpose({
  submit,
  clearValidate,
  resetFields,
});
</script>

<style scoped lang="less"></style>
