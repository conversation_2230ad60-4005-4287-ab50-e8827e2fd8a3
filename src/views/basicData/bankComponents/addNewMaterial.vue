<template>
  <div class="add-material">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      labelAlign="right"
    >
      <a-form-item
        label="名称"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
        name="materialName"
      >
        <a-input
          v-model:value="formData.materialName"
          placeholder="请输入名称"
        ></a-input>
      </a-form-item>
      <a-form-item
        label="规格"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
        name="specification"
      >
        <a-input
          v-model:value="formData.specification"
          placeholder="请输入规格"
        ></a-input>
      </a-form-item>
      <a-form-item
        label="分类"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
        name="classificationId"
      >
        <a-tree-select
          ref="treeSelect"
          v-model:value="formData.classificationId"
          show-search
          :tree-data="classificationIdTreeData"
          :field-names="classificationIdReplaceFields"
          placeholder="请选择分类"
          dropdown-matc-select-width
          allow-clear
          tree-node-filter-prop="name"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          @change="handleCategoryChange"
          :disabled="id ? true : false"
          @select="onSelect"
        />
      </a-form-item>
      <a-form-item
        v-if="formData.msgCode"
        label="物料编码："
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
      >
        {{ formData.msgCode }}</a-form-item
      >

      <a-form-item
        label="物料编码"
        name="materialNo"
        v-if="showMaterialNo || id"
        required
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
      >
        <a-input
          v-model:value="formData.materialNo"
          placeholder="请输入物料编码"
          :maxLength="20"
          allow-clear
          :addon-before="bbc"
          :disabled="id ? true : false"
        />
      </a-form-item>
      <a-form-item
        label="单位"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
        name="unitId"
      >
        <a-select
          v-model:value="formData.unitId"
          show-search
          placeholder="请选择单位"
          style="width: 100%"
          :default-active-first-option="false"
          :not-found-content="null"
          :options="unitIdOptions"
          optionFilterProp="name"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :disabled="id ? true : false"
        ></a-select>
      </a-form-item>
      <a-form-item
        label="容量"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
        name="capacity"
        help="容量不得为空，非销售产品可以输入1进行保存"
      >
        <a-flex
          ><a-input-number
            placeholder="容量"
            v-model:value="formData.capacity"
            :min="0"
            :formatter="formatter2"
          />
          <div class="ml-1 mt-1">瓦时</div></a-flex
        >
      </a-form-item>
      <a-form-item
        label="默认仓库"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
      >
        <a-select
          v-model:value="formData.defaultWarehouseId"
          show-search
          placeholder="请选择所属仓库"
          style="width: 100%"
          :default-active-first-option="false"
          :not-found-content="null"
          :options="defaultWarehouseOptions"
          optionFilterProp="name"
          :fieldNames="{
            label: 'warehouseName',
            value: 'id',
          }"
          @change="onDefaultWarehouse"
        ></a-select>
      </a-form-item>

      <a-form-item
        label="默认库位"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
      >
        <a-select
          v-model:value="formData.defaultWarehouseAreaId"
          show-search
          placeholder="请选择仓库区域"
          style="width: 100%"
          :default-active-first-option="false"
          :not-found-content="null"
          :options="defaultWarehouseAreaOptions"
          optionFilterProp="name"
          :fieldNames="{
            label: 'warehouseArea',
            value: 'id',
          }"
        ></a-select>
      </a-form-item>

      <a-form-item
        label="备注附件"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
        name="remarkFileList"
      >
        <form-upload-contract
          v-if="true"
          v-model:value="formData.remarkFileList"
          sence="article"
          :fileTypes="[]"
          :fileSize="100"
          :readonly="false"
          hasDownLoad
          :fileLName="true"
          :fileLimit="9999"
          :detailType="'1'"
          :msgCodeName="formData.msgCode"
          @del="(index) => delFile(index, 'remarkFileList')"
          :delShow="true"
        >
        </form-upload-contract>
        <div
          v-else
          v-for="(item, index) in formData.file"
          :key="index"
          @click.stop="openUrl(item.fileVisitUrl)"
          :href="item.fileVisitUrl"
          :title="item.fileName"
          class="cursor-pointer inline-block"
          style="color: #959ec3"
        >
          <i
            v-if="item.fileName"
            class="iconfont icon-jichu-lianjie text-xs -middle mr-1"
            style="color: #959ec3"
          ></i
          ><span class="underline"> {{ item.fileName }}</span>
        </div>
      </a-form-item>
      <a-form-item
        label="销售价格"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
        name="sellPrice"
      >
        <a-input-number
          v-model:value="formData.sellPrice"
          class="w-full"
          :formatter="formatter"
          :stringMode="true"
        />
      </a-form-item>

      <a-form-item
        label="备注"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
        name="remark"
      >
        <a-textarea v-model:value="formData.remark" placeholder="请输入备注" />
      </a-form-item>

      <a-form-item
        label="图片"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
        name="materialImage"
      >
        <form-upload-contract
          v-if="true"
          v-model:value="formData.materialImage"
          sence="article"
          :fileTypes="[]"
          :fileSize="100"
          :readonly="false"
          hasDownLoad
          :fileLName="true"
          :fileLimit="9999"
          :detailType="'1'"
          @del="(index) => delFile(index, 'materialImage')"
          :delShow="true"
        >
        </form-upload-contract>
        <div
          v-else
          v-for="(item, index) in formData.materialImage"
          :key="index"
          @click.stop="openUrl(item.fileVisitUrl)"
          :href="item.fileVisitUrl"
          :title="item.fileName"
          class="cursor-pointer inline-block"
          style="color: #959ec3"
        >
          <i
            v-if="item.fileName"
            class="iconfont icon-jichu-lianjie text-xs -middle mr-1"
            style="color: #959ec3"
          ></i
          ><span class="underline"> {{ item.fileName }}</span>
        </div>
      </a-form-item>
      <a-form-item
        style="margin-left: 18%"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
      >
        <a-checkbox
          v-model:checked="formData.isExpense"
          @change="toggleCheckbox($event, 'isExpense')"
          >耗用</a-checkbox
        >
        <div class="mt-4">
          <a-checkbox
            v-if="formData.isExpense !== 0"
            :disabled="formData.isExpense == 0 ? true : false"
            v-model:checked="formData.isEconomicMaterial"
            @change="toggleCheckbox($event, 'isEconomicMaterial')"
            >经济物料</a-checkbox
          >
          <a-checkbox
            v-if="formData.isEconomicMaterial !== 0"
            v-model:checked="formData.isEconomicMaterialTemplate"
            @change="toggleCheckbox($event, 'isEconomicMaterialTemplate')"
            >经济物料模板</a-checkbox
          >
        </div>
        <div class="flex flex-row mt-4" v-if="formData.isExpense !== 0">
          <div class="flex-none w-28">耗用默认生产线</div>
          <a-select
            show-search
            placeholder="请选择耗用默认生产线"
            optionFilterProp="lineName"
            :options="lineAllListOptions"
            :not-found-content="null"
            v-model:value="formData.lineId"
            :default-active-first-option="false"
            :fieldNames="{
              label: 'lineName',
              value: 'id',
            }"
          ></a-select>
        </div>
        <div class="flex flex-row mt-4">
          <a-checkbox v-model:checked="formData.isLineEdgeLibrary"
            >线边库类型</a-checkbox
          >
          <a-radio-group
            v-model:value="formData.lineEdgeLibraryType"
            :disabled="!formData.isLineEdgeLibrary"
          >
            <a-radio
              :value="item.value"
              v-for="item in plainOptions"
              :key="item.value"
              >{{ item.label }}</a-radio
            >
          </a-radio-group>
        </div>
      </a-form-item>

      <a-form-item
        style="margin-left: 18%"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
      >
        <a-checkbox
          v-model:checked="formData.isHomemade"
          @change="toggleCheckbox($event, 'isHomemade')"
          >自制</a-checkbox
        ></a-form-item
      >
      <a-form-item
        v-if="formData.isHomemade == 1"
        label="关联BOM"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
        name="bomNo"
        :rules="[{ required: true, message: '关联BOM不得为空' }]"
      >
        <a-select
          v-model:value="formData.bomNo"
          :options="bomOptions"
          :fieldNames="{
            label: 'bomName',
            value: 'bomNo',
          }"
          optionFilterProp="bomName"
          show-search
          placeholder="请选择关联BOM"
        ></a-select>
      </a-form-item>

      <a-form-item
        v-if="formData.isHomemade == 1"
        label="技术协议编号"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
        name="fileNumber"
        :rules="[{ required: true, message: '技术协议编号不得为空' }]"
      >
        <a-input
          v-model:value="formData.fileNumber"
          placeholder="请输入技术协议编号"
        />
      </a-form-item>

      <a-form-item
        v-if="formData.isHomemade == 1"
        label="技术协议附件"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
        name="file"
        :rules="[{ required: true, message: '技术协议附件必填不得为空' }]"
      >
        <form-upload-contract
          v-if="true"
          v-model:value="formData.file"
          sence="article"
          :fileTypes="[]"
          :fileSize="100"
          :readonly="false"
          hasDownLoad
          :fileLName="true"
          :fileLimit="9999"
          :detailType="'1'"
          @del="delFile"
          :delShow="true"
        >
        </form-upload-contract>
        <div
          v-else
          v-for="(item, index) in formData.fileDetailModel"
          :key="index"
          @click.stop="openUrl(item.fileVisitUrl)"
          :href="item.fileVisitUrl"
          :title="item.fileName"
          class="cursor-pointer inline-block"
          style="color: #959ec3"
        >
          <i
            v-if="item.fileName"
            class="iconfont icon-jichu-lianjie text-xs -middle mr-1"
            style="color: #959ec3"
          ></i
          ><span class="underline"> {{ item.fileName }}</span>
        </div>
      </a-form-item>

      <a-form-item
        v-if="formData.isHomemade == 1"
        label="用途"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
      >
        <a-input v-model:value="formData.use" placeholder="请输入用途" />
      </a-form-item>
      <a-form-item
        v-if="formData.isHomemade == 1"
        label="是否需要调试"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
        name="isNeedDebugging"
      >
        <a-select
          v-model:value="formData.isNeedDebugging"
          :options="[
            { label: '是', value: '1' },
            { label: '否', value: '0' },
          ]"
        />
      </a-form-item>
      <a-form-item
        style="margin-left: 18%"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
      >
        <a-checkbox
          v-model:checked="formData.isMarket"
          @change="toggleCheckbox($event, 'isMarket')"
          >销售</a-checkbox
        >
      </a-form-item>
      <a-form-item
        style="margin-left: 18%"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
      >
        <a-checkbox
          v-model:checked="formData.isPurchase"
          @change="toggleCheckbox($event, 'isPurchase')"
          >采购</a-checkbox
        ></a-form-item
      >
      <a-form-item
        v-if="formData.isPurchase == 1"
        label="品牌"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
        name="brand"
      >
        <a-input
          v-model:value="formData.brand"
          placeholder="请输入品牌"
          :maxLength="20"
          allow-clear
        >
        </a-input>
      </a-form-item>

      <a-form-item
        v-if="formData.isPurchase == 1"
        label="安全库存"
        :label-col="{ span: 4, offset: 0 }"
        :wrapper-col="{ span: 20, offset: 0 }"
        name="minimumStock"
      >
        <a-input-number
          placeholder="请输入安全库存"
          v-model:value="formData.minimumStock"
          class="w-full"
          :stringMode="true"
        />
        <p class="text-xs text-secondar-text mt-1">
          当物料库存低于安全库存时，系统将根据当前排产订单所需物料情况自动发起采购订单，以补足库存，确保生产计划正常进行！
        </p>
      </a-form-item>

      <a-form-item
        v-if="formData.isPurchase == 1"
        label="供应商"
        :label-col="{ span: 3, offset: 0 }"
        :wrapper-col="{ span: 21, offset: 0 }"
      >
        <div style="margin-bottom: 5px; text-align: right">
          <mw-button @click="addSuppiler">新增</mw-button>
        </div>
        <mw-table
          :columns="columns"
          :data-source="formData.materialSupplierRelationParams"
          :rowKey="(index) => index"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key == 'supplierId'">
              <a-select
                placeholder="请选择供应商"
                :options="supplierIdListOptions"
                v-model:value="record.supplierId"
                optionFilterProp="name"
                @change="handleChange"
                :field-names="{
                  label: 'name',
                  value: 'id',
                }"
                show-search
              >
              </a-select>
            </template>
            <template v-if="column.key == 'taxRate'">
              <a-select
                class="w-full"
                v-model:value="record.taxRate"
                :options="taxRateList"
                placeholder="请选择税率"
              />
              <!-- <a-input-number
                v-model:value="record.taxRate"
                :stringMode="true"
              />
                :min="0"
                :max="999999"
              /> -->
            </template>
            <template v-if="column.key == 'price'">
              <a-input-number v-model:value="record.price" :stringMode="true" />
            </template>
            <template v-if="column.key == 'action'">
              <i
                @click="deleteSupplier(index)"
                class="iconfont icon-jichu-shanchu deleteIcon cursor-pointer text-secondar-text hover:text-primary-text ml-1"
              ></i>
            </template>
          </template>
        </mw-table>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { reactive, ref, watch, getCurrentInstance } from "vue";
import { getAllBomList } from "@/api/basicData/bom.js";
import { list as getAllCategory } from "@/api/basicData/category.js";
import { list as getAllUnit } from "@/api/basicData/unit.js";
import { list as getAllSupplier } from "@/api/basicData/supplier.js";
import { formatter2 } from "@/common/validate.js";
import { SearchOutlined } from "@ant-design/icons-vue";
import {
  page,
  getInfo as getInfoRepository,
} from "@/api/basicData/repository.js";
import { usePagenation } from "@/common/setup";
import FormUploadContract from "@/components/form-upload-contract.vue";
import { list as getLineAllList } from "@/api/basicData/productionLine.js";
import { getMaterialGenerateMaterialNo } from "@/api/basicData/material.js";
// import { stringType } from "ant-design-vue/es/_util/type";
import { taxRateList } from "@/common/constant.js";
const formRef = ref(null);
const showMaterialNo = ref(false);
const bbc = ref();
const lineAllListOptions = ref([]);
const formData = reactive({
  msgCode: void 0,
  materialName: void 0,
  specification: void 0,
  bomNo: void 0,
  classificationId: void 0,
  fileNumber: void 0,
  unitId: void 0,
  defaultWarehouseAreaId: void 0,
  defaultWarehouseId: void 0,
  remark: void 0,
  brand: void 0,
  minimumStock: void 0,
  use: void 0,
  capacity: void 0,
  isNeedDebugging: "0",
  isHomemade: 0,
  isExpense: 1,
  isMarket: 1,
  isPurchase: 1,
  isEconomicMaterial: 0,
  isEconomicMaterialTemplate: 0,
  sellPrice: 0,
  fileDetailModel: [],
  file: [],
  materialImage: [],
  lineId: "",
  materialSupplierRelationParams: [
    {
      supplierId: undefined,
      price: 0,
      taxRate: 13,
    },
  ],
  isLineEdgeLibrary: false,
  lineEdgeLibraryType: void 0,
});
const optionsExpendExpendLine = ref([]);
const props = defineProps({
  clickData: {
    type: Object,
    default: () => ({}),
  },
  id: String,
});

const classificationIdReplaceFields = {
  children: "children",
  label: "name",
  value: "id",
};

const rules = {
  materialName: [
    {
      required: true,
      message: "请输入名称",
      trigger: "blur",
    },
  ],
  // bomNo: [
  //   {
  //     required: true,
  //     trigger: "change",
  //     message: "请选择关联BOM",
  //   },
  // ],
  unitId: [
    {
      required: true,
      trigger: "change",
      message: "请选择单位",
    },
  ],
  minimumStock: [
    {
      required: false,
    },
  ],
  // fileNumber: [
  //   {
  //     required: true,
  //     message: "请输入技术协议编号",
  //     trigger: "blur",
  //   },
  // ],
  specification: [
    {
      required: true,
      message: "请输入规格",
      trigger: "blur",
    },
  ],
  defaultWarehouseId: [
    {
      required: false,
    },
  ],
  defaultWarehouseAreaId: [
    {
      required: false,
    },
  ],
  classificationId: [
    {
      required: true,
      message: "请选择分类",
      trigger: "change",
    },
  ],
  capacity: [
    {
      required: true,
      message: "请输入容量",
      trigger: "change",
    },
  ],
  materialSupplierRelationParams: [
    {
      required: false,
      message: "请选择供应商",
      trigger: "change",
    },
  ],
  isNeedDebugging: [
    {
      required: false,
    },
  ],
  remark: [
    {
      required: false,
    },
  ],
  brand: [
    {
      required: false,
    },
  ],
  fileDetailModel: [
    {
      required: false,
    },
  ],
  file: [
    {
      required: false,
    },
  ],
  materialImage: [
    {
      required: false,
    },
  ],
  sellPrice: [
    {
      required: false,
    },
  ],
  isLineEdgeLibrary: [
    {
      required: false,
    },
  ],
  lineEdgeLibraryType: [
    {
      required: false,
    },
  ],
};

const columns = [
  { title: "供应商名称", key: "supplierId", dataIndex: "supplierId" },
  { title: "税率", key: "taxRate", dataIndex: "taxRate" },
  { title: "含税价格", key: "price", dataIndex: "price" },
  { title: "操作", key: "action", dataIndex: "action", width: 90 },
];

const bomOptions = ref([]);

const classificationIdTreeData = ref([]);

const unitIdOptions = ref([]);

const defaultWarehouseOptions = ref([]);

const defaultWarehouseAreaOptions = ref([]);

const supplierIdListOptions = ref([]);
const toggleCheckbox = (e, fieldName) => {
  formData[fieldName] = e.target.checked == true ? 1 : 0;
  if (fieldName == "isExpense") {
    formData.isEconomicMaterial = 0;
    formData.isEconomicMaterialTemplate = 0;
  }
  if (fieldName == "isEconomicMaterial") {
    formData.isEconomicMaterialTemplate = 0;
  }
  if (fieldName == "isHomemade") {
    if (!e.target.checked) {
      formData.bomNo = void 0;
      formData.fileNumber = void 0;
      formData.file = [];
      formData.use = void 0;
      formData.isNeedDebugging = "";
      formData.bomName = void 0;
      formData.isEconomicMaterialTemplate = 0;
    }
  }
  if (fieldName == "isPurchase") {
    if (!e.target.checked) {
      formData.brand = void 0;
      formData.minimumStock = 0;
      formData.materialSupplierRelationParams = [];
    }
  }
};
const getBomList = async () => {
  const { code, data } = await getAllBomList({
    keyword: "",
    ignoreStatus: false,
  });
  if (code == 200) {
    bomOptions.value = data;
  }
};
const lineAllList = async () => {
  const { code, data } = await getLineAllList({
    pageNum: 1,
    pageSize: 99999,
  });
  if (code == 200) {
    lineAllListOptions.value = [...data];
  }
};

const getClassificationIdTreeData = async () => {
  let result = await getAllCategory("material");
  classificationIdTreeData.value = result.data?.map((item) => {
    return { ...item, selectable: item?.children == 0 };
  });
};

const getUnitList = async () => {
  let result = await getAllUnit();
  unitIdOptions.value = result.data;
};

const getList = async () => {
  let param = {
    warehouseType: "",
  };
  pageParam.value.pageSize = 999999;
  let result = await page(pageParam.value, param);
  defaultWarehouseOptions.value = result.data;
};

const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

const onDefaultWarehouse = async (e) => {
  formData.defaultWarehouseAreaId = void 0;
  let res = await getInfoRepository(e);
  defaultWarehouseAreaOptions.value =
    res?.data?.warehouseMenuDetailVoList || [];
};

const getSupplierList = async () => {
  let result = await getAllSupplier();
  supplierIdListOptions.value = result?.data || [];
};

const delFile = (index, key) => {
  // formData[key].splice(index, 1);
};

// 预览
const openUrl = (url) => {
  window.open(url, "_blacnk");
};

const addSuppiler = () => {
  formData.materialSupplierRelationParams.push({
    supplierId: undefined,
    price: 0,
    taxRate: 13,
  });
};

const handleCategoryChange = async (value, label, extra) => {
  let res = await getMaterialGenerateMaterialNo({ classificationId: value });
  formData.msgCode = res.data;
  formData.remarkFileList = [];
  formData.materialNo = void 0;
  let { allCheckedNodes } = extra;
  if (allCheckedNodes.length > 0) {
    let { autogenerationMaterialCodeFlag } = allCheckedNodes[0].node.props;
    showMaterialNo.value = !autogenerationMaterialCodeFlag;
  }
};
const handleChange = () => {};

const deleteSupplier = (index) => {
  formData.materialSupplierRelationParams.splice(index, 1);
};
const onSelect = (selectedKeys, node) => {
  bbc.value =
    (node.parentCode ? node.parentCode : "") + (node.code ? node.code : "");
};

getBomList();
lineAllList();
getClassificationIdTreeData();

getUnitList();

getList();

getSupplierList();
const { proxy } = getCurrentInstance();
const submit = () => {
  if (formData.isExpense) {
    if (!formData.lineId) {
      proxy.$message.error("默认生产线不得为空");
      return false;
    } else {
      return formRef.value.validateFields();
    }
  } else {
    return formRef.value.validateFields();
  }
};
const infodata = (val) => {
  if (!val.remarkFileList) {
    val.remarkFileList = [];
  }
  Object.keys(val).forEach((key) => {
    formData[key] = val[key];
    if (key == "remarkFileList") {
      formData[key] = val[key] ? val[key] : [];
    }
    if (key == "fileDetailModel") {
      formData[key] = val[key] ? val[key] : [];
    }
    if (key == "file") {
      formData[key] = val[key] ? val[key] : [];
    }
    if (key == "materialImage") {
      formData[key] = val[key] ? val[key] : [];
    }
    // if (key == "isEconomicMaterialTemplate") {
    //   formData[key] = val[key] ? (val[key] == 0 ? false : true) : false;
    // }

    if (key == "materialSupplierRelationList") {
      formData.materialSupplierRelationParams = val[key]
        ? val[key].map((item, index) => {
            item.supplierId = Number(item.supplierId);
            return item;
          })
        : [];
    }
    if (key == "defaultWarehouseId") {
      formData.defaultWarehouseId = val[key] ? Number(val[key]) : "";
      if (val[key]) {
        onDefaultWarehouse(val[key]);
      }
    }
    if (key == "materialNo") {
      formData.msgCode = val[key];
      formData.materialNo = val[key];
    }
  });
};
const plainOptions = ref([
  {
    label: "PACK线边库物料",
    value: 7,
  },
  {
    label: "电芯线边库物料",
    value: 8,
  },
]);
watch(
  () => formData.isLineEdgeLibrary,
  (newVal) => {
    console.log("[ newVal ] >", newVal);
    if (!newVal) {
      // 当 isLineEdgeLibrary 为 false 时，清空选中项
      formData.lineEdgeLibraryType = void 0;
    }
  },
  // 添加 immediate: true 确保初始状态也触发
  { immediate: true }
);

defineExpose({ submit, formData, infodata });
</script>
