<template>
  <mw-drawer
    width="38%"
    :custom-title="id ? '修改物料' : '新增物料'"
    :visible="visible"
    @close="onClose"
    :spinning="spinning"
  >
    <template #header>
      <div v-if="!isStop">
        <a-popconfirm
          title="确认取消？"
          @confirm="submitCancel"
          v-if="canEdit"
          placement="bottomRight"
        >
          <mw-button :loading="cancelLoading">停用物料</mw-button>
        </a-popconfirm>
      </div>

      <mw-button
        v-if="!id || editStat"
        @click="submitForm"
        :loading="submitLoading"
        >确定</mw-button
      >
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      :label-col="{ span: 7, offset: -1 }"
    >
      <a-form-item label="物料名称" name="materialName">
        <a-input
          v-model:value="formData.materialName"
          placeholder="请输入物料名称"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item label="物料规格" name="specification">
        <a-input
          v-model:value="formData.specification"
          placeholder="请输入物料规格"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item label="物料单位" name="unitId">
        <a-select
          v-model:value="formData.unitId"
          show-search
          placeholder="请选择物料单位"
          style="width: 100%"
          :default-active-first-option="false"
          :not-found-content="null"
          :options="unitIdOptions"
          optionFilterProp="name"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :disabled="status == 9 || status == 10 || !id ? false : true"
        ></a-select>
      </a-form-item>
      <a-form-item label="所属仓库">
        <a-select
          v-model:value="formData.defaultWarehouseId"
          show-search
          placeholder="请选择所属仓库"
          style="width: 100%"
          :default-active-first-option="false"
          :not-found-content="null"
          :options="defaultWarehouseOptions"
          optionFilterProp="name"
          :fieldNames="{
            label: 'warehouseName',
            value: 'id',
          }"
          @change="onDefaultWarehouse"
        ></a-select>
      </a-form-item>
      <a-form-item label="仓库区域">
        <a-select
          v-model:value="formData.defaultWarehouseAreaId"
          show-search
          placeholder="请选择仓库区域"
          style="width: 100%"
          :default-active-first-option="false"
          :not-found-content="null"
          :options="defaultWarehouseAreaOptions"
          optionFilterProp="name"
          :fieldNames="{
            label: 'warehouseArea',
            value: 'id',
          }"
        ></a-select>
      </a-form-item>
      <a-form-item label="物料分类" name="classificationId">
        <!-- tree-default-expand-all -->
        <a-tree-select
          ref="treeSelect"
          v-model:value="formData.classificationId"
          show-search
          :tree-data="classificationIdTreeData"
          :field-names="classificationIdReplaceFields"
          placeholder="请选择物料分类"
          dropdown-matc-select-width
          allow-clear
          tree-node-filter-prop="name"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          @change="handleCategoryChange"
          @select="onSelect"
          :disabled="status == 9 || status == 10 || !id ? false : true"
        />
      </a-form-item>
      <a-form-item
        label="物料编码"
        name="materialNo"
        v-if="showMaterialNo || id"
      >
        <!-- <a-input
          v-model:value="formData.materialNo"
          placeholder="请输入物料编码"
          :maxLength="20"
          allow-clear
          :disabled="!!id"
        ></a-input> -->
        <a-input
          v-model:value="formData.materialNo"
          placeholder="请输入物料编码"
          :maxLength="20"
          allow-clear
          :disabled="!!id"
          :addon-before="bbc"
        />
      </a-form-item>

      <a-form-item label="安全库存">
        <a-input-number
          v-model:value="formData.minimumStock"
          class="w-full"
          :stringMode="true"
        />
        <p class="text-xs text-secondar-text mt-1">
          当物料库存低于安全库存时，系统将根据当前排产订单所需物料情况自动发起采购订单，以补足库存，确保生产计划正常进行！
        </p>
      </a-form-item>
      <a-form-item label="品牌" name="brand">
        <a-input
          v-model:value="formData.brand"
          placeholder="请输入品牌"
          allow-clear
        ></a-input>
      </a-form-item>

      <!-- <a-form-item label="当前库存" >
        <a-input-number  v-model:value="formData.currentStock" :max="9999999" class="w-full" />
      </a-form-item> -->
      <a-form-item label="可用库存" v-if="!!id">
        <a-input-number
          v-model:value="formData.availableStock"
          class="w-full"
          disabled
          :stringMode="true"
        />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <!--  :disabled="!!id" -->
        <a-input
          v-model:value="formData.remark"
          placeholder="请输入备注"
          :maxLength="20"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item name="materialImage" label="物料图片">
        <!-- <form-upload
          v-if="!props.id"
          v-model:value="formData.materialImage"
          sence="article"
          :fileSize="100"
          list-type="picture-card"
        ></form-upload>
        <div v-else>
          <div
            v-if="formData.materialImage?.fileVisitUrl"
            style="width: 100px; height: 100px"
          >
            <img
              width="100"
              height="100"
              :src="formData.materialImage?.fileVisitUrl"
              alt=""
            />
            <button @click="deleteUpload"><DeleteOutlined /></button>
          </div>
        </div> -->
        <form-upload
          v-model:value="formData.materialImage"
          sence="delivery"
          :fileTypes="[]"
          :fileSize="100"
          hasDownLoad
        ></form-upload>
      </a-form-item>
      <a-form-item name="file" label="附件">
        <div class="overflow">
          <div v-if="formData.file?.fileVisitUrl">
            <i
              class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
              style="color: #959ec3"
            ></i>
            <a
              style="word-break: break-all; color: #959ec3"
              :href="formData.file?.fileVisitUrl"
              :title="formData.file?.fileName"
              target="_blank"
              class="underline"
              >{{ formData.file?.fileName }}
            </a>
            <button @click="deleteUpload"><DeleteOutlined /></button>
          </div>
          <!-- 'doc', 'docx', 'xls', 'xlsx', 'pdf    -->
          <form-upload
            v-if="deleteUploadSwitch || !props.id"
            v-model:value="formData.file"
            sence="article"
            list-type="picture-card"
            :fileTypes="[]"
            :fileSize="100"
          ></form-upload>
        </div>
      </a-form-item>

      <!-- 功能不删,暂时隐藏 -->
      <!-- <a-form-item label="核算补料中的数量">
        <a-input-number
          v-model:value="formData.checkAddingQuantity"
          :max="999999"
          class="w-full"
        />
      </a-form-item> -->
      <div class="">
        <div class="mb-2">所属供应商</div>
        <div class="px-2">
          <div
            class="flex justify-center items-center w-full text-xs text-primary-text mb-1"
          >
            <div class="flex-1">供应商名称</div>
            <div class="flex-1">税率%</div>
            <div class="w-30">含税单价</div>
          </div>
          <div
            class="flex justify-between items-center w-full"
            v-for="(item, index) in formData.materialSupplierRelationParams"
            :key="index"
          >
            <div class="flex-1">
              <!-- <a-select
                v-model:value="item.supplierId"
                placeholder="请选择供应商"
                allow-clear
                show-arrow
                style="width: 80%"
                show-search
              >
                <a-select-option
                  optionFilterProp="name"
                  v-for="item in supplierIdListOptions"
                  :key="item.id"
                  :value="item.id"
                  >{{ item.name }}</a-select-option
                >
              </a-select> -->
              <a-select
                placeholder="请选择供应商"
                :options="supplierIdListOptions"
                v-model:value="item.supplierId"
                optionFilterProp="name"
                @change="handleChange"
                :field-names="{
                  label: 'name',
                  value: 'id',
                }"
                show-search
              >
              </a-select>
            </div>
            <div class="flex-1">
              <a-input-number
                style="width: 130px"
                v-model:value="item.taxRate"
                :stringMode="true"
              />
            </div>
            <div class="w-100 flex justify-end items-center">
              <a-input-number
                style="width: 130px"
                v-model:value="item.price"
                :formatter="formatter8"
                :stringMode="true"
              />
              <i
                @click="deleteSupplier(index)"
                class="iconfont icon-jichu-shanchu deleteIcon cursor-pointer text-secondar-text hover:text-primary-text ml-1"
              ></i>
            </div>
          </div>
          <div class="text-center mt-4">
            <mw-button @click="addSuppiler">添加供应商</mw-button>
          </div>
        </div>
      </div>
      <!-- <a-form-item label="供应商" name="supplierIdList">
        <a-select
          v-model:value="formData.supplierIdList"
          placeholder="请选择供应商"
          mode="multiple"
          allow-clear
          show-arrow
        >
          <a-select-option
            v-for="item in supplierIdListOptions"
            :key="item.id"
            :value="item.id"
            >{{ item.name }}</a-select-option
          >
        </a-select>
      </a-form-item> -->
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  onBeforeMount,
  defineEmits,
  computed,
} from "vue";
import {
  add,
  getInfo,
  update,
  cancelMaterial,
} from "@/api/basicData/material.js";
import { formatter8 } from "@/common/validate.js";
import { list as getAllUnit } from "@/api/basicData/unit.js";
import { list as getAllCategory } from "@/api/basicData/category.js";
import { list as getAllSupplier } from "@/api/basicData/supplier.js";
import {
  page,
  getInfo as getInfoRepository,
} from "@/api/basicData/repository.js";
import { usePagenation } from "@/common/setup";
import FormUpload from "@/components/form-upload.vue";
import { DeleteOutlined } from "@ant-design/icons-vue";
const { proxy } = getCurrentInstance();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
});
const emit = defineEmits(["update:visible", "finish"]);
const deleteUploadSwitch = ref(false);
const bbc = ref();
const formRef = ref(),
  submitLoading = ref(false),
  cancelLoading = ref(false),
  spinning = ref(false),
  status = ref(""),
  formData = ref({
    defaultWarehouseAreaId: undefined,
    defaultWarehouseId: undefined,
    materialName: undefined,
    minimumStock: undefined,
    specification: undefined,
    unitId: undefined,
    brand: undefined,
    remark: undefined,
    classificationId: undefined,
    materialNo: undefined,
    checkAddingQuantity: 0,
    availableStock: "",
    currentStock: 0,
    // supplierIdList: undefined,
    materialSupplierRelationParams: [
      {
        supplierId: undefined,
        price: 0,
        taxRate: 13,
      },
    ],
  }),
  showMaterialNo = ref(false),
  unitIdOptions = ref([]),
  classificationIdOptions = ref([]),
  supplierIdListOptions = ref([]),
  classificationIdTreeData = ref([]),
  defaultWarehouseOptions = ref([]),
  defaultWarehouseAreaOptions = ref([]),
  classificationIdReplaceFields = ref({
    children: "children",
    label: "name",
    value: "id",
  }),
  rules = ref({
    materialName: [
      {
        required: true,
        message: "请输入物料名称",
        trigger: "blur",
      },
    ],
    minimumStock: [
      {
        required: true,
        message: "请输入安全库存",
        trigger: "blur",
      },
    ],
    specification: [
      {
        required: true,
        message: "请输入物料规格",
        trigger: "blur",
      },
    ],
    defaultWarehouseId: [
      {
        required: true,
        message: "请选择默认所属仓库",
        trigger: "blur",
      },
    ],
    defaultWarehouseAreaId: [
      {
        message: "请选择默认所属仓库区域",
        trigger: "blur",
      },
    ],
    unitId: [
      {
        required: true,
        message: "请选择物料单位",
        trigger: "change",
      },
    ],
    classificationId: [
      {
        required: true,
        message: "请选择物料分类",
        trigger: "change",
      },
    ],
    materialNo: [
      {
        required: true,
        message: "请输入物料编码",
        trigger: "blur",
      },
    ],
    supplierIdList: [
      {
        required: true,
        message: "请选择供应商",
        trigger: "change",
      },
    ],
    // materialImage: [
    //   {
    //     required: true,
    //     message: "物料图片",
    //     trigger: "change",
    //   },
    // ],
  });
const onClose = () => {
  deleteUploadSwitch.value = false;
  formData.value.supplierIdList = undefined;
  formRef.value.resetFields();
  formData.value.materialSupplierRelationParams = [
    {
      supplierId: undefined,
      price: 0,
      taxRate: 13,
    },
  ];
  formData.value.defaultWarehouseId = undefined;
  formData.value.defaultWarehouseAreaId = undefined;
  formData.value.currentStock = 0;
  formData.value.availableStock = 0;
  formData.value.minimumStock = 0;
  emit("update:visible", false);
};
const submitForm = () => {
  formRef.value
    .validate()
    .then(async () => {
      try {
        // 供应商必填暂时去掉
        // let { length } = formData.value.materialSupplierRelationParams;
        // if (
        //   length == 0 ||
        //   (length == 1 &&
        //     !formData.value.materialSupplierRelationParams[0].supplierId)
        // ) {
        //   throw new Error("物料至少要归属一家供应商");
        // }
        // const uniqueUsersSet = new Set(
        //   formData.value.materialSupplierRelationParams.map(
        //     (item) => item.supplierId
        //   )
        // );
        // if (
        //   formData.value.materialSupplierRelationParams.length >
        //   uniqueUsersSet.size
        // ) {
        //   throw new Error("供应商不能重复选择");
        // }

        submitLoading.value = true;

        let param = {
          ...formData.value,
          materialImage: formData.value.materialImage
            ? formData.value.materialImage[0]
            : {},
        };
        if (
          formData.value.file &&
          formData.value.file.length &&
          formData.value.file.length > 0
        ) {
          for (let i = 0; i < formData.value.file.length; i++) {
            param.file = formData.value.file[i];
          }
        } else {
          if (formData.value.file && formData.value.file.fileVisitUrl) {
            param.file = formData.value.file;
          } else {
            param.file = {};
          }
        }

        if (props.id) {
          param.id = props.id;
        }
        if (props.id) {
          let res = await update(param);
          if (res.code == 200) {
            proxy.$message.success("修改成功");
            onClose();
          }
        } else {
          let res = await add(param);
          if (res.code == 200) {
            proxy.$message.success("添加成功");
            onClose();
          }
        }

        emit("finish");
        formData.value.defaultWarehouseId = undefined;
        formData.value.defaultWarehouseAreaId = undefined;
        formData.value.currentStock = 0;
        formData.value.availableStock = 0;
      } catch (error) {
        error.message && proxy.$message.error(error.message);
      }

      submitLoading.value = false;
    })
    .catch((error) => {
      submitLoading.value = false;
    });
};
const getUnitList = async () => {
  spinning.value = true;
  let result = await getAllUnit();
  unitIdOptions.value = result.data;
  spinning.value = false;
};
const getClassificationIdTreeData = async () => {
  spinning.value = true;
  let result = await getAllCategory("material");
  classificationIdTreeData.value = result.data?.map((item) => {
    return { ...item, selectable: item?.children == 0 };
  });
  spinning.value = false;
};
const getSupplierList = async () => {
  spinning.value = true;
  let result = await getAllSupplier();
  supplierIdListOptions.value = result.data;
  spinning.value = false;
};
const handleCategoryChange = (value, label, extra) => {
  let { allCheckedNodes } = extra;
  if (allCheckedNodes.length > 0) {
    let { autogenerationMaterialCodeFlag } = allCheckedNodes[0].node.props;
    showMaterialNo.value = !autogenerationMaterialCodeFlag;
  }
};
const onSelect = (selectedKeys, node) => {
  bbc.value =
    (node.parentCode ? node.parentCode : "") + (node.code ? node.code : "");
};
const submitCancel = async () => {
  cancelLoading.value = true;
  try {
    let res = await cancelMaterial(props.id);
    if (res.code == 200) {
      proxy.$message.success("操作完成");
      onClose();
    }
    emit("finish");
    cancelLoading.value = false;
  } catch (error) {
    cancelLoading.value = false;
  }
};

const getList = async () => {
  let param = {
    warehouseType: "RAWMATERIAL",
  };
  pageParam.value.pageSize = 999999;
  let result = await page(pageParam.value, param);
  defaultWarehouseOptions.value = result.data;
};
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
const onDefaultWarehouse = async (e) => {
  let res = await getInfoRepository(e);
  defaultWarehouseAreaOptions.value = res.data.warehouseMenuDetailVoList;
};

const isStop = ref(1);
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      getUnitList();
      getClassificationIdTreeData();
      getSupplierList();
      getList();
      if (props.id) {
        spinning.value = true;
        let result = await getInfo(props.id);
        status.value = result.data.status;
        isStop.value = result.data.isStop;
        if (result.data.defaultWarehouseId) {
          onDefaultWarehouse(result.data.defaultWarehouseId);
        }
        for (const key in formData.value) {
          formData.value[key] = result.data[key];
          formData.value.defaultWarehouseId = result.data.defaultWarehouseId
            ? Number(result.data.defaultWarehouseId)
            : undefined;
          if (
            result.data.materialImage &&
            result.data.materialImage.fileVisitUrl
          ) {
            formData.value.materialImage = [result.data?.materialImage];
          } else {
            formData.value.materialImage = [];
          }

          if (!result.data?.file || !result.data?.file.fileVisitUrl) {
            formData.value.file = [];
            deleteUploadSwitch.value = true;
          } else {
            formData.value.file = result.data?.file;
          }
        }

        formData.value.supplierIdList =
          result.data.materialSupplierRelationList?.map(
            (item) => item.supplierId
          );
        formData.value.materialSupplierRelationParams =
          result.data.materialSupplierRelationList?.map((item) => {
            return {
              supplierId: Number(item.supplierId),
              price: item.price,
              taxRate: item.taxRate,
            };
          });
        formData.brand = result.data.brand;
        formData.value.remark = result.data.remark;
        spinning.value = false;
      }
    }
  }
);
const canEdit = computed(() => {
  return props.id && ![2, 7, 9, 10].includes(status.value);
});
const editStat = computed(() => {
  return props.id && ![2, 7].includes(status.value);
});
const addSuppiler = () => {
  if (!formData.value.materialSupplierRelationParams) {
    formData.value.materialSupplierRelationParams = [];
  }
  formData.value.materialSupplierRelationParams.push({
    supplierId: undefined,
    price: 0,
    taxRate: 13,
  });
};
const deleteSupplier = (index) => {
  if (formData.value.materialSupplierRelationParams.length > 1) {
    formData.value.materialSupplierRelationParams.splice(index, 1);
  } else {
    proxy.$message.error("至少保留一家供应商");
  }
};

const deleteUpload = () => {
  formData.value.file = [];
  formData.materialImage = [];
  deleteUploadSwitch.value = true;
};
</script>
<style lang="less" scoped>
.bg {
  height: 40px;
  line-height: 40px;
  background: rgba(34, 34, 34, 0.08);
  border-radius: 8px;
  text-align: center;
  margin-bottom: 24px;
}
.deleteIcon {
  font-size: 24px;
}
</style>
