<template>
  <div class="space-y-3 p-4 bg-background rounded-lg">
    <a-row v-for="(item, index) in areas" :key="index">
      <a-col :span="24" style="display: flex">
        <a-input
          :disabled="btnType == 'detail'"
          v-model:value="item.warehouseArea"
          :maxlength="20"
          placeholder="请输入仓储区域名称"
          @change="update"
          class="rig intp"
        />
        <a-input-number
          v-model:value="item.sort"
          placeholder="排序"
          allow-clear
          @change="sortChange($event, item)"
          :stringMode="true"
        ></a-input-number>
        <mw-button
          :disabled="btnType == 'detail'"
          v-if="areas.length > 1"
          @click.stop="generateDetale(item)"
          class="ml-2"
          danger
        >
          删除
        </mw-button>
        <mw-button
          v-if="!item.qrcodeAll && item.id"
          @click.stop="
            generateQRCode(item.id, item.value, item.e, item.warehouseArea)
          "
          class="ml-2"
          >生成二维码
        </mw-button>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="24" class="text-center" v-if="btnType !== 'detail'">
        <mw-button @click="add"> 新增 </mw-button>
      </a-col>
    </a-row>

    <a-modal v-model:visible="visible" title="" @ok="handleOk">
      <a-row>
        <!-- <a-col  :span="24" class="top center"> 
          <a-radio-group v-model:value="item.value" @change="handleOptionChange($event,item.id,item.id,item.e,item.value)" :options="plainOptions">
          </a-radio-group> 
        </a-col> -->
        <a-col :span="24" class="top center">
          <div v-if="qrCodeDataimg" class="erwm" id="qrCodeDataimg">
            <div
              ref="printContent"
              id="print-content"
              class="w-2/4 inline-flex justify-center flex-col"
            >
              <div class="qrcodetitle" id="qrcodetitle">
                <p>仓库名称：{{ warehouseName }}</p>
                <!-- <p>仓库地址：{{ warehouseAddress }}</p> -->
                <p>库位名称：{{ warehouseAreadata }}</p>
                <!-- {{item.warehouseName}}  <span v-if="item.warehouseName&&item.warehouseArea">/</span> -->
              </div>
              <img id="qrCodeData" :src="qrCodeDataimg" alt="QR Code" />
            </div>
          </div>
        </a-col>
        <a-col :span="24" class="top center" style="">
          <mw-button @click.stop="printDocument(qrCodeDataimg)"
            >打印二维码
          </mw-button>
          <mw-button @click.stop="downloadImage()" class="ml-2"
            >保存二维码</mw-button
          >
        </a-col>
      </a-row>
    </a-modal>
  </div>
</template>
<script setup>
import printJS from "print-js";
import qrcode from "qrcode";
import { warehouse } from "@/api/basicData/repository.js";
import { defineProps, reactive, ref, watch, defineEmits, onMounted } from "vue";
import _cloneDeep from "lodash/cloneDeep";
import { message } from "ant-design-vue";
import { useRoute, useRouter } from "vue-router";
import html2canvas from "html2canvas";
import { getToken } from "@/utils/auth.js";
import { useUserStore } from "@/stores/user.js";

const token = getToken();

const props = defineProps({
  value: {
    type: Array,
    default: () => [],
  },
  warehouseName: String,
  warehouseAddress: String,
  btnType: String,
});
const warehouseName = ref("");
const warehouseAddress = ref("");
const qrCodeDataimg = ref("");

watch(
  () => props,
  (val) => {
    warehouseName.value = val.warehouseName;
    warehouseAddress.value = val.warehouseAddress;
  },
  { deep: true, immediate: true }
);

const router = useRouter();
const visible = ref(false);
const plainOptions = [
  { label: "无密二维码", value: "1" },
  { label: "加密二维码", value: "2" },
];
const sortChange = (e, val) => {
  val.sort = e;
  console.log(e, val, "valval");
};
const emit = defineEmits(["update:value"]);
//生成一个uuid
function guid() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
const add = () => {
  areas.value.push({
    warehouseArea: "",
    qrcodeAll: true,
    uuid: guid(),
    sort: void 0,
  });
  update();
};
const areas = ref([]);
const update = () => {
  console.log(areas.value);
  emit("update:value", areas.value);
};
// 获取其他数据
watch(
  () => props.value,
  (val) => {
    areas.value = val;
  },
  { deep: true, immediate: true }
);
// 删除区域
const generateDetale = (data, itemValue, iteme, warehouseArea) => {
  areas.value.forEach((item, index, arr) => {
    if (data?.id) {
      if (data.id == item.id) {
        arr.splice(index, 1);
      }
    } else {
      if (data.uuid == item.uuid) {
        arr.splice(index, 1);
      }
    }
  });
  update();
};
const warehouseAreadata = ref("");
// 生成二维码
const codeItemid = ref();
const userStore = useUserStore();
const generateQRCode = async (itemId, itemValue, iteme, warehouseArea) => {
  warehouseAreadata.value = warehouseArea;
  codeItemid.value = itemId;
  const formData = {
    referId: codeItemid.value,
    type: "area",
    tenantId: userStore.user.tenantId,
    userId: userStore.user.userId,
  };

  let warehouseData = [];
  console.log("userStore", userStore.user.tenantId);
  warehouseData = await warehouse(formData);
  // http://localhost:3005/basicData/repository

  console.log(
    "http://localhost:3005" +
      "/newWarehouse-area?tenantId=" +
      userStore.user.tenantId +
      "&itemId=" +
      codeItemid.value +
      "&userId=" +
      userStore.user.userId +
      "&token=" +
      token
  );
  // const url =
  //   // 二维码链接地址，可以根据需要自行修改
  //   import.meta.env.VITE_DOMAIN +
  //   "/warehouse-area?iteme=" +
  //   iteme +
  //   "&itemId=" +
  //   codeItemid.value +
  //   "&token=" +
  //   token;
  const url =
    // 二维码链接地址，可以根据需要自行修改
    import.meta.env.VITE_DOMAIN + "/warehouse-area?itemId=" + codeItemid.value;
  console.log(url, "url");
  // 二维码链接地址，可以根据需要自行修改
  const options = {
    width: 200, // 二维码图片宽度
    height: 200, // 二维码图片高度
    margin: 0,
  };
  qrcode.toDataURL(url, options, (error, dataURL) => {
    if (error) {
    } else {
      qrCodeDataimg.value = dataURL;
      console.log("[ dataURL ] >", dataURL);
      // areas.value.forEach((item,index) => {
      //   if(itemId===item.id){
      //   item.qrCodeData = dataURL;
      //   update();
      //   }
      // });
    }
  });
  router.push(warehouseData);
  visible.value = true;
};
// 关闭二维码
const handleOk = (e) => {
  visible.value = false;
};

const downloadImage = () => {
  // 获取要保存的 DOM 元素
  const elementToSave = document.querySelector("#qrCodeDataimg");

  // 使用 html2canvas 将 DOM 元素转换为图像
  html2canvas(elementToSave).then((canvas) => {
    // 将 canvas 转换为 base64 编码的图像数据字符串
    const imageData = canvas.toDataURL("image/png");
    // 创建下载链接
    const downloadLink = document.createElement("a");
    downloadLink.href = imageData;
    downloadLink.download = "image.png";
    // 通过模拟点击下载链接，触发图像保存到本地相册
    downloadLink.click();
  });
};

// 打印
const printDocument = (itemqrCodeData) => {
  // if(!itemvalue){
  //   message.error('请先选择是否加密');
  //   return
  // }else if(!itemqrCodeData){
  //   message.error('请先选择生成二维码');
  //   return
  // }
  if (!itemqrCodeData) {
    message.error("请先选择生成二维码");
    return;
  }
  var originalBodyStyles = window.getComputedStyle(document.body).cssText;
  var originalPageStyles = document.getElementById("qrCodeData").style.cssText;
  document.getElementById("qrCodeDataimg").style.width = "100%";
  document.getElementById("qrcodetitle").style.width = "100%";
  // document.getElementById("qrcodetitle").style.color = "red";
  // document.getElementById("qrcodetitle").style.textAlign = "left";
  document.getElementById("qrcodetitle").style.fontSize = "16px";
  document.getElementById("qrcodetitle").style.marginLeft = "1%";
  printJS({
    printable: document.getElementById("qrCodeDataimg"),
    type: "html",
    style: `
    @media print {
      .print-area {
        page-break-inside: avoid;
        page-break-after: always;  /* 强制分页，保证最多只有1页 */
      }

    }
    @page {
      size: A4; /* 设置页面尺寸为A4 */
      margin: 0; /* 设置页边距为0 */
      page-break-after: always;  /* 强制分页，保证最多只有1页 */
    }
    body {
      position: absolute;
      top: 0%;
      left: 0%;
      transform: translate(-10%, 0%);
      color: #333; /* 设置文字颜色 */
      zoom:310%
    }

  `,
    scanstyle: false,
    // css: '/path/to/style.css',
    background: true,
    orientation: "portrait",
    documentTitle: "我的打印文件",
    targetStyles: ["*"], //添加样式
  });
  // 恢复原始样式
  document.body.style.cssText = originalBodyStyles;
  document.getElementById("qrCodeDataimg").style.cssText = originalPageStyles;
  document.getElementById("qrcodetitle").style.cssText = originalPageStyles;
};

// const handleOptionChange = async (e, referId, itemId, itemValue, iteme) => {
//   const formData = {
//     referId: codeItemid.value,
//     type: "area",
//   };
//   let warehouseData = [];
//   e.target.value = "";
//   if (e.target.value == 1) {
//     // 全局前置守卫
//     router.beforeEach((to, from, next) => {
//       // 这里可以根据条件判断是否需要进行token拦截
//       if (
//         to.path ===
//         import.meta.env.VITE_DOMAIN +
//           "/warehouse-area?iteme=" +
//           iteme +
//           "&itemId=" +
//           codeItemid.value
//       ) {
//         // 如果是指定的页面，不进行token拦截
//         next();
//       }
//     });
//     warehouseData = await noWarehouse(formData);
//   } else {
//     warehouseData = await warehouse(formData);
//   }

//   const url =
//     import.meta.env.VITE_DOMAIN +
//     "/warehouse-area?iteme=" +
//     iteme +
//     "&itemId=" +
//     codeItemid.value;
//   // 二维码链接地址，可以根据需要自行修改
//   const options = {
//     width: 200, // 二维码图片宽度
//     height: 200, // 二维码图片高度
//   };
//   qrcode.toDataURL(url, options, (error, dataURL) => {
//     if (error) {
//     } else {
//       areas.value.forEach((item, index) => {
//         if (itemId === item.id) {
//           item.qrCodeData = dataURL;
//           update();
//         }
//       });
//     }
//   });

//   router.push(warehouseData);
//   // localStorage.setItem('warehouseData',JSON.stringify(warehouseData.data) )
// };
// handleOptionChange();
</script>

<style lang="less" scoped>
.qrcodetitle {
  text-align: left;
  font-size: 16px;
}

/* 这里是你要应用的打印样式，可以根据实际需求进行修改 */

.top {
  padding-top: 10px;
}
.rig {
  margin-right: 10px;
}
.paddinglr {
  padding: 0 3px;
}
.center {
  display: flex;
  justify-content: center;
}
.intp {
  width: 55%;
}
.erwm {
  background: #fafafa;
  width: 472px;
  height: 337px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.erwm img {
  width: 100%;
  margin: auto;
}
.ant-modal-mask {
}
</style>
