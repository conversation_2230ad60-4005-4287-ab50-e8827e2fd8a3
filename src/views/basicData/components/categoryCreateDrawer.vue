<template>
  <mw-drawer
    :custom-title="title"
    :visible="visible"
    @close="onClose"
    :spinning="spinning"
    destroyOnClose="true"
  >
    <template v-slot:header>
      <a-popconfirm
        title="确认取消？"
        @confirm="submitCancel"
        v-if="id && status != 1"
        placement="bottomRight"
      >
        <mw-button :loading="cancelLoading" v-if="type != 'product'"
          >取消分类</mw-button
        >
      </a-popconfirm>
      <mw-button @click="submitForm" :loading="submitLoading">确定</mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
    >
      <a-form-item label="一级分类" name="parentId" v-if="hasParent">
        <a-select
          v-model:value="formData.parentId"
          placeholder="请选择一级分类"
          allow-clear
          :disabled="id != ''"
          :options="parentIdOptions"
          optionFilterProp="name"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          show-search
          showArrow
        />
      </a-form-item>
      <a-form-item label="分类名称" name="name">
        <a-input
          v-model:value="formData.name"
          placeholder="请输入分类名称"
          :maxLength="12"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item
        label="分类编码"
        name="code"
        v-if="props.type == 'material' && !hasParent"
        required
      >
        <a-input
          v-model:value="formData.code"
          placeholder="请输入分类编码"
          allow-clear
          :disabled="id != ''"
          @input="inputChange"
          :maxlength="2"
        ></a-input>
      </a-form-item>
      <a-form-item
        label="分类编码"
        name="codeHasParent"
        v-if="route.query.type == 'product' || hasParent"
        required
      >
        <a-input
          v-model:value="formData.codeHasParent"
          placeholder="请输入分类编码"
          allow-clear
          :disabled="id != ''"
          @input="inputChange"
          :maxlength="3"
        ></a-input>
      </a-form-item>
      <!-- 功能不删,暂时隐藏 -->
      <a-form-item
        label="物料编码生成规则"
        name="autogenerationMaterialCodeFlag"
        help="新增物料时，该分类下物料编码的生成方式"
        v-if="hasParent"
      >
        <a-radio-group v-model:value="formData.autogenerationMaterialCodeFlag">
          <a-radio :value="1">自动生成</a-radio>
          <a-radio :value="0">手动输入</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  computed,
  defineEmits,
  reactive,
} from "vue";
import {
  add,
  getInfo,
  update,
  getByParentId,
  deleteCategroy,
} from "@/api/basicData/category.js";
import { detail } from "../../../api/purchase/requistion";
import { useRoute, useRouter } from "vue-router";
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const hasParentTrue = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  hasParent: {
    type: Boolean,
    default: false,
  },
  id: String,
  type: String,
});
const emit = defineEmits(["update:visible", "finish"]);
const title = computed(() => {
  let action = props.id ? "修改" : "新增";
  let obj = props.hasParent ? "二级分类" : "一级分类";
  return action + obj;
});
const formRef = ref(),
  submitLoading = ref(false),
  cancelLoading = ref(false),
  spinning = ref(false),
  status = ref(),
  formData = reactive({
    parentId: undefined,
    name: undefined,
    code: undefined,
    autogenerationMaterialCodeFlag: 1,
  }),
  parentIdOptions = ref([]),
  rules = ref({
    parentId: [
      {
        required: true,
        message: "请选择一级分类",
        trigger: "change",
      },
    ],
    name: [
      {
        required: true,
        message: "请输入分类名称",
        trigger: "blur",
      },
    ],
    codeHasParent: [
      {
        min: 3,
        required: true,
        message: "分类编码必须是3位",
        trigger: "blur",
      },
    ],
    code: [
      {
        min: 2,
        required: true,
        message: "分类编码必须是2位",
        trigger: "blur",
      },
    ],
    autogenerationMaterialCodeFlag: [
      {
        required: true,
        message: "物料编码生成规则不能为空",
        trigger: "change",
      },
    ],
  });
const onClose = () => {
  formRef.value.resetFields();
  formData.parentId = undefined;
  emit("update:visible", false);
  formData.code = undefined;
  formData.codeHasParent = undefined;
};
const submitForm = () => {
  formRef.value
    .validate()
    .then(async () => {
      submitLoading.value = true;
      if (props.id) {
        let res = await update({ ...formData, id: props.id });
        if (res.code == 200) {
          proxy.$message.success("修改成功");
          onClose();
        }
      } else {
        let parentId = formData.parentId || 0;
        let res = await add({
          code: formData.codeHasParent,
          ...formData,
          parentId,
          type: props.type,
        });
        // delete formData.codeHasParent;
        if (res.code == 200) {
          proxy.$message.success("添加成功");
          onClose();
        }
      }
      submitLoading.value = false;
      emit("finish");
    })
    .catch((error) => {
      submitLoading.value = false;
    });
};
const submitCancel = async () => {
  cancelLoading.value = true;
  try {
    let res = await deleteCategroy(props.id);
    if (res.code == 200) {
      proxy.$message.success("操作完成");
      onClose();
    }
    emit("finish");

    cancelLoading.value = false;
  } catch (error) {
    cancelLoading.value = false;
  }
};

const inputChange = (e) => {
  let value = e.target.value;
  value = value.replace(/[\W]/g, "");
  formData.code = value;
};

watch(
  () => props.visible,
  async (val) => {
    if (val) {
      if (props.hasParent) {
        hasParentTrue.value = props.hasParent;
        spinning.value = true;
        let result = await getByParentId(0, props.type);
        parentIdOptions.value = result.data;
        formData.parentId = undefined;
        formData.autogenerationMaterialCodeFlag = 1;

        spinning.value = false;
      } else {
        formData.parentId = undefined;
        formData.autogenerationMaterialCodeFlag = undefined;
      }
      if (props.id) {
        spinning.value = true;
        let result = await getInfo(props.id);
        for (const key in formData) {
          formData[key] = result.data[key];
          status.value = result.data.status;
          formData.codeHasParent = result.data.code;
        }
        spinning.value = false;
      }
    }
  }
);
</script>
