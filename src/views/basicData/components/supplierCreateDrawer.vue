<template>
  <mw-drawer
    :custom-title="id ? '修改供应商' : '新增供应商'"
    :visible="visible"
    @close="onClose"
    closeText="取消"
    :spinning="spinning"
  >
    <template v-slot:header>
      <mw-button @click="submitForm" :loading="submitLoading">确定</mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 4 }"
      layout="horizontal"
      :colon="false"
    >
      <a-form-item label="供应商" name="name">
        <a-input
          v-model:value="formData.name"
          placeholder="请输入供应商名称"
          :maxLength="20"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item label="状态" name="status">
        <a-select
          v-model:value="formData.status"
          placeholder="状态选择"
          allow-clear
          :style="{ width: '100%' }"
        >
          <a-select-option
            v-for="(item, index) in supplierStatus"
            :key="index"
            :value="item.value"
            :disabled="item.disabled"
            >{{ item.label }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="等级" name="level">
        <a-select
          v-model:value="formData.level"
          placeholder="等级选择"
          allow-clear
          :style="{ width: '100%' }"
        >
          <a-select-option
            v-for="(item, index) in supplierLevels"
            :key="index"
            :value="item.value"
            :disabled="item.disabled"
            >{{ item.label }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="联系人" name="relationName">
        <a-input
          v-model:value="formData.relationName"
          placeholder="请输入联系人"
          :maxLength="8"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item label="联系方式" name="relationPhone">
        <a-input
          v-model:value="formData.relationPhone"
          placeholder="请输入联系方式"
          :maxLength="32"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item label="岗位职务" name="relationPost">
        <a-input
          v-model:value="formData.relationPost"
          placeholder="请输入岗位职务"
          :maxLength="8"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item label="联系地址" name="relationAddress">
        <a-textarea
          v-model:value="formData.relationAddress"
          placeholder="请输入联系地址"
          :auto-size="{ minRows: 2, maxRows: 2 }"
          :style="{ width: '100%' }"
          allow-clear
          :maxLength="20"
        />
      </a-form-item>
      <a-form-item label="供应商品" name="sellGoodsType">
        <a-input
          v-model:value="formData.sellGoodsType"
          placeholder="请输入供应商品"
          :maxLength="20"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item label="商品规格" name="sellGoodsSpecifiy">
        <a-input
          v-model:value="formData.sellGoodsSpecifiy"
          placeholder="请输入商品规格"
          :maxLength="50"
          allow-clear
        >
        </a-input>
      </a-form-item>
      <div class="bg">以下是供应商收款信息（非必填）</div>
      <a-form-item label="企业名称" name="payee">
        <a-input
          v-model:value="formData.payee"
          placeholder="请输入收款企业名称"
          :maxLength="20"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item label="纳税识别号" name="taxpayerIdentityNumber">
        <a-input
          v-model:value="formData.taxpayerIdentityNumber"
          placeholder="请输入纳税识别号"
          :maxLength="20"
          allow-clear
        >
        </a-input>
      </a-form-item>
      <a-form-item label="开户行" name="openingBank">
        <a-input
          v-model:value="formData.openingBank"
          placeholder="请输入开户行"
          :maxLength="20"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item label="银行账号" name="bankAccount">
        <a-input
          v-model:value="formData.bankAccount"
          placeholder="请输入银行账号"
          :maxLength="20"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item label="联系手机" name="payeeRelationPhone">
        <a-input
          v-model:value="formData.payeeRelationPhone"
          placeholder="请输入联系手机"
          :maxLength="32"
          allow-clear
        >
        </a-input>
      </a-form-item>
      <a-form-item label="联系地址" name="payeeRelationAddress">
        <a-textarea
          v-model:value="formData.payeeRelationAddress"
          placeholder="请输入联系地址"
          :auto-size="{ minRows: 2, maxRows: 2 }"
          :style="{ width: '100%' }"
          allow-clear
          :maxLength="20"
        />
      </a-form-item>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import { defineProps, defineEmits, ref, getCurrentInstance, watch } from "vue";
import { supplierStatus, supplierLevels } from "@/common/constant.js";
import { checkPhone } from "@/common/validate";
import { add, getInfo, update } from "@/api/basicData/supplier.js";
const { proxy } = getCurrentInstance();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
});
const emit = defineEmits(["update:visible", "finish"]);

const formRef = ref(),
  submitLoading = ref(false),
  spinning = ref(false),
  formData = ref({
    name: undefined,
    status: undefined,
    level: undefined,
    relationName: undefined,
    relationPhone: undefined,
    relationPost: undefined,
    relationAddress: undefined,
    sellGoodsType: undefined,
    sellGoodsSpecifiy: undefined,
    payee: undefined,
    taxpayerIdentityNumber: undefined,
    openingBank: undefined,
    bankAccount: undefined,
    payeeRelationPhone: undefined,
    payeeRelationAddress: undefined,
  }),
  rules = ref({
    name: [
      {
        required: true,
        message: "请输入供应商名称",
        trigger: "blur",
      },
    ],
    status: [
      {
        required: true,
        message: "状态选择",
        trigger: "change",
      },
    ],
    level: [
      {
        required: true,
        message: "等级选择",
        trigger: "change",
      },
    ],
    relationName: [
      {
        required: true,
        message: "请输入联系人",
        trigger: "blur",
      },
    ],
    relationPhone: [
      {
        required: true,
        message: "请输入联系方式",
        trigger: "blur",
      },
      {
        validator: checkPhone,
        trigger: "blur",
      },
    ],
    relationPost: [
      {
        required: true,
        message: "请输入岗位职务",
        trigger: "blur",
      },
    ],
    relationAddress: [
      {
        required: true,
        message: "请输入联系地址",
        trigger: "blur",
      },
    ],
    sellGoodsType: [
      {
        required: true,
        message: "请输入供应商品",
        trigger: "blur",
      },
    ],
    sellGoodsSpecifiy: [
      {
        required: true,
        message: "请输入商品规格",
        trigger: "blur",
      },
    ],
    payee: [],
    taxpayerIdentityNumber: [],
    openingBank: [],
    bankAccount: [],
    payeeRelationPhone: [
      {
        validator: checkPhone,
        trigger: "blur",
      },
    ],
    payeeRelationAddress: [],
  });
const onClose = () => {
  formRef.value.resetFields();
  emit("update:visible", false);
};
const submitForm = () => {
  formRef.value
    .validate()
    .then(async () => {
      submitLoading.value = true;
      let {
        sellGoodsType,
        sellGoodsSpecifiy,
        payee,
        taxpayerIdentityNumber,
        openingBank,
        bankAccount,
        payeeRelationPhone,
        payeeRelationAddress,
      } = formData.value;
      //配合后端整理参数格式
      let param = {
        ...formData.value,
        insertParam: {
          sellGoodsType,
          sellGoodsSpecifiy,
          payee,
          taxpayerIdentityNumber,
          openingBank,
          bankAccount,
          payeeRelationPhone,
          payeeRelationAddress,
        },
      };
      if (props.id) {
        let res = await update({ ...param, id: props.id });
        if (res.code == 200) {
          proxy.$message.success("修改成功");
          onClose();
        }
      } else {
        let res = await add(param);
        if (res.code == 200) {
          proxy.$message.success("添加成功");
          onClose();
        }
      }
      submitLoading.value = false;
      emit("finish");
    })
    .catch((error) => {
      submitLoading.value = false;
    });
};
watch(
  () => props.visible,
  async (val) => {
    if (val && props.id) {
      spinning.value = true;
      let result = await getInfo(props.id);
      for (const key in formData.value) {
        formData.value[key] = result.data[key];
      }
      spinning.value = false;
    }
  }
);
</script>
<style lang="less" scoped>
.bg {
  height: 40px;
  line-height: 40px;
  background: rgba(34, 34, 34, 0.08);
  border-radius: 8px;
  text-align: center;
  margin-bottom: 24px;
}
</style>
