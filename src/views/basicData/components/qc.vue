<template>
  <div class="qc-box">
    <a-col :span="24" class="top center">
      <div v-if="qrCodeDataimg" class="erwm" id="qrCodeDataimg">
        <div ref="printContent" id="print-content">
          <div class="qrcodetitle" id="qrcodetitle">
            <p>物料编号：{{ recordData?.materialNo || "" }}</p>
            <p>物料名称：{{ recordData?.materialName || "" }}</p>
            <p>物料规格：{{ recordData?.specification || "" }}</p>
            <p>所属仓库：{{ recordData?.defaultWarehouseName || "" }}</p>
            <p>仓库区域：{{ recordData?.defaultWarehouseAreaName || "" }}</p>
          </div>
          <div class="img-box">
            <img id="qrCodeData" :src="qrCodeDataimg" alt="QR Code" />
          </div>
        </div>
      </div>
    </a-col>
    <a-col :span="24" class="top center" style="">
      <mw-button
        shape="round"
        @click.stop="printDocument(qrCodeDataimg)"
        class="rig"
        >打印二维码</mw-button
      >
      <mw-button shape="round" @click.stop="downloadImage()"
        >保存二维码</mw-button
      >
    </a-col>
  </div>
</template>

<script setup>
import printJS from "print-js";
import html2canvas from "html2canvas";
import qrcode from "qrcode";
import { onMounted, ref } from "vue";

const props = defineProps({
  recordData: {
    type: Object,
    default: () => {},
  },
});

const options = {
  width: 80,
  height: 70,
};
const qrCodeDataimg = ref(void 0);
const url = import.meta.env.VITE_DOMAIN + "/empty";
const createdQr = () => {
  qrcode.toDataURL(url, options, (error, dataURL) => {
    if (error) {
    } else {
      qrCodeDataimg.value = dataURL;
    }
  });
};

const downloadImage = () => {
  // 获取要保存的 DOM 元素
  const elementToSave = document.querySelector("#qrCodeDataimg");

  // 使用 html2canvas 将 DOM 元素转换为图像
  html2canvas(elementToSave).then((canvas) => {
    // 将 canvas 转换为 base64 编码的图像数据字符串
    const imageData = canvas.toDataURL("image/png");
    // 创建下载链接
    const downloadLink = document.createElement("a");
    downloadLink.href = imageData;
    downloadLink.download = "image.png";
    // 通过模拟点击下载链接，触发图像保存到本地相册
    downloadLink.click();
  });
};

// 打印
const printDocument = (itemqrCodeData) => {
  var originalBodyStyles = window.getComputedStyle(document.body).cssText;
  var originalPageStyles = document.getElementById("qrCodeData").style.cssText;
  document.getElementById("qrCodeDataimg").style.width = "100%";
  document.getElementById("qrcodetitle").style.width = "100%";
  document.getElementById("qrcodetitle").style.fontSize = "12px";
  document.getElementById("qrcodetitle").style.marginLeft = "33%";
  printJS({
    printable: document.getElementById("qrCodeDataimg"),
    type: "html",
    style: `
    @media print {
      .print-area {
        page-break-inside: avoid;
        page-break-after: always;  /* 强制分页，保证最多只有1页 */
      }

    }
    @page {
      size: A4; /* 设置页面尺寸为A4 */
      margin: 0; /* 设置页边距为0 */
      page-break-after: always;  /* 强制分页，保证最多只有1页 */
    }
    body {
      position: absolute;
      top: 25%;
      left: 0%;
      transform: translate(-22%, 0);
      color: #333; /* 设置文字颜色 */
      zoom:310%
    }

  `,
    scanstyle: false,
    // css: '/path/to/style.css',
    background: true,
    orientation: "portrait",
    documentTitle: "我的打印文件",
    targetStyles: ["*"], //添加样式
  });
  // 恢复原始样式
  document.body.style.cssText = originalBodyStyles;
  document.getElementById("qrCodeDataimg").style.cssText = originalPageStyles;
  document.getElementById("qrcodetitle").style.cssText = originalPageStyles;
};

onMounted(() => {
  createdQr();
});
</script>

<style lang="less" scoped>
.qc-box {
  width: 100%;
  .qc {
    width: 60px;
    height: 50px;
    margin: auto;
  }

  .bt-box {
    text-align: center;
  }
}

#print-content {
  display: flex;
  flex-direction: column;
}
.qrcodetitle {
  text-align: left;
  // margin: auto;
}

.top {
  padding-top: 10px;
}

.center {
  display: flex;
  justify-content: center;
  text-align: center;
}

.erwm {
  background: #fafafa;
  width: 75%;
  // padding: 23% 0;
}

.rig {
  margin-right: 10px;
}

.img-box {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}

.erwm img {
  width: 150px;
  height: 150px;
  // margin: auto;
  margin-top: 20px;
}
</style>
