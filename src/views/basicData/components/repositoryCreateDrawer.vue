<template>
	<mw-drawer :custom-title="btnType == 'detail' ? '详情' : id ? '修改仓库' : '新增仓库'" :visible="visible" @close="onClose" closeText="取消" :spinning="spinning">
		<template v-slot:header v-if="btnType != 'detail'">
			<mw-button @click="submitForm" :loading="submitLoading">确定</mw-button>
		</template>
		<a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 5, offset: -1 }" layout="horizontal" :colon="false">
			<a-form-item label="选择类型" name="type">
				<a-select v-model:value="formData.type" placeholder="请选择类型" allow-clear :disabled="id != '' || btnType == 'detail'" :options="typeOptions" />
			</a-form-item>
			<a-form-item label="仓库名称" name="warehouseName">
				<a-input :disabled="btnType == 'detail'" v-model:value="formData.warehouseName" placeholder="请输入仓库名称" :maxLength="20" allow-clear></a-input>
			</a-form-item>
			<a-form-item label="仓库地址" name="warehouseAddress">
				<a-input :disabled="btnType == 'detail'" v-model:value="formData.warehouseAddress" placeholder="请输入仓库地址" allow-clear></a-input>
			</a-form-item>
			<a-form-item label="排序" name="sort">
				<a-input-number class="w-full" :disabled="btnType == 'detail'" v-model:value="formData.sort" placeholder="请输入排序" :stringMode="true" allow-clear></a-input-number>
			</a-form-item>
			<a-form-item label="发货仓" name="isSendWarehouse">
				<a-select
					v-model:value="formData.isSendWarehouse"
					placeholder=""
					allow-clear
					:disabled="btnType == 'detail'"
					:options="[
						{ label: '是', value: 1 },
						{ label: '否', value: 0 },
					]" />
			</a-form-item>
			<div>
				<div class="text-primary-text mb-3">仓库区域划分</div>
				<repository-area v-model:value="formData.warehouseMenuDetailVoList" :warehouseName="formData.warehouseName" :warehouseAddress="formData.warehouseAddress" :btnType="btnType" />
			</div>
		</a-form>
	</mw-drawer>
</template>
<script setup>
import { defineProps, defineEmits, ref, getCurrentInstance, watch, reactive } from "vue";
import { getDicByType } from "@/utils/util.js";
import { add, getInfo, update } from "@/api/basicData/repository.js";
import repositoryArea from "./repositoryArea.vue";
const { proxy } = getCurrentInstance();

const props = defineProps({
	visible: {
		type: Boolean,
		default: false,
	},
	id: String,
	btnType: String,
});
const emit = defineEmits(["update:visible", "finish"]);

const formRef = ref(),
	submitLoading = ref(false),
	spinning = ref(false),
	typeOptions = ref([]),
	formData = reactive({
		type: undefined,
		warehouseName: undefined,
		warehouseAddress: undefined,
		warehouseMenuDetailVoList: [],
		sort: void 0,
		isSendWarehouse: 0,
	}),
	rules = ref({
		type: [
			{
				required: true,
				message: "请选择类型",
				trigger: "change",
			},
		],
		warehouseName: [
			{
				required: true,
				message: "请输入仓库名称",
				trigger: "blur",
			},
		],
		warehouseAddress: [
			{
				required: true,
				message: "请输入仓库地址",
				trigger: "blur",
			},
		],
	});
const onClose = () => {
	formData.warehouseMenuDetailVoList = [];
	formData.isSendWarehouse = 0;
	formRef.value.resetFields();
	emit("update:visible", false);
};
const submitForm = () => {
	formRef.value
		.validate()
		.then(async () => {
			console.log(formData, "formData");

			formData.warehouseMenuDetailVoList.forEach((item) => {
				if (!item.warehouseArea) {
					throw new Error("仓库区域不能为空");
				}
			});
			console.log(formData.warehouseMenuDetailVoList, "formData.warehouseMenuDetailVoList");
			submitLoading.value = true;

			if (props.id) {
				let res = await update({ ...formData, id: props.id });
				if (res.code == 200) {
					proxy.$message.success("修改成功");
					onClose();
				}
			} else {
				console.log(formData, "formData");
				let res = await add(formData);
				if (res.code == 200) {
					proxy.$message.success("添加成功");
					onClose();
				}
			}
			submitLoading.value = false;
			emit("finish");
		})
		.catch((error) => {
			submitLoading.value = false;
			error.message && proxy.$message.warning(error.message);
		});
};
watch(
	() => props.visible,
	async (val) => {
		if (val) {
			let { dics } = await getDicByType("warehouse_type", "仓库");
			typeOptions.value = dics;
			if (props.id) {
				spinning.value = true;
				let result = await getInfo(props.id);
				for (const key in formData) {
					formData[key] = result.data[key];
				}
				formData.isSendWarehouse = result.data.isSendWarehouse || 0;
				spinning.value = false;
			}
			if (formData.warehouseMenuDetailVoList?.length == 0) {
				formData.warehouseMenuDetailVoList = [{ warehouseArea: "", uuid: "1561-16516-1516-445" }];
			}
		}
	}
);
</script>
<style lang="less" scoped>
.bg {
	height: 40px;
	line-height: 40px;
	background: rgba(34, 34, 34, 0.08);
	border-radius: 8px;
	text-align: center;
	margin-bottom: 24px;
}
</style>
