<template>
  <div v-if="store.$state.cacheObject">
    <search :searchData="searchData" @search="refresh">
      <mw-button
        @click="exportOrg"
        :loading="exportLoading"
        :disabled="!data?.length"
        v-permission="'production:line:export'"
        >导出Excel</mw-button
      >
      <mw-button
        v-permission="'production:line:add'"
        :font="'iconfont icon-xianxing-121'"
        @click="addLine"
        >新增</mw-button
      >
    </search>

    <mw-table
      :scroll="{ x: 'max-content' }"
      class="leading-5.5"
      :columns="columns"
      hasPage
      :data-source="data"
      :rowKey="(record) => record.id"
      :loading="tableLoading"
      :pageConfig="paginationProps"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key == 'lineInfo'">
          <div
            class="overflow"
            style="margin-bottom: 1px"
            :title="record.lineName"
          >
            {{ record.lineName }}
          </div>
          <div class="text-secondar-text" :title="record.lineNo">
            编码：{{ record.lineNo }}
          </div>
          <div class="text-secondar-text overflow" :title="record.region">
            城市：{{ record.region }}
          </div>
        </template>
        <template v-if="column.key == 'status'">
          <div style="height: 67px">
            <dictionary
              :statusOptions="productionLineStatusFilter"
              :value="record.status"
              isBackgroundColor
            />
          </div>
        </template>
        <template v-if="column.key == 'introduce'">
          <div style="height: 67px">
            {{ record.introduce }}
          </div>
        </template>
        <template v-if="column.key == 'createByName'">
          <div style="height: 67px">
            {{ record.createByName }}
          </div>
        </template>
        <template v-if="column.key == 'createTime'">
          <div style="height: 67px">
            {{ record.createTime }}
          </div>
        </template>
        <template v-if="column.key == 'button'">
          <mw-button
            @click="rowClick(record)"
            v-permission="'production:line:update'"
            >编辑</mw-button
          >
        </template>
      </template>
    </mw-table>
  </div>
  <empty name="ProductionLine" v-else />
  <mw-drawer
    :visible="addLineVisible"
    @close="closeDrawer"
    :customTitle="lineNo ? '更新生产线' : '新增生产线'"
    :spinning="spinning"
  >
    <template #header>
      <a-popconfirm
        title="确认取消？删除后将无法恢复。"
        ok-text="是"
        cancel-text="否"
        @confirm="deleteLine"
        placement="bottomRight"
        v-if="lineNo && selectedProductLine.status == 0"
      >
        <mw-button :loading="removeLoading">取消生产线</mw-button>
      </a-popconfirm>
      <mw-button
        @click="onSubmit"
        :loading="loading"
        v-if="!selectedProductLine.status"
        >{{ lineNo ? "更新" : "提交" }}</mw-button
      >
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 5, offset: 0 }"
      :wrapper-col="{ span: 19, offset: 0 }"
      layout="horizontal"
      :colon="false"
      :hideRequiredMark="formDisabled"
    >
      <a-form-item label="生产线名称" name="lineName">
        <a-input
          v-model:value="formData.lineName"
          placeholder="请输入生产线名称"
          :style="{ width: '100%' }"
          :maxlength="20"
          allow-clear
          :disabled="formDisabled"
        >
        </a-input>
      </a-form-item>
      <a-form-item label="生产线编号" name="lineName" v-if="lineNo">
        <a-input
          v-model:value="lineNo"
          :style="{ width: '100%' }"
          :maxlength="20"
          disabled
        >
        </a-input>
      </a-form-item>
      <a-form-item label="所属城市" name="region">
        <a-input
          v-model:value="formData.region"
          placeholder="请输入所属城市"
          :style="{ width: '100%' }"
          :maxlength="8"
          allow-clear
          :disabled="formDisabled"
        ></a-input>
      </a-form-item>
      <a-form-item label="生产线简介" name="introduce">
        <a-textarea
          v-model:value="formData.introduce"
          placeholder="请输入生产线简介"
          :rows="3"
          :maxlength="50"
          :style="{ width: '100%', height: '80px' }"
          :disabled="formDisabled"
        >
        </a-textarea>
      </a-form-item>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
} from "vue";
import Search from "@/components/search/index.vue";
import { productionLineStatusFilter } from "@/common/constant.js";
import {
  getPage,
  addNew,
  update,
  remove,
  cancel,
  exportLine,
} from "@/api/basicData/productionLine.js";
import { usePagenation } from "@/common/setup";
import { useUserStore } from "@/stores/user.js";
import { uniqueArr, exportExecl } from "@/utils/util.js";

const columns = ref([
  {
    title: "生产线信息",
    key: "lineInfo",
  },
  {
    title: "状态",
    key: "status",
  },
  {
    title: "生产线介绍",
    key: "introduce",
  },
  {
    title: "创建者",
    key: "createByName",
    ellipsis: true,
  },
  {
    title: "创建时间",
    key: "createTime",
  },
  {
    title: "操作",
    key: "button",
    fixed: "right",
  },
]);
const data = ref([]);
const addLineVisible = ref(false);
const loading = ref(false);
const lineNo = ref(undefined);
const tableLoading = ref(false);
const { proxy } = getCurrentInstance();
const spinning = ref(false),
  exportLoading = ref(false);
const searchData = ref({
  searchButtons: [],
  operationButtons: [],
  fields: {
    status: {
      name: "生产状态",
      type: "a-select",
      options: productionLineStatusFilter,
      placeholder: "选择状态",
      width: "120px",
      value: "",
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: "输入生产线编号/ 名称快速搜索",
      width: "250px",
      allowClear: true,
    },
  },
});
const formRef = ref();
const removeLoading = ref(false),
  store = useUserStore(),
  selectedProductLine = ref({});
const formDisabled = computed(() => {
  let result = lineNo.value && selectedProductLine.value.status !== 0;
  return result;
});
const getList = async () => {
  tableLoading.value = true;
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  //单个搜索信息
  // searchParam[searchData.filterData.value.filterBy] =
  //   searchData.filterData.value.filterValue;
  let result = await getPage(searchParam, { ...pageParam.value });

  data.value = result.data;
  // state.corpId = result.data.data.records[0].corpId;
  paginationProps.value.total = result.total;
  tableLoading.value = false;
};
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

const rules = reactive({
  lineName: [
    {
      required: true,
      message: "请输入生产线名称",
      trigger: "blur",
    },
  ],
  region: [
    {
      required: true,
      message: "请输入所属城市",
      trigger: "blur",
    },
  ],
  introduce: [
    {
      required: true,
      message: "请输入生产线简介",
      trigger: "blur",
    },
  ],
});
const formData = reactive({
  lineName: "",
  region: "",
  introduce: "",
});

onBeforeMount(() => {
  getList();
});
function closeDrawer() {
  formRef.value.resetFields();
  selectedProductLine.value = {};
  lineNo.value = undefined;
  formData.lineName = "";
  formData.region = "";
  formData.introduce = "";
  addLineVisible.value = false;
}
function rowClick(record) {
  spinning.value = true;
  addLineVisible.value = true;
  lineNo.value = record.lineNo;
  formData.lineName = record.lineName;
  formData.region = record.region;
  formData.introduce = record.introduce;
  selectedProductLine.value = record;
  setTimeout(() => {
    spinning.value = false;
  }, 200);
}
function addLine() {
  addLineVisible.value = true;
  lineNo.value = undefined;
}
// 新增
async function onSubmit() {
  formRef.value
    .validate()
    .then(async () => {
      loading.value = true;
      if (lineNo.value) {
        let res = await update({ lineNo: lineNo.value, ...toRaw(formData) });
        if (res.code == 200) {
          proxy.$message.success("更新成功");
        }
      } else {
        let res = await addNew(toRaw(formData));
        if (res.code == 200) {
          proxy.$message.success("添加成功");
        }
      }
      loading.value = false;
      closeDrawer();
      getList();
    })
    .catch((error) => {
      loading.value = false;
    });
}
// 删除
async function deleteLine() {
  removeLoading.value = true;
  try {
    let res = await cancel({ lineNo: lineNo.value });
    if (res.code == 200) {
      proxy.$message.success("操作成功");
    }
    removeLoading.value = false;
    closeDrawer();
    getList();
  } catch (error) {
    removeLoading.value = false;
  }
}
const exportOrg = async () => {
  exportLoading.value = true;
  pageParam.value.pageSize = 100000;
  let searchParam = {
    // ...pageParam.value,
  };
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  //单个搜索信息
  if (searchData.value.filterData) {
    searchParam[searchData.value.filterData.value.filterBy] =
      searchData.value.filterData.value.filterValue;
  }
  let result = await exportLine(searchParam, pageParam.value);
  const fileName = "生产线.xlsx";
  exportExecl(fileName, result);
  exportLoading.value = false;
};
</script>

<style lang="scss" scoped></style>
