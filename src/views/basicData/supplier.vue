<template>
  <search :searchData="searchData" @search="refresh">
    <mw-button
      @click="exportOrg"
      :loading="exportLoading"
      :disabled="!data?.length"
      v-permission="'common:supplier:export'"
      >导出Excel</mw-button
    >
    <mw-button
      v-permission="'common:supplier:add'"
      @click="$router.push({ name: 'SupplierCreate' })"
      :font="'iconfont icon-xianxing-121'"
      >新增</mw-button
    >
  </search>
  <!-- :customRow="rowClick" -->
  <mw-table
    :scroll="{ x: 'max-content' }"
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'info'">
        <p class="overflow" :title="record.name">
          {{ record.name }}
        </p>
        <p class="text-secondar-text">编号：{{ record.number }}</p>
        <p class="text-secondar-text">
          联系人：{{ record.relationName }}「{{ record.relationPhone }}」
        </p>
      </template>
      <template v-if="column.key == 'status'">
        <div class="">
          <dictionary
            :statusOptions="supplierStatusFilters"
            :value="record.status"
            isBackgroundColor
          />
        </div>
      </template>
      <template v-if="column.key == 'level'">
        <div class="">
          <dictionary
            :statusOptions="supplierLevelsFilters"
            :value="record.level"
          />
        </div>
      </template>
      <template v-if="column.key == 'sellGoodsType'">
        <div class="">
          {{ record.sellGoodsType }}
        </div>
      </template>
      <template v-if="column.key == 'createTime'">
        <div class="">
          {{ record.createTime }}
        </div>
      </template>

      <!-- 操作 -->
      <template v-if="column.dataIndex == 'button'">
        <mw-button
          @click="rowClick(record)"
          v-permission="'common:supplier:edit'"
          class="mr-2"
          :disabled="record.tenantId != userStore.user.tenantId"
          >编辑</mw-button
        >

        <a-popconfirm
          title="确定是否删除!"
          ok-text="是"
          cancel-text="否"
          @confirm="onConfirm(record)"
          @cancel="onCancel(record)"
          v-permission="'common:supplier:edit'"
        >
          <mw-button
            v-permission="'common:supplier:delete'"
            :disabled="record.tenantId != userStore.user.tenantId"
            >删除</mw-button
          >
        </a-popconfirm>
      </template>
    </template>
  </mw-table>
  <supplier-create-drawer
    v-model:visible="addVisible"
    :id="id"
    @finish="getList"
  />
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
} from "vue";
import {
  page,
  exportSupplier,
  supplierDelete,
} from "@/api/basicData/supplier.js";
import { usePagenation } from "@/common/setup";
import Search from "@/components/search/index.vue";
import supplierCreateDrawer from "./components/supplierCreateDrawer.vue";
import {
  supplierStatusFilters,
  supplierLevelsFilters,
} from "@/common/constant.js";
import { uniqueArr, exportExecl } from "@/utils/util.js";
import { useRouter, useRoute } from "vue-router";
import { useUserStore } from "@/stores/user.js";
const userStore = useUserStore();
const router = useRouter();
const columns = ref([
  {
    title: "供应商信息",
    key: "info",
  },
  {
    title: "状态",
    key: "status",
    align: "left",
  },
  {
    title: "等级",
    key: "level",
    align: "left",
  },
  // {
  //   title: "供应商品",
  //   dataIndex: "sellGoodsType",
  //   key: "sellGoodsType",
  //   width: "300px",
  //   align: "left",
  //   width: "200px",
  // },
  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
  },
  {
    title: "操作",
    dataIndex: "button",
    key: "button",
  },
]);
const { proxy } = getCurrentInstance();
const supplierStatus = ref([
  {
    label: "全部状态",
    value: "",
  },
  {
    label: "正常合作",
    value: 0,
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  {
    label: "待审批",
    value: 7,
    color: "rgba(24, 144, 255, 1)",
    backGroundColor: "rgba(230, 247, 255, 1)",
  },
  {
    label: "审批拒绝",
    value: 9,
    color: "rgba(34, 34, 34, 1)",
    backGroundColor: "rgba(34, 34, 34, 0.04)",
  },
]);
const data = ref([]),
  addUnitDrawer = ref(false),
  loading = ref(false),
  addVisible = ref(false),
  id = ref(""),
  exportLoading = ref(false);
const searchData = ref({
  operationButtons: [],
  fields: {
    status: {
      name: "供应商状态",
      type: "a-select",
      options: supplierStatus,
      placeholder: "供应商状态",
      value: "",
      width: "120px",
      allowClear: true,
    },
    level: {
      name: "供应商等级",
      type: "a-select",
      options: supplierLevelsFilters,
      placeholder: "供应商等级",
      width: "120px",
      value: "",
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: "输入供应商编号/名称",
      width: "240px",
      allowClear: true,
    },
  },
});
const getList = async () => {
  loading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let result = await page(pageParam.value, searchParam);
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
const rowClick = (record) => {
  // return {
  //   onClick: (event) => {
  router.push({ name: "SupplierCreate", query: { id: record.id } });
  //   },
  // };
};
const showCreateDrawer = () => {
  id.value = "";
  addVisible.value = true;
};
const exportOrg = async () => {
  exportLoading.value = true;
  pageParam.value.pageSize = 100000;
  let searchParam = {
    // ...pageParam.value,
  };
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  //单个搜索信息
  if (searchData.value.filterData) {
    searchParam[searchData.value.filterData.value.filterBy] =
      searchData.value.filterData.value.filterValue;
  }
  let result = await exportSupplier(searchParam, pageParam.value);
  const fileName = "供应商.xlsx";
  exportExecl(fileName, result);
  exportLoading.value = false;
};
async function onConfirm(record) {
  let resultData = await supplierDelete({ id: record.id });
  getList();
}
const onCancel = (record) => {
  message.error("取消成功");
};
</script>
<style lang="less" scoped>
:deep(.ant-form-item) {
  margin-bottom: 16px;
}
:deep(.ant-table-cell) {
  vertical-align: top;
}
</style>
