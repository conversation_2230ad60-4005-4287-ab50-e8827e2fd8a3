<template>
  <a-spin :spinning="spinning">
    <a-space class="absolute right-0">
      <mw-button @click="$router.push({ name: 'Supplier' })">取消</mw-button>

      <template v-if="formData.status == 9 || formData.status == 10">
        <mw-button @click="submitForm('save')" :loading="submitLoading">保存</mw-button>
        <mw-button @click="submitForm('saveAndSubmit')" :loading="submitLoading">保存并提交</mw-button>
      </template>
      <mw-button v-else @click="submitForm('saveAndSubmit')" :loading="submitLoading">保存</mw-button>
    </a-space>

    <a-form ref="formRef" :model="formData" :rules="rules" layout="inline" :colon="false" labelAlign="left"
      :label-col="{ span: 7, offset: 1 }" style="display: block">
      <p class="text-base mb-4">基本信息</p>
      <a-row>
        <a-col :span="6">
          <a-form-item label="供应商" name="name" :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.name" placeholder="请输入供应商名称" :maxLength="20" allow-clear></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="联系人" name="relationName" :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.relationName" placeholder="请输入联系人" :maxLength="8" allow-clear></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="联系方式" name="relationPhone" :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.relationPhone" placeholder="请输入联系方式" :maxLength="32" allow-clear></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="岗位职务" name="relationPost" :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.relationPost" placeholder="请输入岗位职务" :maxLength="8" allow-clear></a-input>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row style="width: 100%">
        <a-col :span="6">
          <a-form-item label="联系地址" name="relationAddress" :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }">
            <a-textarea v-model:value="formData.relationAddress" placeholder="请输入联系地址"
              :auto-size="{ minRows: 2, maxRows: 2 }" style="width: 98%" allow-clear :maxLength="100" />
          </a-form-item>
        </a-col>
        <!-- <a-col :span="6">
          <a-form-item
            label="状态"
            name="status"
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
          >
            <a-select
              v-model:value="formData.status"
              placeholder="状态选择"
              allow-clear
            >
              <a-select-option
                v-for="(item, index) in supplierStatus"
                :key="index"
                :value="item.value"
                :disabled="item.disabled"
                >{{ item.label }}</a-select-option
              >
            </a-select>
          </a-form-item>
        </a-col> -->
        <a-col :span="6">
          <a-form-item label="等级" name="level" :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }">
            <a-select v-model:value="formData.level" placeholder="等级选择" allow-clear>
              <a-select-option v-for="(item, index) in supplierLevels" :key="index" :value="item.value"
                :disabled="item.disabled">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row style="width: 100%">
        <a-col :span="6">
          <div class="my-4">供应商资料附件</div>
          <!--  :fileTypes="['jpg', 'jpeg', 'png', 'webp', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf', 'zip', 'rar']" -->
          <form-upload-contract v-model:value="formData.file" sence="article" :fileTypes="[]" :multiple="true"
            :fileSize="100" hasDownLoad :fileLName="true" :fileLimit="9999999999" :detailType="'1'"
            @del="(index) => delFile(index, 'file')" :delShow="true">
          </form-upload-contract>
        </a-col>
      </a-row>
      <p class="text-base my-4 w-full">开票信息</p>
      <a-row style="width: 100%">
        <a-col :span="6">
          <!--  name="payee" -->
          <a-form-item label="企业名称" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.payee" placeholder="请输入收款企业名称" :maxLength="20" allow-clear></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <!-- name="taxpayerIdentityNumber" -->
          <a-form-item label="纳税识别号" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.taxpayerIdentityNumber" placeholder="请输入纳税识别号">
            </a-input>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <!--  name="openingBank" -->
          <a-form-item label="开户行" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.openingBank" placeholder="请输入开户行" :maxLength="20" allow-clear></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <!-- name="bankAccount"class="mt-4" -->
          <a-form-item label="银行账号" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.bankAccount" placeholder="请输入银行账号" :maxLength="50" allow-clear></a-input>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- <p class="w-full text-base my-4">
        预付款设置<span class="text-secondar-text text-sm"
          >(如无需预付货款，则无需设置)</span
        >
      </p>
      <a-row class="w-full">
        <a-form-item name="isPrepayment">
          <a-checkbox v-model:checked="formData.isPrepayment"
            >需预付货款</a-checkbox
          >
        </a-form-item>
        <a-form-item
          name="prepaymentRatio"
          label="预付比例"
          v-if="formData.isPrepayment"
        >
          <a-input-number
            v-model:value="formData.prepaymentRatio"
            addon-after="%"
            :controls="false"
            :max="100"
          ></a-input-number>
        </a-form-item>
      </a-row> -->
      <p class="w-full text-base my-4">供应物料</p>
      <mw-table style="width: 100%" :columns="columns" :data-source="materialSupplierRelationList"
        :rowKey="(record) => record.id">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex == 'price'">
            <a-input-number style="width: 130px" v-model:value="record.price" :formatter="formatter8"
              :stringMode="true" />
          </template>
          <template v-if="column.dataIndex == 'taxRate'">
            <a-select class="w-full" v-model:value="record.taxRate" :options="taxRateList" placeholder="请选择税率" />
            <!-- <a-input-number
              style="width: 130px"
              v-model:value="record.taxRate"
              :formatter="formatter8"
            /> -->
          </template>
          <template v-if="column.dataIndex == 'action'">
            <mw-button type="link" :loading="record.removeLoading" @click="removeMaterial(record, index)"><i
                class="iconfont icon-jichu-shanchu"></i></mw-button>
          </template>
        </template>
        <template #footer>
          <div class="text-center">
            <mw-button @click="materialVisible = true">选择物料</mw-button>
          </div>
        </template>
      </mw-table>
    </a-form>
  </a-spin>
  <material-choose-drawer v-model:visible="materialVisible" @choose="handleChooseMaterial" />
</template>
<script setup>
import {
  defineProps,
  defineEmits,
  ref,
  getCurrentInstance,
  watch,
  onBeforeMount,
} from "vue";
import { supplierStatus, supplierLevels } from "@/common/constant.js";
import { formatter8 } from "@/common/validate.js";
import { checkPhone } from "@/common/validate";
import {
  add,
  getInfo,
  update,
  zeroUpdate,
  removeMaterialSupplierRelationFlag,
} from "@/api/basicData/supplier.js";
import MaterialChooseDrawer from "@/components/MaterialChooseDrawer.vue";
import { useRouter, useRoute } from "vue-router";
import { taxRateList } from "@/common/constant.js";
import FormUploadContract from "@/components/form-upload-contract.vue";
const { proxy } = getCurrentInstance();

const route = useRoute(),
  router = useRouter();

const formRef = ref(),
  submitLoading = ref(false),
  spinning = ref(false),
  materialVisible = ref(false),
  materialSupplierRelationList = ref([]),
  formData = ref({
    name: undefined,
    status: 0,
    level: undefined,
    relationName: undefined,
    relationPhone: undefined,
    relationPost: undefined,
    relationAddress: undefined,
    payee: undefined,
    taxpayerIdentityNumber: undefined,
    openingBank: undefined,
    bankAccount: undefined,
    isPrepayment: false,
    prepaymentRatio: 0,
    file: undefined,
  }),
  rules = ref({
    name: [
      {
        required: true,
        message: "请输入供应商名称",
        trigger: "blur",
      },
    ],
    status: [
      {
        required: true,
        message: "状态选择",
        trigger: "change",
      },
    ],
    level: [
      {
        required: true,
        message: "等级选择",
        trigger: "change",
      },
    ],
    relationName: [
      {
        required: true,
        message: "请输入联系人",
        trigger: "blur",
      },
    ],
    relationPhone: [
      {
        required: true,
        message: "请输入联系方式",
        trigger: "blur",
      },
    ],
    relationPost: [
      {
        required: true,
        message: "请输入岗位职务",
        trigger: "blur",
      },
    ],
    relationAddress: [
      {
        required: true,
        message: "请输入联系地址",
        trigger: "blur",
      },
    ],
    payee: [
      {
        required: true,
        message: "请输入企业名称",
        trigger: "blur",
      },
    ],
    taxpayerIdentityNumber: [
      {
        required: true,
        message: "请输入纳税识别号",
        trigger: "blur",
      },
    ],
    openingBank: [
      {
        required: true,
        message: "请输入开户银行",
        trigger: "blur",
      },
    ],
    bankAccount: [
      {
        required: true,
        message: "请输入银行账号",
        trigger: "blur",
      },
    ],
    prepaymentRatio: [
      {
        required: true,
        message: "请输入预付比例",
        trigger: "blur",
      },
    ],
  }),
  columns = ref([
    {
      title: "物料名称",
      dataIndex: "materialName",
    },
    {
      title: "物料编码",
      dataIndex: "materialNo",
    },
    {
      title: "物料规格",
      dataIndex: "specification",
    },
    {
      title: "物料单位",
      dataIndex: "unitName",
    },
    {
      title: "物料分类",
      dataIndex: "classificationParentName",
    },
    {
      title: "含税单价",
      dataIndex: "price",
    },
    {
      title: "税率%",
      dataIndex: "taxRate",
    },
    {
      title: "",
      dataIndex: "action",
      width: "100px",
    },
  ]);

const submitForm = (type) => {
  formRef.value
    .validate()
    .then(async () => {
      try {
        materialSupplierRelationList.value.forEach((m) => {
          if (!m.price) {
            throw new Error("请填写物料的单价");
          }
        });
        submitLoading.value = true;
        let {
          payee,
          taxpayerIdentityNumber,
          openingBank,
          bankAccount,
          isPrepayment,
          prepaymentRatio,
        } = formData.value;
        //配合后端整理参数格式
        let param = {
          ...formData.value,
          insertParam: {
            payee,
            taxpayerIdentityNumber,
            openingBank,
            bankAccount,
            isPrepayment: isPrepayment ? 1 : 0,
            prepaymentRatio,
          },
          materialSupplierRelationParams:
            materialSupplierRelationList.value.map((m) => {
              return {
                materialId: m.id,
                price: m.price,
                taxRate: m.taxRate,
              };
            }),
        };
        if (route.query.id) {
          // 编辑
          if (type == 'save') {
            console.log('[ 只保存 ]',)
            let res = await zeroUpdate({ ...param, id: route.query.id });
            if (res.code == 200) {
              router.push({ name: "Supplier" });
            }
          } else if (type == 'saveAndSubmit') {
            console.log('[ 保存并提交 ] >',)
            let res = await update({ ...param, id: route.query.id });
            if (res.code == 200) {
              router.push({ name: "Supplier" });
            }
          }

        } else {
          // 新增
          let res = await add(param);
          if (res.code == 200) {
            router.push({ name: "Supplier" });
          }
        }
        submitLoading.value = false;
      } catch (error) {
        submitLoading.value = false;
        error.message && proxy.$message.warning(error.message);
      }
    })
    .catch((error) => {
      submitLoading.value = false;
    });
};
onBeforeMount(async () => {
  if (route.query.id) {
    spinning.value = true;
    let result = await getInfo(route.query.id);
    materialSupplierRelationList.value =
      result.data.materialSupplierRelationList;
    console.log(
      materialSupplierRelationList.value,
      "materialSupplierRelationList.valuev"
    );
    for (const key in formData.value) {
      if (result.data[key] !== undefined) {
        formData.value[key] = result.data[key];
      }
    }
    formData.isPrepayment = result.data ? true : false;
    spinning.value = false;
  }
});
// watch(
//   () => route.query.id,
//   async (val) => {

//   }
// );
const handleChooseMaterial = (material) => {
  material.taxRate = 13;
  let index = materialSupplierRelationList.value.findIndex(
    (m) => m.id == material.id
  );
  if (index < 0) {
    materialSupplierRelationList.value.push(material);
  }
};
const removeMaterial = async (record, index) => {
  if (route.query.id) {
    record.removeLoading = true;
    if (record.materialId) {
      let result = await removeMaterialSupplierRelationFlag(
        record.materialId,
        route.query.id
      );
      record.removeLoading = false;
      if (result.data == false) {
        proxy.$message.warning("该物料必须要归属一家供应商");
        return;
      }
    }
    materialSupplierRelationList.value.splice(index, 1);

    // debugger;
  }
};
</script>
<style lang="less" scoped>
.bg {
  height: 40px;
  line-height: 40px;
  background: rgba(34, 34, 34, 0.08);
  border-radius: 8px;
  text-align: center;
  margin-bottom: 24px;
}
</style>
