<template>
	<search :searchData="searchData" @search="refresh">
		<mw-button @click="exportOrg" :loading="exportLoading" :disabled="!data?.length" v-permission="'warehouse:menu:export'"> 导出Excel </mw-button>
		<mw-button v-permission="'warehouse:menu:add'" :font="'iconfont icon-xianxing-121'" @click="showCreateDrawer()">新增</mw-button>
	</search>

	<!--   :customRow="rowClick"  -->
	<mw-table :columns="columns" :data-source="data" :loading="loading" :rowKey="(record) => record.id" hasPage @change="onTableChange" :pageConfig="paginationProps">
		<template #bodyCell="{ column, record }">
			<template v-if="column.key == 'info'">
				<p class="overflow" :title="record.warehouseName">
					{{ record.warehouseName }}
				</p>
				<p class="text-secondar-text">地址：{{ record.warehouseAddress }}</p>
			</template>
			<template v-if="column.key == 'type'">
				<div class="" v-if="record.type">
					<dictionary :statusOptions="warehouseTypes" :value="record.type" isBackgroundColor />
				</div>
			</template>
			<template v-if="column.dataIndex == 'createBy'">
				<div class="">
					{{ record.createBy }}
				</div>
			</template>
			<template v-if="column.dataIndex == 'isSendWarehouse'">
				<div class="">
					{{ record.isSendWarehouse == 1 ? "是" : "" }}
				</div>
			</template>
			<template v-if="column.dataIndex == 'createTime'">
				<div class="">
					{{ record.createTime }}
				</div>
			</template>
			<!-- 操作 -->
			<template v-if="column.dataIndex == 'button'">
				<mw-button @click="rowClick(record, 'detail')" v-permission="'warehouse:menu:detail'" class="mr-2">详情</mw-button>
				<mw-button @click="rowClick(record)" v-permission="'warehouse:menu:edit'">编辑</mw-button>

				<!-- <a-popconfirm
          title="仓库下有区域，会同时删除区域，确定是否删除!"
          ok-text="是"
          cancel-text="否"
          @confirm="onConfirm(record)"
          @cancel="onCancel(record)"
        >
          <a-button
            class="ml-3"
            type="primary"
            danger
            v-permission="'warehouse:menu:remove'"
          >
            删除
          </a-button>
        </a-popconfirm> -->
			</template>
		</template>
	</mw-table>
	<repository-create-drawer v-model:visible="addVisible" :id="id" @finish="getList" :btnType="btnType" />
</template>
<script setup>
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance, computed } from "vue";
import { page, exportRepository, menuDelete } from "@/api/basicData/repository.js";
import { getDicByType } from "@/utils/util.js";
import { usePagenation } from "@/common/setup";
import Search from "@/components/search/index.vue";
import repositoryCreateDrawer from "./components/repositoryCreateDrawer.vue";
import { uniqueArr, exportExecl } from "@/utils/util.js";

const columns = ref([
	{
		title: "仓库信息",
		key: "info",
	},
	{
		title: "仓库类型",
		key: "type",
	},
	{
		title: "添加人员",
		dataIndex: "createBy",
	},
	{
		title: "发货仓",
		dataIndex: "isSendWarehouse",
	},
	{
		title: "排序",
		dataIndex: "sort",
	},
	{
		title: "创建时间",
		dataIndex: "createTime",
	},
	{
		title: "操作",
		dataIndex: "button",
	},
]);
const { proxy } = getCurrentInstance();
const data = ref([]),
	loading = ref(false),
	addVisible = ref(false),
	id = ref(""),
	warehouseTypes = ref([]),
	btnType = ref(),
	exportLoading = ref(false);
const searchData = ref({
	operationButtons: [],
	fields: {
		warehouseType: {
			name: "仓库类型",
			type: "a-select",
			options: [],
			placeholder: "仓库类型",
			value: "",
			width: "120px",
			allowClear: true,
		},
		keyword: {
			type: "a-input-search",
			placeholder: "输入仓库名称",
			width: "240px",
			allowClear: true,
		},
	},
});
const getList = async () => {
	loading.value = true;
	let searchParam = {};
	//搜索信息
	for (const key in searchData.value.fields) {
		searchParam[key] = searchData.value.fields[key].value;
	}
	let result = await page(pageParam.value, searchParam);
	data.value = result.data;
	paginationProps.value.total = result.total;
	loading.value = false;
};
const getWarehouseTypeOption = async () => {
	let { dics, dicFilters } = await getDicByType("warehouse_type", "仓库");
	warehouseTypes.value = dics;
	searchData.value.fields.warehouseType.options = dicFilters;
};
onBeforeMount(async () => {
	await getList();
	await getWarehouseTypeOption();
});
const { paginationProps, onTableChange, refresh, pageParam } = usePagenation(getList);
const rowClick = (record, typeVal) => {
	// return {
	//   onClick: (event) => {
	btnType.value = typeVal;
	id.value = record.id.toString();
	addVisible.value = true;
	//   },
	// };
};
const showCreateDrawer = () => {
	id.value = "";
	addVisible.value = true;
};
const exportOrg = async () => {
	exportLoading.value = true;
	pageParam.value.pageSize = 100000;
	let searchParam = {
		// ...pageParam.value,
	};
	//搜索信息
	for (const key in searchData.value.fields) {
		searchParam[key] = searchData.value.fields[key].value;
	}
	//单个搜索信息
	if (searchData.value.filterData) {
		searchParam[searchData.value.filterData.value.filterBy] = searchData.value.filterData.value.filterValue;
	}
	let result = await exportRepository(searchParam, pageParam.value);
	const fileName = "仓库.xlsx";
	exportExecl(fileName, result);
	exportLoading.value = false;
};
// async function onConfirm(record) {
//   let resultData = await menuDelete({ id: record.id });
//   getList();
// }
const onCancel = (record) => {
	message.error("取消成功");
};
</script>
<style lang="less" scoped>
:deep(.ant-table-cell) {
	vertical-align: top;
}
</style>
