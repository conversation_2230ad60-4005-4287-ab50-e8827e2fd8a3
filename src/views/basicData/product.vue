<template>
  <div v-if="store.$state.cacheObject">
    <search :searchData="searchData" @search="refresh">
      <a-button
        shape="round"
        type="primary"
        @click="exportOrg"
        :loading="exportLoading"
        :disabled="!data?.length"
        v-permission="'production:product:export'"
        >导出Excel</a-button
      >
      <a-button
        shape="round"
        type="primary"
        v-permission="'production:product:classification'"
        @click="
          $router.push({
            name: 'Category',
            query: { type: 'product' },
          })
        "
        >分类管理</a-button
      >
      <a-button
        shape="round"
        type="primary"
        v-permission="'production:product:add'"
        @click="addProduct"
        >新增产品</a-button
      >
    </search>
    <mw-table
      :scroll="{ x: 'max-content' }"
      class="leading-5.5"
      :columns="columns"
      hasPage
      :pageConfig="paginationProps"
      @change="onTableChange"
      :data-source="data"
      :rowKey="(record) => record.id"
      :loading="tableLoading"
      :customRow="
        (record) => {
          return {
            onClick: (event) => {},
          };
        }
      "
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'productInfo'">
          <div class="overflow" :title="record.productName">
            {{ record.productName }}
          </div>
          <div class="text-secondar-text" :title="record.productNo">
            编码：{{ record.productNo }}
          </div>
          <div
            class="text-secondar-text overflow"
            :title="record.specification"
          >
            规格：{{ record.specification }}
          </div>
        </template>
        <template v-if="column.dataIndex == 'category'">
          <div style="height: 66px">{{ record.classification }}</div>
        </template>
        <template v-if="column.dataIndex == 'unit'">
          <div style="height: 66px">
            {{ record.unit }}
          </div>
        </template>

        <template v-if="column.dataIndex == 'status'">
          <div style="height: 66px">
            <dictionary
              :statusOptions="productionLineStatusFilter"
              :value="record.status"
              isBackgroundColor
              :statusExtend="record.statusExtend"
            />
          </div>
        </template>
        <template v-if="column.dataIndex == 'relateBOM'">
          <div style="height: 66px">
            <div class="overflow">
              {{ record.bomName || "-" }}
            </div>
            <div class="text-secondar-text" v-if="record.bomNo">
              编码：{{ record.bomNo }}
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex == 'createTime'">
          <div style="height: 66px">
            {{ record.createTime }}
          </div>
        </template>
        <template v-if="column.dataIndex == 'use'">
          <div style="height: 66px">
            {{ record.use }}
          </div>
        </template>
        <template v-if="column.dataIndex == 'sellPrice'">
          <div style="height: 66px">
            {{ record.sellPrice }}
          </div>
        </template>
        <template v-if="column.dataIndex == 'fileNumber'">
          <div style="height: 66px">
            {{ record.fileNumber }}
          </div>
        </template>
        <template v-if="column.dataIndex == 'contractNumber'">
          <div style="height: 66px">
            {{ record.contractNumber }}
          </div>
        </template>
        <div>
          <template v-if="column.key == 'button'">
            <!-- v-permission="'production:product:detail'" -->
            <a-button
              shape="round"
              type="primary"
              @click="parentRowClick(record, 'detail')"
              v-permission="'production:product:detail'"
            >
              详情
            </a-button>
            <!-- v-if="record.status !== 7" -->
            <a-button
              shape="round"
              v-if="record.status != 7 && record.status != 1"
              class="ml-3"
              type="primary"
              @click="parentRowClick(record)"
              v-permission="'production:product:update'"
            >
              编辑
            </a-button>
            <a-popconfirm
              title="确定是否删除"
              ok-text="是"
              cancel-text="否"
              @confirm="onConfirm(record)"
              @cancel="onCancel(record)"
            >
              <a-button
                shape="round"
                class="ml-3"
                type="primary"
                danger
                v-permission="'production:product:delete'"
              >
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </div>
      </template>
    </mw-table>
  </div>
  <empty name="Product" v-else />

  <mw-drawer
    width="35%"
    :visible="addProductVisible"
    @close="closeDrawer"
    :customTitle="
      butType == 'detail' ? '详情' : productNo ? '更新产品' : '新增产品'
    "
    :spinning="spinning"
  >
    <template #header>
      <a-popconfirm
        title="确认取消？取消后将无法恢复。"
        ok-text="是"
        cancel-text="否"
        @confirm="cancelProduct"
        placement="bottomRight"
        v-if="productNo && canceledBtn && butType != 'detail'"
      >
        <a-button shape="round" type="primary" :loading="removeLoading"
          >取消产品
        </a-button>
      </a-popconfirm>
      <!-- v-if="!quotedBom && butType !== 'detail'"产品支持编辑，判断暂时去掉 -->
      <mw-button @click="onSubmit" :loading="loading">{{
        productNo ? "更新" : "提交"
      }}</mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      :hideRequiredMark="quotedBom"
      :label-col="{ span: 7 }"
    >
      <a-form-item label="关联BOM" name="bomNo" required>
        <div style="display: flex" v-if="!quotedBom && butType !== 'detail'">
          <a-select
            v-model:value="formData.bomNo"
            show-search
            placeholder="请输入BOM名称"
            :options="allBomList"
            optionFilterProp="label"
            @change="handleChangBom"
          ></a-select>
          <!-- 关联bom暂时注释 -->
          <!-- <a-button class="ml-4" type="primary" @click="gotoBom(formData.bomNo)"
            >新增BOM</a-button
          > -->
        </div>
        <div v-else>
          <mw-button type="link" @click="gotoBom(formData.bomNo)">
            {{ formData.bomNo }}</mw-button
          >
        </div>
      </a-form-item>
      <a-form-item label="产品规格" name="specification">
        <!-- :disabled="butType == 'detail' || quotedBom" -->
        <a-input
          v-model:value="formData.specification"
          placeholder="请输入产品规格"
          :style="{ width: '100%' }"
          allow-clear
          :disabled="productNo || butType == 'detail'"
        ></a-input>
      </a-form-item>
      <a-form-item label="产品名称" name="productName">
        <a-input
          v-model:value="formData.productName"
          placeholder="请输入产品名称"
          :style="{ width: '100%' }"
          allow-clear
          disabled
        >
        </a-input>
      </a-form-item>
      <a-form-item label="产品编码" v-if="productNo">
        <!-- :disabled="quotedBom" -->
        <a-input
          v-model:value="productNo"
          placeholder="产品编码"
          :style="{ width: '100%' }"
          :maxlength="20"
          allow-clear
          :disabled="productNo || butType == 'detail'"
        >
          <!-- :disabled="butType == 'detail'" -->
        </a-input>
      </a-form-item>

      <a-form-item label="产品分类" name="classificationId">
        <!--   :disabled="quotedBom" -->
        <a-select
          placeholder="请选择产品分类"
          :options="classifications"
          v-model:value="formData.classificationId"
          optionFilterProp="name"
          :field-names="{
            label: 'name',
            value: 'id',
          }"
          show-search
          :disabled="butType == 'detail'"
        >
        </a-select>
      </a-form-item>
      <a-form-item label="产品单位" name="unitId">
        <!--  :disabled="quotedBom" -->
        <a-select
          placeholder="请选择产品单位"
          :options="unitIdOptions"
          v-model:value="formData.unitId"
          optionFilterProp="name"
          :field-names="{
            label: 'name',
            value: 'id',
          }"
          show-search
          :disabled="butType == 'detail'"
        >
        </a-select>
      </a-form-item>
      <a-form-item label="默认所属仓库">
        <a-select
          :disabled="butType == 'detail'"
          v-model:value="formData.defaultWarehouseId"
          show-search
          placeholder="请选择默认所属仓库"
          style="width: 100%"
          :default-active-first-option="false"
          :not-found-content="null"
          :options="defaultWarehouseOptions"
          optionFilterProp="name"
          :fieldNames="{
            label: 'warehouseName',
            value: 'id',
          }"
          @change="onDefaultWarehouse"
        ></a-select>
      </a-form-item>
      <a-form-item label="默认所属仓库区域">
        <a-select
          :disabled="butType == 'detail'"
          v-model:value="formData.defaultWarehouseAreaId"
          show-search
          placeholder="请选择默认所属仓库区域"
          style="width: 100%"
          :default-active-first-option="false"
          :not-found-content="null"
          :options="defaultWarehouseAreaOptions"
          optionFilterProp="name"
          :fieldNames="{
            label: 'warehouseArea',
            value: 'id',
          }"
        ></a-select>
      </a-form-item>
      <a-form-item label="是否需要调试">
        <a-select
          :disabled="butType == 'detail'"
          v-model:value="formData.isNeedDebugging"
          show-search
          placeholder="请选择是否需要调试"
        >
          <a-select-option value="1">是</a-select-option>
          <a-select-option value="0">否</a-select-option></a-select
        >
      </a-form-item>
      <a-form-item label="产品用途" name="use">
        <!-- :disabled="quotedBom" -->
        <a-input
          :disabled="butType == 'detail'"
          v-model:value="formData.use"
          placeholder="请输入产品用途"
          :style="{ width: '100%' }"
          allow-clear
        >
        </a-input>
      </a-form-item>

      <a-form-item label="销售价格" name="sellPrice">
        <a-input-number
          :disabled="butType == 'detail'"
          v-model:value="formData.sellPrice"
          :formatter="formatter2"
          :stringMode="true"
        />
      </a-form-item>
      <a-form-item label="技术协议编号">
        <!--  :disabled="quotedBom" -->
        <a-input
          :disabled="butType == 'detail'"
          v-model:value="formData.fileNumber"
          placeholder="请输入技术协议编号"
          :style="{ width: '100%' }"
          allow-clear
        >
        </a-input>
      </a-form-item>
      <!-- <a-form-item label="订单编号">
        <a-input
          v-model:value="formData.orderNumber"
          placeholder="请输入订单编号"
          :style="{ width: '100%' }"
          :maxlength="20"
          allow-clear
        >
        </a-input>
      </a-form-item>
      <a-form-item label="合同编号">
        <a-input
          v-model:value="formData.contractNumber"
          placeholder="请输入合同编号"
          :style="{ width: '100%' }"
          :maxlength="20"
          allow-clear
        >
        </a-input>
      </a-form-item> -->
      <a-form-item label="产品协议">
        <!-- <form-upload
          v-model:value="formData.fileDetailModel"
          sence="article"
          :fileTypes="[]"
          :fileSize="100"
          :readonly="butType == 'detail'"
          hasDownLoad
          v-if="butType != 'detail'"
        ></form-upload> -->
        <form-upload-contract
          v-if="butType != 'detail'"
          v-model:value="formData.fileDetailModel"
          sence="article"
          :fileTypes="[]"
          :fileSize="100"
          :readonly="butType == 'detail'"
          hasDownLoad
          :fileLName="true"
          :fileLimit="9999"
          :detailType="'1'"
          @del="delFile"
          :delShow="true"
        >
        </form-upload-contract>
        <div
          v-else
          v-for="(item, index) in formData.fileDetailModel"
          :key="index"
          @click.stop="openUrl(item.fileVisitUrl)"
          :href="item.fileVisitUrl"
          :title="item.fileName"
          class="cursor-pointer inline-block"
          style="color: #959ec3"
        >
          <i
            v-if="item.fileName"
            class="iconfont icon-jichu-lianjie text-xs -middle mr-1"
            style="color: #959ec3"
          ></i
          ><span class="underline"> {{ item.fileName }}</span>
        </div>
      </a-form-item>

      <!-- <a-form-item label="关联BOM状态" name="status">
        <a-select
          placeholder="请选择产品单位"
          :options="bomStatusOptions"
          v-model:value="formData.status"
          optionFilterProp="name"
          :field-names="{
            label: status,
            value: value,
          }"
          show-search
          :disabled="productNo"
        >
        </a-select>
      </a-form-item> -->
      <a-form-item label="备注信息">
        <a-textarea
          :disabled="butType == 'detail'"
          class="flex-1"
          v-model:value="formData.remark"
          placeholder="请输入备注信息"
          :rows="3"
          :maxlength="200"
        />
      </a-form-item>
      <a-form-item label="备注附件">
        <!-- <a-textarea
            class="flex-1"
            v-model:value="formData.remark"
            placeholder="请输入备注信息"
            :rows="3"
            :maxlength="200"
          /> -->
        <!-- <form-upload
          v-model:value="formData.remarkFileList"
          sence="article"
          :fileSize="100"
          :readonly="quotedBom"
          hasDownLoad
          v-if="!quotedBom"
        ></form-upload> -->

        <!-- :detailType="butType" -->
        <!-- :readonly="quotedBom" -->
        <form-upload-contract
          v-model:value="formData.remarkFileList"
          sence="article"
          :fileTypes="[]"
          :fileSize="100"
          :readonly="butType == 'detail'"
          hasDownLoad
          :fileLName="true"
          :fileLimit="9999"
          :detailType="'1'"
          @del="delFile"
          :delShow="true"
        >
        </form-upload-contract>
        <!-- v-if="!quotedBom" -->
      </a-form-item>

      <mw-button
        v-if="butType != 'detail'"
        @click="quoteBOM"
        style="margin: auto; display: flex; margin-top: 9%"
      >
        引用产品</mw-button
      >
    </a-form>

    <!-- 引用产品 -->

    <mw-drawer
      :visible="quoteBOMVisible"
      @close="closeBomDrawer"
      customTitle="引用产品"
    >
      <template #header>
        <mw-button @click="quoteBOMOnSubmitOnSubmit">确定</mw-button>
      </template>
      <div>
        <div>
          <a-form-item>
            <template #label>
              <div class="w-15 text-left text-primary-text">产品名称</div>
            </template>
            <a-select
              v-model:value="selectedProdList.productName"
              show-search
              placeholder="请选择产品"
              style="width: 100%"
              :default-active-first-option="false"
              :not-found-content="null"
              :options="quoteProList"
              optionFilterProp="productName"
              :fieldNames="{
                label: 'productName',
                value: 'productNo',
              }"
              @search="handleSearchBom"
              @change="handleChangProd"
            ></a-select>
          </a-form-item>
          <a-form-item>
            <template #label>
              <div class="w-15 text-left text-primary-text">产品规格</div>
            </template>
            <div class="underline" v-if="selectedProdList.specification">
              {{ selectedProdList.specification }}
              <span></span>
            </div>
            <div v-else>{{ "-" }}</div>
          </a-form-item>
          <a-form-item>
            <template #label>
              <div class="w-15 text-left text-primary-text">产品分类</div>
            </template>
            <div class="underline" v-if="selectedProdList.classification">
              {{ selectedProdList.classification }}
              <span></span>
            </div>
            <div v-else>{{ "-" }}</div>
          </a-form-item>
          <a-form-item>
            <template #label>
              <div class="w-15 text-left text-primary-text">产品单位</div>
            </template>
            <div class="underline" v-if="selectedProdList.unit">
              {{ selectedProdList.unit }}
              <span></span>
            </div>
            <div v-else>
              {{ "-" }}
              <span></span>
            </div>
          </a-form-item>
          <a-form-item>
            <template #label>
              <div class="w-15 text-left text-primary-text">产品用途</div>
            </template>
            <div class="underline" v-if="selectedProdList.use">
              {{ selectedProdList.use }}
              <span></span>
            </div>
            <div v-else>{{ "-" }}</div>
          </a-form-item>
        </div>
        <!-- <div class="">
        <div class="px-4 bg-background rounded-lg divide-y divide-border">
          <div
            class="text-primary-text py-4"
            v-for="item in bomRelatedMaterialList"
            :key="item.id"
          >
            <div class="flex justify-between items-center leading-5.5 mb-1">
              <div class="flex-1 text-title overflow">
                {{ item.materialName }}
              </div>
            </div>
            <div class="flex justify-between items-center leading-5.5">
              <div class="space-y-1">
                <div class="">{{ item.specification }}</div>
              </div>
              <div class="">x {{ item.quantity }} {{ item.unit }}</div>
            </div>
          </div>
        </div>
      </div> -->
      </div>
    </mw-drawer>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
} from "vue";
import Search from "@/components/search/index.vue";
import { productionLineStatusFilter } from "@/common/constant.js";
import {
  getPage,
  addNew,
  update,
  remove,
  cancel,
  productDetail,
  exportProduct,
  productDelete,
  list,
} from "@/api/basicData/product.js";
import { getAllBomList, listByAvailableStatus } from "@/api/basicData/bom.js";
import { list as getAllUnit } from "@/api/basicData/unit.js";
import { list as getAllCategory } from "@/api/basicData/category.js";
import { usePagenation } from "@/common/setup";
import FormUpload from "@/components/form-upload.vue";
import FormUploadContract from "@/components/form-upload-contract.vue";
import { useUserStore } from "@/stores/user.js";
import { uniqueArr, exportExecl } from "@/utils/util.js";
import { useRoute, useRouter } from "vue-router";

import { formatter2 } from "@/common/validate.js";
import { message } from "ant-design-vue";
import {
  page as pageRepository,
  getInfo as getInfoRepository,
} from "@/api/basicData/repository.js";
const route = useRoute();
const router = useRouter();
const columns = ref([
  {
    title: "产品信息",
    dataIndex: "productInfo",
    key: "productInfo",
  },
  {
    title: "产品分类",
    dataIndex: "category",
    key: "category",
  },
  {
    title: "单位",
    dataIndex: "unit",
    key: "unit",
  },
  {
    title: "产品状态",
    dataIndex: "status",
    key: "status",
  },
  {
    title: "关联BOM信息",
    dataIndex: "relateBOM",
    key: "relateBOM",
  },

  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
  },
  {
    title: "产品用途",
    dataIndex: "use",
    key: "use",
  },
  {
    title: "销售价格",
    dataIndex: "sellPrice",
    key: "sellPrice",
  },
  {
    title: "技术协议编号",
    dataIndex: "fileNumber",
    key: "fileNumber",
  },
  // {
  //   title: "编号（最新）",
  //   dataIndex: "orderNumber",
  //   key: "orderNumber",
  // },
  {
    title: "合同编号最新",
    dataIndex: "contractNumber",
    key: "contractNumber",
  },
  {
    title: "操作",
    dataIndex: "button",
    fixed: "right",
    key: "button",
  },
]);

// const formatter = (value) => {
//   return `${value}`
//     .replace(/\B(?=(\d{3})+(?!\d))/g, "")
//     .replace(/^(-)*(\d+)\.(\d{3}).*$/, "$1$2.$3");
// };
const data = ref([]),
  addProductVisible = ref(false),
  loading = ref(false),
  productNo = ref(),
  tableLoading = ref(false),
  classifications = ref([]),
  unitIdOptions = ref([]),
  bomStatusOptions = ref([]),
  allBomList = ref([]),
  quotedBom = ref(false),
  quotedBomName = ref(),
  spinning = ref(false),
  canceledBtn = ref(false),
  store = useUserStore(),
  quoteBOMVisible = ref(false),
  quoteProList = ref([]),
  selectedProdList = ref({}),
  exportLoading = ref(false),
  defaultWarehouseOptions = ref([]),
  butType = ref(),
  defaultWarehouseAreaOptions = ref([]);

const { proxy } = getCurrentInstance();
const searchData = ref({
  searchButtons: [],
  operationButtons: [],
  fields: {
    classificationId: {
      name: "产品分类",
      type: "a-select",
      options: [],
      placeholder: "选择分类",
      width: "120px",
      fieldNames: {
        label: "name",
        value: "id",
      },
      value: "",
    },
    keyword: {
      type: "a-input-search",
      placeholder: "输入产品名称",
      width: "240px",
    },
  },
});

const formRef = ref();
const removeLoading = ref(false);
const getList = async () => {
  tableLoading.value = true;
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  if (store.$state.paramsBomNo) {
    searchData.value.fields.keyword.value = store.$state.paramsBomNo;
    searchParam.keyword = store.$state.paramsBomNo;
  }
  //单个搜索信息
  // searchParam[searchData.value.filterData.value.filterBy] =
  //   searchData.value.filterData.value.filterValue;
  let result = await getPage(searchParam, { ...pageParam.value });

  data.value = result.data;
  // state.corpId = result.data.data.records[0].corpId;
  paginationProps.value.total = result.total;
  store.setParamsBomNo();
  tableLoading.value = false;
};
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

const rules = reactive({
  productName: [
    {
      required: true,
      message: "请输入产品名称",
      trigger: "blur",
    },
  ],
  specification: [
    {
      required: true,
      message: "请输入产品规格",
      trigger: "blur",
    },
  ],
  classificationId: [
    {
      required: true,
      message: "请选择产品分类",
      trigger: "blur",
    },
  ],
  unitId: [
    {
      required: true,
      message: "请选择产品单位",
      trigger: "blur",
    },
  ],
  fileDetailModel: [
    {
      required: true,
      message: "请上传产品协议",
      trigger: "blur",
    },
  ],
  remarkFileList: [
    {
      required: true,
      message: "请上备注附件",
      trigger: "blur",
    },
  ],
});
const productNameTitle = computed(() => {
  let bomName = "";
  allBomList.value.forEach((item, index) => {
    if (item.value == formData.bomNo) {
      bomName = item.label;
    }
  });
  let item;
  if ((formData.bomNo && bomName) || formData.specification) {
    item = bomName + "\u00a0" + formData.specification;
  }
  return item;
});
const formData = reactive({
  productName: productNameTitle,
  specification: "",
  fileDetailModel: [],
  remarkFileList: [],
  classificationId: undefined,
  unitId: undefined,
  bomNo: undefined,
  remark: undefined,
  sellPrice: 0,
  fileNumber: "",
  use: "",
  status: undefined,
});
onBeforeMount(() => {
  getClassificationIdTreeData();
  getList();
  pageRepositoryList();
});
const getClassificationIdTreeData = async () => {
  let result = await getAllCategory("product");
  classifications.value = result.data?.map((item) => {
    return { ...item };
  });
  searchData.value.fields.classificationId.options = [
    {
      name: "全部分类",
      id: "",
    },
    ...classifications.value,
  ];
};
const pageRepositoryList = async () => {
  let param = {
    warehouseType: "FINISHED",
  };
  pageParam.value.pageSize = 999999;
  let result = await pageRepository(pageParam.value, param);
  defaultWarehouseOptions.value = result.data;
};
const onDefaultWarehouse = async (e) => {
  let res = await getInfoRepository(e);
  defaultWarehouseAreaOptions.value = res.data.warehouseMenuDetailVoList;
};
const getUnitList = async () => {
  let result = await getAllUnit();
  unitIdOptions.value = result.data;
};
function closeDrawer() {
  butType.value = undefined;
  formData.specification = "";
  formData.fileDetailModel = [];
  formData.remarkFileList = [];
  formData.classificationId = undefined;
  formData.unitId = undefined;
  formData.bomNo = undefined;
  formData.remark = undefined;
  formData.sellPrice = 0;
  formData.fileNumber = "";
  formData.use = "";
  formData.status = undefined;
  formData.isNeedDebugging = undefined;

  addProductVisible.value = false;
  quotedBom.value = false;
  quotedBomName.value = undefined;
  productNo.value = undefined;
  canceledBtn.value = false;

  formRef.value.resetFields();
}

async function rowClick(record, typeVal) {
  butType.value = typeVal;
  spinning.value = true;
  addProductVisible.value = true;
  await getClassificationIdTreeData();
  await getUnitList();
  await getAllBom("");
  let res = await productDetail({ productNo: record.productNo });
  spinning.value = false;
  if (res.data.defaultWarehouseId) {
    onDefaultWarehouse(res.data.defaultWarehouseId);
  }
  productNo.value = res.data.productNo;

  formData.specification = res.data.specification;
  formData.fileDetailModel = res.data.fileDetailModel || [];
  if (res.data.remarkFileList) {
    formData.remarkFileList = res.data.remarkFileList;
  }

  formData.bomNo = res.data.bomNo;
  formData.remark = res.data.remark;
  formData.sellPrice = res.data.sellPrice;
  formData.ileNumber = res.data.ileNumber;
  formData.orderNumber = res.data.orderNumber;
  formData.contractNumber = res.data.contractNumber;
  formData.fileNumber = res.data.fileNumber;
  formData.isNeedDebugging = res.data.isNeedDebugging;
  formData.status = res.data.status;
  formData.defaultWarehouseAreaId = res.data.defaultWarehouseAreaId;
  if (res.data?.defaultWarehouseId) {
    formData.defaultWarehouseId = Number(res.data?.defaultWarehouseId);
  }
  formData.use = res.data.use;

  // formData.productNo==res.data.productNo
  bomStatusOptions.value = [
    {
      label: res.data.status == 0 ? "未使用" : "使用中",
      value: res.data.status,
    },
  ];

  if (res.data.classificationId !== null) {
    formData.classificationId = String(res.data.classificationId);
  }

  formData.unitId = res.data.unitId;
  // if(typeVal == 'detail'){
  //   quotedBom.value = true
  // }else{
  //    quotedBom.value = !!res.data.bomNo || !![2, 7, 9, 10].includes(res.data.status);
  // }

  quotedBom.value =
    !!res.data.bomNo || !![2, 7, 9, 10].includes(res.data.status);
  quotedBomName.value = res.data.bomName;
  canceledBtn.value = record.status == 0;
}
function addProduct() {
  addProductVisible.value = true;
  getClassificationIdTreeData();
  getUnitList();
  getAllBom("");
}
// 新增
async function onSubmit() {
  formRef.value
    .validate()
    .then(async () => {
      loading.value = true;
      // return;
      if (productNo.value) {
        let res = await update({
          productNo: productNo.value,
          ...toRaw(formData),

          remarkFileList: formData.remarkFileList,
          productName: formData.productName,
        });
        if (res.code == 200) {
          proxy.$message.success("更新成功");
          closeDrawer();
        }
      } else {
        let res = await addNew({
          ...toRaw(formData),

          remarkFileList: formData.remarkFileList,
          productName: formData.productName,
        });
        if (res.code == 200) {
          proxy.$message.success("添加成功");
          getList();

          closeDrawer();
        }
      }
      canceledBtn.value = false;
      loading.value = false;

      formData.defaultWarehouseId = undefined;
      formData.defaultWarehouseAreaId = undefined;
    })
    .catch((error) => {
      loading.value = false;
    });
}

const parentRowClick = (record, typeVal) => {
  rowClick(record, typeVal);
};

// 取消产品的删除
async function cancelProduct() {
  removeLoading.value = true;
  let res = await cancel({ productNo: productNo.value });
  if (res.code == 200) {
    proxy.$message.success("操作成功");
  }
  removeLoading.value = false;
  closeDrawer();
  getList();
}

// 获取bom列表
async function getAllBom(keyword) {
  let result = await listByAvailableStatus({
    keyword: keyword,
  });
  let { data } = result;
  allBomList.value = data.map((item) => {
    return {
      label: item.bomName,
      value: item.bomNo,
      status: item.status == 0 ? "未使用" : "使用中",
    };
  });
  bomStatusOptions.value = data.map((item) => {
    return {
      label: item.status == 0 ? "未使用" : "使用中",
      value: item.status,
    };
  });
  // bomStatus
}
// 搜索BOM
let BomTimer;
const handleSearchBom = (value) => {
  if (value.length) {
    clearTimeout(BomTimer);
    BomTimer = setTimeout(() => {
      getAllBom(value);
    }, 500);
  } else {
    setTimeout(() => {
      allBomList.value = [];
    }, 500);
  }
};

const handleChangBom = async (value) => {};
// 预览
const openUrl = (url) => {
  window.open(url, "_blacnk");
};
const exportOrg = async () => {
  exportLoading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  //单个搜索信息
  if (searchData.value.filterData) {
    searchParam[searchData.value.filterData.value.filterBy] =
      searchData.value.filterData.value.filterValue;
  }
  let result = await exportProduct(searchParam, pageParam.value);
  const fileName = "产品.xlsx";
  exportExecl(fileName, result);
  exportLoading.value = false;
};
const gotoBom = (bomNo) => {
  store.setParamsBomNo(bomNo);
  router.push({
    name: "Bom",
    params: { bomNo: bomNo },
  });
};
async function onConfirm(record) {
  let resultData = await productDelete({ productNo: record.productNo });

  getList();
}
const onCancel = (record) => {
  // message.error('Click on No');
};
// 关闭引用bomt弹窗
const closeBomDrawer = () => {
  quoteBOMVisible.value = false;
  selectedProdList.value.productName = undefined;
  selectedProdList.value.specification = undefined;
  selectedProdList.value.classification = undefined;
  selectedProdList.value.unit = undefined;
  selectedProdList.value.use = undefined;
};
// 引用产品
const quoteBOM = () => {
  quoteBOMVisible.value = true;
  getAllList("");
};
// 获取bom列表
async function getAllList(keyword) {
  let result = await list({
    ignoreCancel: true,
  });
  let { data } = result;
  quoteProList.value = data;
}
const handleChangProd = async (value) => {
  selectedProdList.value = quoteProList.value.filter(
    (item) => item.productNo == value
  )[0];
  // 获取选中的bom信息
  // let res = await getBomMaterialList({ productNo: value });
  // bomRelatedMaterialList.value = res.data;
};
//确认引入bom
const quoteBOMOnSubmitOnSubmit = () => {
  formData.specification = selectedProdList.value.specification;
  formData.classification = selectedProdList.value.classification;

  formData.use = selectedProdList.value.use;

  formData.classificationId = selectedProdList.value.classificationId = String(
    selectedProdList.value.classificationId
  );

  formData.unit = selectedProdList.value.unit;
  formData.unitId = String(selectedProdList.value.unitId);

  // let name=selectedProdList.value.classification
  // let id=selectedProdList.value.classificationId
  // 这个是给分类绑定当前选中的值，只限制选中可以修改，需求更改为可以修改其他的
  // classifications.value = [
  //   {
  //     name: selectedProdList.value.classification,
  //     id: selectedProdList.value.classificationId,
  //   },
  // ];

  // name: "全部分类",
  //     id: "",
  // unitIdOptions.value = [
  //   {
  //     name: selectedProdList.value.unit,
  //     id: selectedProdList.value.unitId,
  //   },
  // ];
  // quoteBOMVisible.value = false;
  closeBomDrawer();
};

const delFile = (index) => {
  formData.remarkFileList.splice(index, 1);
};
</script>

<style lang="scss" scoped></style>
