<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="80%"
    :customTitle="computeCustomTitle(openType)"
  >
    <template #header>
      <mw-button
        title="确定"
        v-if="openType !== 'detail'"
        @click="formSubmit"
        :loading="submitLoading"
      ></mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      labelAlign="left"
      :label-col="{ span: 7, offset: 1 }"
    >
      <a-row>
        <a-col :span="12">
          <a-form-item
            label="开具发票"
            name="issueInvoiceType"
            :rules="[{ required: true, message: '开具发票!' }]"
            required
          >
            <!--   v-if="openType !== 'add'" -->
            <a-select
              :disabled="openType === 'detail' || openType === 'firstTime'"
              allowClear
              show-search
              placeholder="开具发票"
              :options="issueInvoiceTypeList"
              v-model:value="formData.issueInvoiceType"
              optionFilterProp="label"
            >
            </a-select>
            <!-- <div v-else>
              {{ formData.issueInvoiceType == 1 ? "蓝字发票" : "红字发票" }}
            </div> -->
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="关联-销售订单"
            name="marketOrderId"
            required
            v-if="openType !== 'firstTime'"
          >
            <a-select
              :disabled="openType === 'detail'"
              placeholder="请选择关联-销售订单"
              :options="postNoContractReviewListOptions"
              v-model:value="formData.marketOrderId"
              optionFilterProp="orderNames"
              @change="handleChange"
              :field-names="{
                label: 'orderNames',
                value: 'id',
              }"
              show-search
            >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="选择客户"
            name="customerNo"
            required
            v-if="openType === 'firstTime'"
          >
            <a-select
              :disabled="openType === 'detail'"
              placeholder="请选择客户"
              :options="customerPageList"
              v-model:value="formData.customerNo"
              optionFilterProp="label"
              @change="customerHandleChange"
              show-search
            >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            :label="billType == 2 ? '选择退货单' : '选择发货单'"
            name="shippingNumber"
            required
            v-if="openType === 'firstTime'"
          >
            <a-select
              :disabled="openType === 'detail'"
              :placeholder="billType == 2 ? '选择退货单' : '选择发货单'"
              :options="deliveryOrderList"
              v-model:value="formData.shippingNumber"
              optionFilterProp="shippingNumber"
              @change="handleChange"
              :field-names="{
                label: 'shippingNumber',
                value: 'shippingNumber',
              }"
              show-search
            >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="业务-归属人">
            <a-input v-model:value="formData.bizBelongUserName" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="订单金额">
            <a-input v-model:value="formData.orderAmount" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="客户代码">
            <a-input v-model:value="formData.customerNos" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <div class="-ml-6">
            <a-form-item label="开票产品明细" required>
              <mw-button
                title="新增"
                :font="'iconfont icon-xianxing-121'"
                style="margin-left: -22%"
                @click="handleAdd"
                v-if="openType !== 'detail'"
              ></mw-button>
            </a-form-item>
          </div>
          <mw-table
            class="mb-5"
            :data-source="formData.invoiceDetailParamList"
            :columns="
              openType == 'firstTime' || isBeginning == 1
                ? columnsTrendsFirstTime
                : columnsSaleInvoice
            "
            :scroll="{ x: 'max-content' }"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'shippingRecordNo'">
                <a-select
                  :disabled="openType === 'detail'"
                  show-search
                  style="width: 100%"
                  :placeholder="billType == 2 ? '选择退货单' : '选择发货单'"
                  :options="shippingNumberList"
                  v-model:value="record.shippingRecordNo"
                  @change="
                    (val, option) => shippingNumberChange(val, option, record)
                  "
                  optionFilterProp="shippingRecordNo"
                  :field-names="{
                    label: 'shippingRecordNo',
                    value: 'shippingRecordNo',
                  }"
                >
                </a-select>
              </template>

              <template v-if="column.dataIndex === 'materialName'">
                <div disabled v-if="openType === 'detail'">
                  {{
                    record.materialNo +
                    " / " +
                    record.materialName +
                    " / " +
                    record.specification
                  }}
                </div>

                <a-select
                  v-else
                  :disabled="openType === 'detail'"
                  show-search
                  style="width: 100%"
                  placeholder="请选择产品"
                  :options="
                    openType == 'firstTime'
                      ? productListData
                      : record.productListData
                  "
                  v-model:value="record.materialId"
                  @change="(val, option) => onProductList(val, option, record)"
                  optionFilterProp="name"
                  :field-names="{
                    label: 'name',
                    value: 'materialId',
                  }"
                >
                </a-select>
              </template>
              <template v-if="column.dataIndex === 'invQuantity'">
                {{
                  (record.invQuantity = subtractTwo(
                    Number(record.invoiceQuantityNum || 0),
                    Number(record.quantityShipped || 0)
                  ))
                }}
              </template>

              <template v-if="column.dataIndex === 'taxRate'">
                <a-select
                  v-if="props.openType !== 'detail'"
                  class="w-full"
                  :disabled="props.type == 'det' ? true : false"
                  v-model:value="record.taxRate"
                  :options="taxRateList"
                  placeholder="请选择税率"
                  @change="changeInvoiceQuantity($event, record)"
                />
                <!--  v-if="openType !== 'firstTime'"-->
                <!-- <a-input-number
                  v-model:value="record.taxRate"
                  placeholder="税率"
                  :disabled="openType === 'detail'"
                  @change="changeInvoiceQuantity($event, record)"
                  :stringMode="true"
                ></a-input-number>
                ></a-input-number> -->
                <!-- <a-input-number
                  v-if="openType == 'firstTime'"
                  v-model:value="record.taxRate"
                  placeholder="税率"
                  :disabled="openType === 'detail'"
                ></a-input-number> -->
              </template>

              <template v-if="column.dataIndex === 'classificationId'">
                {{ "暂无" }}
              </template>
              <!-- 新增的含税单价-->
              <template v-if="column.dataIndex === 'invoicePrice'">
                <a-input-number
                  v-model:value="record.invoicePrice"
                  :formatter="formatter8"
                  placeholder="含税单价"
                  :disabled="openType === 'detail'"
                  @change="changeInvoiceQuantity($event, record)"
                  :stringMode="true"
                ></a-input-number
              ></template>
              <!-- 含税金额 -->
              <template v-if="column.dataIndex === 'invoiceAmount'">
                <!-- 本次可以开票数量如果是期初的时候 -->
                <a-input-number
                  v-if="openType !== 'firstTime' && isBeginning !== 1"
                  v-model:value="record.invoiceAmount"
                  placeholder="含税金额"
                  :disabled="openType === 'detail'"
                  :formatter="formatter2"
                  :stringMode="true"
                ></a-input-number>

                <span
                  v-if="
                    record.invoiceQuantity &&
                    record.invoicePrice &&
                    openType == 'firstTime'
                  "
                >
                  {{
                    (record.invoiceAmount = roundNumFun(
                      Number(record.invoiceQuantity * record.invoicePrice),
                      2
                    ))
                  }}
                </span>
              </template>

              <!-- 已开票数量 -->
              <template v-if="column.dataIndex === 'invoicedQuantity'">
                {{ record.invoicedQuantity }} {{ record.unit }}
              </template>

              <template v-if="column.dataIndex === 'invoiceName'">
                <a-input
                  :disabled="openType === 'detail'"
                  v-model:value="record.invoiceName"
                  placeholder="名称"
                ></a-input>
              </template>
              <template v-if="column.dataIndex === 'invoiceSpecification'">
                <a-input
                  :disabled="openType === 'detail'"
                  v-model:value="record.invoiceSpecification"
                  placeholder="规格"
                ></a-input>
              </template>

              <template v-if="column.dataIndex === 'invoicableQuantity'"
                >{{ record.invoicableQuantity }}

                {{ record.unit }}</template
              >
              <!-- 期初的本次开票数量 -->
              <template v-if="column.dataIndex === 'invoiceQuantity'">
                <div class="flex items-center">
                  <a-input-number
                    :disabled="openType === 'detail'"
                    v-model:value="record.invoiceQuantity"
                    placeholder="数量"
                    allow-clear
                    @change="changeInvoiceQuantity($event, record)"
                    :stringMode="true"
                  />
                  <!-- <a-input-number
                    v-else
                    :disabled="openType === 'detail'"
                    v-model:value="record.invoiceQuantity"
                    placeholder="数量"
                    allow-clear
                    @change="changeInvoiceQuantity($event, record)"
                  /> -->

                  <span class="ml-1">{{ record.unit }}</span>
                </div>
              </template>
              <!-- 已开票数量 -->
              <template v-if="column.dataIndex === 'invoiceQuantityNum'">
                {{ record.invoiceQuantityNum }}
              </template>

              <!-- 未税金额 -->
              <template v-if="column.dataIndex === 'noTaxAmount'">
                <a-input-number
                  v-if="props.openType !== 'firstTime' && isBeginning !== 1"
                  :disabled="openType === 'detail'"
                  v-model:value="record.noTaxAmount"
                  placeholder="未税金额"
                  allow-clear
                  :formatter="formatter2"
                  :stringMode="true"
                />
                <div v-else>{{ record.noTaxAmount }}</div>
              </template>
              <!-- 操作 -->
              <template v-if="column.dataIndex === 'operation'">
                <a-popconfirm title="确定是否删除" @confirm="onDelete(index)">
                  <mw-button
                    title="删除"
                    danger
                    v-if="
                      formData.invoiceDetailParamList.length > 1 &&
                      openType !== 'detail'
                    "
                  ></mw-button>
                </a-popconfirm>
              </template>
            </template>
          </mw-table>
        </a-col>
        <a-col :span="12">
          <a-form-item label="开票类型" name="makeInvoiceType" required>
            <a-select
              :disabled="openType === 'detail'"
              placeholder="请选择开票类型"
              :options="billingTypeList"
              v-model:value="formData.makeInvoiceType"
              optionFilterProp="label"
              show-search
            >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="含税总金额" required name="invoiceAmount">
            <a-input v-model:value="formData.invoiceAmount" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="备注">
            <a-input
              :disabled="openType === 'detail'"
              v-model:value="formData.remark"
              placeholder="备注"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="未税总金额" required name="noTaxAmount">
            <a-input v-model:value="formData.noTaxAmount" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="openType == 'detail'">
          <a-form-item label="开票日期">
            <a-date-picker
              :disabled="openType === 'detail'"
              v-model:value="formData.invoiceTime"
              style="width: 100%"
              valueFormat="YYYY-MM-DD"
            />
          </a-form-item>
        </a-col>
        <!-- <a-col :span="12">
          <a-form-item
            label="发票类型"
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
          >
            <a-input
              v-model:value="formData.invoiceType"
              placeholder="发票类型"
            />
          </a-form-item>
        </a-col> -->
        <a-col :span="12" v-if="openType == 'detail'">
          <a-form-item label="发票号">
            <a-input
              v-model:value="formData.invoiceNo"
              placeholder="发票号"
              :disabled="openType === 'detail'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="币种" required name="currencyType">
            <a-select
              :disabled="openType === 'detail'"
              v-model:value="formData.currencyType"
              :options="dictTypeData"
              placeholder="请选择币种"
              :fieldNames="{
                label: 'dictLabel',
                value: 'dictValue',
              }"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="openType == 'detail'">
          <a-form-item label="发票文件">
            <form-upload
              v-if="openType !== 'detail'"
              v-model:value="formData.file"
              sence="article"
              :fileTypes="[]"
              :fileSize="100"
              :showDeletdPopup="true"
              hasDownLoad
            ></form-upload>
            <div v-else>
              <div
                v-for="(item, index) in formData.file"
                :key="index"
                :href="item.fileVisitUrl"
                :title="item.fileName"
                class="cursor-pointer inline-block"
                style="color: #959ec3"
                @click="downFile(item.fileVisitUrl, item.fileName)"
              >
                <i
                  class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                  style="color: #959ec3"
                ></i>

                <span class="underline">{{ item.fileName }}</span>
              </div>
            </div>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  reactive,
  getCurrentInstance,
  computed,
  defineProps,
  defineEmits,
  watch,
} from "vue";
import { downFile } from "@/common/setup/index.js";
import { billingTypeList } from "@/common/constant.js";
import { issueInvoiceTypeList } from "@/common/constant.js";
import { selectMenuListIgnoreNoStock } from "@/api/basicData/repository.js";
import { subtractTwo } from "@/common/calculationAlgorithm.js";
import FormUpload from "@/components/form-upload.vue";
import {
  formatter8,
  formatter2,
  calculateUntaxedAmount,
} from "@/common/validate.js";
import { postCustomerPage, productList } from "@/api/proSchedul/scheduling.js"; //客户信息
import {
  marketOrderListV2,
  marketOrderListV3,
  getSaleOrderDetail,
  getNotInvoiceProduct,
  postInvoiceAdd,
  postInvoiceDetail, //详情
  dictDataType,
  getCustomerNotInvoiceProduct,
  initialTreatment,
  getShippingList,
  getShippingListV1,
} from "@/api/sales/index.js";
import { roundNumFun } from "@/common/validate.js";
import { numMulti } from "@/common/calculationAlgorithm.js";
import { taxRateList } from "@/common/constant.js";
// import _cloneDeep from "lodash/cloneDeep";
// const taxRateListData = ref([]);
const emit = defineEmits(["update:visible", "finish"]);
const { proxy } = getCurrentInstance();
const formRef = ref();
const submitLoading = ref(false);
const productListData = ref([]);
const postNoContractReviewListOptions = ref([]);
const dictTypeData = ref([]);
const customerPageList = ref([]);
const deliveryOrderList = ref([]);
const rowSelection = computed(() => {
  let total = 0;
  formData.invoiceDetailParamList.forEach((acc, cur) => {
    if (acc.invoiceAmount && acc?.invoiceAmount !== null) {
      total += Number(acc?.invoiceAmount);
    }
  });
  return roundNumFun(total, 2);
});
const TotalUntaxedAmount = computed(() => {
  let total = 0;
  formData.invoiceDetailParamList.forEach((acc, cur) => {
    if (acc.noTaxAmount && acc?.noTaxAmount !== null) {
      total += Number(acc?.noTaxAmount);
    }
  });
  return roundNumFun(total, 2);
});
// 弹窗标题信息
const computeCustomTitle = (openType) => {
  let stageFirst = props.isBeginning == 1 ? " ( 期初 ) " : "";
  switch (openType) {
    case "add":
      return props.billType == "2"
        ? "新增订单发票（红字）"
        : "新增订单发票（蓝字）";
    case "firstTime":
      return "新增订单发票（期初处理）";
    case "detail":
      return "订单发票详情" + stageFirst;
  }
};

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: "",
  },
  openType: {
    type: String,
    required: "",
  },
  isBeginning: {
    type: String,
    required: "",
  },
  billType: String,
});

const formData = reactive({
  issueInvoiceType: 1,
  makeInvoiceType: 1,
  currencyType: "CNY",
  marketOrderId: void 0,
  orderAmount: void 0,
  invoicePrice: void 0,
  invoiceAmount: rowSelection,
  noTaxAmount: TotalUntaxedAmount,
  invoiceTime: void 0,
  invoiceType: void 0,
  invoiceNo: void 0,
  exchangeRate: void 0,
  bizBelongUserName: void 0,
  customerNo: void 0,
  invoiceDetailParamList: [],
});
const shippingNumberList = ref();
const dataSourceData = ref([]);
const columnsFirstTime = ref([
  { title: "产品信息", dataIndex: "materialName", width: "350px" },
  { title: "可开票数量", dataIndex: "invoicableQuantity" },
  { title: "已开票数量", dataIndex: "invoicedQuantity" },
  { title: "本次开票数量", dataIndex: "invoiceQuantity" },
  { title: "含税单价", dataIndex: "invoicePrice" },
  { title: "含税金额", dataIndex: "invoiceAmount" },
  { title: "税率", dataIndex: "taxRate" },
  { title: "未税金额", dataIndex: "noTaxAmount" },
  { title: "操作", dataIndex: "operation" },
]);
const columnsTrendsFirstTime = computed(() => {
  console.log(props.openType, props.isBeginning, "openType");
  if (props.openType == "detail" && props.isBeginning == 1) {
    return columnsFirstTime.value.filter(
      (column) =>
        column.dataIndex !== "invoicableQuantity" &&
        column.dataIndex !== "invoicedQuantity"
    );
  }
  return columnsFirstTime.value;
});
const columnsSaleInvoices = computed(() => {
  return [
    {
      title: props.billType == 2 ? "退货单" : "发货单",
      dataIndex: "shippingRecordNo",
      width: "180px",
    },
    { title: "开票产品", dataIndex: "materialName", width: "350px" },
    { title: "开票名称", dataIndex: "invoiceName" },
    { title: "开票规格", dataIndex: "invoiceSpecification" },
    { title: "开票数量", dataIndex: "invoiceQuantity" },
    { title: "含税单价", dataIndex: "invoicePrice" },
    { title: "含税金额", dataIndex: "invoiceAmount" },
    { title: "税率", dataIndex: "taxRate" },
    { title: "未税金额", dataIndex: "noTaxAmount" },
    {
      title: props.billType == 2 ? "退货单数量" : "发货单数量",
      dataIndex: "quantityShipped",
    },
    { title: "已开票数量", dataIndex: "invoiceQuantityNum" },
    { title: "可开票数量", dataIndex: "invQuantity" },
    { title: "操作", dataIndex: "operation" },
  ];
});

const columnsSaleInvoice = computed(() => {
  if (props.openType == "detail") {
    return columnsSaleInvoices.value.filter(
      (item) => item.dataIndex !== "invQuantity"
    );
  } else {
    return columnsSaleInvoices.value;
  }
});
const rules = reactive({
  orderId: [
    { required: true, trigger: "blur", message: "请选择关联-销售订单" },
  ],

  checkInformationFlag: [
    { required: true, trigger: "blur", message: "请选择是否核对提货人信息" },
  ],
  deliveryNoteFlag: [
    { required: true, trigger: "blur", message: "请选择是否需要送货单" },
  ],

  invoiceFlag: [
    { required: true, trigger: "blur", message: "请选择是否需要发票" },
  ],
  productSpecificationFlag: [
    { required: true, trigger: "blur", message: "请选择是否需要产品说明书" },
  ],
  warningMarkFlag: [
    { required: true, trigger: "blur", message: "请选择是否需要警示标识" },
  ],
});

function onClose() {
  emit("update:visible", false);
  formData.file = [];
  formData.marketOrderId = void 0;
  formData.orderAmount = void 0;
  formData.makeInvoiceType = 1;
  formData.issueInvoiceType = 1;
  formData.invoiceTime = void 0;
  formData.invoiceType = void 0;
  formData.invoiceNo = void 0;
  formData.exchangeRate = void 0;
  formData.remark = void 0;
  formData.currencyType = "CNY";
  formData.invoiceDetailParamList = [];
  formData.bizBelongUserName = void 0;
  formData.customerNo = void 0;
  formData.customerNos = void 0;
  customerPageList.value = [];
  deliveryOrderList.value = [];
  formData.shippingNumber = void 0;
  productListData.value = [];
  shippingNumberList.value = [];
}
const shippingNumberChange = (e, val, item) => {
  item.materialId = void 0;
  item.invoiceName = void 0;
  item.invoiceSpecification = void 0;
  item.quantityShipped = void 0;
  item.invQuantity = void 0;
  item.invoiceQuantityNum = void 0;
  item.invoiceAmount = void 0;
  item.invoicePrice = void 0;
  item.productListData = val.shippingMaterialList?.map((item, index) => {
    item.mateName = item.materialName;
    item.name = `${item.materialNo}\u0020/\u0020${item.materialName}\u0020/\u0020${item.specification}`;

    return item;
  });
};

const apiShippingNumberList = async (val) => {
  let res;
  if (props.billType == 2) {
    res = await getShippingListV1({ marketOrderId: val });
  } else {
    res = await getShippingList({ marketOrderId: val });
  }
  shippingNumberList.value = res.data.shippingRecordList;
};

// 字典查询
const getDictDataType = async () => {
  let dictType = "market_currency_type";
  let res = await dictDataType(dictType);
  dictTypeData.value = res.data;
};

// 关联-销售合同
const postNoContractReview = async () => {
  let param = {
    // status: props.billType == "2" ? 7 : undefined,
  };
  let res;
  if (props.billType == 2) {
    res = await marketOrderListV3(param);
  } else {
    res = await marketOrderListV2(param);
  }
  console.log("[ res ] >", res);
  postNoContractReviewListOptions.value = res.data.map((item) => {
    return {
      ...item,
      orderNames: `${item.orderName}(${item.orderNo})`,
    };
  });
};
// 关联-销售订单
const handleChange = (e, val) => {
  productListData.value = [];
  formData.invoiceDetailParamList = [];
  if (props.openType == "firstTime") {
    formData.bizBelongUserName = val.bizBelongUserName;
    formData.orderAmount = val.shippingAmount;
    formData.customerNos = val.customerNo;
    val.productList?.map((item, index) => {
      item.invoiceQuantity = item.invoicableQuantity;
      item.shippingNumber = val.shippingNumber;
      item.name = `${item.materialNo}\u0020/\u0020${item.materialName}\u0020/\u0020${item.specification}`;
      return item;
    });
    productListData.value = val.productList;
    formData.saleDept = val.saleDept;
  } else {
    getSaleOrderDetailList(e); //
    apiShippingNumberList(e); //chan
  }
};
// 选择客户
const postCustomerPageList = async () => {
  let res = await postCustomerPage();
  customerPageList.value = res.data.map((item) => {
    return {
      label: item.customerNo + "\u0020/\u0020" + item.customerName,
      value: item.customerNo,
    };
  });
};
// 选择客户获取订单
const customerHandleChange = async (e) => {
  formData.shippingNumber = void 0;
  formData.invoiceDetailParamList = [];
  productListData.value = [];
  let res = await getCustomerNotInvoiceProduct({
    customerNo: e,
  });
  deliveryOrderList.value = res.data;
  console.log(deliveryOrderList.value, "deliveryOrderList.value");
};
const getWarehousedetail = async (valid) => {
  let key = "FINISHED";
  let res = await selectMenuListIgnoreNoStock({
    warehouseType: key,
    materialId: valid,
  });
  dataSourceData.value = res.data;
};

const onProductList = (e, item, val) => {
  console.log("[ item ] >", item);
  getWarehousedetail(e);
  // taxRateListData.value = _cloneDeep(taxRateList);
  // const newArr = await taxRateListData.value.map((items) => items.value);
  // const isRepeat = await newArr.some((ite) => ite == Number(item.taxRate));
  // console.log(isRepeat, "isRepeat");
  // if (!isRepeat) {
  //   await taxRateListData.value.push({
  //     label: item.taxRate,
  //     value: item.taxRate,
  //   });
  // } else {
  // }
  val.invoiceAmount =
    props.openType == "firstTime"
      ? item.includingTaxTotalPrice || 0
      : item.invoiceAmount || 0;
  val.shippingQuantity = item.shippingQuantity;
  val.shippingNumber = item.shippingNumber;
  val.invoiceQuantityNum = item.invoiceQuantity;
  val.classificationName = item.classificationName;
  val.warehouseId = item.warehouseId;
  val.invoicePrice = item.unitPrice;
  val.quantityShipped = item.quantityShipped;

  val.taxRate = item.taxRate;
  val.materialNo = item.materialNo;
  val.materialName = item.materialName;

  val.amount = item.amount;
  val.quantity = item.quantity;
  val.specification = item.specification;
  val.invoiceName = item.mateName;
  val.invoiceSpecification = item.specification;
  val.invoicableQuantity = item.invoicableQuantity;
  val.unit = item.unit;
  val.invoiceQuantity = item.invoicableQuantity;

  if (props.openType == "firstTime") {
    val.invoicePrice = item.includingTaxPrice;
  } else {
    val.invoicePrice = item.unitPrice;
  }

  val.taxRate = item.taxRate;
  val.invoicedQuantity = item.invoicedQuantity;
  console.log(item.invoicePrice, "00000", item.includingTaxPrice);

  if (val.invoicePrice && val.invoiceQuantity) {
    console.log("456789");
    val.noTaxAmount = calculateUntaxedAmount(
      val.invoicePrice || 0,
      val.invoiceQuantity || 0,
      val.taxRate || 0
    );
  } else {
    val.noTaxAmount = 0;
  }
};

// 销售订单详情
const getSaleOrderDetailList = async (val) => {
  let res = await getSaleOrderDetail({ id: val });
  formData.bizBelongUserName = res.data.bizBelongUserName;
  formData.customerNos = res.data.customerNo;
  formData.orderAmount = res.data.totalContractAmount;
};
// 产品订单详情
const getSelectProductByOrderList = async (val) => {
  let res = await getNotInvoiceProduct({ marketOrderId: val });
  productListData.value = res.data?.map((item, index) => {
    item.mateName = item.materialName;
    item.name = `${item.materialNo}\u0020/\u0020${item.materialName}\u0020/\u0020${item.specification}`;

    return item;
  });
};

// 产品明细添加
const handleAdd = () => {
  formData.invoiceDetailParamList.push({});
};
// 产品删除
const onDelete = (ind) => {
  formData.invoiceDetailParamList.forEach((element, index) => {
    if (index === ind) {
      formData.invoiceDetailParamList.splice(index, 1);
    }
  });
};

// postInvoiceDetail详情接口
const postInvoiceDetails = async (val) => {
  try {
    let res = await postInvoiceDetail({ invoiceId: val });
    if (val && val !== undefined && props.isBeginning == 0) {
      getSaleOrderDetailList(res.data.marketOrderId);
      customerHandleChange(res.data.marketOrderId);
      apiShippingNumberList(res.data.marketOrderId);
    }
    if (props.isBeginning == 1) {
      formData.bizBelongUserName = "无";
      formData.customerNos = "无";
    }
    Object.assign(formData, {
      invoiceDetailParamList: res.data.invoiceDetailVos,
      marketOrderId:
        props.isBeginning == 0 ? String(res.data.marketOrderId) : "无",
      orderAmount: res.data.orderAmount,
      makeInvoiceType: res.data.makeInvoiceType,
      invoiceTime: res.data.invoiceTime,
      invoiceType: res.data.invoiceType,
      invoiceNo: res.data.invoiceNo,
      remark: res.data.remark,
      exchangeRate: res.data.exchangeRate,
      file: res.data?.file ? res.data?.file : [],
      issueInvoiceType: res.data.issueInvoiceType,
      currencyType: res.data.currencyType,
    });
    res.data.invoiceDetailVos.forEach((item) => {
      (item.productId = item.productId),
        (item.invoiceQuantityNum = item.invoiceQuantity),
        (item.invoicePrice = item.unitPrice);
    });
  } catch (e) {}
};
// 确认新增
const formSubmit = async () => {
  formRef.value.validate().then(async () => {
    try {
      const isNotEmpty = (value) =>
        value !== "" &&
        value !== null &&
        value !== undefined &&
        (value >= 0 || typeof value === "number");

      const hasEmptyData = formData.invoiceDetailParamList.every((obj) => {
        if (props.openType == "firstTime" || props.isBeginning == 1) {
          const invoiceQuantity =
            obj.invoiceQuantity &&
            obj.invoiceQuantity !== "" &&
            obj.invoiceQuantity !== null &&
            obj.invoiceQuantity !== undefined;
          const invoicePrice = isNotEmpty(obj?.invoicePrice);
          const taxRate = isNotEmpty(obj?.taxRate);
          console.log(invoiceQuantity, "1");
          console.log(invoicePrice, "2");
          console.log(taxRate, "3");
          return invoiceQuantity && invoicePrice && taxRate;
        } else {
          const invoiceQuantity =
            obj.invoiceQuantity &&
            obj.invoiceQuantity !== "" &&
            obj.invoiceQuantity !== null &&
            obj.invoiceQuantity !== undefined;
          const invoicePrice = isNotEmpty(obj?.invoicePrice);
          const invoiceAmount = isNotEmpty(obj?.invoiceAmount);
          const taxRate = isNotEmpty(obj?.taxRate);
          const noTaxAmount = isNotEmpty(obj?.noTaxAmount);
          console.log(invoiceQuantity, "1");
          console.log(invoicePrice, "2");
          console.log(invoiceAmount, "3");
          console.log(taxRate, "4");
          console.log(noTaxAmount, "5");
          return (
            invoiceQuantity &&
            invoicePrice &&
            invoiceAmount &&
            taxRate &&
            noTaxAmount
          );
        }
      });
      console.log(hasEmptyData, "hasEmptyData");
      if (!hasEmptyData) {
        proxy.$message.warning("数据错误，请检查后重试");
        return;
      }
      let param = {
        ...formData,
      };
      if (props.id) {
        param.id = props.id;
      }
      submitLoading.value = true;
      let res;
      if (props.openType == "firstTime") {
        res = await initialTreatment(param);
      } else {
        res = await postInvoiceAdd(param);
      }
      if (res.code == 200) {
        proxy.$message.success("操作成功");
        onClose();
        emit("finish");
      }
      submitLoading.value = false;
    } catch (error) {
      submitLoading.value = false;
    }
  });
};

// 本次开票数量输入时更新含税总金额
const changeInvoiceQuantity = (e, val, type) => {
  console.log("e", e, val, type);
  if (type == "firstTime") {
    val.invoiceAmount = numMulti(e, val.invoicePrice || 0);
  } else {
    if (val.invoiceQuantity && val.invoicePrice) {
      val.invoiceAmount = roundNumFun(
        Number(val.invoiceQuantity * val.invoicePrice),
        2
      );
    }

    if (val.invoicePrice && val.invoiceQuantity) {
      val.noTaxAmount = calculateUntaxedAmount(
        val.invoicePrice,
        val.invoiceQuantity,
        val.taxRate
      );
    }
  }
};
watch(
  () => props.billType,
  (val) => {
    formData.issueInvoiceType = val;
  }
);
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      if (props.id) {
        console.log(props, "ooo");
        postInvoiceDetails(props.id);
      }
      if (props.openType == "firstTime") {
        postCustomerPageList();
      }
      getDictDataType(); //字典查询
      postNoContractReview();
    }
  }
);
</script>
<style lang="less" scoped>
:deep(.ant-form label) {
  padding: 10px;
}
</style>
