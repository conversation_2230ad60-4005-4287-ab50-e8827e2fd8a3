<template>
  <div class="mb-4 text-right">
    <search :searchData="searchData" @search="refresh">
      <!-- v-permission="'sales:contractReview:add'" -->
      <mw-button
        :title="'新增'"
        :font="'iconfont icon-xianxing-121'"
        @click="onOpen('')"
      ></mw-button>
    </search>
  </div>
  <mw-table
    :scroll="{ x: 'max-content' }"
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'status'">
        <dictionary
          :statusOptions="purchaseReturnsList"
          :value="record.status"
          isBackgroundColor
        ></dictionary>
      </template>

      <template v-if="column.key == 'button'">
        <mw-button :title="'详情'" @click="onOpenDetail(record.id)"></mw-button>
        <!-- 暂时不改 -->
        <!-- <a-button type="primary" @click="onOpen(record.id)" class="ml-2">
          编辑
        </a-button> -->

        <!-- <a-popconfirm
        detailContractReviewDrawer
        title="确定是否删除"
        ok-text="是"
        cancel-text="否"
        @confirm="onConfirm(record)"
        @cancel="onCancel(record)">
        <a-button type="primary" danger class="ml-2">
          删除
        </a-button>
      </a-popconfirm> -->
      </template>
    </template>
  </mw-table>
  <!-- 新增 -->
  <add-contract-review-drawer
    ref="refAddContractReviewDrawer"
    v-model:visible="visibleAddContractReviewDrawer"
    @finish="getList"
    v-model:id="contractReviewId"
  />
  <detail-contract-review-drawer
    ref="refVisibleDetail"
    v-model:visible="visibleDetail"
    v-model:id="contractReviewId"
  />
</template>
<script setup>
import Search from "@/components/search/index.vue";
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import { usePagenation } from "@/common/setup";
import addContractReviewDrawer from "./addSaleOrderDrawer.vue";
import detailContractReviewDrawer from "./detailSaleOrderDrawer.vue";
import { postShippingList } from "@/api/sales/index.js";
import { purchaseReturnsList } from "@/common/constant.js";

const columns = ref([
  {
    title: "销售订单名称",
    dataIndex: "orderName",
    key: "orderName",
  },
  {
    title: "发货记录编号",
    dataIndex: "shippingRecordNo",
    key: "shippingRecordNo",
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status",
  },

  {
    title: "销售订单编号",
    dataIndex: "orderNo",
    key: "orderNo",
  },
  {
    title: "客户编号",
    dataIndex: "customerNo",
    key: "customerNo",
  },
  {
    title: "出库数量",
    dataIndex: "totalQuantityShipped",
    key: "totalQuantityShipped",
  },
  {
    title: "出库金额",
    dataIndex: "totalAmountShipped",
    key: "totalAmountShipped",
  },
  {
    title: "订单金额",
    dataIndex: "totalOrderAmount",
    key: "totalOrderAmount",
  },
  {
    title: "创建日期",
    dataIndex: "createTime",
    key: "createTime",
  },
  {
    title: "操作",
    dataIndex: "button",
    key: "button",
    fixed: "right",
  },
]);

// const searchData = reactive({
//         fields: {
//           status: {
//             name: "状态",
//             type: "a-select",
//             options: [],
//             placeholder: "选择状态",
//             width: "120px",
//             fieldNames: {
//               label: "name",
//               value: "id",
//             },
//             value: "",
//           },
//           keyword: {
//               type: "a-input-search",
//               placeholder:'输入合同编号/合同名称/客户名称',
//               width: "300px",
//             },
//         },

//     });

const searchData = ref({
  searchButtons: [],
  operationButtons: [],
  fields: {
    rangeDate: {
      type: "a-range-picker",
      value: [],
      valueFormat: "YYYY-MM-DD",
      width: "240px",
      placeholder: ["开始日期", "结束日期"],
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: "发货编码/销售订单/客户编码/客户名称",
      width: "340px",
      allowClear: true,
    },
  },
});

// status: {deliveryNoteFlag  deliveryNoteFlag     deliveryNoteFlag
//       name: "产品分类",
//       type: "a-select",
//       options: [],
//       placeholder: "选择分类",
//       width: "120px",
//       fieldNames: {
//         label: "name",
//         value: "id",
//       },
//       value: "",
//     },
//   searchData.value.fields.status.options = [
//   {
//     name: "全部分类",
//     id: "",
//   },
//   ...statusList

// ];
const { proxy } = getCurrentInstance();
const visibleDetail = ref(false);
const visibleAddContractReviewDrawer = ref(false);

const contractReviewId = ref("");
const data = ref([]),
  loading = ref(false);
const refAddContractReviewDrawer = ref(null);
const getList = async () => {
  loading.value = true;
  //分页信息
  let searchParam = {};

  //搜索信息

  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }

  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  // delete  searchData.value.fields.rangeDate

  let result = await postShippingList({ ...pageParam.value }, searchParam);
  // let result = await postShippingList(pageParam.value, param);
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

const onOpen = (valId) => {
  contractReviewId.value = valId;
  visibleAddContractReviewDrawer.value = true;
};

const onOpenDetail = (valId) => {
  contractReviewId.value = valId;
  visibleDetail.value = true;
};

async function onConfirm(record) {
  getList();
}
const onCancel = (record) => {
  // message.error('Click on No');
};
</script>
<style lang="less" scoped>
:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  background: #fff;
}
:deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
  background: #fff;
}
</style>
