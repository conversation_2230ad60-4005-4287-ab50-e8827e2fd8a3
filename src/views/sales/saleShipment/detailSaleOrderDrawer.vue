<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="70%"
    :customTitle="
      '销售发货详情' + `&nbsp &nbsp &nbsp订单编号：${formData.shippingRecordNo}`
    "
  >
    <template #header>
      <mw-button
        :title="'导出'"
        @click="shippingExportPdf"
        :loading="submitLoading"
      ></mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      labelAlign="left"
    >
      <a-row>
        <a-col :span="12">
          <a-form-item
            :label-col="{ span: 6, offset: 0 }"
            label="关联-销售订单"
            name="orderId"
          >
            <a-select
              disabled
              placeholder="请选择关联-销售订单"
              :options="postNoContractReviewListOptions"
              v-model:value="formData.orderId"
              optionFilterProp="name"
              @change="handleChange"
              :field-names="{
                label: 'orderName',
                value: 'id',
              }"
              show-search
            >
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="出库日期" :label-col="{ span: 6, offset: 2 }">
            <a-date-picker
              v-if="!planNo"
              v-model:value="formData.outboundTime"
              style="width: 100%"
              valueFormat="YYYY-MM-DD"
              disabled
            />
            <span v-else>{{ formData.outboundTime }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="客户代码" :label-col="{ span: 6 }">
            <a-input
              v-model:value="formData.contractList.customerNo"
              disabled
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="联系人" :label-col="{ span: 6, offset: 2 }">
            <a-input
              v-model:value="formData.relationName"
              placeholder="请输入联系人"
              disabled
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="联系方式" :label-col="{ span: 6 }">
            <a-input
              v-model:value="formData.relationPhone"
              placeholder="请输入联系方式"
              disabled
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="发货地址" :label-col="{ span: 6, offset: 2 }">
            <a-input
              v-model:value="formData.address"
              placeholder="请输入发货地址"
              disabled
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="发货数量" :label-col="{ span: 6 }">
            <a-input
              v-model:value="formData.contractList.totalQuantityShipped"
              disabled
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="出库金额" :label-col="{ span: 6, offset: 2 }">
            <a-input
              v-model:value="formData.contractList.totalAmountShipped"
              disabled
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item
            name="file"
            label="附件"
            required
            :label-col="{ span: 6 }"
          >
            <div class="overflow" v-if="formData.file?.fileVisitUrl">
              <i
                class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                style="color: #959ec3"
              ></i>
              <a
                :href="formData.file?.fileVisitUrl"
                :title="formData.file?.fileName"
                target="_blank"
                class="underline"
                style="color: #959ec3"
                >{{ formData.file?.fileName }}
              </a>
            </div>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="备注" :label-col="{ span: 6, offset: 2 }">
            <a-textarea
              v-model:value="formData.remark"
              placeholder="备注"
              allow-clear
              disabled
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <div>
            <a-form-item label="出库明细"> </a-form-item>
          </div>
          <a-table
            :scroll="{ x: 'max-content' }"
            bordered
            :data-source="formData.shippedProductList"
            :columns="columnsRelationProduct"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'materialId'">
                {{ record.materialNo }} / {{ record.materialName }} /
                {{ record.specification }}
              </template>
              <template v-if="column.dataIndex === 'classificationId'">
                {{ "暂无" }}
              </template>
              <template v-if="column.dataIndex === 'warehouseId'">
                {{ record.warehouseName }}
              </template>
            </template>
          </a-table>
        </a-col>

        <a-col :span="24">
          <div>
            物流信息
            <div class="flex mt-3 mb-3">
              <div>快递公司： {{ formData.expressCompanyName || "--" }}</div>
              <div class="ml-3">
                快递单号： {{ formData.expressNo || "--" }}
              </div>
              <div class="ml-3">
                {{ formData.shippingStatus == 1 ? "已签收" : "未签收" }}
              </div>
            </div>
            <div class="flex">
              <div class="mb-2">
                物流反馈类型：
                {{ formData.feedbackType == 1 ? "人工反馈" : "自动抓取" }}
                <mw-button
                  class="ml-10"
                  @click="onEnterDrawerSwitch"
                  v-if="formData.feedbackType == 1"
                  >录入</mw-button
                >
              </div>
            </div>
          </div>
          <a-table
            bordered
            :data-source="formData.logisticsInformationVO"
            :columns="colSticsInformationVO"
          >
            <!-- <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'logisticCode'">
                <a-button
                  type="link"
                  @click="showAogDetailsDrawer(record.traces)"
                  >{{ record?.logisticCode }}</a-button
                >
              </template>
            </template> -->
          </a-table>
        </a-col>
      </a-row>
    </a-form>
  </mw-drawer>
  <!-- <aog-detailsDrawer
      v-model:visible="aogDetailsDrawerSwitch"
      :dataList="valList"
    ></aog-detailsDrawer> -->
  <enterDrawer
    v-model:visible="enterDrawerSwitch"
    :enterId="enterId"
    @finishDetail="finishDetail"
  ></enterDrawer>
</template>
<script setup>
import { ref, reactive, defineProps, defineEmits, watch } from "vue";
import { postCustomerPage } from "@/api/proSchedul/scheduling.js"; //客户信息
import {
  getShippingDetail,
  postNoSaleOrderList,
  getChinaCompanyList,
  getSelectProductListByOrder,
  shippingRecordExportPdf,
} from "@/api/sales/index.js";
import { exportExecl } from "@/utils/util.js";
import { selectMenuListIgnoreNoStock } from "@/api/basicData/repository.js";
import { useRouter } from "vue-router";
// import aogDetailsDrawer from "./aogDetailsDrawer.vue";
import enterDrawer from "./enterDrawer.vue";
// const aogDetailsDrawerSwitch = ref(false);
const router = useRouter();
const productListData = ref([]);
const postNoContractReviewListOptions = ref([]);
const emit = defineEmits(["update:visible", "finish"]);
const dataSourceData = ref([]);

const enterDrawerSwitch = ref(false);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
});
const enterId = ref();
const formRef = ref();
const chinaCompanyListOptions = ref([]);
const customerPageList = ref([]);
const submitLoading = ref(false);
const formData = reactive({
  orderId: undefined,
  outboundTime: void 0,
  contractList: {
    customerNo: void 0,
    totalQuantityShipped: void 0,
  },
  relationName: void 0,
  relationPhone: void 0,
  address: void 0,
  shippedProductList: [{}],
});
const colSticsInformationVO = ref([
  {
    title: "时间",
    dataIndex: "acceptTime",
    width: "150px",
  },
  {
    title: "物流信息",
    dataIndex: "acceptStation",
    width: "150px",
  },

  // {
  //   title: "物流状态",
  //   dataIndex: "state",
  //   width: "150px",
  // },
  // {
  //   title: "增值物流状态",
  //   dataIndex: "stateEx",
  //   width: "150px",
  // },
  // {
  //   title: "所在城市",
  //   dataIndex: "location",
  //   width: "150px",
  // },
]);
const columnsRelationProduct = ref([
  {
    title: "选择-销售产品",
    dataIndex: "materialId",
    width: "150px",
  },
  // {
  //   title: "产品分类",
  //   dataIndex: "classificationName",
  //   width: "150px",
  // },
  // {
  //   title: "仓库",
  //   dataIndex: "warehouseId",
  //   width: "150px",
  // },

  // {
  //   title: "库存数量",
  //   dataIndex: "stockQuantity",
  //   width: "150px",
  // },
  // {
  //   title: "待出库数量",
  //   dataIndex: "unshippedQuantity",
  //   width: "150px",
  // },
  {
    title: "本次-出库数",
    dataIndex: "quantityShipped",
    width: "150px",
  },
  {
    title: "本次出库金额",
    dataIndex: "amountShipped",
    width: "150px",
  },
  // {
  //   title: "库存数量",
  //   dataIndex: "quantityStorage",
  //   width: "150px",
  // },
  // {
  //   title: "备注",
  //   dataIndex: "remark",
  //   width: "150px",
  // },
]);
const rules = reactive({
  contractClassify: [
    {
      required: true,
      message: "请选择",
      trigger: "blur",
    },
  ],
});
const finishDetail = () => {
  getDetail(props.id);
};
const shippingExportPdf = async () => {
  submitLoading.value = true;
  let result = await shippingRecordExportPdf({ id: props.id });
  const fileName = "发货.pdf";
  exportExecl(fileName, result);
  submitLoading.value = false;
};
function onClose() {
  emit("update:visible", false);
  formRef.value.resetFields();
  formData.outboundTime = void 0;
}
// 客户
const postCustomerPageList = async () => {
  let res = await postCustomerPage();
  customerPageList.value = res.data;
};

// 产品详情
const getSelectProductByOrderList = async (val) => {
  let res = await getSelectProductListByOrder({ id: val });
  productListData.value = res.data;
};
// 仓库
const getWarehousedetail = async (valid) => {
  let key = "FINISHED";
  let res = await selectMenuListIgnoreNoStock({
    warehouseType: key,
    materialId: valid,
  });
  dataSourceData.value = res.data;
};
// 关联-销售合同
const postNoContractReview = async () => {
  let param = {
    statusList: [0],
  };
  let res = await postNoSaleOrderList(param);
  postNoContractReviewListOptions.value = res.data;
};
// // 物流信息
const chinaCompanyList = async () => {
  let res = await getChinaCompanyList();
  chinaCompanyListOptions.value = res.data;
};

// 编辑详情
// contractReviewId
const getDetail = async (val) => {
  let res = await getShippingDetail({ id: val });
  getSelectProductByOrderList(res.data.orderId);
  getWarehousedetail(res.data.productId);
  formData.orderId = String(res.data.orderId);
  formData.outboundTime = res.data.outboundTime;
  formData.contractList = {
    customerNo: res.data.customerNo,
    // customerName: res.data.customerName,
    // totalContractAmount: res.data.totalOrderAmount,
    totalAmountShipped: res.data.totalAmountShipped,
    totalQuantityShipped: res.data.totalQuantityShipped,
  };
  formData.relationName = res.data.relationName;
  formData.relationPhone = res.data.relationPhone;
  formData.address = res.data.address;
  // formData.expressCompanyId = res.data.expressCompanyCode;
  formData.shippedProductList = res.data.shippingProductRelationList;
  formData.deliveryNoteFlag = res.data.deliveryNoteFlag;
  formData.checkInformationFlag = res.data.checkInformationFlag;
  formData.productSpecificationFlag = res.data.productSpecificationFlag;
  formData.warningMarkFlag = res.data.warningMarkFlag;
  formData.invoiceFlag = res.data.invoiceFlag;
  formData.deliveryNote = res.data.deliveryNote;
  formData.shippingRecordNo = res.data.shippingRecordNo;

  formData.file = res.data.file;
  formData.freight = res.data.freight;
  formData.createTime = res.data.createTime;
  formData.feedbackType = res.data.feedbackType;
  if (
    res.data.logisticsInformationVO &&
    res.data.logisticsInformationVO.traces
  ) {
    formData.logisticsInformationVO = res.data.logisticsInformationVO.traces;
  }

  formData.expressCompanyName = res.data.expressCompanyName;
  formData.expressCompanyCode = res.data.expressCompanyCode;
  formData.expressNo = res.data.expressNo;
  formData.shippingStatus = res.data.shippingStatus;
};

const onEnterDrawerSwitch = () => {
  enterDrawerSwitch.value = true;
  enterId.value = props.id;
  console.log(props.id, "valie");
};
// const getLabelList = (config, valueList) => {
//   const labelList = valueList.map((value) => {
//     const item = config.find((item) => item.value === value);
//     return item ? item.label : "";
//   });
//   return labelList.join(" ， ");
// };
// const onJump = (val) => {
//   if (val == "product") {
//     const url = import.meta.env.VITE_DOMAIN + "/basicData/product";
//     router.push({
//       name: "Product",
//     });
//   } else {
//     const url = import.meta.env.VITE_DOMAIN + "/basicData/bom";
//     router.push({
//       name: "Bom",
//     });
//   }
// };
// // 产品明细添加
// const handleAdd = () => {
//   const newData = {
//     id: Date.now(),
//     productId: "",
//     unitPrice: 0.0,
//     quantity: "",
//     amount: 0.0,
//     isNeedDebugging: "",
//     productUse: "",
//   };
//   formData.relationProductList.push(newData);
// };
// // 产品删除
// const onDelete = (val) => {
//   formData.relationProductList.forEach((element, index) => {
//     if (element.id == val.id) {
//       formData.relationProductList.splice(index, 1);
//     }
//   });
// };
// // 物流信息详情
// const showAogDetailsDrawer = (val) => {
//   valList.value = val;
//   aogDetailsDrawerSwitch.value = true;
// };
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      formData.logisticsInformationVO = [];
      formData.expressCompanyCode = void 0;
      formData.expressCompanyName = void 0;
      formData.expressNo = void 0;
      formData.shippingStatus = void 0;
      if (props.id) {
        await getDetail(props.id);
      }
      await postCustomerPageList();
      await postNoContractReview();
      await chinaCompanyList();
    }
  }
);
</script>
<style lang="less" scoped></style>
