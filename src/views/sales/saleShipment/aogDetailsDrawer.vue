<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="60%"
    customTitle="物流信息详情"
  >
    <a-table
      bordered
      :data-source="LogisticsDetailsData"
      :columns="LogisticsDetailsColumns"
      :scroll="{ y: 700 }"
    >
    </a-table>
  </mw-drawer>
</template>
<script setup>
import { ref, defineProps, watch, defineEmits } from "vue";
const emit = defineEmits(["update:visible", "finish"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  dataList: {
    type: Array,
    default: false,
  },
});
const LogisticsDetailsData = ref([]);
const LogisticsDetailsColumns = ref([
  {
    title: "接收位置",
    dataIndex: "acceptStation",
    width: "150px",
  },
  {
    title: "接收城市",
    dataIndex: "location",
    width: "150px",
  },
  {
    title: "接收时间",
    dataIndex: "acceptTime",
    width: "150px",
  },
]);

const onClose = () => {
  emit("update:visible", false);
};
watch(
  () => props.visible,
  async (val) => {
    LogisticsDetailsData.value = props.dataList;
  }
);
</script>
<style scoped>
.LogisticsDetailStyle {
  height: 100vh;
}
</style>
