<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="40%"
    customTitle="物流信息录入"
    ><template #header>
      <mw-button @click="submitForm" :loading="submitLoading">确定</mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :colon="false"
      labelAlign="left"
    >
      <a-form-item
        :label-col="{ span: 5 }"
        label="物流当前状态"
        name="shippingStatus"
      >
        <a-select
          :options="whetherSignForReceipt"
          v-model:value="formData.shippingStatus"
          optionFilterProp="label"
          placeholder="物流当前状态"
          show-search
        >
        </a-select>
      </a-form-item>
      <a-form-item :label-col="{ span: 5 }" label="物流时间" name="expressTime">
        <a-date-picker
          class="w-full"
          placeholder="物流时间"
          v-model:value="formData.expressTime"
          valueFormat="YYYY-MM-DD HH:mm:ss"
          :disabled-date="disabledDateNoConThenBefore"
        />
      </a-form-item>
      <a-form-item :label-col="{ span: 5 }" label="物流信息" name="expressInfo">
        <a-textarea
          placeholder="物流信息"
          v-model:value="formData.expressInfo"
          :rows="4"
        />
      </a-form-item>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import { ref, defineProps, defineEmits } from "vue";
const emit = defineEmits(["update:visible", "finishDetail"]);
import { whetherSignForReceipt } from "@/common/constant.js";
import { shippingRecord } from "@/api/sales/index.js";
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  enterId: Number,
});
const formRef = ref(null);
const submitLoading = ref(false);
const formData = ref({
  shippingStatus: void 0,
  expressTime: void 0,
  expressInfo: void 0,
});
const rules = {
  shippingStatus: [
    {
      required: true,
      message: "请选择物流当前状态",
      trigger: "blur",
    },
  ],
  expressTime: [
    {
      required: true,
      message: "请选择物流时间",
      trigger: "blur",
    },
  ],
  expressInfo: [
    {
      required: true,
      message: "请填写物流信息",
      trigger: "blur",
    },
  ],
};
const onClose = () => {
  formRef.value.resetFields();
  emit("update:visible", false);
};
const submitForm = () => {
  formRef.value.validate().then(async () => {
    try {
      console.log(props.enterId, "enterId");
      console.log(formData.value, "formData.value");
      submitLoading.value = true;
      let param = {
        ...formData.value,
        shippingRecordId: props.enterId || "",
      };
      let res = await shippingRecord(param);
      console.log(res, "res");
      if (res.code === 200) {
        emit("finishDetail");
        onClose();
      }
      submitLoading.value = false;
    } catch (error) {
      console.log(error);
      submitLoading.value = false;
    }
  });
};
</script>
<style scoped>
.LogisticsDetailStyle {
  height: 100vh;
}
</style>
