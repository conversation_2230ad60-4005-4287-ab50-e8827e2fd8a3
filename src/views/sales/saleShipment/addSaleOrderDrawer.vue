<template>
	<mw-drawer :visible="visible" @close="onClose()" width="80%" :customTitle="props.id ? '编辑销售发货' : '新增销售发货'">
		<template #header>
			<mw-button :title="'确定'" @click="formSubmit" :loading="submitLoading"></mw-button>
		</template>
		<a-form ref="formRef" :model="formData" :rules="rules" layout="horizontal" :colon="false" labelAlign="left"
			:label-col="{ span: 7, offset: 1 }">
			<a-row>
				<a-col :span="12">
					<a-form-item label="关联-销售订单" name="orderId" required>
						<a-select placeholder="请选择关联-销售订单" :options="postNoContractReviewListOptions"
							v-model:value="formData.orderId" optionFilterProp="label" @change="handleChange" show-search>
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="出库日期">
						<a-date-picker v-model:value="formData.outboundTime" style="width: 100%" valueFormat="YYYY-MM-DD" />
					</a-form-item>
				</a-col>
				<!-- 
            <a-col :span="12"> 
              <a-form-item  label="出库日期"  :label-col="{ span: 7, offset: 2 }" :wrapper-col="{span: 17,offset:1}">
                <a-date-picker
                  v-if="!planNo"
                  v-model:value="formData.contractList.reviewTime"
                  style="width: 100%"
                  valueFormat="YYYY-MM-DD"
                  disabled
                />
                <span v-else>{{ formData.contractList.reviewTime }}</span>
              </a-form-item>
            </a-col> -->

				<a-col :span="12">
					<a-form-item label="客户代码">
						<a-input v-model:value="formData.contractList.customerNo" disabled />
					</a-form-item>
				</a-col>

				<a-col :span="12">
					<a-form-item label="联系人" required name="relationName">
						<a-input v-model:value="formData.relationName" placeholder="请输入联系人" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="联系方式" required name="relationPhone">
						<a-input v-model:value="formData.relationPhone" placeholder="请输入联系方式" />
						<!-- :rules="[ { required: true, message: '请输入手机号码!', }, {
            pattern: /^(1[3456789]\d{9}|0\d{2,3}-\d{7,8})$/, message:
            '手机号码格式不正确!', }, ]" -->
					</a-form-item>
				</a-col>
				<!-- <a-col :span="12">
          <a-form-item label="客户名称">
            <a-input
              v-model:value="formData.contractList.customerName"
              disabled
            />
          </a-form-item>
        </a-col> -->

				<a-col :span="12">
					<a-form-item label="发货地址" required name="address">
						<a-input v-model:value="formData.address" placeholder="请输入发货地址" />
					</a-form-item>
				</a-col>
				<!-- <a-col :span="12"> 
              <a-form-item  label="物流单号" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{span: 17,offset:1}">
                <a-input   v-model:value="formData.expressNo" disabled />
              </a-form-item>
            </a-col> -->
				<!-- <a-col :span="12">
          <a-form-item label="物流单号">
            <a-input
              v-model:value="formData.expressNo"
              placeholder="物流单号"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="物流信息">
            <a-select
              placeholder="请选择物流信息"
              :options="chinaCompanyListOptions"
              v-model:value="formData.expressCompanyId"
              optionFilterProp="companyName"
              :field-names="{
                label: 'companyName',
                value: 'expressCompanyId',
              }"
              show-search
            >
            </a-select>
          </a-form-item>
        </a-col> -->
				<a-col :span="12">
					<a-form-item label="附件">
						<form-upload v-if="!props.id" v-model:value="formData.file" sence="article" :fileTypes="[]"
							:fileSize="100"></form-upload>
						<div v-else @click.stop="openUrl(formData.file?.fileVisitUrl)" :href="formData.file?.fileVisitUrl"
							:title="formData.file?.fileName" class="cursor-pointer inline-block" style="color: #959ec3">
							<i class="iconfont icon-jichu-lianjie text-xs align-middle mr-1" style="color: #959ec3"></i>

							<span class="underline">{{ formData.file?.fileName }}</span>
						</div>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="备注" name="remark">
						<a-textarea style="width: 100%" v-model:value="formData.remark" placeholder="备注" allow-clear />
					</a-form-item>
				</a-col>
				<!-- <a-col :span="12">
          <a-form-item label="合同金额">
            <a-input
              v-model:value="formData.contractList.totalContractAmount"
              disabled
            />
          </a-form-item>
        </a-col> -->

				<!-- <a-col :span="12">
              <a-form-item  label="待-收款金额" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{span: 17,offset:1}">
                  <a-input   v-model:value="formData.contractList.contractNo" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="12"> 
              <a-form-item  label="已-收款金额" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{span: 17,offset:1}">
                <a-input   v-model:value="formData.contractList.productMarketClassification" disabled />
              </a-form-item>
            </a-col> -->
				<a-col :span="24">
					<div>
						<a-form-item label="出库明细" required name="shippedProductList" :label-col="{ span: -8 }">
							<mw-button :title="'新增'" @click="handleAdd"> </mw-button>
						</a-form-item>
					</div>
					<a-table bordered :data-source="formData.shippedProductList" :columns="columnsRelationProduct"
						:scroll="{ x: 'max-content' }">
						<template #bodyCell="{ column, record }">
							<template v-if="column.dataIndex === 'materialId'">
								<a-select show-search style="width: 100%" placeholder="请选择销售产品" :options="productListData"
									v-model:value="record.materialId" @change="(val, option) => onProductList(val, option, record)"
									:filter-option="filterOption" :field-names="{
										label: 'materialName',
										value: 'materialId',
									}">
								</a-select>
							</template>
							<template v-if="column.dataIndex === 'classificationId'">
								{{ "暂无" }}
							</template>
							<template v-if="column.dataIndex === 'warehouseId'">
								<!-- {{ record.warehouseName }} -->
								<a-select show-search style="width: 100%" placeholder="请选择仓库" :options="record.dataSourceData"
									v-model:value="record.warehouseId" optionFilterProp="warehouseName" :field-names="{
										label: 'warehouseName',
										value: 'warehouseId',
									}" @change="onWarehouseList($event, record)">
								</a-select>
							</template>
							<template v-if="column.dataIndex === 'areaId'">
								<a-select v-model:value="record.areaId" style="width: 100%" placeholder="请选择货位"
									:options="record.areaOption" @change="areaChange($event, record)" />
							</template>
							<template v-if="column.dataIndex === 'stockQuantity'">
								{{ record.stockQuantity }}
							</template>

							<template v-if="column.dataIndex === 'unshippedQuantity'">
								<!-- <a-textarea
                  style="width: 100%; height: 10px"
                  v-model:value="record.unshippedQuantity"
                  placeholder="请输入"
                  allow-clear
                /> -->
								{{ record.unshippedQuantity }}
							</template>
							<template v-if="column.dataIndex === 'quantityShipped'">
								<a-input-number style="width: 100%; height: 100%" v-model:value="record.quantityShipped"
									placeholder="出库数" allow-clear :stringMode="true" />
							</template>
							<template v-if="column.dataIndex === 'remark'">
								<a-textarea style="width: 100%; height: 35px" v-model:value="record.remark" placeholder="备注"
									allow-clear />
							</template>
							<template v-if="column.dataIndex === 'operation'">
								<a-popconfirm title="确定是否删除" @confirm="onDelete(record)">
									<mw-button :title="'删除'" danger></mw-button>
								</a-popconfirm>
							</template>
						</template>
					</a-table>
				</a-col>
				<!-- <a-col :span="12">
          <a-form-item
            label="是否核对提货人"
            required
            name="checkInformationFlag"
            placeholder="请选择是否核对提货人"
          >
            <a-radio-group v-model:value="formData.checkInformationFlag">
              <a-radio :value="0">是</a-radio>
              <a-radio :value="1">否</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="是否需要说明书"
            required
            placeholder="请选择是否需要产品说明书"
            name="productSpecificationFlag"
          >
            <a-radio-group v-model:value="formData.productSpecificationFlag">
              <a-radio :value="0">是</a-radio>
              <a-radio :value="1">否</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="是否需要标识"
            required
            name="warningMarkFlag"
            placeholder="请选择是否需要标识"
          >
            <a-radio-group v-model:value="formData.warningMarkFlag">
              <a-radio :value="0">是</a-radio>
              <a-radio :value="1">否</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="是否需要发票"
            required
            name="invoiceFlag"
            placeholder="请选择是否需要发票"
          >
            <a-radio-group v-model:value="formData.invoiceFlag">
              <a-radio :value="0">是</a-radio>
              <a-radio :value="1">否</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="是否需要送货单"
            required
            name="deliveryNoteFlag"
            placeholder="请选择是否需要送货单"
          >
            <a-radio-group v-model:value="formData.deliveryNoteFlag">
              <a-radio :value="0">是</a-radio>
              <a-radio :value="1">否</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col> -->
			</a-row>
		</a-form>
	</mw-drawer>
</template>
<script setup>
import { ref, reactive, getCurrentInstance, defineProps, defineEmits, watch } from "vue";
import { productDetail } from "@/api/basicData/product.js";
// import { selectMenuListIgnoreNoStock } from "@/api/basicData/repository.js";
import { postShippingAdd, postContractReviewUpdate, getSaleOrderDetail, getChinaCompanyList, getOrderList, listByMaterialIgnoreNoStockV1 } from "@/api/sales/index.js";
import FormUpload from "@/components/form-upload.vue";
const productListData = ref([]);
const postNoContractReviewListOptions = ref([]);
const emit = defineEmits(["update:visible", "finish"]);

const props = defineProps({
	visible: {
		type: Boolean,
		default: false,
	},
	id: {
		type: String,
		required: true,
	},
});

const { proxy } = getCurrentInstance();
const formRef = ref();
const chinaCompanyListOptions = ref([]);
const submitLoading = ref(false);
const formData = reactive({
	relationName: void 0,
	relationPhone: void 0,
	contractId: void 0,
	orderName: void 0,
	outboundTime: void 0,
	contractList: {
		customerNo: void 0,
		// customerName: undefined,
		// totalContractAmount: undefined,
	},
	remark: void 0,
	useCountry: void 0,
	foreignCountry: void 0,
	address: void 0,
	expressCompanyId: void 0,
	expressNo: void 0,
	shippedProductList: [],
	checkInformationFlag: void 0,
	deliveryNoteFlag: void 0,
	invoiceFlag: void 0,
	productSpecificationFlag: void 0,
	warningMarkFlag: void 0,
});
const columnsRelationProduct = ref([
	{
		title: "选择-销售产品",
		dataIndex: "materialId",
		width: "350px",
	},
	{
		title: "产品总数",
		dataIndex: "productQuantity",
		width: "150px",
	},
	{
		title: "已出库数量",
		dataIndex: "shippedQuantity",
		width: "150px",
	},
	{
		title: "可出库数量",
		dataIndex: "residueQuantity",
		width: "150px",
	},
	// {
	//   title: "待出库数量",
	//   dataIndex: "unshippedQuantity",
	//   width: "150px",
	// },
	// {
	//   title: "产品分类",
	//   dataIndex: "classificationName",
	//   width: "150px",
	// },
	{
		title: "选择仓库",
		dataIndex: "warehouseId",
		width: "200px",
	},
	{
		title: "选择库位",
		dataIndex: "areaId",
		width: "200px",
	},
	{
		title: "物料数量",
		dataIndex: "stockQuantity",
		width: "150px",
	},

	{
		title: "本次出库数",
		dataIndex: "quantityShipped",
		width: "150px",
	},
	{
		title: "备注",
		dataIndex: "remark",
		width: "150px",
	},
	{
		title: "操作",
		dataIndex: "operation",
		width: "150px",
	},
]);
const rules = reactive({
	orderId: [
		{
			required: true,
			trigger: "blur",
			message: "请选择关联-销售订单",
		},
	],
	expressCompanyId: [
		{
			required: true,
			trigger: "blur",
			message: "请选择物流信息",
		},
	],

	freight: [
		{
			required: true,
			trigger: "blur",
			message: "请输入运费金额",
		},
	],
	checkInformationFlag: [
		{
			required: true,
			trigger: "blur",
			message: "请选择是否核对提货人信息",
		},
	],
	deliveryNoteFlag: [
		{
			required: true,
			trigger: "blur",
			message: "请选择是否需要送货单",
		},
	],

	invoiceFlag: [
		{
			required: true,
			trigger: "blur",
			message: "请选择是否需要发票",
		},
	],
	productSpecificationFlag: [
		{
			required: true,
			trigger: "blur",
			message: "请选择是否需要产品说明书",
		},
	],
	warningMarkFlag: [
		{
			required: true,
			trigger: "blur",
			message: "请选择是否需要警示标识",
		},
	],
});

const changeContract = (e) => {
	// formData.relationContractId=''
};
function onClose() {
	emit("update:visible", false);
	formRef.value.resetFields();
	formData.relationName = void 0;
	formData.relationPhone = void 0;
	formData.contractId = void 0;
	formData.orderName = void 0;
	formData.contractList = {};
	formData.remark = void 0;
	formData.useCountry = void 0;
	formData.foreignCountry = void 0;
	formData.address = void 0;
	formData.expressCompanyId = void 0;
	formData.expressNo = void 0;
	formData.shippedProductList = [];
	formData.checkInformationFlag = void 0;
	formData.deliveryNoteFlag = void 0;
	formData.invoiceFlag = void 0;
	formData.productSpecificationFlag = void 0;
	formData.warningMarkFlag = void 0;
	formData.file = {};
	formData.outboundTime = void 0;
}
const filterOption = (input, option) => {
	console.log(input, option); // 根据名称和ID进行查询
	return option.materialName?.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// // 产品详情信息
const productDetailList = async (e, val) => {
	let res = await productDetail({ productNo: e });
};
// // 物流信息
const chinaCompanyList = async () => {
	let res = await getChinaCompanyList();
	chinaCompanyListOptions.value = res.data;
};
// 关联-销售合同
const postNoContractReview = async () => {
	let res = await getOrderList();
	postNoContractReviewListOptions.value =
		res.data?.map((item) => {
			item.label = `订单 ${item.orderName}（${item.orderNo}）`;
			item.value = item.orderId;
			return item;
		}) || [];
};
const handleChange = (e, val) => {
	formData.shippedProductList = [];

	console.log(e, val, "ee");

	productListData.value = val.productDetailList.map((item) => {
		console.log(item, "item");
		item.materialName = item.materialNo + " / " + item.materialName + " / " + item.specification;

		// +"/" + item.specification;
		return item;
	});

	// getSelectProductByOrderList(e);
	getSaleOrderDetailList(e);
};
const getWarehousedetail = async (valid) => {
	let key = "RAWMATERIAL";
	let res = await listByMaterialIgnoreNoStockV1({
		warehouseType: key,
		materialId: valid,
	});
	return res.data;
};

const onProductList = async (e, val, item) => {
	item.warehouseId = void 0;
	// item.areaId = void 0;
	item.productQuantity = val.productQuantity;
	item.residueQuantity = val.residueQuantity;
	item.shippedQuantity = val.shippedQuantity;
	let res = await getWarehousedetail(e);
	console.log("[ res ] >", res);
	if (res.length) {
		item.dataSourceData = res.filter((item) => item.isSendWarehouse == 1);
	} else {
		item.dataSourceData = [];
	}
	//
	item.warehouseId = res[0]?.warehouseId || void 0;
	item.warehouseName = res[0]?.warehouseName || void 0;
	item.stockQuantity = res[0]?.totalInventory || void 0;
	onWarehouseList(res[0]?.warehouseId, item);

	// totalInventory
	productListData.value.forEach((item, index) => {
		if (item.materialId == e) {
			val.classificationName = item.classificationName;
			val.unshippedQuantity = item.unshippedQuantity;
		}
	});
};

// onWarehouseList--仓库change
const onWarehouseList = (e, val) => {
	val.areaOption = [];
	// val.areaId = void 0;
	val.stockQuantity = void 0;
	val.dataSourceData.forEach((item, index) => {
		if (item.warehouseId == e) {
			val.stockQuantity = item.totalInventory || 0;
			val.areaOption = item.warehouseMenuDetailVoList.map((item) => {
				return {
					label: item.warehouseArea,
					value: item.warehouseAreaId,
					inventory: item.inventory,
				};
			});
			val.areaId = item.warehouseMenuDetailVoList[0]?.warehouseAreaId;
			areaChange(val.areaId, val);
		}
	});
};

// // 销售订单详情
const getSaleOrderDetailList = async (val) => {
	let res = await getSaleOrderDetail({ id: val });
	formData.outboundTime = res.data.outboundTime;
	// formData.contractList.totalContractAmount = res.data.totalContractAmount;
	formData.contractList.customerNo = res.data.customerNo;
	// formData.contractList.customerName = res.data.customerName;
	formData.relationName = res.data.bizBelongUserName;
	formData.relationPhone = res.data.phone;
	formData.customerId = res.data.customerId;
};

// 产品明细添加
const handleAdd = () => {
	const newData = {
		id: Date.now(),
		areaOption: [],
		// areaId: void 0,
	};
	formData.shippedProductList.push(newData);
};
// 产品删除
const onDelete = (val) => {
	formData.shippedProductList.forEach((element, index) => {
		if (props.id) {
			if (element.materialId == val.materialId) {
				formData.shippedProductList.splice(index, 1);
			}
		} else {
			if (element.id == val.id) {
				formData.shippedProductList.splice(index, 1);
			}
		}
	});
};
const areaChange = (val, record) => {
	record.areaOption.forEach((item) => {
		if (val == item.value) {
			record.stockQuantity = item.inventory;
		}
	});
};
// 确认新增
const formSubmit = async () => {
	formRef.value.validate().then(async () => {
		try {
			if (formData.shippedProductList.length < 1) {
				proxy.$message.error("明细信息不能为空");
				return;
			}

			let containsValue = formData.shippedProductList.every((item) => item.warehouseId && item.warehouseId !== null);

			if (!containsValue) {
				proxy.$message.error("请选择仓库，仓库不能为空");
				return;
			}

			const hasOneSatisfied = formData.shippedProductList.every((item) => {
				return Number(item.residueQuantity) >= Number(item.quantityShipped);
			});
			const hasOneSatisfiedArea = formData.shippedProductList.every((item) => {
				return Number(item.stockQuantity) >= Number(item.quantityShipped);
			});
			if (!hasOneSatisfied) {
				// proxy.$message.error("请联系仓库人员确认库存数量，如未在成品仓，请联系仓库更改仓库货位");
				proxy.$message.error("本次出库数量不得大于可出库数量");
				return;
			}
			if (!hasOneSatisfiedArea) {
				// proxy.$message.error("请联系仓库人员确认库存数量，如未在成品仓，请联系仓库更改仓库货位");
				proxy.$message.error("本次出库数量不得大于物料数量");
				return;
			}
			let param = {
				...formData,
				shippedMaterialList: formData.shippedProductList,
				file: formData.file ? formData.file[0] : {},
			};
			delete param.shippedProductList;

			submitLoading.value = true;
			let res;
			if (props.id) {
				param.id = props.id;
				res = await postContractReviewUpdate(param);
			} else {
				res = await postShippingAdd(param);
			}

			if (res.code == 200) {
				proxy.$message.success("操作成功");
				onClose();
			}

			submitLoading.value = false;
			emit("finish");
		} catch (error) {
			submitLoading.value = false;
		}
	});
};
watch(
	() => props.visible,
	async (val) => {
		if (val) {
			postNoContractReview();
			chinaCompanyList();
			// postproductList()
		}
	}
);
</script>
<style lang="less" scoped>
:deep(.ant-form label) {
	padding: 10px;
}
</style>
