<template>
  <div>
    <mw-table
      :scroll="{ x: 'max-content' }"
      :columns="columns"
      :data-source="props.data"
      :loading="loading"
      :rowKey="(record) => record.id"
      hasPage
      @change="onTableChange"
      :pageConfig="paginationProps"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key == 'type'">
          {{
            record.type == "MATERIAL"
              ? "物料"
              : record.type == "PRODUCT"
              ? "产品"
              : "-"
          }}</template
        ></template
      >
    </mw-table>
  </div>
</template>

<script setup>
import { ref, defineProps } from "vue";
const props = defineProps({
  data: {
    type: Array,
    default: [],
  },
  id: {
    type: String,
    required: true,
  },
});
const columns = ref([
  { title: "销售订单编号", dataIndex: "marketOrderId", key: "marketOrderId" },
  { title: "类型", dataIndex: "type", key: "type" },
  { title: "赠品名称", dataIndex: "name", key: "name" },
  { title: "赠品编号", dataIndex: "no", key: "no" },
  { title: "规格", dataIndex: "specification", key: "specification" },
  { title: "数量", dataIndex: "quantity", key: "quantity" },
  { title: "创建日期", dataIndex: "createTime", key: "createTime" },
]);
</script>

<style></style>
