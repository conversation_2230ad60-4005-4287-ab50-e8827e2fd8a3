<template>
  <mw-drawer :visible="visible" @close="onClose()" width="60%" :customTitle="props.id ? '编辑销售订单' : '新增销售订单'">
    <template #header>
      <mw-button @click="formSubmit" :loading="submitLoading">确定</mw-button>
    </template>
    <a-form ref="formRef" :model="formData" :rules="rules" layout="horizontal" :colon="false" labelAlign="left">
      <a-row>
        <a-col :span="12">
          <a-form-item label="业务-归属人" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.contractList.customerNo" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="所属部门" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.contractList.customerNo" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }" label="关联-销售订单"
            name="orderId" required>
            <a-select placeholder="请选择关联-销售订单" :options="postNoContractReviewListOptions"
              v-model:value="formData.orderId" optionFilterProp="name" @change="handleChange" :field-names="{
                label: 'orderName',
                value: 'id',
              }" show-search>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="客户代码" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.contractList.customerNo" disabled />
          </a-form-item>
        </a-col>

        <!-- <a-col :span="12">
          <a-form-item
            label="客户名称"
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
          >
            <a-input
              v-model:value="formData.contractList.customerName"
              disabled
            />
          </a-form-item>
        </a-col> -->

        <a-col :span="12">
          <a-form-item label="合同金额" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.contractList.totalContractAmount" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="客户代码" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.contractList.customerNo" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <div>
            <a-form-item label="开票产品明细" required>
              <mw-button @click="handleAdd">新增</mw-button>
            </a-form-item>
          </div>
          <a-table bordered :data-source="formData.shippedProductList" :columns="columnsSaleInvoice"
            :scroll="{ x: 'max-content' }" :expand-column-width="300" :expand-column-height="100">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'productId'">
                <a-select show-search style="width: 100%" placeholder="请选择销售产品" :options="productListData"
                  v-model:value="record.productId" @change="onProductList($event, record)" :field-names="{
                    label: 'productName',
                    value: 'productId',
                  }">
                </a-select>
              </template>
              <template v-if="column.dataIndex === 'classificationId'">
                {{ "暂无" }}
              </template>
              <template v-if="column.dataIndex === 'warehouseId'">
                <a-select show-search style="width: 100%" placeholder="请选择仓库" :options="dataSourceData"
                  v-model:value="record.warehouseId" :field-names="{
                    label: 'warehouseName',
                    value: 'warehouseId',
                  }" @change="onWarehouseList">
                </a-select>
              </template>
              <template v-if="column.dataIndex === 'quantityShipped'">
                <a-textarea style="width: 100%; height: 10px" v-model:value="record.quantityShipped" placeholder="请输入"
                  allow-clear />
              </template>
              <template v-if="column.dataIndex === 'remark'">
                <a-textarea style="width: 100%; height: 10px" v-model:value="record.remark" placeholder="请输入"
                  allow-clear />
              </template>
              <template v-if="column.dataIndex === 'operation'">
                <a-popconfirm title="确定是否删除" @confirm="onDelete(record)">
                  <mw-button danger v-if="formData.shippedProductList.length > 1">
                    删除
                  </mw-button>
                </a-popconfirm>
              </template>
            </template>
          </a-table>
        </a-col>
        <a-col :span="12">
          <a-form-item :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }" label="开票类型"
            name="orderId" required>
            <a-select placeholder="请选择开票类型" :options="postNoContractReviewListOptions" v-model:value="formData.orderId"
              optionFilterProp="name" @change="handleChange" :field-names="{
                label: 'orderName',
                value: 'id',
              }" show-search>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="本次开票金额" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }"
            required name="">
            <a-input v-model:value="formData.contractList.customerNo" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="开票日期" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.contractList.customerNo" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="发票类型" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.contractList.customerNo" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="发票号" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.contractList.customerNo" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="汇率" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.contractList.customerNo" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="备注" :label-col="{ span: 7, offset: 2 }" :wrapper-col="{ span: 17, offset: 1 }">
            <a-input v-model:value="formData.contractList.customerNo" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  reactive,
  getCurrentInstance,
  defineProps,
  defineEmits,
  watch,
} from "vue";
import { productDetail } from "@/api/basicData/product.js";
import { selectMenuListIgnoreNoStock } from "@/api/basicData/repository.js";
import {
  postShippingAdd,
  getSelectProductListByOrder,
  postContractReviewUpdate,
  getSelectUnshippedlist,
  getSaleOrderDetail,
} from "@/api/sales/index.js";
const productListData = ref([]);
const postNoContractReviewListOptions = ref([]);
const emit = defineEmits(["update:visible", "finish"]);

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
});

const { proxy } = getCurrentInstance();
const formRef = ref();
const submitLoading = ref(false);
const formData = reactive({
  relationName: undefined,
  relationPhone: undefined,
  contractId: undefined,
  orderName: undefined,
  contractList: {
    requirementDeliveryTime: undefined,
    customerNo: undefined,
    customerName: undefined,
    totalContractAmount: undefined,
  },
  remark: undefined,
  useCountry: undefined,
  foreignCountry: undefined,
  orderConfig: {},
  address: undefined,
  expressNo: undefined,
  shippedProductList: [],
  checkInformationFlag: undefined,
  deliveryNoteFlag: undefined,
  invoiceFlag: undefined,
  productSpecificationFlag: undefined,
  warningMarkFlag: undefined,
});
const dataSourceData = ref([]);
const columnsSaleInvoice = ref([
  {
    title: "关联-产品信息",
    dataIndex: "productId",
    width: "150px",
  },
  {
    title: "货物编号",
    dataIndex: "classificationName",
    width: "150px",
  },
  {
    title: "货物名称",
    dataIndex: "warehouseId",
    width: "150px",
  },
  {
    title: "规格型号",
    dataIndex: "quantityShipped",
    width: "150px",
  },
  {
    title: "开票数量",
    dataIndex: "remark",
    width: "150px",
  },
  {
    title: "开票金额",
    dataIndex: "remark",
    width: "150px",
  },
  {
    title: "税率",
    dataIndex: "remark",
    width: "150px",
  },
  {
    title: "无税金额",
    dataIndex: "remark",
    width: "150px",
  },
  {
    title: "税额",
    dataIndex: "remark",
    width: "150px",
  },
  {
    title: "本币金额",
    dataIndex: "remark",
    width: "150px",
  },
  {
    title: "本币税合计",
    dataIndex: "remark",
    width: "150px",
  },
  {
    title: "本币税额",
    dataIndex: "remark",
    width: "150px",
  },
  {
    title: "退货标志",
    dataIndex: "remark",
    width: "150px",
  },
  {
    title: "销售-数量",
    dataIndex: "remark",
    width: "150px",
  },
  {
    title: "销售单价",
    dataIndex: "remark",
    width: "150px",
  },
  {
    title: "销售金额",
    dataIndex: "remark",
    width: "150px",
  },
  {
    title: "退货数",
    dataIndex: "remark",
    width: "150px",
  },
]);
const rules = reactive({
  orderId: [
    {
      required: true,
      trigger: "blur",
      message: "请选择关联-销售订单",
    },
  ],

  checkInformationFlag: [
    {
      required: true,
      trigger: "blur",
      message: "请选择是否核对提货人信息",
    },
  ],
  deliveryNoteFlag: [
    {
      required: true,
      trigger: "blur",
      message: "请选择是否需要送货单",
    },
  ],

  invoiceFlag: [
    {
      required: true,
      trigger: "blur",
      message: "请选择是否需要发票",
    },
  ],
  productSpecificationFlag: [
    {
      required: true,
      trigger: "blur",
      message: "请选择是否需要产品说明书",
    },
  ],
  warningMarkFlag: [
    {
      required: true,
      trigger: "blur",
      message: "请选择是否需要警示标识",
    },
  ],
});

const changeContract = (e) => {
  // formData.relationContractId=''
};
function onClose() {
  emit("update:visible", false);
  formRef.value.resetFields();
  formData.relationName = undefined;
  (formData.relationPhone = undefined),
    (formData.contractId = undefined),
    (formData.orderName = undefined),
    (formData.contractList = {}),
    (formData.remark = undefined),
    (formData.useCountry = undefined),
    (formData.foreignCountry = undefined),
    (formData.orderConfig = {});
  formData.address = undefined;
  formData.expressNo = undefined;
  formData.shippedProductList = [];
  formData.checkInformationFlag = undefined;
  formData.deliveryNoteFlag = undefined;
  formData.invoiceFlag = undefined;
  formData.productSpecificationFlag = undefined;
  formData.warningMarkFlag = undefined;
}
// // 产品详情信息
const productDetailList = async (e, val) => {
  let res = await productDetail({ productNo: e });
};
// 关联-销售合同
const postNoContractReview = async () => {
  let res = await getSelectUnshippedlist();
  postNoContractReviewListOptions.value = res.data;
};
const handleChange = (e) => {
  getSelectProductByOrderList(e);
  getSaleOrderDetailList(e);
};
const getWarehousedetail = async (valid) => {
  let key = "FINISHED";
  let res = await selectMenuListIgnoreNoStock({
    warehouseType: key,
    materialId: valid,
  });
  dataSourceData.value = res.data;
};

const onProductList = (e, val) => {
  getWarehousedetail(e);
  productListData.value.forEach((item, index) => {
    if (item.productId == e) {
      val.classificationName = item.classificationName;
    }
  });
  // productDetailList(e,val
};

// onWarehouseList--仓库change
const onWarehouseList = () => { };

// 销售订单详情
const getSaleOrderDetailList = async (val) => {
  let res = await getSaleOrderDetail({ id: val });
  formData.contractList.requirementDeliveryTime =
    res.data.requirementDeliveryTime;
  formData.contractList.totalContractAmount = res.data.totalContractAmount;
  formData.contractList.customerNo = res.data.customerNo;
  formData.contractList.customerName = res.data.customerName;
  formData.relationName = res.data.bizBelongUserName;
  formData.relationPhone = res.data.phone;
  formData.customerId = res.data.customerId;
};
// 产品ing但详情
const getSelectProductByOrderList = async (val) => {
  let res = await getSelectProductListByOrder({ id: val });
  productListData.value = res.data;
};

// 产品明细添加
const handleAdd = () => {
  const newData = {
    id: Date.now(),
  };
  formData.shippedProductList.push(newData);
};
// 产品删除
const onDelete = (val) => {
  formData.shippedProductList.forEach((element, index) => {
    if (props.id) {
      if (element.productId == val.productId) {
        formData.shippedProductList.splice(index, 1);
      }
    } else {
      if (element.id == val.id) {
        formData.shippedProductList.splice(index, 1);
      }
    }
  });
};
// 确认新增
const formSubmit = async () => {
  formRef.value.validate().then(async () => {
    submitLoading.value = true;

    if (formData.shippedProductList.length < 1) {
      proxy.$message.error("明细信息不能为空");
      return;
    }

    formData.shippedProductList.forEach((item, index) => {
      delete item.id;
      delete item.classificationName;
    });
    let param = {
      ...formData,
    };
    delete formData.contractList;
    let res;
    if (props.id) {
      param.id = props.id;
      res = await postContractReviewUpdate(param);
    } else {
      res = await postShippingAdd(param);
    }
    if (res.code == 200) {
      proxy.$message.success("操作成功");
      onClose();
    }
    submitLoading.value = false;
    emit("finish");
  });
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      postNoContractReview();
      // postproductList()
    }
  }
);
</script>
<style lang="less" scoped>
:deep(.ant-form label) {
  padding: 10px;
}
</style>
