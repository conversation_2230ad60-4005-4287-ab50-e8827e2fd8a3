<template>
  <!-- <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="60%"
    customTitle="赠品"
    destroyOnClose="true"
  > -->
  <!-- <template #header>
    
  </template> -->
  <div class="flex" style="display: flex; justify-content: space-between">
    <mw-button @click="onAddGift" class="mb-3">新增</mw-button>
    <mw-button @click="formSubmit" :loading="submitLoading">提交</mw-button>
  </div>

  <mw-table
    :scroll="{ x: 'max-content' }"
    :columns="columns"
    :data-source="gifts"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'type'">
        <a-select
          style="width: 100px"
          placeholder="类型"
          :options="matProType"
          v-model:value="record.type"
          show-search
          @change="onType($event, record)"
        >
        </a-select>
      </template>
      <template v-if="column.key == 'referId'">
        <a-select
          style="width: 100px"
          placeholder="名称"
          :options="record.type == 'PRODUCT' ? productData : materialData"
          v-model:value="record.referId"
          optionFilterProp="materialName"
          :field-names="{
            label: record.type == 'MATERIAL' ? 'materialName' : 'productName',
            value: 'id',
          }"
          show-search
          @change="onMatProData(record)"
        >
        </a-select>
      </template>
      <template v-if="column.key == 'quantity'">
        <a-input-number
          v-model:value="record.quantity"
          :stringMode="true"
        ></a-input-number
      ></template>
      <template v-if="column.key == 'operate'">
        <mw-button @click="onGiftDetail(record)"> 删除 </mw-button>
      </template>
    </template>
  </mw-table>
  <!-- </mw-drawer> -->
</template>
<script setup>
import { ref, getCurrentInstance, defineProps, defineEmits } from "vue";
import { addGift } from "@/api/sales/index.js";
import { AllList } from "@/api/basicData/material.js";
import { list } from "@/api/basicData/product.js";
import { matProType } from "@/common/constant.js";

const emit = defineEmits(["update:visible", "finish"]);
const { proxy } = getCurrentInstance();
const submitLoading = ref(false);
const matProData = ref([]);
const gifts = ref([]);
const materialData = ref();
const productData = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
});
const columns = ref([
  {
    title: "类型",
    dataIndex: "type",
    key: "type",
  },
  {
    title: "赠品名称",
    dataIndex: "referId",
    key: "referId",
  },
  {
    title: "赠品规格",
    dataIndex: "specification",
    key: "specification",
  },
  {
    title: "赠品编码",
    dataIndex: "no",
    key: "no",
  },
  {
    title: "剩余库存数量",
    dataIndex: "total",
    key: "total",
  },
  {
    title: "数量",
    dataIndex: "quantity",
    key: "quantity",
  },

  {
    title: "操作",
    dataIndex: "operate",
    key: "operate",
  },
]);
function onClose() {
  emit("update:visible", false);
  gifts.value = [];
}
const onAddGift = () => {
  gifts.value.push({ giftId: Date.now() });
};
const onType = async (e, val) => {
  let res = [];
  if (e == "MATERIAL") {
    res = await AllList();
    materialData.value = res.data;
  } else {
    res = await list({ ignoreCancel: true });
    productData.value = res.data;
  }

  gifts.value.forEach((item, index) => {
    if (item.giftId == val.giftId) {
      item.no = undefined;
      item.specification = undefined;
      item.name = undefined;
      item.referId = undefined;
      item.total = undefined;
    }
  });
};
const onMatProData = (val) => {
  let item =
    val.type == "PRODUCT"
      ? productData.value.find((item) => {
          return item.id == val.referId;
        })
      : materialData.value.find((item) => {
          return item.id == val.referId;
        });
  val.specification = item.specification;
  val.no = item.productNo || item.materialNo;
  val.name = item.productName || item.materialName;
  val.total = item.total;
};
const formSubmit = async () => {
  let param = {
    marketOrderId: props.id,
    gifts: gifts.value,
  };
  let res = await addGift(param);
  if (res.code == 200) {
    proxy.$message.success("添加成功");
    emit("formSubmitDataSuccess", "2");
  }

  gifts.value = [];
};
const onGiftDetail = (val) => {
  // if
  gifts.value.forEach((item, index) => {
    if (item.giftId == val.giftId) {
      gifts.value.splice(index, 1);
    }
  });
};
</script>
<style lang="less" scoped>
:deep(.ant-form label) {
  padding: 10px;
}
</style>
