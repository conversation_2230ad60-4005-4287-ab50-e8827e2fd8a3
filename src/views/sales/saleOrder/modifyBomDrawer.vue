<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="30%"
    customTitle="选择BOM"
    destroyOnClose="true"
  >
    <template #header>
      <mw-button @click="formSubmit" :loading="submitLoading">确定</mw-button>
    </template>
    <a-select
      class="w-full"
      placeholder="请选择BOM"
      :options="bomList"
      v-model:value="bomID"
      @change="bomChange"
      optionFilterProp="bomName"
      :field-names="{
        label: 'bomName',
        value: 'bomNo',
      }"
      show-search
    ></a-select>
  </mw-drawer>
</template>
<script setup>
import { ref, getCurrentInstance, defineProps, defineEmits, watch } from "vue";
const emit = defineEmits(["update:visible", "finish"]);

import { getAllBomList } from "@/api/basicData/bom.js";
import { updateBom } from "@/api/sales/index.js";
const { proxy } = getCurrentInstance();
const submitLoading = ref(false);
const bomList = ref([]);
const bomID = ref(void 0);
const bomData = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  bomDetailData: {
    type: Object,
    default: {},
  },
  bomIndex: {
    type: String,
    default: "",
  },
});

function onClose() {
  emit("update:visible", false);

  bomID.value = void 0;
}
const getBomList = async () => {
  let res = await getAllBomList({ keyword: "", ignoreStatus: false });
  if (res.code == 200) {
    bomList.value = res.data;
  }
};
const bomChange = (e, item) => {
  console.log(e, item, "lll");
  bomData.value = item;
};
const formSubmit = async () => {
  console.log(props.bomDetailData.materialId, "-------");

  let param = {
    id: props.bomDetailData.materialId,
    bomId: bomData.value.id,
  };
  let res = await updateBom(param);
  console.log(res, "--==");
  if (res.code == 200) {
    proxy.$message.success("添加成功");

    emit("bomID-child", bomData.value, props.bomIndex);
    onClose();
  }
};
watch(
  () => props.visible,
  () => {
    getBomList();
  }
);
</script>
<style lang="less" scoped>
:deep(.ant-form label) {
  padding: 10px;
}
</style>
