<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="60%"
    customTitle="赠品"
    destroyOnClose="true"
  >
    <a-tabs v-model:activeKey="activeKey" @change="onSwitchLayouts">
      <a-tab-pane key="1" tab="赠品">
        <giftDrawer
          v-model:id="props.id"
          @formSubmitDataSuccess="formSubmitDataSuccess"
        ></giftDrawer>
      </a-tab-pane>
      <a-tab-pane key="2" tab="赠品记录">
        <giftRecord
          v-model:id="props.id"
          :data="giftRecordListData"
        ></giftRecord>
      </a-tab-pane>
    </a-tabs>
  </mw-drawer>
</template>
<script setup>
import { ref, getCurrentInstance, defineProps, defineEmits, watch } from "vue";
import giftDrawer from "./giftDrawer.vue";
import giftRecord from "./giftRecord.vue";
import { giftRecordList } from "@/api/sales/index.js";

const emit = defineEmits(["update:visible", "finish"]);
const activeKey = ref("1");
const giftRecordListData = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
});

function onClose() {
  emit("update:visible", false);
}
const onSwitchLayouts = async (e) => {
  if (e == 2) {
    let res = await giftRecordList({ marketOrderId: props.id });
    giftRecordListData.value = res.data;
  }
};
const formSubmitDataSuccess = async () => {
  activeKey.value = "2";
  let res = await giftRecordList({ marketOrderId: props.id });
  giftRecordListData.value = res.data;
};
watch(
  () => props.visible,
  async (val) => {
    // giftRecordList
  }
);
</script>
<style lang="less" scoped>
:deep(.ant-form label) {
  padding: 10px;
}
</style>
