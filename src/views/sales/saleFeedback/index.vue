<template>
  <div class="mb-4 text-right">
    <search :searchData="searchData" @search="refresh">
      <mw-button
        title="新增"
        :font="'iconfont icon-xianxing-121'"
        @click="onOpen('', 'add')"
      ></mw-button>
    </search>
  </div>
  <mw-table
    :scroll="{ x: 'max-content' }"
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'status'">
        <div v-if="record.status == '0'">已审核</div>
        <div v-if="record.status == '1'">财务已审核</div>
        <div v-if="record.status == '7'">待审批</div>
        <div v-if="record.status == '8'">审批通过</div>
        <div v-if="record.status == '9'">审批不通过</div>
        <div v-if="record.status == '10'">已撤销</div>
      </template>
      <template v-if="column.key == 'isContract'">
        {{ record.isContract == "1" ? "是" : "否" }}
      </template>
      <template v-if="column.key == 'payeeAmount'">
        {{ record.payeeAmount
        }}{{
          record.currencyType == "CNY"
            ? "人民币"
            : record.currencyType == "USD"
            ? "美元"
            : record.currencyType == "EUR"
            ? "欧元"
            : ""
        }}
      </template>

      <template v-if="column.key == 'payeeType'">
        <div v-if="record.payeeType == '1'">销售收款</div>
        <div v-if="record.payeeType == '2'">销售退款</div>
        <div v-if="record.payeeType == '3'">其他收款</div>
      </template>
      <template v-if="column.key == 'settlementType'">
        <div v-if="record.settlementType == '1'">现汇</div>
        <div v-if="record.settlementType == '2'">银行承兑</div>
        <div v-if="record.settlementType == '3'">现金</div>
        <div v-if="record.settlementType == '4'">转账</div>
        <div v-if="record.settlementType == '5'">其他</div>
        <div v-if="record.settlementType == '6'">商业承兑</div>
      </template>

      <template v-if="column.key == 'button'">
        <mw-button
          title="详情"
          @click="onOpen(record.id, 'detail')"
        ></mw-button>
        <a-button
          shape="round"
          type="primary"
          @click="onOpen(record.id, 'edit')"
          v-if="[9, 10].includes(record.status)"
          class="ml-2"
        >
          编辑
        </a-button>

        <!-- <a-popconfirm
        detailContractReviewDrawer
        title="确定是否删除"
        ok-text="是"
        cancel-text="否"
        @confirm="onConfirm(record)">
        <a-button type="primary" danger class="ml-2">
          删除
        </a-button>
      </a-popconfirm> -->
      </template>
    </template>
  </mw-table>
  <!-- 新增 -->
  <add-contract-review-drawer
    ref="refAddContractReviewDrawer"
    v-model:visible="visibleAddContractReviewDrawer"
    @finish="getList"
    v-model:id="contractReviewId"
    v-model:refundType="refundType"
  />
  <detail-contract-review-drawer
    ref="refVisibleDetail"
    v-model:visible="visibleDetail"
    v-model:id="contractReviewId"
  />
</template>
<script setup>
import Search from "@/components/search/index.vue";
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import { usePagenation } from "@/common/setup";
import addContractReviewDrawer from "./addSaleOrderDrawer.vue";
import { postReceiptPage } from "@/api/sales/index.js";
import { postCustomerPage } from "@/api/proSchedul/scheduling.js"; //客户信息
const postCustomerPageData = ref();
const refundType = ref();
const columns = ref([
  {
    title: "回款单单据编号",
    dataIndex: "receiptNumber",
    key: "receiptNumber",
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status",
  },
  {
    title: "客户",
    dataIndex: "customerName",
    key: "customerName",
  },
  {
    title: "合同",
    dataIndex: "contractName",
    key: "contractName",
  },

  {
    title: "到账信息  ",
    dataIndex: "accountInfo",
    key: "accountInfo",
  },
  {
    title: "收款金额",
    dataIndex: "payeeAmount",
    key: "payeeAmount",
  },

  {
    title: "收款日期",
    dataIndex: "payeeTime",
    key: "payeeTime",
  },
  {
    title: "收款类型",
    dataIndex: "payeeType",
    key: "payeeType",
  },

  {
    title: "结算方式",
    dataIndex: "settlementType",
    key: "settlementType",
  },
  {
    title: "收款金额大写",
    dataIndex: "payeeAmountUpper",
    key: "payeeAmountUpper",
  },
  {
    title: "其他",
    dataIndex: "other",
    key: "other",
  },
  {
    title: "操作",
    dataIndex: "button",
    key: "button",
    fixed: "right",
  },
]);

const searchData = ref({
  searchButtons: [],
  operationButtons: [],
  fields: {
    rangeDate: {
      type: "a-range-picker",
      value: [],
      valueFormat: "YYYY-MM-DD",
      width: "240px",
      placeholder: ["开始日期", "结束日期"],
    },
    customerId: {
      name: "客户",
      type: "a-select",
      dropdownMatcSelectWidth: true,
      showSearch: true,
      options: postCustomerPageData,
      placeholder: "选择客户",
      width: "220px",
      optionFilterProp: "customerName",
      fieldNames: {
        label: "customerName",
        value: "id",
      },
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: "请输入回款单号/合同号",
      width: "270px",
      allowClear: true,
    },
  },
});
const postCustomerPageList = async () => {
  let res = await postCustomerPage();
  postCustomerPageData.value = res.data;
};
// status: {deliveryNoteFlag  deliveryNoteFlag     deliveryNoteFlag
//       name: "产品分类",
//       type: "a-select",
//       options: [],
//       placeholder: "选择分类",
//       width: "120px",
//       fieldNames: {
//         label: "name",
//         value: "id",
//       },
//       value: "",
//     },
//   searchData.value.fields.status.options = [
//   {
//     name: "全部分类",
//     id: "",
//   },
//   ...statusList

// ];
const { proxy } = getCurrentInstance();
const visibleDetail = ref(false);
const visibleAddContractReviewDrawer = ref(false);

const contractReviewId = ref("");
const data = ref([]),
  loading = ref(false);
const refAddContractReviewDrawer = ref(null);
const getList = async () => {
  loading.value = true;
  //分页信息
  let searchParam = {};

  //搜索信息

  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }

  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await postReceiptPage({ ...pageParam.value }, searchParam);
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
  postCustomerPageList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

const onOpen = (valId, type) => {
  contractReviewId.value = valId;
  refundType.value = type;
  visibleAddContractReviewDrawer.value = true;
};
async function onConfirm(record) {
  getList();
}
</script>
<style lang="less" scoped>
:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  background: #fff;
}

:deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
  background: #fff;
}

:deep(.mw-table .ant-table) {
  overflow-x: auto;
}
</style>
