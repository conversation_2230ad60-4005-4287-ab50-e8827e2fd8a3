<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="60%"
    :custom-title="customTitle(props.btnType)"
    :destroyOnClose="true"
  >
    <template #header>
      <mw-button
        @click="formSubmit"
        :loading="loading"
        v-if="props.btnType !== 'detail'"
      >
        确定
      </mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      labelAlign="left"
      :label-col="{ span: 3, offset: 0 }"
    >
      <a-form-item label="销售订单" name="orderId" required>
        <div class="flex items-center">
          <div class="w-1/2 mr-5">
            <a-select
              :options="unReturnList"
              v-model:value="formData.orderId"
              optionFilterProp="orderNo"
              :placeholder="'销售订单'"
              show-search
              allowClear
              :field-names="{
                label: 'orderNo',
                value: 'orderId',
              }"
              @change="changeOrder"
              :disabled="props.btnType == 'add' ? false : true"
            ></a-select>
          </div>

          <div v-if="returnCustomerList">
            {{ returnCustomerList?.customerNo }}
            <span
              v-if="
                returnCustomerList?.customerNo &&
                returnCustomerList?.customerName
              "
              >-</span
            >
            {{ returnCustomerList?.customerName }}
          </div>
        </div>
      </a-form-item>

      <a-form-item
        required
        name="returnMaterialList"
        :rules="[{ required: true, message: '退货明细不能为空' }]"
      >
        <template v-slot:label>
          <div class="my-label">退货明细</div>
        </template>
        <mw-table
          :scroll="{ x: 'max-content' }"
          :columns="columns"
          :data-source="formData.returnMaterialList"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex == 'warehouse'">
              <a-form-item-rest>
                <a-select
                  show-search
                  style="width: 100%"
                  placeholder="请选择仓库"
                  :options="dataSourceData"
                  v-model:value="record.warehouseId"
                  optionFilterProp="label"
                  @change="onWarehouseList($event, record)"
                >
                </a-select>
              </a-form-item-rest>
            </template>
            <template v-if="column.dataIndex == 'warehouseArea'">
              <a-form-item-rest>
                <a-select
                  v-model:value="record.warehouseAreaId"
                  style="width: 100%"
                  placeholder="请选择货位"
                  :options="record.areaOption"
                  @change="areaChange($event, record)"
                />
              </a-form-item-rest>
            </template>
            <template v-if="column.dataIndex == 'returnNum'">
              <a-form-item-rest>
                <a-input-number
                  v-model:value="record.returnNum"
                  :min="0"
                  v-if="props.btnType !== 'detail'"
                  :stringMode="true"
                ></a-input-number>
              </a-form-item-rest>
            </template>
            <template v-if="column.dataIndex == 'returnNu'">
              {{ record.returnNum }}
            </template>
          </template>
        </mw-table>
      </a-form-item>
      <a-form-item label="备注" name="remark" required>
        <div class="w-1/2">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入备注信息"
            :maxLength="100"
            allow-clear
            :disabled="props.btnType == 'detail'"
          ></a-textarea>
        </div>
      </a-form-item>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  reactive,
  getCurrentInstance,
  defineProps,
  defineEmits,
  watch,
  computed,
} from "vue";
import {
  marketReturnAdd,
  selectUnReturn,
  returnGetCustomerByOrderId,
  marketReturnUpdate,
  marketReturnDetail,
} from "@/api/sales/index.js";
import { menuListCollect } from "@/api/inventoryAllocation.js";
import { returnNoStatus } from "@/common/constant.js";
import _cloneDeep from "lodash/cloneDeep";
import { getInfoRepository } from "@/api/basicData/material";
const emit = defineEmits(["update:visible", "finish"]);
const { proxy } = getCurrentInstance();
const formRef = ref();
const loading = ref(false);
const unReturnList = ref();
const returnCustomerList = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
  btnType: String,
  recordVal: Object,
});
const status = ref();
const customTitle = (openType) => {
  const foundStatus = returnNoStatus.find((item) => item.value == status.value);
  switch (openType) {
    case "add":
      return `新增退货`;
    case "edit":
      return (
        `编辑详情      ${" ".repeat(10)}` +
        formData.returnNo +
        " 状态：" +
        foundStatus?.label
      );
    case "detail":
      return `退货详情 ` + formData.returnNo + "  状态：" + foundStatus?.label;
    default:
      return ``;
  }
};
const formData = reactive({
  returnMaterialList: [],
});
const columnsReturn = ref([
  { title: "销售产品", dataIndex: "materialName" },
  { title: "可退货数量", dataIndex: "availableQuantity" },
  { title: "本次退货数量", dataIndex: "returnNum" },
  { title: "退货数量", dataIndex: "returnNu" },
]);
const columns = computed(() => {
  if (props.btnType == "detail") {
    return columnsReturn.value.filter(
      (item) =>
        item.dataIndex !== "availableQuantity" && item.dataIndex !== "returnNum"
    );
  } else if (props.btnType == "add") {
    //
    return [
      { title: "销售产品", dataIndex: "materialName" },
      { title: "选择仓库", dataIndex: "warehouse" },
      { title: "选择库位", dataIndex: "warehouseArea" },
      { title: "可退货数量", dataIndex: "availableQuantity" },
      { title: "本次退货数量", dataIndex: "returnNum" },
    ];
  } else {
    return columnsReturn.value.filter((item) => item.dataIndex !== "returnNu");
  }
});
// 关闭弹窗
function onClose() {
  formData.orderId = void 0;
  emit("update:visible", false);
  returnCustomerList.value = {};
  formRef.value.resetFields();
}
// 退货--销售订单列表
const selectUnReturnList = async () => {
  let res = await selectUnReturn();
  unReturnList.value = res.data;
};
//选择订单的change
const getWarehousedetail = async (id) => {
  let key = "FINISHED";
  let res = await menuListCollect(
    {
      // warehouseType: key,
      // warehouseTypes: key,
      // materialId: id,
    },
    { pageNum: 1, pageSize: 99999 }
  );
  return res.data;
};
const dataSourceData = ref([]);
const setWarehouseOptions = async () => {
  let res = await getWarehousedetail();
  dataSourceData.value = res.map((item) => {
    return {
      label: item.warehouseName,
      value: item.id,
    };
  });
};
const onWarehouseList = async (e, val) => {
  val.areaOption = [];
  let res = await getInfoRepository(e);
  val.areaOption = res.data.warehouseMenuDetailVoList.map((item) => {
    return {
      label: item.warehouseArea,
      value: item.id,
      inventory: item.inventory,
    };
  });
};
const areaChange = (val, record) => {
  console.log("[ val,record ] >", val, record);
};
const changeOrder = async (e, val) => {
  formData.returnMaterialList = val.returnDetail.map((item) => {
    item.returnNum = item.availableQuantity;
    return item;
  });
  changeCustomer(e, val);
};
//客户信息
const changeCustomer = async (e) => {
  let res = await returnGetCustomerByOrderId({ orderId: e });
  returnCustomerList.value = res.data;
};

// 提交
const formSubmit = async () => {
  let params = _cloneDeep(formData);

  // 过滤掉 returnNum <= 0 的数据，只保留有效的退货数据
  const validReturnItems = params.returnMaterialList.filter(
    (item) => item.returnNum > 0
  );

  if (validReturnItems.length === 0) {
    return proxy.$message.error("请至少输入一个有效的退货数量");
  }

  // 对有效数据进行验证
  if (validReturnItems.some((item) => !item.returnNum)) {
    return proxy.$message.error("退货数量不能为空");
  }
  if (
    validReturnItems.some((item) => item.returnNum > item.availableQuantity)
  ) {
    return proxy.$message.error("退货数量不能大于可退货数量");
  }

  // 只对有效的退货数据进行仓库和库位验证
  if (validReturnItems.some((item) => !item.warehouseId)) {
    return proxy.$message.error("请选择仓库");
  }
  if (validReturnItems.some((item) => !item.warehouseAreaId)) {
    return proxy.$message.error("请选择库位");
  }

  // 更新参数，只包含有效的退货数据
  params.returnMaterialList = validReturnItems.map((item) => {
    return {
      ...item,
      areaOption: undefined,
    };
  });

  console.log("[ params.returnMaterialList ] >", params.returnMaterialList);
  formRef.value.validate().then(async () => {
    try {
      loading.value = true;
      let res;
      if (props?.recordVal?.returnId) {
        params.returnId = props.recordVal.returnId;
        res = await marketReturnUpdate(params);
      } else {
        res = await marketReturnAdd(params);
      }

      if (res.code == 200) {
        onClose();
        emit("finish");
        proxy.$message.success("操作成功");
      }
      loading.value = false;
    } catch (error) {
      loading.value = false;
    }
  });
};
// 详情
const marketReturnDetailApi = async (id) => {
  let res = await marketReturnDetail({ returnId: id });
  status.value = res.data.status;
  Object.assign(formData, {
    orderId: res.data.orderId,
    returnMaterialList: res.data.returnDetailList.map((item) => {
      item.availableQuantity = item.availableNum;
      return item;
    }),
    returnNo: res.data.returnNo,
    remark: res.data.remark,
  });
  changeCustomer(res.data.orderId);
};

const getWarehouseList = async () => {
  // let res = await getWarehousedetail();
  // dataSourceData.value = res.data.data;
};
// marketReturnUpdate;
watch(
  () => props.visible,
  async (val) => {
    //销售订单列表

    selectUnReturnList();
    setWarehouseOptions();
    await getWarehouseList();
    if (props?.recordVal?.returnId) {
      marketReturnDetailApi(props.recordVal.returnId);
    } else {
      returnCustomerList.value = {};
      Object.assign(formData, {
        orderId: void 0,
        returnMaterialList: [],
        returnNo: void 0,
        remark: void 0,
      });
    }
  }
);
</script>

<style scoped></style>
