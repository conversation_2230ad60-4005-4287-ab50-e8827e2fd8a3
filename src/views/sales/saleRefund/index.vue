<template>
  <div class="mb-4 text-right">
    <search :searchData="searchData" @search="refresh">
      <mw-button :font="'iconfont icon-xianxing-121'" @click="onOpen('add')">
        新增
      </mw-button>
    </search>
  </div>
  <mw-table
    :scroll="{ x: 'max-content' }"
    :columns="columns"
    :data-source="dataList"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'status'">
        <dictionary
          :statusOptions="returnNoStatus"
          :value="record.status"
          isBackgroundColor
          :statusExtend="record.statusExtend"
        />
      </template>
      <template v-if="column.key == 'operation'">
        <mw-button @click="onOpen('detail', record)" class="mr-2">
          详情
        </mw-button>
        <mw-button
          @click="onOpen('edit', record)"
          v-if="record.status == 2 || record.status == 3"
          >编辑</mw-button
        >
      </template>
    </template>
  </mw-table>
  <sale-return-drawer
    ref="refInventoryCountDrawer"
    @finish="getList"
    v-model:visible="visibleInventoryCountDrawer"
    :btnType="btnType"
    :recordVal="recordVal"
  ></sale-return-drawer>
</template>
<script setup>
import { ref, onBeforeMount, getCurrentInstance } from "vue";
import Search from "@/components/search/index.vue";
import { usePagenation } from "@/common/setup";
import saleReturnDrawer from "./saleReturnDrawer.vue";

import { returnNoStatus } from "@/common/constant.js";
import { marketReturnPage } from "@/api/sales/index.js";
const loading = ref(false);
const btnType = ref();
const dataList = ref([]);
const recordVal = ref();
const visibleInventoryCountDrawer = ref(false);
const searchData = ref({
  fields: {
    status: {
      name: "产品分类",
      type: "a-select",
      placeholder: "选择分类",
      width: "120px",
      value: "",
      allowClear: true,
      options: [{ label: "全部", value: "" }, ...returnNoStatus],
    },
    keyword: {
      type: "a-input-search",
      placeholder: "请输入退货单号/退货订单/客户名称/客户编码",
      width: "340px",
    },
  },
});
const columns = ref([
  { title: "退货单号", dataIndex: "returnNo", key: "returnNo" },
  { title: "状态", dataIndex: "status", key: "status" },
  { title: "退货订单", dataIndex: "orderNo", key: "orderNo" },
  { title: "关联合同", dataIndex: "contractNo", key: "contractNo" },
  {
    title: "客户",
    dataIndex: "customerName",
    key: "customerName",
    customRender: ({ record }) => {
      return record.customerName;
    },
  },
  {
    title: "退货总数",
    dataIndex: "totalReturnQuantity",
    key: "totalReturnQuantity",
  },
  { title: "操作", dataIndex: "operation", key: "operation", fixed: "right" },
]);
onBeforeMount(async () => {
  await getList();
});
// 列表
const getList = async () => {
  loading.value = true;
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let result = await marketReturnPage({ ...pageParam.value, ...searchParam });
  dataList.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
//分页
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

//打开弹窗
const onOpen = (btnTypeVal, record) => {
  btnType.value = btnTypeVal;
  recordVal.value = record;
  visibleInventoryCountDrawer.value = true;
};
</script>

<style lang="scss" scoped></style>
