<template>
  <mw-drawer
    custom-title="详情"
    :visible="visible"
    @close="onClose"
    @spinning="spinning"
    width="70%"
  >
    <a-spin :spinning="loading">
      <!-- <div class="flex items-center">
        <span class="text-xl"
          >采购状态：<dictionary
            :showBadge="false"
            :statusOptions="purchaseStatus"
            :statusExtend="data.statusExtend"
            :value="data.status"
        /></span>
      </div> -->
      <div class="px-3 mb-4">
        <p class="text-base mb-4">
          开票信息
          <mw-button
            v-if="data.inStorageFile?.fileVisitUrl"
            @click.stop="openFile(data.inStorageFile?.fileVisitUrl)"
            >查看送货单</mw-button
          >
        </p>
        <a-descriptions
          :colon="false"
          size="small"
          :labelStyle="{
            color: 'rgba(34, 34, 34, 0.65)',
          }"
        >
          <a-descriptions-item label="供应商"
            ><div>
              <p>{{ data.supplierName || "-" }}</p>
              <!-- <p class="text-secondar-text">
              {{ data.relationName }}「{{ data.relationPhone }}」
            </p> -->
            </div></a-descriptions-item
          >
          <a-descriptions-item label="收款名称">{{
            data.payee || "-"
          }}</a-descriptions-item>
          <a-descriptions-item label="纳税人识别号">{{
            data.taxpayerIdentityNumber || "-"
          }}</a-descriptions-item>
          <a-descriptions-item label="开户行">
            {{ data.openingBank || "-" }}
          </a-descriptions-item>
          <a-descriptions-item label="银行账号">
            {{ data.bankAccount || "-" }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
      <mw-table
        :columns="columns"
        :data-source="data.materialDetailVOList"
        :rowKey="(record) => record.id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'info'">
            <p>{{ record.materialName }}</p>
            <p class="text-secondar-text">
              规格：{{ record.materialSpecification }}
            </p>
          </template>
          <template v-if="column.dataIndex == 'purchaseUnitPrice'"
            ><div class="">{{ record.purchaseUnitPrice }}</div></template
          >
          <template v-if="column.dataIndex == 'purchaseMaterialTotalPrice'"
            ><div class="">
              {{ record.purchaseMaterialTotalPrice }}
            </div></template
          >
          <template v-if="column.dataIndex == 'purchaseMaterialCount'"
            ><div class="">{{ record.purchaseMaterialCount }}</div></template
          >
          <template v-if="column.dataIndex == 'materialAccountsPayable'"
            ><div class="">
              {{ record.materialAccountsPayable }}
            </div></template
          >
          <template v-if="column.dataIndex == 'materialAccountsActual'"
            ><div class="">{{ record.materialAccountsActual }}</div></template
          >

          <template v-if="column.key == 'qualify'">
            <div class="">
              <span class="align-middle"> {{ record.qualifiedCount }}</span
              ><span class="text-ea0c28 align-middle"
                >/{{ record.unqualifiedCount }}</span
              >
            </div>
          </template>
          <template v-if="column.key == 'stored'">
            <p>{{ record.beStoredCount }}</p>
            <a @click="showRecordDrawer(record)" style="color: #959ec3"
              >查看到货记录</a
            >
          </template>
        </template>
      </mw-table>
      <a-form :model="formState" name="basic" :wrapper-col="{ span: 16 }">
        <a-row class="mt-3">
          <a-col :span="8">
            <a-form-item
              label="金额"
              name="invoiceAmount"
              :rules="[{ required: true, message: '请输入金额!' }]"
            >
              <a-input
                v-model:value="formState.invoiceAmount"
                placeholder="金额"
                :disabled="data.status == 1"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              label="税率"
              name="taxRate"
              :rules="[{ required: true, message: '请输入税率!' }]"
            >
              <a-input
                v-model:value="formState.taxRate"
                placeholder="税率"
                :disabled="data.status == 1"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              label="发票号"
              name="invoiceNumber"
              :rules="[{ required: true, message: '请输入发票号!' }]"
            >
              <a-input
                v-model:value="formState.invoiceNumber"
                placeholder="发票号"
                :disabled="data.status == 1"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="发票凭证">
              <form-upload
                class="ml-2"
                v-model:value="invoiceFiles"
                sence="settlementInvoice"
                title="上传发票"
                :fileTypes="[]"
                :fileSize="100"
                hasDownLoad
              ></form-upload>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              label="开票时间"
              name="billingTime"
              :rules="[{ required: true, message: '请输入发票号!' }]"
            >
              <a-date-picker
                class="ml-5"
                placeholder="开票时间"
                :disabled="data.status == 1"
                v-model:value="formState.billingTime"
                style="width: 80%"
                valueFormat="YYYY-MM-DD"
              />
              <!-- <span v-else>{{ formState.billingTime }}</span> -->
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <!-- <p class="my-4">发票凭证</p> -->

      <mw-button
        title=" 提交结算"
        v-if="data.status == 0"
        class="mt-2"
        :loading="submitLoading"
        @click="submit"
      ></mw-button>
    </a-spin>
  </mw-drawer>
</template>
<script setup>
import {
  onBeforeMount,
  reactive,
  ref,
  getCurrentInstance,
  computed,
  watch,
  defineEmits,
  defineProps,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import { getInfo, uploadInvoiceFile } from "@/api/settlement.js";
import { purchaseStatus } from "@/common/constant.js";
import RecordDrawer from "./components/recordDrawer.vue";
import { getAllUser } from "@/api/system/user.js";
import FormUpload from "@/components/form-upload.vue";
const emit = defineEmits(["update:visible", "finish"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
});
const columns = ref([
  {
    title: "物料名称",
    dataIndex: "materialName",
  },
  {
    title: "物料编码",
    dataIndex: "materialNo",
  },
  {
    title: "物料规格",
    dataIndex: "materialSpecification",
  },
  {
    title: "采购单价(元)",
    dataIndex: "purchaseUnitPrice",
  },
  {
    title: "入仓数量",
    dataIndex: "beStoredCount",
  },
  {
    title: "入仓应付(元)",
    dataIndex: "payable",
  },
]);
const { proxy } = getCurrentInstance();

const route = useRoute();
const id = ref(""),
  loading = ref(false),
  data = ref({}),
  invoiceFiles = ref([]),
  submitLoading = ref(false);
const formState = reactive({
  invoiceAmount: undefined,
  taxRate: undefined,
  invoiceNumber: undefined,
  billingTime: undefined,
});
const openFile = (url) => {
  window.open(url, "_blank");
};
const getDetail = async () => {
  loading.value = true;
  let result = await getInfo(id.value);
  data.value = result.data;
  invoiceFiles.value = result.data.invoiceFile ? [data.value.invoiceFile] : [];
  loading.value = false;
};
const orderReadonly = computed(() => {
  return data.value.status != 0;
});
// onBeforeMount(async () => {
//   id.value = route.query.id;
//   await getDetail();
// });
const submit = async () => {
  try {
    submitLoading.value = true;
    if (!invoiceFiles.value?.length) {
      throw new Error("请上传发票");
    }
    let { settlementId } = data.value;
    let param = {
      id: settlementId,
      invoiceFile: invoiceFiles.value[0],
      ...formState,
    };
    let res = await uploadInvoiceFile(param);
    if (res.code === 200) {
      proxy.$message.success("提交结算成功");
      onClose();
    }
    await getDetail();
    submitLoading.value = false;
  } catch (error) {
    error.message && proxy.$message.warning(error.message);
    submitLoading.value = false;
  }
};
const onClose = () => {
  emit("update:visible", false);
};
watch(
  () => props.visible,
  async (val) => {
    id.value = props.id;
    await getDetail();
  }
);
</script>
<style lang="less" scoped>
:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  // background: transparent;
}
:deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
  // background: transparent;
}
:deep(.ant-table-cell) {
  vertical-align: top;
}
:deep(.ant-steps-icon) {
  vertical-align: middle !important;
  font-size: 0 !important;
  .iconfont {
    font-size: 20px !important;
  }
}
</style>
