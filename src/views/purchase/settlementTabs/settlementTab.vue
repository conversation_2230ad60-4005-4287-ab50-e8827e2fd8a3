<template>
  <mw-drawer
    custom-title=""
    :visible="visible"
    @close="onClose"
    @spinning="spinning"
    :closable="false"
    width="70%"
    destroyOnClose="true"
    :footer-style="{ textAlign: 'right' }"
  >
    <template #header>
      <mw-button
        @click="exportOrg"
        :loading="exportLoading"
        v-permission="'purchase:settlement:infoExportExcel'"
        >导出</mw-button
      >
    </template>
    <a-tabs v-model:activeKey="activeKey" @tabClick="getTabName" animated>
      <a-tab-pane key="1" tab="采购结算">
        <purchaseBalanceDetail
          :id="idValue"
          :data="purchaseBalanceDetailData"
          :isBeginningSwitch="isBeginningSwitch"
        ></purchaseBalanceDetail>
      </a-tab-pane>
      <a-tab-pane key="2" tab="审批" force-render>
        <examineDetail :id="idValue" :data="examineDetailData"></examineDetail>
      </a-tab-pane>
    </a-tabs>
  </mw-drawer>
</template>

<script setup>
import { ref, watch, defineEmits, defineProps } from "vue";
import examineDetail from "./examineDetail.vue";
import purchaseBalanceDetail from "./purchaseBalanceDetail.vue";
import { oaDetail, getDetail, setTleInfoExportExce } from "@/api/settlement.js";
import { exportExecl } from "@/utils/util.js";
const emit = defineEmits(["update:visible", "finish"]);
const activeKey = ref("1");
const exportLoading = ref();
const examineDetailData = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
  detailType: String,
  isBeginningSwitch: String,
});
const idValue = ref("");
const purchaseBalanceDetailData = ref();
const onClose = () => {
  emit("update:visible", false);
  activeKey.value = "1";
};
const getTabName = async (data) => {
  let param = { settlementId: props.id };
  let res;
  if (data == 2) {
    res = await oaDetail(param);
    examineDetailData.value = res.data;
  } else if (data == 1) {
    res = await getDetail(param);
    purchaseBalanceDetailData.value = res.data;
  }
};
const exportOrg = async () => {
  try {
    exportLoading.value = true;
    let result = await setTleInfoExportExce({ settlementId: props.id });
    const fileName = "采购结算.xlsx";
    exportExecl(fileName, result);
    exportLoading.value = false;
  } catch (err) {
    exportLoading.value = false;
  }
};
watch(
  () => props.visible,
  async (val) => {
    idValue.value = props.id;

    if (activeKey.value == 1) {
      let res = await getDetail({ settlementId: props.id });
      purchaseBalanceDetailData.value = res.data;
    }
  }
);
</script>

<style></style>
