<template>
  <div>
    <div class="w-full flex mb-5">
      <p class="w-1/3">
        开具发票类型：{{
          props.data?.issueInvoiceType == 2
            ? "红字发票"
            : props.data?.issueInvoiceType == 1
            ? "蓝字发票"
            : "---"
        }}
      </p>
      <p class="w-1/2">供应商：{{ props.data?.supplierName }}</p>
      <p class="w-1/3">结算单号：{{ props.data?.settlementNumber || "--" }}</p>
    </div>
    <div>
      <p class="w-1/3 mb-5">
        <span v-if="data?.status || data?.status == 0">
          结算状态：<dictionary
            :statusOptions="settlementStatus"
            :value="data?.status"
            isBackgroundColor
        /></span>
        <span v-else>结算状态：--</span>
      </p>
    </div>

    <mw-table
      :columns="isBeginningSwitch == '1' ? columnsFirstTime : columns"
      :data-source="data?.materialDetailVOList"
      :loading="loading"
      :rowKey="(record) => record.id"
      hasPage
      :scroll="{ x: 'max-content' }"
      @change="onTableChange"
      :pageConfig="paginationProps"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'materialName'">
          <div v-if="isBeginningSwitch == '1'">
            {{ record.materialNo }} / {{ record.materialName }} /
            {{ record.materialSpecification }}
          </div>
        </template>

        <template v-if="column.dataIndex == 'file'">
          <!-- <a :href="record.file.fileVisitUrl"></a> -->
          <div v-if="record.file?.fileVisitUrl">
            <i
              class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
              style="color: #959ec3"
            ></i>
            <a
              :href="record.file?.fileVisitUrl"
              :title="record.file?.fileName"
              target="_blank"
              class="underline"
              style="color: #959ec3"
              >{{ record.file?.fileName }}
            </a>
          </div>
        </template>
      </template>
    </mw-table>
    <div class="w-full flex mt-5">
      <p class="w-1/4">产品总数：{{ data?.materialCount || "--" }}</p>
      <p class="w-1/4">开票金额：{{ data?.invoiceAmount || "--" }}</p>
      <p class="w-1/4">税率：{{ data?.taxRate || "--" }}</p>
      <p class="w-1/4">发票号：{{ data?.invoiceNumber || "--" }}</p>
    </div>
    <div class="w-full flex mt-5">
      <p class="w-1/4">
        发票类型：{{
          data?.invoiceType == 1
            ? "增值税专用发票"
            : data?.invoiceType == 2
            ? "普通发票"
            : data?.invoiceType == 3
            ? "其他"
            : "" || "--"
        }}
      </p>
      <!-- <p class="w-1/4">业务类型：{{ data?.businessType || "--" }}</p>
      <p class="w-1/4">采购类型：{{ data?.purchaseType || "--" }}</p> -->
      <p class="w-1/8">开票日期：{{ data?.billingTime || "--" }}</p>
    </div>
  </div>
</template>

<script setup>
import { usePagenation } from "@/common/setup";
import {
  purchaseStatusList,
  issueInvoiceTypeList,
  settlementStatus,
} from "@/common/constant.js";
import {
  onBeforeMount,
  reactive,
  ref,
  getCurrentInstance,
  computed,
  watch,
  defineEmits,
  defineProps,
} from "vue";
import { getPurchaseSettlement } from "@/api/purchase/order.js";
const props = defineProps({
  id: String,
  data: Object,
  isBeginningSwitch: String,
});
const { paginationProps, onTableChange, refresh, pageParam } = usePagenation();
// const data = ref();
const loading = ref(false);
const columns = ref([
  { title: "物料名称", dataIndex: "materialName" },
  { title: "物料编号", dataIndex: "materialNo" },
  { title: "物料规格", dataIndex: "materialSpecification" },
  { title: "结算数", dataIndex: "settlementCount" },
  { title: "结算单价", dataIndex: "settlementPrice" },
  { title: "税率", dataIndex: "taxRate" },
  { title: "总金额", dataIndex: "settlementAmount" },
]);
const columnsFirstTime = ref([
  { title: "入库单号", dataIndex: "batchId" },
  { title: "物料信息", dataIndex: "materialName" },
  // { title: "可开票数量", dataIndex: "unSettlementCount" },
  { title: "本次开票数量", dataIndex: "settlementCount" },
  { title: "含税单价", dataIndex: "settlementPrice" },
  { title: "含税金额", dataIndex: "settlementAmount" },
  { title: "税率", dataIndex: "taxRate" },
  { title: "未税单价", dataIndex: "untaxedPrice" },
  { title: "未税金额", dataIndex: "untaxedAmount" },
]);
</script>

<style></style>
