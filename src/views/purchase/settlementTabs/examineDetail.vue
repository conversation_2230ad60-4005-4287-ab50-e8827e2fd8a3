<template>
  <mw-table
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    :scroll="{ x: 'max-content' }"
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex == 'type'">
        <!-- isBackgroundColor -->
        <dictionary
          :statusOptions="oaExamineType"
          :value="record.type"
          :statusExtend="record.type"
        />
      </template>
      <template v-if="column.dataIndex == 'userNames'">
        {{ record?.userNames ? record?.userNames.join() : "" }}
      </template>
      <template v-if="column.dataIndex == 'status'">
        <!-- isBackgroundColor  -->
        <dictionary
          :statusOptions="examineAndApproveStatus"
          :value="record.status"
        />
      </template>
    </template>
  </mw-table>
</template>

<script setup>
import {
  onBeforeMount,
  reactive,
  ref,
  getCurrentInstance,
  computed,
  watch,
  defineEmits,
  defineProps,
  onMounted,
} from "vue";
import { usePagenation } from "@/common/setup";
import {
  oaDetail,
  getContractInfo,
  getMarketOrder,
  getPurchaseSettlement,
} from "@/api/purchase/order.js";
import { oaExamineType, examineAndApproveStatus } from "@/common/constant.js";
const props = defineProps({
  id: String,
  data: Object || Array,
});
// const data = ref();
const loading = ref(false);
const columns = ref([
  { title: "流程节点", dataIndex: "type" },
  { title: "部门", dataIndex: "deptName" },
  { title: "操作人/审批人", dataIndex: "userNames" },
  { title: "状态", dataIndex: "status" },
  { title: "时间", dataIndex: "date" },
]);

const { paginationProps, onTableChange, refresh, pageParam } = usePagenation();
// const requestOaDetail = async () => {
//   let res = await oaDetail({ purchaseId: props.id });
//   data.value = res.data;
// };

// onBeforeMount(async () => {
//   await requestOaDetail();
// });
watch(
  () => props,
  async (val) => {}
);
</script>

<style></style>
