<template>
  <mw-table
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    :scroll="{ x: 'max-content' }"
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex == 'type'">
        <dictionary
          :statusOptions="oaExamineType"
          :value="record.type"
          :statusExtend="record.type"
          isBackgroundColor
        />
      </template>
      <template v-if="column.dataIndex == 'userNames'">
        {{ record?.userNames.join() }}
      </template>
    </template>
  </mw-table>
</template>

<script setup>
import { ref, watch, defineProps } from "vue";
import { usePagenation } from "@/common/setup";
import { oaExamineType } from "@/common/constant.js";
const props = defineProps({
  id: String,
  data: Object || Array,
});
const loading = ref(false);
const columns = ref([
  { title: "流程节点", dataIndex: "type" },
  { title: "部门", dataIndex: "deptName" },
  { title: "操作人/审批人", dataIndex: "userNames" },
  { title: "状态", dataIndex: "status" },
  { title: "时间", dataIndex: "date" },
]);

const { paginationProps, onTableChange, refresh, pageParam } = usePagenation();

watch(
  () => props,
  async (val) => {}
);
</script>

<style></style>
