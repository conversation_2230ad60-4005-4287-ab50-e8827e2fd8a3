<template>
  <div>
    <p class="pb-5">合同编号：{{ data?.contractNo || "--" }}</p>
    <p>
      合同状态：<dictionary
        v-if="data?.status"
        :statusOptions="statusList"
        :value="data?.status"
        isBackgroundColor
      />
      <span v-else>{{ "--" }}</span>
    </p>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, defineProps } from "vue";
import { getContractInfo } from "@/api/purchase/order.js";
import { statusList } from "@/common/constant.js";

const props = defineProps({
  id: String,
  data: Object,
});
// const data = ref();
// const statusList = ref([
//   {
//     label: "待下单",
//     value: "0",
//   },
//   {
//     label: "已下单",
//     value: "1",
//   },
//   {
//     label: "待审批",
//     value: "7",
//   },
//   {
//     label: "审批通过",
//     value: "8",
//   },
//   {
//     label: "审批不通过",
//     value: "9",
//   },
//   {
//     name: "审批撤销",
//     value: "10",
//   },
// ]);
// const requestGetContractInfo = async () => {
//   let res = await getContractInfo({ purchaseId: props.id });
//   data.value = res.data;
// };

// onBeforeMount(async () => {
//   await requestGetContractInfo();
// });
</script>

<style></style>
