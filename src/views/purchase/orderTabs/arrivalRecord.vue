<template>
  <mw-drawer
    :custom-title="customTitle(props.lockingStatus)"
    :visible="visible"
    @close="onClose"
    @spinning="spinning"
    destroyOnClose="true"
    :closable="false"
    width="80%"
  >
    <template #header>
      <mw-button
        v-if="lockingStatus == 1"
        :loading="saveLoading"
        @click="onSave"
        >保存</mw-button
      >
      <mw-button
        v-if="lockingStatus == 1"
        @click="onPriceLocking"
        :loading="submitLoading"
        >保存并锁定</mw-button
      >
    </template>
    <div v-for="(item, index) in detailList" :key="index">
      <div class="mb-2 mt-5">
        <b>到货时间：{{ item.createTime }}</b>
        <b class="ml-5">创建人：{{ item.createName }}</b>
        <b class="ml-5">到货单编码：{{ item.recordNumber }}</b>
        <b
          class="ml-5 text-primary testRemark"
          @click="onDeliveryRemarks('到货备注', item)"
          >备注：{{ item.arrivalRemark || "--" }}</b
        >
      </div>

      <mw-table
        :data-source="item.details"
        :columns="visibleColumns"
        :scroll="{ x: 'max-content' }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'qualityTestingCount'">
            <a-input-number
              v-if="lockingStatus == 1"
              style="width: 100%"
              v-model:value="record.qualityTestingCount"
              @change="(e) => qualityChange(e, record)"
              :formatter="formatter6"
              :stringMode="true"
            ></a-input-number>
          </template>
          <template v-if="column.key == 'arrivalTotalPrice'">
            <a-input-number
              v-if="lockingStatus == 1"
              style="width: 100%"
              @change="(e) => arrivalTotalPriceChange(e, record)"
              v-model:value="record.arrivalTotalPrice"
              :formatter="formatter6"
              :stringMode="true"
            ></a-input-number>
          </template>
          <template v-if="column.key == 'price'">
            <a-input-number
              v-if="lockingStatus == 1"
              style="width: 100%"
              v-model:value="record.price"
              @change="(e) => arrivalChange(e, record)"
              :formatter="formatter6"
              :stringMode="true"
            ></a-input-number>
          </template>
          <template v-if="column.key == 'taxRate'">
            <!-- <a-input-number
              v-if="lockingStatus == 1"
              style="width: 100%"
              v-model:value="record.taxRate"
              :formatter="formatter6"
              :stringMode="true"
            ></a-input-number>
            ></a-input-number> -->

            <a-select
              v-if="lockingStatus == 1"
              class="w-full"
              v-model:value="record.taxRate"
              :options="taxRateList"
              placeholder="请选择税率"
            />
          </template>
          <template v-if="column.key == 'qualifiedOrNot'">
            {{
              (record.qualifiedCount = countAlgorithm(record.qualifiedCount))
            }}/{{ record.unqualifiedCount }}
          </template>

          <template v-if="column.key == 'materialRemark'">
            <a-input
              v-if="lockingStatus == 1"
              v-model:value="record.materialRemark"
              placeholder="请输入物料备注"
            ></a-input>
          </template>
        </template>
      </mw-table>
    </div>
  </mw-drawer>
  <PublicRemarks
    v-model:visible="publicRemarksVisible"
    :publicRemarksTitle="publicRemarksTitle"
    :remarkRecord="remarkRecord"
    @finish="getList"
  />
</template>

<script setup>
import {
  ref,
  getCurrentInstance,
  computed,
  watch,
  defineEmits,
  defineProps,
} from "vue";
import { formatter6 } from "@/common/validate.js";
const emit = defineEmits(["update:visible", "finish", "success"]);
const { proxy } = getCurrentInstance();
import { selectMaterialArrivalRecordByOrderIdList } from "@/api/purchase/order.js";
import { updateMaterialArrivalRecord } from "@/api/purchase/order.js";
import PublicRemarks from "@/components/publicRemarks.vue";
import {
  selectMaterialArrivalRecordByRecordNumber,
  arrivalRecordLocking,
} from "@/api/purchase/arrivalRecord.js";
import { taxRateList } from "@/common/constant.js";
const publicRemarksTitle = ref();
const publicRemarksVisible = ref(false);
const saveLoading = ref();
const submitLoading = ref();
const remarkRecord = ref({});
const detailList = ref([]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
  openType: String,
  lockingStatus: Number,
});
// 科学计数法
const countAlgorithm = (num) => {
  let result = String(num);
  if (result.indexOf("-") >= 0 && !result.includes("e")) {
    return result;
  }
  if (result.indexOf("-") >= 0) {
    result = "0" + String(Number(result) + 1).substr(1);
  }
  return result;
};

const customTitle = (openType) => {
  switch (openType) {
    case 1:
      return `到货记录   未锁定`;
    case 2:
      return `到货记录   审核中`;
    case 3:
      return `到货记录   已锁定`;
    default:
      return `到货记录 `;
  }
};
const columns = ref([
  {
    title: "物料编码",
    dataIndex: "materialNo",
    key: "materialNo",
  },
  {
    title: "物料名称",
    dataIndex: "materialName",
    key: "materialName",
  },
  {
    title: "物料规格",
    dataIndex: "specification",
    key: "specification",
  },
  {
    title: "到货数量",
    dataIndex: "qualityTestingCount",
    key: "qualityTestingCount",
    width: 107,
  },
  {
    title: "含税单价",
    dataIndex: "price",
    key: "price",
    width: 107,
  },
  {
    title: "税率",
    dataIndex: "taxRate",
    key: "taxRate",
    width: 107,
  },
  {
    title: "总价",
    dataIndex: "arrivalTotalPrice",
    key: "arrivalTotalPrice",
    width: 107,
  },
  {
    title: "质检合格/不合格",
    dataIndex: "qualifiedOrNot",
    key: "qualifiedOrNot",
  },
  {
    title: "已入库",
    dataIndex: "beStoredCount",
    key: "beStoredCount",
  },
  {
    title: "物料备注",
    dataIndex: "materialRemark",
    key: "materialRemark",
  },
]);
const rules = ref({
  supplierId: [
    {
      required: true,
      message: "供应商",
      trigger: "blur",
    },
  ],
});
const visibleColumns = computed(() => {
  if (props.lockingStatus == 1) {
    return columns.value; // 如果满足条件，返回所有列
  } else {
    return columns.value.filter((item) => item.dataIndex !== "operate");
  }
});
const onClose = () => {
  emit("update:visible", false);
  emit("finish");
};
const getList = async () => {
  if (props.openType == "arrivalRecordTrue") {
    let res = await selectMaterialArrivalRecordByRecordNumber({
      recordNumber: props.id,
    });
    detailList.value = [res.data];
  } else {
    let res = await selectMaterialArrivalRecordByOrderIdList({
      orderId: props.id,
    });
    detailList.value = res.data;
  }
};
const onSave = async (value) => {
  try {
    let params = {
      updateParamList: detailList.value[0].details,
    };
    saveLoading.value = true;
    let res = await updateMaterialArrivalRecord(params);
    if (res.code == 200) {
      proxy.$message.success("保存成功");
      emit("success");
      onClose();
    }
    saveLoading.value = false;
  } catch (err) {
    saveLoading.value = false;
  }
};

const qualityChange = (e, row) => {
  const allNum = (e ? e : 0) * (row.price ? row.price : 0);
  row.arrivalTotalPrice = allNum;
};

const arrivalChange = (e, row) => {
  const allNum =
    (e ? e : 0) * (row.qualityTestingCount ? row.qualityTestingCount : 0);
  row.arrivalTotalPrice = allNum;
};
const arrivalTotalPriceChange = (e, row) => {
  // const allNum = (e?e:0) * (row.qualityTestingCount ? row.qualityTestingCount:0)
  // row.arrivalTotalPrice = allNum
  row.price =
    (e ? e : 0) / (row.qualityTestingCount ? row.qualityTestingCount : 0);
};
// 到货备注：
const onDeliveryRemarks = (title, val) => {
  publicRemarksVisible.value = true;
  publicRemarksTitle.value = title;
  remarkRecord.value = val;
};
// 价格锁定
const onPriceLocking = async () => {
  try {
    let params = {
      updateParamList: detailList.value[0].details,
    };
    submitLoading.value = true;
    let saveRes = await updateMaterialArrivalRecord(params);

    if (saveRes.code == 200) {
      let res = await arrivalRecordLocking({ recordNumber: props.id });
      if (res.code == 200) {
        submitLoading.value = false;
        getList();
        onClose();
        emit("success");
        proxy.$message.success("锁定成功");
      } else {
        submitLoading.value = false;
      }
    } else {
      submitLoading.value = false;
    }
  } catch (err) {
    submitLoading.value = false;
  }
};

watch(
  () => props.visible,
  async (val) => {
    if (val) {
      getList();
    }
  },
  { deep: true }
);
</script>

<style>
.testRemark {
  display: inline-block;
  white-space: nowrap;
  width: 30%;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  margin-bottom: -3px;
}
</style>
