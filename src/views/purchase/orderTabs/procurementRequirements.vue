<template>
  <div>
    <mw-table :columns="columns" :data-source="data" :loading="loading" :rowKey="(record) => record.id" hasPage
      :scroll="{ x: 'max-content' }" :pageConfig="paginationProps">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key == 'status'">
          <!-- 0-待生成订单，1-已生成订单，2-已取消，7-待审批，8-审批通过，9-审批不通过，10-已撤销 -->
          <div class="" v-if="record.status == 0">待生成订单</div>
          <div class="" v-if="record.status == 1">已生成订单</div>
          <div class="" v-if="record.status == 2">已取消</div>
          <div class="" v-if="record.status == 7">待审批</div>
          <div class="" v-if="record.status == 8">审批通过</div>
          <div class="" v-if="record.status == 9">审批不通过</div>
          <div class="" v-if="record.status == 10">已撤销</div>
        </template>
        <template v-if="column.key == 'type'">
          <div class="">{{ getNames(record.type) }}
            <template v-if="record.type == 'MARKET_ORDER' || record.type == 'UPDATE_ORDER_BOM'"> ({{ record.referNo
              }})</template>
          </div>
        </template>
      </template>
    </mw-table>
  </div>
</template>

<script setup>
import { ref, reactive, defineProps } from "vue";
import { statusList, purchaseOrderType } from "@/common/constant.js";

const demandData = ref([{}]);
const loading = ref(false);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
  data: Object || Array,
});
const columns = ref([
  {
    title: "需求编号",
    key: "requireNumber",
    dataIndex: "requireNumber",
  },
  {
    title: "需求类型",
    key: "type",
    dataIndex: "type",
  },

  {
    title: "供应商",
    key: "supplierName",
    dataIndex: "supplierName",
  },
  {
    title: "物料名称",
    key: "materialName",
    dataIndex: "materialName",
  },
  {
    title: "物料编号",
    key: "materialNo",
    dataIndex: "materialNo",
  },
  {
    title: "物料数量",
    key: "purchaseCount",
    dataIndex: "purchaseCount",
  },
  // {
  //   title: "单价（估价）",
  //   key: "status",
  //   dataIndex: "status",
  // },
  // {
  //   title: "总价（估价）",
  //   key: "createTime",
  //   dataIndex: "createTime",
  // },
  {
    title: "状态",
    key: "status",
    dataIndex: "status",
  },
]);
const getNames = (val) => {
  if (!val) return '-'
  return purchaseOrderType.find((item) => item.value == val).label
}
</script>

<style></style>
