<template>
  <div>
    <p class="pb-5">销售订单名称：{{ data?.orderName || "--" }}</p>
    <p class="pb-5">销售单号：{{ data?.orderNo || "--" }}</p>

    <p class="pb-5" v-if="data?.status">
      订单状态：
      <dictionary
        :statusOptions="orderStatus"
        :value="data?.status"
        :statusExtend="data?.status"
        isBackgroundColor
      />
    </p>
    <p v-else class="pb-5">订单状态：--</p>
    <mw-table
      :columns="columns"
      :data-source="data?.contractProductRelationList"
      :loading="loading"
      :rowKey="(record) => record.id"
      hasPage
      :scroll="{ x: 'max-content' }"
      @change="onTableChange"
      :pageConfig="paginationProps"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'file'">
          <!-- <a :href="record.file.fileVisitUrl"></a> -->
          <div v-if="record.file?.fileVisitUrl">
            <i
              class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
              style="color: #959ec3"
            ></i>
            <a
              :href="record.file?.fileVisitUrl"
              :title="record.file?.fileName"
              target="_blank"
              class="underline"
              style="color: #959ec3"
              >{{ record.file?.fileName }}
            </a>
          </div>
        </template>
      </template>
    </mw-table>
  </div>
</template>

<script setup>
import { usePagenation } from "@/common/setup";
import {
  onBeforeMount,
  reactive,
  ref,
  getCurrentInstance,
  computed,
  watch,
  defineEmits,
  defineProps,
} from "vue";
import { getMarketOrder } from "@/api/purchase/order.js";
import { orderStatus } from "@/common/constant.js";
const props = defineProps({
  id: String,
  data: Object,
});
// const data = ref();
const loading = ref(false);
const columns = ref([
  { title: "产品名称", dataIndex: "productName" },
  { title: "单价", dataIndex: "unitPrice" },
  { title: "数量", dataIndex: "quantity" },
  { title: "金额", dataIndex: "amount" },
  { title: "产品规格书/技术协议", dataIndex: "file" },
  { title: "关联BOM", dataIndex: "bomName" },
  { title: "规格书/技术协议编号", dataIndex: "fileNumber" },
  { title: "是否调试", dataIndex: "isNeedDebugging" },
  { title: "产品用途", dataIndex: "productUse" },
]);

const { paginationProps, onTableChange, refresh, pageParam } = usePagenation();

// const requestGetMarketOrder = async () => {
//   let res = await getMarketOrder({ purchaseId: props.id });
//   data.value = res.data;
// };

// onBeforeMount(async () => {
//   await requestGetMarketOrder();
// });
</script>

<style></style>
