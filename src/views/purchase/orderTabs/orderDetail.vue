<template>
  <!-- <mw-drawer
    custom-title="详情"
    :visible="visible"
    @close="onClose"
    @spinning="spinning"
    width="70%"
  > -->
  <a-spin :spinning="loading">
    <a-alert
      class="mb-4 rounded-lg"
      v-if="!orderReadonly"
      message="您好，采购订单已创建成功，请注意关注查看采购进度，确保及时收货交付"
      type="info"
      show-icon
    />
    <div class="bg-background p-4 rounded-lg">
      <div class="flex items-center">
        <span class="text-xl"
          >采购状态：<dictionary
            :showBadge="false"
            :statusOptions="purchaseStatus"
            :statusExtend="data.statusExtend"
            :value="data.status"
        /></span>
        <div class="w-60 ml-40">
          <a-steps
            :current="data.status == 2 ? 1 : data.status"
            :status="stepStatus"
            size="small"
          >
            <a-step title="采购中">
              <template #icon>
                <i class="iconfont icon-xianxing-30 align-middle"></i>
              </template>
            </a-step>
            <a-step :title="data.status == 2 ? '已取消' : '采购完成'">
              <template #icon>
                <i class="iconfont icon-xianxing-30 align-middle"></i>
              </template>
            </a-step>
          </a-steps>
        </div>
      </div>
      <!-- @click="showQualifyRecords" -->
      <div class="mt-3 text-secondar-text">
        您可以：<a-space>
          <mw-button
            title="物料到货"
            @click="showQualify(false)"
            v-permission="'purchase:order:materialQualityTesting'"
            v-if="data.status == 0"
          ></mw-button>
          <mw-button
            title="物料到货（仓库）"
            @click="showQualify(true)"
            v-permission="'purchase:order:warehouseMaterialQualityTesting'"
            v-if="data.status == 0"
          ></mw-button>

          <template v-if="!orderReadonly">
            <a-popconfirm
              title="确认采购完成后，将不能对采购订单进行任何操作哦！请您知晓！"
              @confirm="submitCompletePurchase"
            >
              <mw-button
                title="采购完成"
                v-permission="'purchase:order:completePurchase'"
              ></mw-button>
            </a-popconfirm>
            <a-popconfirm
              title="取消采购后，该笔订单将不可恢复，也不能进行任何操作哦！请您知晓！"
              @confirm="submitCancelPurchase"
            >
              <mw-button
                title="取消采购"
                v-permission="'purchase:order:cancelPurchase'"
              ></mw-button>
            </a-popconfirm>
          </template>

          <mw-button
            title="采购备注"
            @click="showRemarkDrawer"
            v-permission="'purchase:order:purchasingRemark'"
          ></mw-button>
          <mw-button
            title="到货记录"
            @click="onArrivalRecord"
            v-permission="'purchase:order:selectMaterialArrivalRecordByOrderId'"
          ></mw-button>
        </a-space>
      </div>
    </div>
    <div class="p-4">
      <p class="text-base mb-4">采购信息</p>

      <a-descriptions
        :colon="false"
        size="small"
        :labelStyle="{
          color: 'rgba(34, 34, 34, 0.65)',
        }"
      >
        <a-descriptions-item label="供应商"
          ><div>
            <p>{{ data.supplierName }}</p>
            <p class="text-secondar-text">
              {{ data.relationName }}「{{ data.relationPhone }}」
            </p>
          </div></a-descriptions-item
        >
        <a-descriptions-item label="采购跟进"
          >{{ data.purchaseByName }}

          <mw-button
            title="变更"
            size="small"
            v-if="!orderReadonly"
            @click="showUpdatePurchaseBy"
            v-permission="'purchase:order:changePurchaseBy'"
            class="ml-2"
          ></mw-button>
        </a-descriptions-item>
        <a-descriptions-item label="订单编号">{{
          data.purchaseNumber
        }}</a-descriptions-item>

        <a-descriptions-item label="采购时间">
          {{ data.createTime }}
        </a-descriptions-item>
        <a-descriptions-item label="到货时间">
          {{ data.createTime }}
        </a-descriptions-item>
        <a-descriptions-item label="采购总价">
          <span class="text-ea0c28 text-xl align-middle -mt-1">
            <span class="text-sm">¥</span> {{ data.purchaseTotalPrice }}
          </span>
        </a-descriptions-item>
        <a-descriptions-item label="采购合同">
          <span class="mr-2" v-if="!data.fileDetailModel">未上传</span>
          <form-upload-contract
            class="mr-2"
            v-model:value="files"
            :fileSize="10"
            sence="orderContract"
            :hasDownLoad="true"
            :fileTypes="[]"
            @done="upload"
            :readonly="orderReadonly"
          ></form-upload-contract>
        </a-descriptions-item>
      </a-descriptions>
    </div>
    <mw-table
      v-permission="'purchase:order:materialDetail'"
      :columns="columns"
      :data-source="data.purchaseDetailVOList"
      :rowKey="(record) => record.id"
      :scroll="{ x: 'max-content' }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key == 'info'">
          <p>{{ record.materialName }} / {{ record.materialNo }}</p>
          <p class="text-secondar-text">
            规格：{{ record.materialSpecification }}
          </p>
        </template>
        <template v-if="column.dataIndex == 'purchaseUnitPrice'"
          ><div class="">{{ record.purchaseUnitPrice }}</div></template
        >
        <template v-if="column.dataIndex == 'purchaseMaterialTotalPrice'"
          ><div class="">
            {{ record.purchaseMaterialTotalPrice }}
          </div></template
        >
        <template v-if="column.dataIndex == 'purchaseMaterialCount'"
          ><div class="">{{ record.purchaseMaterialCount }}</div></template
        >
        <template v-if="column.dataIndex == 'materialAccountsPayable'"
          ><div class="">
            {{ record.materialAccountsPayable }}
          </div></template
        >
        <template v-if="column.dataIndex == 'materialAccountsActual'"
          ><div class="">{{ record.materialAccountsActual }}</div></template
        >
        <template v-if="column.dataIndex == 'taxRate'"
          ><div class="">{{ record.taxRate }}</div></template
        >

        <template v-if="column.key == 'qualify'">
          <div class="">
            <span class="align-middle"> {{ record.qualifiedCount }}</span
            ><span class="text-ea0c28 align-middle"
              >/{{ record.unqualifiedCount }}</span
            >
          </div>
        </template>
        <template v-if="column.key == 'stored'">
          <p>{{ record.beStoredCount }}</p>
          <a @click="showRecordDrawer(record)" style="color: #959ec3"
            >查看到货记录</a
          >
        </template>
        <template v-if="column.key == 'operate'">
          <div
            class="text-center flex flex-col"
            v-if="record.materialArrivedCount == 0"
          >
            <mw-button
              title="编辑"
              class="w-full mb-2"
              @click="onEdit(record)"
            ></mw-button>
            <a-popconfirm
              title="是否确认取消采购？"
              @confirm="onDeleteMaterial(record)"
            >
              <mw-button title="取消采购" danger> </mw-button>
            </a-popconfirm>
          </div>
        </template>
      </template>
    </mw-table>
  </a-spin>
  <!-- 物料到货记录弹窗  @close="qualifyRecordsVisible = false"-->
  <mw-drawer
    :visible="qualifyRecordsVisible"
    @close="closeQualifyRecordsVisible"
    customTitle="物料到货质检"
    :spinning="qualifyRecordsLoading"
  >
    <template #header>
      <mw-button
        v-if="!orderReadonly"
        @click="showQualify(false)"
        :loading="submitLoading"
      >
        新增物料到货</mw-button
      >
    </template>
    <div
      v-if="qualifyRecords?.length"
      class="px-4 bg-background rounded-lg divide-y divide-border"
    >
      <div
        class="text-primary-text py-4"
        v-for="item in qualifyRecords"
        :key="item.recordId"
      >
        <div class="space-y-2 divide-y divide-border">
          <div
            v-for="element in item.materialDetailVOList"
            :key="element.materialId"
            class="pt-2"
          >
            <div class="space-y-1">
              <div class="flex items-center">
                <dictionary
                  :statusOptions="purchaseStatus"
                  :value="item.status"
                  isBackgroundColor
                />
                <p class="text-title overflow ml-1 flex-1">
                  {{ element.materialName || "-" }}
                </p>
              </div>
              <p>规格：{{ element.materialSpecification }}</p>
              <p>质检总数：{{ element.quantityTestingCount }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <a-empty v-else />
  </mw-drawer>
  <!-- 物料到货操作弹窗 -->
  <mw-drawer
    :visible="qualifyVisible"
    @close="closeQualifyDrawer"
    customTitle="物料到货质检"
  >
    <template #header>
      <mw-button @click="submitQualify" :loading="submitLoading">
        确定申请</mw-button
      >
    </template>
    <div class="px-4 bg-background rounded-lg divide-y divide-border">
      <div
        class="text-primary-text py-4"
        v-for="(item, index) in qualifyData"
        :key="item.id"
      >
        <div class="flex justify-between items-center leading-5.5 mb-1">
          <div class="flex-1 text-title overflow">
            <span>{{ item.materialName || "-" }} </span>
            <span class="ml-1">({{ item.materialNo || "--" }})</span>
          </div>
          <div class="" @click="qualifyDataRemove(index)">
            <i class="iconfont icon-jichu-shanchu align-middle"></i>
            <span class="align-middle ml-1">移除</span>
          </div>
        </div>
        <div class="flex justify-between items-end leading-5.5 mb-1">
          <div class="space-y-1">
            <div class="">规格：{{ item.materialSpecification }}</div>
            <!-- <div class="">价格：{{ item.price }}</div> -->
            <div class="">质检总数：</div>
          </div>
          <div class="">
            <span>x </span>
            <a-input-number
              class="w-24"
              v-model:value="item.unMaterialArrivedCount"
              :formatter="formatter6"
              @change="onUnMaterialArrivedCount($event, item)"
              :stringMode="true"
            />
          </div>
        </div>
        <div class="flex justify-between">
          <div class="" v-if="!isWarehouse">
            含税单价：
            <a-input-number
              v-model:value="item.price"
              :formatter="formatter6"
              @change="onUnMaterialArrivedCount($event, item)"
              :stringMode="true"
            />
          </div>
          <div class="" v-if="!isWarehouse">
            税率%：
            <!-- <a-input-number
              v-model:value="item.taxRate"
              :formatter="formatter2"
              :stringMode="true"
            /> -->
            <a-select
              class="w-24"
              v-model:value="item.taxRate"
              :options="taxRateList"
              placeholder="请选择税率"
            />
          </div>
        </div>
        <div v-if="!isWarehouse">
          含税总价：
          <a-input-number
            class="mt-2"
            style="width: 110px"
            v-model:value="item.arrivalTotalPrice"
            @change="onArrivalTotalPrice($event, item)"
            :formatter="formatter6"
            :stringMode="true"
          />
        </div>
        <div>
          物料备注：
          <a-input
            class="mt-2"
            style="width: 80%"
            v-model:value="item.materialRemark"
          />
        </div>
      </div>
    </div>
    <p class="my-4">上传送货单</p>
    <form-upload
      v-model:value="deliveryOrderFiles"
      sence="delivery"
      :fileTypes="[]"
      :fileSize="100"
      hasDownLoad
    ></form-upload>

    <div>
      到货备注：
      <a-input class="mt-2" style="width: 80%" v-model:value="arrivalRemark" />
    </div>
  </mw-drawer>
  <!-- 备注 -->
  <mw-drawer
    :visible="remarkVisible"
    @close="closeRemarkDrawer"
    customTitle="采购备注"
  >
    <template #header>
      <mw-button
        title="确定"
        @click="submitForm"
        :loading="submitRemarkLoading"
      >
      </mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
    >
      <a-form-item label="" name="remark">
        <a-textarea
          v-model:value="formData.remark"
          placeholder="请输入备注信息"
          :maxLength="100"
          allow-clear
        ></a-textarea>
      </a-form-item>
    </a-form>
  </mw-drawer>
  <!-- 变更采购人 -->
  <mw-drawer
    :visible="purchaseByVisible"
    @close="closeUpdatePurchaseByDrawer"
    customTitle="变更采购人"
  >
    <template #header>
      <mw-button
        title="确定"
        @click="submitUpdatePurchaseBy"
        :loading="submitPurcaseByLoading"
      ></mw-button>
    </template>
    <a-select
      style="width: 100%"
      v-model:value="purchaseBy"
      placeholder="请选择采购人"
      allow-clear
    >
      <a-select-option
        v-for="item in userListOptions"
        :key="item.userId"
        :value="item.userId.toString()"
        >{{ item.nickName }}「{{ item.phonenumber }}」</a-select-option
      >
    </a-select>
  </mw-drawer>
  <mw-drawer
    :visible="handelConfrimEditVisible"
    @close="onCloseEditDrawer"
    customTitle="编辑"
  >
    <template #header>
      <mw-button
        title="确定"
        @click="handelConfrimEdit"
        :loading="handelConfrimEditLoading"
      ></mw-button>
    </template>
    <div>
      <div class="flex items-center mb-2">
        <div class="w-20">物料编码：</div>
        <div>{{ editRecordInfo.materialNo }}</div>
      </div>
      <div class="flex items-center mb-2">
        <div class="w-20">物料名称：</div>
        <div>{{ editRecordInfo.materialName }}</div>
      </div>
      <div class="flex items-center mb-2">
        <div class="w-20">采购数量：</div>
        <div>
          <a-input-number
            class="w-24"
            v-model:value="editRecordInfo.purchaseMaterialCount"
            :formatter="formatter6"
            :stringMode="true"
          />
        </div>
      </div>
      <div class="flex items-center mb-2">
        <div class="w-20">含税单价：</div>
        <div>
          <a-input-number
            class="w-24"
            v-model:value="editRecordInfo.purchaseUnitPrice"
            :formatter="formatter6"
            :stringMode="true"
          />
        </div>
      </div>
      <div class="flex items-center mb-2">
        <div class="w-20">金额：</div>
        <div>{{ purchaseMaterialTotalPrice }}</div>
      </div>
      <div class="flex items-center mb-2">
        <div class="w-20">税率%：</div>
        <div>
          <a-input-number
            class="w-24"
            v-model:value="editRecordInfo.taxRate"
            :formatter="formatter6"
            :stringMode="true"
          />
        </div>
      </div>
    </div>
  </mw-drawer>
  <!-- 查看到货记录 -->
  <record-drawer
    v-model:visible="recordVisible"
    :materialId="currentMaterialId"
    :purchaseNumber="data.purchaseNumber"
  />
  <arrivalRecord
    @finish="getDetail"
    v-model:visible="arrivalRecordVisible"
    :id="data.id"
    @success="success"
  ></arrivalRecord>
  <!-- </mw-drawer> -->
</template>
<script setup>
import {
  onBeforeMount,
  reactive,
  ref,
  getCurrentInstance,
  computed,
  defineProps,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  getInfo,
  materialQualityTesting,
  changePurchaseBy,
  completePurchase,
  cancelPurchase,
  purchasingRemark,
  uploadContract,
  selectMaterialArrivalPage,
  newSelectMaterialPriceList,
  deleteMaterial,
  getMaterialDetail,
  updateMaterialDetail,
} from "@/api/purchase/order.js";
import { purchaseStatus } from "@/common/constant.js";
import FormUploadContract from "@/components/form-upload-contract.vue";
import RecordDrawer from "../components/recordDrawer.vue";
import { getAllUser } from "@/api/system/user.js";
import FormUpload from "@/components/form-upload.vue";
import { formatter6, formatter2 } from "@/common/validate.js";
import arrivalRecord from "./arrivalRecord.vue";
import _cloneDeep from "lodash/cloneDeep";
import Decimal from "decimal.js";
// const emit = defineEmits(["update:visible", "finish"]);
import { taxRateList } from "@/common/constant.js";
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
});

const columns = ref([
  {
    title: "采购物料",
    key: "info",
    width: "240px",
  },

  {
    title: "含税单价",
    dataIndex: "purchaseUnitPrice",
  },
  {
    title: "税率%",
    key: "taxRate",
    dataIndex: "taxRate",
  },
  {
    title: "采购总价(元)",
    dataIndex: "purchaseMaterialTotalPrice",
  },
  {
    title: "采购数量",
    dataIndex: "purchaseMaterialCount",
  },
  {
    title: "到货数量",
    dataIndex: "materialArrivedCount",
  },
  {
    title: "应付货款(元)",
    dataIndex: "materialAccountsPayable",
  },
  {
    title: "实付货款(元)",
    dataIndex: "materialAccountsActual",
  },
  {
    title: "已结算数量",
    dataIndex: "settlementCount",
  },
  {
    title: "已结算金额",
    dataIndex: "settlementAmount",
  },
  {
    title: "未结算金额",
    dataIndex: "unSettlementAmount",
  },
  {
    title: "到货总价",
    dataIndex: "arrivalTotalPrice",
  },
  {
    title: "合格/不合格",
    key: "qualify",
  },
  {
    title: "已入仓",
    key: "stored",
  },
  {
    title: "",
    key: "operate",
    fixed: "right",
  },
]);
const { proxy } = getCurrentInstance();
const isWarehouse = ref(false);
const route = useRoute();
const arrivalRecordVisible = ref(false);
const id = ref(""),
  loading = ref(false),
  data = ref({}),
  qualifyVisible = ref(false),
  qualifyData = ref([]),
  deliveryOrderFiles = ref([]),
  submitLoading = ref(false),
  files = ref([]);
const arrivalRemark = ref("");
const stepStatus = computed(() => {
  switch (data.value.status) {
    case 1:
      return "finish";
    case 2:
      return "error";
    default:
      return "";
  }
});
const closeQualifyRecordsVisible = () => {
  qualifyRecordsVisible.value = false;
  getDetail();
};
const onArrivalRecord = () => {
  arrivalRecordVisible.value = true;
};
const onArrivalTotalPrice = (e, val) => {
  qualifyData.value.forEach((item, index) => {
    if (item.materialId == val.materialId) {
      item.arrivalTotalPrice = e;
      if (item.arrivalTotalPrice == 0) {
        item.price = 0;
        // val.unMaterialArrivedCount = 0;
      }
      if (item.arrivalTotalPrice && val.unMaterialArrivedCount > 0) {
        item.price = item.arrivalTotalPrice / val.unMaterialArrivedCount;
      }
    }
  });
};
const onUnMaterialArrivedCount = (e, val) => {
  qualifyData.value.forEach((item, index) => {
    if (item.materialId == val.materialId) {
      console.log(item, "item");
      item.arrivalTotalPrice = item.price * val.unMaterialArrivedCount;
    }
  });
};
const getDetail = async () => {
  loading.value = true;
  let result = await getInfo(id.value);
  data.value = result.data;
  files.value = result.data.fileDetailModel ? [data.value.fileDetailModel] : [];
  loading.value = false;
  let reslist = await getMaterialDetail(id.value);
  data.value.purchaseDetailVOList = reslist.data;
};
const onDeleteMaterial = async (value) => {
  let param = { orderInfoId: value.id };
  let res = await deleteMaterial(param);
  if (res.code == 200) {
    proxy.$message.success("删除成功");
    getDetail();
  }
};
const editRecordInfo = ref();
const purchaseMaterialTotalPrice = computed(() => {
  let res = Decimal(editRecordInfo.value.purchaseMaterialCount || 0).mul(
    Decimal(editRecordInfo.value.purchaseUnitPrice || 0)
  );
  return Number(res);
});
const onEdit = async (record) => {
  //
  console.log("[ record 点击编辑] >", record);
  editRecordInfo.value = _cloneDeep(record);
  handelConfrimEditVisible.value = true;
};
const onCloseEditDrawer = () => {
  handelConfrimEditLoading.value = false;
  handelConfrimEditVisible.value = false;
};
const handelConfrimEditVisible = ref(false);
const handelConfrimEditLoading = ref(false);
const handelConfrimEdit = async () => {
  handelConfrimEditLoading.value = true;
  ///
  console.log(
    "[ purchaseMaterialTotalPrice.value ] >",
    purchaseMaterialTotalPrice.value
  );
  let res = await updateMaterialDetail({
    purchaseInfoId: editRecordInfo.value.id,
    purchaseMaterialCount: editRecordInfo.value.purchaseMaterialCount,
    purchaseMaterialTotalPrice: purchaseMaterialTotalPrice.value,
    purchaseUnitPrice: editRecordInfo.value.purchaseUnitPrice,
    taxRate: editRecordInfo.value.taxRate,
  });

  if (res.code == 200) {
    proxy.$message.success("操作成功");
    getDetail();
    onCloseEditDrawer();
  }
  handelConfrimEditLoading.value = false;
  //
};

const orderReadonly = computed(() => {
  return data.value.status != 0;
});
onBeforeMount(async () => {
  // id.value = route.query.id;
  id.value = props.id;
  await getDetail();
});
const showQualify = async (val) => {
  if (val) {
    isWarehouse.value = true;
  } else {
    isWarehouse.value = false;
  }

  let res = await newSelectMaterialPriceList({ purchaseId: data.value.id });
  res.data.forEach((ite) => {
    ite.arrivalTotalPrice = ite.price * ite.unMaterialArrivedCount;
  });
  qualifyData.value = res.data;

  // qualifyData.value = data.value.purchaseDetailVOList?.map((item) => {
  //   return { ...item, quantityTestingCount: item.purchaseMaterialCount };
  // });
  qualifyVisible.value = true;
};
const closeQualifyDrawer = () => {
  deliveryOrderFiles.value = [];
  qualifyVisible.value = false;
  arrivalRemark.value = void 0;
};
const submitQualify = async () => {
  try {
    submitLoading.value = true;
    qualifyData.value.forEach((element) => {
      if (!element.unMaterialArrivedCount) {
        throw new Error("请输入质检总数");
      } else {
        element.quantityTestingCount = element.unMaterialArrivedCount;
      }
    });
    // if (!deliveryOrderFiles.value?.length) {
    //   throw new Error("请上传送货单");
    // }
    let { id, purchaseNumber } = data.value;
    let param = {
      purchaseId: id,
      purchaseNumber,
      materialParamList: qualifyData.value,
      deliveryOrderFile: deliveryOrderFiles.value[0],
      arrivalRemark: arrivalRemark.value,
      isWarehouse: isWarehouse.value,
    };
    let res = await materialQualityTesting(param);
    if (res.code == 200) {
      proxy.$message.success("物料到货申请质检成功");
      await getDetail();
      await getQualifyRecords();
      closeQualifyDrawer();
    }
    submitLoading.value = false;
  } catch (error) {
    error.message && proxy.$message.warning(error.message);
    submitLoading.value = false;
  }
};
const qualifyDataRemove = (index) => {
  if (qualifyData.value.length == 1) {
    proxy.$message.warning("至少要有一条物料到货数据");
  } else {
    qualifyData.value.splice(index, 1);
  }
};
const submitCompletePurchase = async () => {
  let res = await completePurchase(data.value.id);
  if (res.code == 200) {
    proxy.$message.success("订单已完成");
  }
  await getDetail();
};
const submitCancelPurchase = async () => {
  let res = await cancelPurchase(data.value.id);
  if (res.code == 200) {
    proxy.$message.success("订单取消成功");
  }
  await getDetail();
};
//上传合同
const upload = async (files) => {
  let param = {
    id: data.value.id,
    fileDetailModel: files[0],
  };
  let res = await uploadContract(param);
  if (res.code == 200) {
    proxy.$message.success("上传合同成功");
  }
  await getDetail();
};
// 备注相关
const formRef = ref(),
  remarkVisible = ref(false),
  submitRemarkLoading = ref(false),
  formData = reactive({
    remark: undefined,
  }),
  rules = ref({
    remark: [
      {
        required: true,
        message: "请输入备注",
        trigger: "blur",
      },
    ],
  });
const showRemarkDrawer = () => {
  formData.remark = data.value.remark;
  remarkVisible.value = true;
};
const closeRemarkDrawer = () => {
  formRef.value.resetFields();
  remarkVisible.value = false;
};
const submitForm = () => {
  formRef.value
    .validate()
    .then(async () => {
      submitRemarkLoading.value = true;
      let res = await purchasingRemark({
        id: data.value.id,
        remark: formData.remark,
      });
      if (res.code == 200) {
        proxy.$message.success("备注成功");
      }
      submitRemarkLoading.value = false;
      closeRemarkDrawer();
      await getDetail();
    })
    .catch((error) => {
      submitRemarkLoading.value = false;
    });
};
// 变更采购人
const purchaseBy = ref(),
  purchaseByVisible = ref(false),
  submitPurcaseByLoading = ref(false),
  userListOptions = ref([]);
const showUpdatePurchaseBy = async () => {
  let result = await getAllUser();
  userListOptions.value = result.data;
  purchaseBy.value = data.value.purchaseBy;
  purchaseByVisible.value = true;
};
const closeUpdatePurchaseByDrawer = () => {
  purchaseByVisible.value = false;
};
const submitUpdatePurchaseBy = async () => {
  submitPurcaseByLoading.value = true;
  try {
    let res = await changePurchaseBy({
      id: data.value.id,
      purchaseBy: purchaseBy.value,
    });
    if (res.code == 200) {
      proxy.$message.success("变更成功");
    }
    await getDetail();
    purchaseByVisible.value = false;
    submitPurcaseByLoading.value = false;
  } catch (error) {
    submitPurcaseByLoading.value = false;
  }
};
const recordVisible = ref(false),
  currentMaterialId = ref("");
const showRecordDrawer = (item) => {
  currentMaterialId.value = item.materialId;
  recordVisible.value = true;
};
// 物料到货记录
const qualifyRecordsVisible = ref(false),
  qualifyRecords = ref([]),
  qualifyRecordsLoading = ref(false);
const getQualifyRecords = async () => {
  qualifyRecordsLoading.value = true;
  let result = await selectMaterialArrivalPage({
    purchaseNumber: data.value.purchaseNumber,
  });
  qualifyRecords.value = result.data.records;
  qualifyRecordsLoading.value = false;
};
// const showQualifyRecords = async () => {
//   qualifyRecordsVisible.value = true;
//   await getQualifyRecords();
// };
// const showQualifyWarehouseRecords =async () => {
//   qualifyVisible.value = true;
//   await getQualifyRecords();
// }
// const onClose = () => {
//   emit("update:visible", false);
// };
// watch(
//   () => props,
//   async (val) => {
//     id.value = props.id;
//     await getDetail();
//   }
// );

const success = () => {
  getDetail();
};
</script>
<style lang="less" scoped>
:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  // background: transparent;
}
:deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
  // background: transparent;
}
:deep(.ant-table-cell) {
  vertical-align: top;
}
:deep(.ant-steps-icon) {
  vertical-align: middle !important;
  font-size: 0 !important;
  .iconfont {
    font-size: 20px !important;
  }
}
</style>
