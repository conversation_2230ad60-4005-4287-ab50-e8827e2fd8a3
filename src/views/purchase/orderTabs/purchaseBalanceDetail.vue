<template>
  <div>
    <div v-for="(item, index) in data" :key="index">
      <p class="pb-5">结算单号：{{ item?.settlementNumber || "--" }}</p>

      <p class="pb-5">
        <!-- 结算状态：{{
          item.status == 0
            ? "待结算"
            : item.status == 1
            ? "已付款"
            : item.status == 7
            ? "审批中"
            : ""
        }} -->
        结算状态：<dictionary
          :statusOptions="settlementStatus"
          :value="item.status"
          isBackgroundColor
        />
      </p>
      <p class="pb-5">发票总金额：{{ item?.invoiceAmount || "--" }}</p>
      <mw-table
        :columns="columns"
        :data-source="item.materialDetailVOList"
        :loading="loading"
        :rowKey="(record) => record.id"
        hasPage
        :scroll="{ x: 'max-content' }"
        @change="onTableChange"
        :pageConfig="paginationProps"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'file'">
            <!-- <a :href="record.file.fileVisitUrl"></a> -->
            <div v-if="record.file?.fileVisitUrl">
              <i
                class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                style="color: #959ec3"
              ></i>
              <a
                :href="record.file?.fileVisitUrl"
                :title="record.file?.fileName"
                target="_blank"
                class="underline"
                style="color: #959ec3"
                >{{ record.file?.fileName }}
              </a>
            </div>
          </template>
        </template>
      </mw-table>
    </div>
  </div>
</template>

<script setup>
import { usePagenation } from "@/common/setup";
import {
  onBeforeMount,
  reactive,
  ref,
  getCurrentInstance,
  computed,
  watch,
  defineEmits,
  defineProps,
} from "vue";
orderStatus;
import { getPurchaseSettlement } from "@/api/purchase/order.js";
import { orderStatus } from "@/common/constant.js";
import { settlementStatus } from "@/common/constant.js";
const props = defineProps({
  id: String,
  data: Array || Object,
});
// const data = ref();
const loading = ref(false);
const columns = ref([
  { title: "物料名称", dataIndex: "materialName" },
  { title: "物料编号", dataIndex: "materialNo" },
  { title: "物料规格", dataIndex: "materialSpecification" },

  { title: "结算数", dataIndex: "settlementCount" },
  { title: "结算单价", dataIndex: "settlementPrice" },
  { title: "税率", dataIndex: "taxRate" },
  { title: "总金额", dataIndex: "settlementAmount" },
]);

const { paginationProps, onTableChange, refresh, pageParam } = usePagenation();

// const requestGetPurchaseSettlement = async () => {
//   let res = await getPurchaseSettlement({ purchaseId: props.id });
//   data.value = res.data;
// };

// onBeforeMount(async () => {
//   await requestGetPurchaseSettlement();
// });
</script>

<style></style>
