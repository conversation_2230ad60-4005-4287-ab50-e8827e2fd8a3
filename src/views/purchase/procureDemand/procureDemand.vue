<template>
  <div>
    <search :searchData="searchData" @search="refresh">
      <!--  v-permission="'purchase:require:generatePurchaseOrder'" -->
      <mw-button
        title="批量取消"
        @click="bantchCancel"
        :loading="cancleLoading"
        :disabled="state.selectedRowKeys.length === 0"
      ></mw-button>
      <mw-button
        title="生成采购订单"
        @click="onGeneratePurchaseOrder"
        :disabled="state.selectedRowKeys.length > 0 ? false : true"
        :loading="btnLoading"
        v-permission="'purchase:require:generatePurchaseOrder'"
      ></mw-button>
      <mw-button
        title="新增"
        :font="'iconfont icon-xianxing-121'"
        @click="onNewProcurementRequirements('pointDemandN')"
        v-permission="'purchase:require:add'"
      ></mw-button>
    </search>
    <mw-table
      :columns="columns"
      :data-source="demandData"
      :loading="loading"
      :rowKey="(record) => record.id"
      @change="onTableChange"
      :row-selection="rowSelection"
      :pageConfig="{ ...paginationProps }"
      hasPage
      :scroll="{ x: 'max-content', y: 'calc(88vh - 300px)' }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key == 'type'">
          <div v-if="record.type == 'NORMAL'">普通采购</div>
          <div v-if="record.type == 'SAFE_STOCK'">安全库存采购订单</div>
          <div v-if="record.type == 'MARKET_ORDER'">
            销售订单采购（订单号：{{ record.referNo || "--" }}）
          </div>
          <div v-if="record.type == 'UPDATE_ORDER_BOM'">
            修改订单BOM采购（订单号：{{ record.referNo || "--" }}）
          </div>
        </template>
        <template v-if="column.key == 'supplierName'">
          <div @click="onModifySupplier(record)">
            <span style="color: #1890ff">
              {{ record.supplierName ? record.supplierName : "(选择供应商)" }}
            </span>
          </div>
        </template>
        <template v-if="column.key == 'status'">
          <!-- :statusExtend="record.statusExtend"
            isBackgroundColor背景颜色 -->
          <dictionary
            :statusOptions="ProcurementRequirementStatus"
            :value="record.status"
        /></template>
        <template v-if="column.key == 'operate'">
          <mw-button
            title="编辑"
            @click="procureEdit(record)"
            v-if="record.status == 0 || record.status == 2"
          ></mw-button>
        </template>

        <!-- ProcurementRequirementStatus -->
      </template>
    </mw-table>
  </div>
  <requistion-create-drawer
    v-model:visible="onNewProcVisible"
    v-model:pointDemand="pointDemand"
    @finish="getList"
    :id="procureId"
  />
  <modifySupplier
    v-model:visible="modifySupplierVisible"
    :id="modifySupplierId"
    @finish="getList"
    :modifyId="modifyId"
    :supplierId="supplierId"
  ></modifySupplier>
</template>

<script setup>
import {
  ref,
  reactive,
  toRaw,
  defineEmits,
  onBeforeMount,
  getCurrentInstance,
  computed,
} from "vue";
import {
  requireList,
  requirePage,
  generatePurchaseOrder,
  batchCancel,
} from "@/api/purchase/procureDemand.js";
import {
  purchaseOrderType,
  ProcurementRequirementStatus,
} from "@/common/constant.js";
import Search from "@/components/search/index.vue";
import { usePagenation } from "@/common/setup";
import modifySupplier from "./modifySupplier.vue";
import requistionCreateDrawer from "../components/requistionCreateDrawer.vue";
import { useRoute, useRouter } from "vue-router";
import { debounce } from "lodash";
import setRight$ from "dingtalk-jsapi/api/biz/navigation/setRight";
import { Modal } from "ant-design-vue";
const { proxy } = getCurrentInstance();
const modifyId = ref();
const demandData = ref([]);
const onNewProcVisible = ref(false);
const modifySupplierVisible = ref(false);
const loading = ref(false);
const pointDemand = ref();
const modifySupplierId = ref("");
const supplierId = ref(void 0);
const route = useRoute();
const router = useRouter();
const procureId = ref();
const state = reactive({
  selectedRowKeys: [],
  // Check here to configure the default column
  loading: false,
});

const procureStatus = [
  {
    label: "全部",
    value: "",
  },
  {
    label: "待生成订单",
    value: "0",
  },
  {
    label: "已生成订单",
    value: "1",
  },
  {
    label: "已取消",
    value: "2",
  },
];
const searchData = reactive({
  fields: {
    rangeDate: {
      type: "a-range-picker",
      valueFormat: "YYYY-MM-DD",
      value: [],
      width: "260px",
      allowClear: true,
      placeholder: ["创建开始日期", "创建结束日期"],
    },
    type: {
      name: "类型",
      type: "a-select",
      options: purchaseOrderType,
      placeholder: "选择状态",
      width: "150px",
      value: "",
      allowClear: true,
    },
    status: {
      name: "全部采购类型",
      type: "a-select",
      options: procureStatus,
      placeholder: "选择状态",
      width: "150px",
      value: "0",
      allowClear: true,
    },
    requireNumber: {
      type: "a-input-search",
      placeholder: "物料编码/物料名称/供应商名称",
      width: "270px",
      allowClear: true,
      allowClear: true,
    },
  },
});
const columns = ref([
  {
    title: "需求编号",
    key: "requireNumber",
    dataIndex: "requireNumber",
  },
  {
    title: "需求类型",
    key: "type",
    dataIndex: "type",
  },
  {
    title: "供应商",
    key: "supplierName",
    dataIndex: "supplierName",
  },
  // {
  //   title: "采购人",
  //   key: "purchaseByName",
  //   dataIndex: "purchaseByName",
  //   width: "100px",
  // },
  {
    title: "创建人",
    key: "createName",
    dataIndex: "createName",
  },

  {
    title: "物料名称",
    key: "materialName",
    dataIndex: "materialName",
  },
  {
    title: "物料编号",
    key: "materialNo",
    dataIndex: "materialNo",
  },
  {
    title: "采购总数",
    key: "purchaseCount",
    dataIndex: "purchaseCount",
    width: "100px",
  },
  {
    title: "需求状态",
    key: "status",
    dataIndex: "status",
  },
  {
    title: "创建时间",
    key: "createTime",
    dataIndex: "createTime",
  },
  {
    title: "操作",
    key: "operate",
    dataIndex: "operate",
    fixed: "right",
  },
]);
const getList = async () => {
  supplierId.value = void 0;
  loading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await requirePage({ ...pageParam.value }, searchParam);
  demandData.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};

const { paginationProps, onTableChange, refresh, pageParam } = usePagenation(
  getList,
  100
);

const onNewProcurementRequirements = (val) => {
  pointDemand.value = val;
  procureId.value = undefined;
  onNewProcVisible.value = true;
};
const procureEdit = (val) => {
  // id.value = val.id;
  procureId.value = val.id;
  onNewProcVisible.value = true;
};
const btnLoading = ref(false);
const onGeneratePurchaseOrder = debounce(async () => {
  btnLoading.value = true;
  let res = await generatePurchaseOrder({
    requireIds: state.selectedRowKeys.map(Number),
  });
  getList();
  if (res.code == 200) {
    proxy.$message.warning("已生成");
    router.push({
      path: "purchaseorder",
    });
  }
  btnLoading.value = false;
}, 300);
const onSelectChange = (selectedRowKeys) => {
  state.selectedRowKeys = selectedRowKeys;
};

const rowSelection = computed(() => {
  return {
    onChange: (selectedRowKeys, selectedRows) => {
      const { paginationProps, onTableChange, refresh, pageParam } =
        usePagenation(getList);
      // 仅保留当前页的选中，且过滤掉非待生成订单
      const currentPageIds = (demandData.value || []).map((r) => r.id);
      const filteredKeys = selectedRowKeys
        .filter((key) => currentPageIds.includes(key))
        .filter((key) => {
          const rec = (demandData.value || []).find((r) => r.id === key);
          return rec && rec.status === 0;
        });
      state.selectedRowKeys = filteredKeys;
    },
    preserveSelectedRowKeys: true,
    getCheckboxProps: (record) => ({
      disabled: record.status !== 0,
    }),
  };
});
// const onDetail = (val) => {
//   detailVisible.value = true;
//   detailId.value = val.id;
// };
onBeforeMount(async () => {
  await getList();
});
const onModifySupplier = (val) => {
  modifySupplierVisible.value = true;
  modifySupplierId.value = val.materialId;
  modifyId.value = val.id;
  supplierId.value = val.supplierId;
};

const cancleLoading = ref(false);
const bantchCancel = async () => {
  if (!state.selectedRowKeys || state.selectedRowKeys.length === 0) {
    proxy.$message.warning("请先选择要取消的采购需求");
    return;
  }
  // 仅操作当前页数据且必须为待生成订单
  const currentPageIds = (demandData.value || []).map((r) => r.id);
  const validIds = state.selectedRowKeys
    .filter((id) => currentPageIds.includes(id))
    .filter((id) => {
      const rec = (demandData.value || []).find((r) => r.id === id);
      return rec && rec.status === 0;
    });
  if (validIds.length === 0) {
    proxy.$message.warning("所选数据不符合取消条件");
    return;
  }

  const confirmed = await new Promise((resolve, reject) => {
    Modal.confirm({
      title: "确认取消",
      content: `确定取消选中的 ${validIds.length} 条采购需求吗？`,
      onOk: () => resolve(true),
      onCancel: () => reject(false),
    });
  }).catch(() => false);
  if (!confirmed) return;

  cancleLoading.value = true;
  try {
    const res = await batchCancel({
      updateParamInners: validIds.map((item) => {
        return {
          id: Number(item),
          status: "2",
        };
      }),
    });
    if (res && res.code === 200) {
      proxy.$message.success("取消成功");
      // 刷新列表与清空当前页选中
      await getList();
      const currentIdsAfter = (demandData.value || []).map((r) => r.id);
      state.selectedRowKeys = state.selectedRowKeys.filter(
        (id) => !currentIdsAfter.includes(id)
      );
      // 同时清空（按需求明确仅操作当前页，直接清空更稳妥）
      state.selectedRowKeys = [];
    } else {
      proxy.$message.error(res?.msg || "取消失败");
    }
  } finally {
    cancleLoading.value = false;
  }
};
</script>

<style lang="less" scoped>
:deep(
    :where(.css-dev-only-do-not-override-u4kgz5).ant-layout .ant-layout-content
  ) {
  flex: none !important;
}
</style>
