<template>
  <mw-drawer
    custom-title=""
    :visible="visible"
    @close="onClose"
    @spinning="spinning"
    destroyOnClose="true"
    :closable="false"
    width="40%"
  >
    <template #header>
      <mw-button title="提交" @click="modifySubmit"></mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
    >
      <a-form-item label="供应商" name="supplierId">
        <a-select
          v-model:value="formData.supplierId"
          placeholder="请选择供应商"
          allow-clear
          show-arrow
          @change="onChangeSupplier"
        >
          <a-select-option
            v-for="item in supplierListOptions"
            :key="item.id"
            :value="item.id"
            >{{ item.name }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="联系人:">
        <span> {{ formData.relationName || "--" }}</span>
      </a-form-item>
      <a-form-item label="联系方式:">
        <span>{{ formData.relationPhone || "--" }}</span>
      </a-form-item>
    </a-form>
  </mw-drawer>
</template>

<script setup>
import {
  onBeforeMount,
  reactive,
  ref,
  getCurrentInstance,
  computed,
  watch,
  defineEmits,
  defineProps,
} from "vue";
import {
  updateSupplier,
  selectSupplierByMaterialId,
} from "@/api/purchase/procureDemand.js";
import { returnSelectAllSupplier } from "@/api/purchase/returnOutbound.js";
const emit = defineEmits(["update:visible", "finish"]);
const { proxy } = getCurrentInstance();
const activeKey = ref("1");
const supplierListOptions = ref();
const formRef = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
  modifyId: String,
  supplierId: String,
});
const formData = ref({
  supplierId: undefined,
  supplierName: undefined,
  relationName: undefined,
  elationPhone: undefined,
});
const rules = ref({
  supplierId: [
    {
      required: true,
      message: "供应商",
      trigger: "blur",
    },
  ],
});
const onClose = () => {
  emit("update:visible", false);
  emit("finish");
  formData.value.supplierId = undefined;
  formData.value.supplierName = undefined;
  formData.value.relationName = undefined;
  formData.value.relationPhone = undefined;
};

// 供应商接口
const returnSelectAllSupplierList = async () => {
  let res = await selectSupplierByMaterialId({ materialId: props.id });
  supplierListOptions.value = res.data;
};
const onChangeSupplier = (e) => {
  let item = supplierListOptions.value.find((item) => item.id === e);
  formData.value.supplierName = item.name;
  formData.value.relationName = item.relationName;
  formData.value.relationPhone = item.relationPhone;
};
const modifySubmit = async () => {
  let param = {
    id: props.modifyId,
    ...formData.value,
    startTime: new Date(),
  };

  let res = await updateSupplier(param);
  if (res.code == 200) {
    proxy.$message.success("修改成功");
    onClose();
  }
  formRef.value.resetFields();
};

// watch(
//   () => props.visible,
//   async (val) => {

//   }
// );

watch(
  () => props.supplierId,
  async (val) => {
    await returnSelectAllSupplierList();
    if (val) {
      const boolan = supplierListOptions.value.some((item) => item.id == val);
      if (boolan) {
        formData.value.supplierId = Number(val);
        onChangeSupplier(Number(val));
      }
    }
  },
  { deep: true }
);
</script>

<style></style>
