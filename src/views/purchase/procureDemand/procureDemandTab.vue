<template>
  <a-drawer
    custom-title=""
    :visible="visible"
    @close="onClose"
    @spinning="spinning"
    :closable="false"
    width="70%"
    :footer-style="{ textAlign: 'right' }"
  >
    <template #footer>
      <mw-button title="关闭" @click="onClose"> </mw-button>
    </template>
    <a-tabs v-model:activeKey="activeKey" @tabClick="getTabName" animated>
      <a-tab-pane key="1" tab="订单详情">
        <orderDetail :id="idValue"></orderDetail>
      </a-tab-pane>
      <a-tab-pane key="2" tab="审批" force-render>
        <examineDetail :id="idValue" :data="examineDetailData"></examineDetail>
      </a-tab-pane>
      <a-tab-pane key="3" tab="采购结算">
        <purchaseBalanceDetail
          :id="idValue"
          :data="purchaseBalanceDetailData"
        ></purchaseBalanceDetail>
      </a-tab-pane>
      <a-tab-pane key="4" tab="采购需求"> </a-tab-pane>
      <a-tab-pane key="5" tab="到货记录"> </a-tab-pane>
    </a-tabs>
  </a-drawer>
</template>

<script setup>
import {
  onBeforeMount,
  reactive,
  ref,
  getCurrentInstance,
  computed,
  watch,
  defineEmits,
  defineProps,
} from "vue";
import examineDetail from "../orderTabs/examineDetail.vue";
import orderDetail from "../orderTabs/orderDetail.vue";
import purchaseBalanceDetail from "../orderTabs/purchaseBalanceDetail.vue";
import {
  oaDetail,
  getContractInfo,
  getMarketOrder,
  getPurchaseSettlement,
} from "@/api/purchase/order.js";

const emit = defineEmits(["update:visible", "finish"]);
const activeKey = ref("1");
const activeKeyId = ref();
const examineDetailData = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "",
  },
});
const idValue = ref("");
const contractReviewData = ref();
const saleOrderDetailData = ref();
const purchaseBalanceDetailData = ref();
const onClose = () => {
  emit("update:visible", false);
  activeKey.value = "1";
};
const getTabName = async (data) => {
  let param = { purchaseId: props.id };
  let res;
  if (data == 2) {
    res = await oaDetail(param);
    examineDetailData.value = res.data;
  } else if (data == 3) {
    res = getPurchaseSettlement(param);
    purchaseBalanceDetailData.value = res.data;
  }
};
watch(
  () => props.visible,
  async (val) => {
    idValue.value = props.id;
    // await getDetail();
  }
);
</script>

<style></style>
