<template>
  <mw-drawer
    :visible="visible"
    :custom-title="customTitle(openType)"
    width="80%"
    @close="onClose"
    :destroyOnClose="true"
  >
    <template #header>
      <mw-button
        title="暂存"
        v-if="openType !== 'firstTime'"
        @click="determineSubmit(true)"
        :loading="submitIsStagingLoading"
      ></mw-button>

      <mw-button
        title="确定"
        @click="determineSubmit(false)"
        :loading="submitLoading"
      ></mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formState"
      layout="horizontal"
      :colon="false"
      labelAlign="left"
      :label-col="{ offset: 1 }"
    >
      <a-row class="pb-5" style="width: 100%">
        <a-col :span="8">
          <a-form-item
            label="开具发票"
            name="issueInvoiceType"
            :rules="[{ required: true, message: '请选择开具发票!' }]"
          >
            <!--   -->
            <a-select
              v-if="openType !== 'add'"
              allowClear
              show-search
              placeholder="开具发票"
              :options="issueInvoiceTypeList"
              v-model:value="formState.issueInvoiceType"
              optionFilterProp="label"
              :disabled="openType === 'firstTime'"
            >
            </a-select>
            <div v-else>
              {{ formState.issueInvoiceType == 1 ? "蓝字发票" : "红字发票" }}
            </div>
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item label="结算单号">
            <a-input
              v-model:value="formState.defaultSettlementNumbers"
              placeholder="结算单号"
              disabled
            />
          </a-form-item>
        </a-col>
        <a-col :span="8"></a-col>
        <a-col :span="8">
          <a-form-item
            label="供应商"
            name="supplierId"
            :rules="[{ required: true, message: '请选择供应商!' }]"
          >
            <a-select
              allowClear
              show-search
              placeholder="供应商"
              :options="supplierListOptions"
              v-model:value="formState.supplierId"
              optionFilterProp="name"
              @change="(val, option) => handleChange(val, option)"
              :field-names="{
                label: 'name',
                value: 'id',
              }"
            >
            </a-select>
          </a-form-item>
        </a-col>
        <!-- <a-col :span="8">
					<a-form-item label="开户行" name="openingBank">
						<a-input v-model:value="formState.openingBank" placeholder="请输入开户行" />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="银行账号" name="bankAccount">
						<a-input v-model:value="formState.bankAccount" placeholder="请输入银行账号" />
					</a-form-item>
				</a-col> -->
      </a-row>
      <a-row class="pb-5">
        <a-col :span="24">
          <a-form-item label="" name="detailParamList">
            <div class="mb-4">结算明细</div>
            <div class="pb-4">
              <mw-button
                title="新增"
                @click="batchAdd('add')"
                :disabled="!formState.supplierId"
                class="mr-2"
              ></mw-button>

              <mw-button
                title="删除"
                @click="start"
                danger
                :disabled="state.selectedRowKeys.length > 0 ? false : true"
              ></mw-button>
            </div>

            <mw-table
              :scroll="{ x: 'max-content' }"
              checkStrictly
              :row-selection="{
                selectedRowKeys: state.selectedRowKeys,
                onChange: deleteChange,
              }"
              :columns="
                openType == 'firstTime' ? columnsFirstTime : columnsDetermine
              "
              :data-source="formState.detailParamList"
              :pageConfig="paginationProps"
              :rowKey="(record) => record.indexNumber"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex == 'materialName'">
                  {{ record.materialNo }} / {{ record.materialName }} /
                  {{ record.materialSpecification }}
                </template>
                <template v-if="column.dataIndex === 'purchaseId'">
                  <a>{{
                    (record.purchaseId = record?.id ? record?.id : "")
                  }}</a>
                </template>

                <template v-if="column.dataIndex === 'unSettlementCount'">
                  {{ record.unSettlementCount }}

                  {{ record.unit }}
                </template>

                <template v-if="column.dataIndex === 'settlementCount'">
                  <div class="flex items-center">
                    <a-input-number
                      class="w-32 mr-1"
                      v-model:value="record.settlementCount"
                      @change="
                        (value) => {
                          settlementCountChange(value, record);
                        }
                      "
                      :stringMode="true"
                    ></a-input-number>
                    <span v-if="openType == 'firstTime'">{{
                      record.unit
                    }}</span>
                    <a v-else
                      >{{ record.beStoredCount }}/
                      {{
                        record.settlementCountTotal
                          ? record.settlementCountTotal
                          : 0
                      }}</a
                    >
                  </div>
                </template>
                <template
                  v-if="column.dataIndex === 'unMaterialArrivedCountReturn'"
                >
                  {{ record.unMaterialArrivedCountReturn }}
                </template>
                <template v-if="column.dataIndex === 'settlementPrice'">
                  <a-input-number
                    style="width: 130px"
                    placeholder="结算单价"
                    v-model:value="record.settlementPrice"
                    :formatter="formatter6"
                    @change="
                      (value) => {
                        settlementPriceChange(value, record);
                      }
                    "
                    :stringMode="true"
                  ></a-input-number>
                </template>
                <template v-if="column.dataIndex === 'taxRate'">
                  <a-select
                    class="w-full"
                    v-model:value="record.taxRate"
                    :options="taxRateLists"
                    placeholder="请选择税率"
                    @change="
                      (value) => {
                        onTaxRate(value, record);
                      }
                    "
                  />
                  <!-- <a-input-number
                    style="width: 130px"
                    v-model:value="record.taxRate"
                    placeholder="税率"
                    @change="
                      (value) => {
                        onTaxRate(value, record);
                      }
                    "
                    :stringMode="true"
                  ></a-input-number>
                  ></a-input-number> -->
                </template>
                <template v-if="column.dataIndex === 'settlementAmount'">
                  {{ record.settlementAmount }}
                </template>
                <template v-if="column.dataIndex === 'untaxedAmount'">
                  <a-input-number
                    style="width: 130px"
                    v-model:value="record.untaxedAmount"
                    placeholder="未税金额"
                    @change="changeUntaxedAmount($event, record)"
                    :stringMode="true"
                  ></a-input-number>
                </template>
              </template>
            </mw-table> </a-form-item
        ></a-col>
      </a-row>
      <a-row class="pb-5" style="width: 100%">
        <a-col :span="8"
          ><a-form-item label="产品总数" name="materialCount">
            <a-input
              v-model:value="formState.materialCount"
              placeholder="产品总数"
              disabled
            /> </a-form-item
        ></a-col>
        <a-col :span="8">
          <a-form-item label="含税总额" name="invoiceAmount">
            <a-input
              v-model:value="formState.invoiceAmount"
              disabled
              placeholder="含税总额"
            /> </a-form-item
        ></a-col>
        <a-col :span="8">
          <a-form-item label="未税总额" name="untaxedAmountTotal">
            <a-input
              v-model:value="formState.untaxedAmountTotal"
              disabled
              placeholder="未税总额"
            /> </a-form-item
        ></a-col>
      </a-row>
      <a-row class="pb-5" style="width: 100%">
        <a-col :span="8">
          <a-form-item
            label="税率"
            name="taxRate"
            :rules="[{ required: true, message: '请输入税率!' }]"
          >
            <a-input-number
              v-model:value="formState.taxRate"
              placeholder="税率"
              :stringMode="true"
            /> </a-form-item
        ></a-col>
        <a-col :span="8">
          <a-form-item
            label="发票号 "
            name="invoiceNumber"
            :rules="[{ required: true, message: '请输入发票号!' }]"
          >
            <a-input
              v-model:value="formState.invoiceNumber"
              placeholder="发票号"
            /> </a-form-item
        ></a-col>
        <a-col :span="8">
          <a-form-item
            label="开票日期"
            name="billingTime"
            :rules="[{ required: true, message: '请选择开票日期' }]"
          >
            <a-date-picker
              v-model:value="formState.billingTime"
              style="width: 100%"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledDateNoConThenBefore"
            /> </a-form-item
        ></a-col>
      </a-row>
      <a-row class="pb-5" style="width: 100%">
        <a-col :span="8">
          <a-form-item
            label="发票类型"
            name="invoiceType"
            :rules="[{ required: true, message: '请选择发票类型' }]"
          >
            <a-select
              :options="billingTypeList"
              v-model:value="formState.invoiceType"
              placeholder="业务类型"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="业务类型" name="businessType">
            <a-input v-model:value="formState.businessType" disabled></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="采购类型" name="purchaseType">
            <a-input
              v-model:value="formState.purchaseType"
              disabled
            ></a-input> </a-form-item
        ></a-col>
      </a-row>
    </a-form>
  </mw-drawer>
  <mw-drawer
    custom-title="选择新增明细"
    v-model:open="batchVisible"
    width="80%"
    @close="onClose('add')"
  >
    <template #header>
      <mw-button @click="formSubmit" :loading="submitLoading">确定</mw-button>
    </template>

    <mw-table
      checkStrictly
      :row-selection="rowSelection"
      :columns="
        openType == 'firstTime' ? columnsDetailFirstTime : detailColumns
      "
      :data-source="orderListData"
      :rowKey="(record) => record.indexNumber"
      hasPage
      :scroll="{ x: 'max-content' }"
      :pageConfig="paginationProps"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'materialName'">
          {{ record.materialName }}
        </template>
        <template v-if="column.dataIndex == 'type'">
          <div v-if="record.type == 'PURCHASE_REQUIRE'">需求采购</div>
          <div v-if="record.type == 'NORMAL'">普通采购</div>
          <div v-if="record.type == 'SAFE_STOCK'">安全库存采购订单</div>
          <div v-if="record.type == 'MARKET_ORDER'">销售订单采购</div>
          <div v-if="record.type == 'UPDATE_ORDER_BOM'">修改订单BOM采购</div>
        </template>

        <!-- materialName -->
        <template v-if="column.dataIndex == 'status'">
          <dictionary
            :statusOptions="purchaseStatusList"
            :value="record.status"
            isBackgroundColor
            :statusExtend="record.statusExtend"
          /> </template
      ></template>
    </mw-table>
  </mw-drawer>
</template>

<script setup>
import {
  reactive,
  ref,
  getCurrentInstance,
  computed,
  watch,
  defineEmits,
  defineProps,
} from "vue";
import { purchaseStatusList } from "@/common/constant.js";
import { issueInvoiceTypeList } from "@/common/constant.js";
import { returnSelectAllSupplier } from "@/api/purchase/returnOutbound.js";
import { billingTypeList } from "@/common/constant.js";
import { formatter6, formatter2, roundNumFun } from "@/common/validate.js";
import { getInfo } from "@/api/basicData/supplier.js";
import {
  settlemenupdatePurchaseSettlement,
  getDetail,
  getDetailV1,
  getUnSettlementBySupplierIdV1,
  getUnSettlementBySupplierIdV2,
  addPurchaseSettlement,
  settlementGetUnSettlementBySupplierIdV1,
  addInitialTreatment,
  CgSelectAllSupplier,
} from "@/api/settlement.js";
import { taxRateList } from "@/common/constant.js";
const emit = defineEmits(["update:visible", "finish"]);
const { proxy } = getCurrentInstance();
const selectedRowsList = ref();
const selectedRowKeysList = ref();
const formRef = ref();
const supplierListOptions = ref([]);
const batchVisible = ref(false);
const orderListData = ref([]);
const detailParamCopyList = ref([]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
  openType: String,
  openRecord: {
    type: Object,
    default: () => {
      return {};
    },
  },
  billType: String,
});
const taxRateLists = computed(() => {
  if (props.openType == "firstTime") {
    return taxRateList;
  } else {
    return [
      {
        label: 0,
        value: 0,
      },
      {
        label: 1,
        value: 1,
      },
      {
        label: 3,
        value: 3,
      },
      {
        label: 6,
        value: 6,
      },
      {
        label: 13,
        value: 13,
      },
    ];
  }
});
const customTitle = (openType) => {
  switch (openType) {
    case "add":
      return "新增采购发票";
    case "edit":
      return "编辑采购发票";
    case "firstTime":
      return "新增采购发票（期初处理）";
  }
};
const state = reactive({
  selectedRowKeys: [],
  loading: false,
});
const invoiceAmountData = computed(() => {
  let total = 0;
  formState.detailParamList.forEach((acc, cur) => {
    if (typeof acc?.settlementAmount === "number") {
      total += acc?.settlementAmount;
    } else {
    }
  });
  return roundNumFun(total, 6);
});
const untaxedAmountTotalData = computed(() => {
  let total = 0;
  formState.detailParamList.forEach((acc, cur) => {
    acc.untaxedAmount = Number(acc.untaxedAmount);
    if (typeof acc?.untaxedAmount == "number") {
      total += acc?.untaxedAmount;
      console.log(acc?.untaxedAmount);
    } else {
    }
  });
  return total;
});

const changeSettlementAmount = (val) => {
  if (val.settlementAmount && val.settlementPrice) {
    val.settlementCount = accDiv(val.settlementAmount, val.settlementPrice);
  }
};
const productCountData = computed(() => {
  let total = 0;
  formState.detailParamList.forEach((acc, cur) => {
    if (typeof acc?.settlementCount === "number") {
      total += acc?.settlementCount;
    } else {
    }
  });
  return total;
});

const formState = reactive({
  detailParamList: [],
  defaultSettlementNumbers: "系统自动生成",
  businessType: "默认",
  purchaseType: "默认",
  invoiceAmount: void 0,
  materialCount: void 0,
  taxRate: void 0,
  billingTime: void 0,
  invoiceNumber: void 0,
  invoiceType: void 0,
  issueInvoiceType: void 0,
  supplierId: void 0,
});
formState.materialCount = productCountData.value;
console.log(productCountData.value, " productCountData.value");
formState.invoiceAmount = invoiceAmountData.value;

const detailColumns = ref([
  { title: "物料编码", dataIndex: "materialNo", fixed: "left" },
  { title: "物料名称", dataIndex: "materialName", fixed: "left" },
  { title: "物料规格", dataIndex: "materialSpecification", fixed: "left" },
  { title: "采购订单号", dataIndex: "purchaseNumber" },
  { title: "到货单号", dataIndex: "recordNumber" },
  { title: "入库流水号", dataIndex: "batchId" },
  { title: "已入库数", dataIndex: "beStoredCount" },
  { title: "已结算数", dataIndex: "settlementCount" },
  { title: "未结算数", dataIndex: "unSettlementCount" },
  { title: "含税单价", dataIndex: "includingTaxPrice" },
  { title: "含税金额", dataIndex: "includingTaxAmount" },
  { title: "税率", dataIndex: "recordTaxRate" },
  { title: "未税单价", dataIndex: "untaxedPrice" },
  { title: "未税金额", dataIndex: "untaxedAmount" },
]);
const columnsDetermine = computed(() => {
  if (props.billType == 2) {
    return [
      { title: "采购订单号", dataIndex: "purchaseNumber" },
      { title: "退货单号", dataIndex: "recordNumberReturn" },
      { title: "退库流水号", dataIndex: "batchIdReturn" },
      // { title: "物料编号", dataIndex: "materialNo" },
      { title: "物料信息", dataIndex: "materialName" },
      { title: "结算数（已退货数/已结算数）", dataIndex: "settlementCount" },
      { title: "含税单价", dataIndex: "settlementPrice" },
      { title: "含税总金额", dataIndex: "settlementAmount" },
      { title: "税率%", dataIndex: "taxRate" },
      { title: "未税单价", dataIndex: "untaxedPrice" },
      { title: "未税金额", dataIndex: "untaxedAmount" },
    ];
  } else {
    return [
      { title: "采购订单号", dataIndex: "purchaseNumber" },
      { title: "到货单号", dataIndex: "recordNumber" },
      { title: "入库流水号", dataIndex: "batchId" },
      // { title: "物料编号", dataIndex: "materialNo" },
      { title: "物料信息", dataIndex: "materialName" },
      { title: "结算数（已入库数/已结算数）", dataIndex: "settlementCount" },
      { title: "退货数量", dataIndex: "unMaterialArrivedCountReturn" },
      { title: "含税单价", dataIndex: "settlementPrice" },
      { title: "含税总金额", dataIndex: "settlementAmount" },
      { title: "税率%", dataIndex: "taxRate" },
      { title: "未税单价", dataIndex: "untaxedPrice" },
      { title: "未税金额", dataIndex: "untaxedAmount" },
    ];
  }
});
const columnsFirstTime = ref([
  { title: "入库单号", dataIndex: "batchId" },
  { title: "物料信息", dataIndex: "materialName" },
  { title: "可开票数量", dataIndex: "unSettlementCount" },
  { title: "本次开票数量", dataIndex: "settlementCount" },
  { title: "含税单价", dataIndex: "settlementPrice" },
  { title: "含税金额", dataIndex: "settlementAmount" },
  { title: "税率", dataIndex: "taxRate" },
  { title: "未税单价", dataIndex: "untaxedPrice" },
  { title: "未税金额", dataIndex: "untaxedAmount" },
]);

const columnsDetailFirstTime = ref([
  { title: "入库单号", dataIndex: "batchId" },
  { title: "物料信息", dataIndex: "materialName" },
  { title: "可开票数量", dataIndex: "unSettlementCount" },
  { title: "未税单价", dataIndex: "untaxedPrice" },
  { title: "未税金额", dataIndex: "untaxedAmount" },
]);
const calculateNetPrice = (withTaxPrice, taxRate) => {
  // 将税率转换为小数形式
  const taxRateDecimal = taxRate / 100;
  const r = 1 + taxRateDecimal;
  // 计算不含税单价
  const netPrice = withTaxPrice / r;
  return netPrice;
};
const subtract = (arg1, arg2) => {
  var r1, r2, m, n;
  try {
    r1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  //last modify by deeka
  //动态控制精度长度
  n = r1 >= r2 ? r1 : r2;
  return (arg2 * m - arg1 * m) / m;
};

const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectedRowKeysList.value,
    selectedRows: selectedRowsList.value,
    onChange: (selectedRowKeys, selectedRows) => {
      selectedRowsList.value = selectedRows;
      selectedRowKeysList.value = selectedRowKeys;
    },
  };
});

// 供应商接口
const CgSelectAllSupplierList = async () => {
  let res;
  if (props.openType == "firstTime") {
    res = await CgSelectAllSupplier();
  } else {
    res = await returnSelectAllSupplier();
  }

  supplierListOptions.value = res.data.map((item) => {
    item.name = item.number + "/" + item.name;
    return item;
    // name: item.name;
    // value: item.id;
  });
};

const getSupplierINfo = async (e) => {
  // let res = await getInfo(e)
  // if (res.data.openingBank) {
  // 	console.log('[ res.data ] >', res.data)
  // 	formState.openingBank = res.data.openingBank
  // 	formState.bankAccount = res.data.bankAccount
  // }
};
const handleChange = async (e, val) => {
  console.log(e, val.number);
  formState.materialCount = 0;
  formState.invoiceAmount = 0;
  if (!e) return;
  formState.detailParamList = [];
  formState.valNumber = val.number;
  // formState.openingBank = val.openingBank
  // formState.bankAccount = val.bankAccount
  orderList(props.openType == "firstTime" ? val.number : e);
  getSupplierINfo(e);
};
const start = () => {
  formState.detailParamList = formState.detailParamList.filter(
    (item) => !state.selectedRowKeys.includes(item.indexNumber)
  );
  accumulation();
  state.selectedRowKeys = [];
};
const deleteChange = (selectedRowKeys) => {
  state.selectedRowKeys = selectedRowKeys;
};
// 采供应商--下面的结算明细列表
const orderList = async (e) => {
  console.log("[ e ] >", e);
  let res;
  if (props.openType == "firstTime") {
    res = await settlementGetUnSettlementBySupplierIdV1({
      supplierNumber: e,
    });
    console.log(res, "res");
  } else {
    if (props.billType == 2) {
      res = await getUnSettlementBySupplierIdV2({
        supplierId: e,
      });
    } else {
      res = await getUnSettlementBySupplierIdV1({
        supplierId: e,
      });
    }
  }
  console.log(res, "res.data------");
  orderListData.value = res.data?.map((item) => {
    item.settlementCountTotal = item?.settlementCount;
    if (props.openType == "firstTime") {
      item.indexNumber = item.batchId + item.materialId;
    }

    return item;
  });
};
const batchAdd = () => {
  batchVisible.value = true;
  selectedRowKeysList.value = formState.detailParamList.map(
    (item) => item.indexNumber
  );
  selectedRowsList.value = formState.detailParamList;
  orderList(
    props.openType == "firstTime" ? formState.valNumber : formState.supplierId
  );
  copiedArray.value = deepCopyArray(formState.detailParamList);
};
const onClose = (val) => {
  if (val == "add") {
    batchVisible.value = false;
  } else {
    emit("update:visible", false);
    formRef.value.resetFields();
  }
  formState.defaultSettlementNumbers = void 0;
};
const copiedArray = ref([]);
const changeUntaxedAmount = (e, val) => {
  formState.untaxedAmountTotal;
  formState.untaxedAmountTotal = roundNumFun(untaxedAmountTotalData.value, 2);
};

function deepCopyArray(source) {
  return JSON.parse(JSON.stringify(source));
}
const formSubmit = async () => {
  batchVisible.value = false;
  formState.materialCount = 0;
  formState.invoiceAmount = 0;
  let countTotal = 0;
  let priceTotal = 0;
  selectedRowsList.value.forEach((item) => {
    item.settlementCountTotal = item.settlementCountTotal;
    item.settlementCount = 0;
    item.purchaseId = item.id;
    if (props.openType == "firstTime") {
      console.log(item, "item");
      item.materialName = item.materialName;
      item.materialSpecification = item.materialSpecification;
      item.materialNo = item.materialNo;
      item.settlementPrice = item.includingTaxPrice || 0;
      item.settlementCount = item.unSettlementCount;
      item.untaxedPrice = item.untaxedPrice;
      item.untaxedAmount = item.untaxedAmount;
    } else {
      item.settlementPrice = item.purchaseUnitPrice;
      item.settlementCount = subtract(
        item.unMaterialArrivedCountReturn,
        subtract(item.settlementCountTotal, item.beStoredCount)
      );
    }

    item.settlementAmount = roundNumFun(
      numMulti(item.settlementCount, item.settlementPrice),
      2
    );
    countTotal = addNum(
      countTotal,
      item?.settlementAmount ? item.settlementAmount : 0
    );
    priceTotal = addNum(
      priceTotal,
      item?.settlementCount ? item.settlementCount : 0
    );

    if (props.openType == "firstTime") {
      item.untaxedPrice = item.untaxedPrice;
      item.untaxedAmount = item.untaxedAmount;
    } else {
      item.untaxedPrice = roundNumFun(
        calculateNetPrice(item.settlementPrice, item.taxRate),
        6
      );
      item.untaxedAmount = roundNumFun(
        numMulti(item.settlementCount, item.untaxedPrice),
        2
      );
    }
  });

  formState.materialCount = priceTotal;
  console.log(priceTotal, "priceTotal");
  formState.invoiceAmount = roundNumFun(countTotal, 6);
  const result = [];
  const idSet = new Set();
  copiedArray.value.forEach((item) => {
    console.log(item, "item");
    result.push(item);
    idSet.add(item.indexNumber);
  });
  selectedRowsList.value.forEach((item) => {
    if (!idSet.has(item.indexNumber)) {
      result.push(item);
    }
  });
  formState.detailParamList = [
    ...Array.from(JSON.parse(JSON.stringify(result))),
  ];
  orderListData.value = [];
  formState.untaxedAmountTotal = roundNumFun(untaxedAmountTotalData.value, 2);
};

const submitLoading = ref(false);
const submitIsStagingLoading = ref(false);
const determineSubmit = (Type) => {
  formRef.value.validate().then(async () => {
    try {
      // 校验结算数量不能超过可结算数量
      if (formState.detailParamList && formState.detailParamList.length > 0) {
        for (let record of formState.detailParamList) {
          const maxSettlementCount =
            (record.beStoredCount || 0) -
            (record.settlementCountTotal || 0) -
            (record.unMaterialArrivedCountReturn || 0);

          if (record.settlementCount > maxSettlementCount) {
            proxy.$message.error(
              `物料 ${
                record.materialName || record.materialNo
              } 的结算数量不能超过可结算数量！可结算数量：${maxSettlementCount}`
            );
            return;
          }
        }
      }

      if (Type) {
        submitIsStagingLoading.value = true;
      } else {
        submitLoading.value = true;
      }

      let prams = {
        isStaging: Type,
        ...formState,
      };
      console.log(detailParamCopyList.value, "value");
      console.log(prams.detailParamList, "value");
      detailParamCopyList.value.forEach((ite) => {
        prams.detailParamList.map((item) => {
          if (ite.indexNumber == item.indexNumber) {
            item.settlementId = ite.settlementId;
            item.settlementInfoId = item.id;
          }
          if (item.settlementId || !props.openRecord.id) {
            item.settlementInfoId = item.id;
          }
          return item;
        });
      });
      let res;
      if (props.openType == "firstTime") {
        res = await addInitialTreatment(prams);
      } else {
        if (props.openRecord.id) {
          prams.id = props.openRecord.id;
          res = await settlemenupdatePurchaseSettlement(prams);
        } else {
          res = await addPurchaseSettlement(prams);
        }
      }

      console.log(res, "res.code");
      if (res.code == 200) {
        try {
          // emit("update:visible", false);
          proxy.$message.success("保存成功");
          formState.detailParamList = [];
          formRef.value.resetFields();
          submitLoading.value = false;
          emit("finish");
          onClose();
        } catch (error) {
          submitLoading.value = false;
          submitIsStagingLoading.value = false;
        }
      }
      submitIsStagingLoading.value = false;
      submitLoading.value = false;
    } catch (error) {
      submitIsStagingLoading.value = false;
      submitLoading.value = false;
      return;
    }
  });
};

//加法
const addNum = (num1, num2) => {
  var sq1, sq2, m;
  try {
    sq1 = num1.toString().split(".")[1].length;
  } catch (e) {
    sq1 = 0;
  }
  try {
    sq2 = num2.toString().split(".")[1].length;
  } catch (e) {
    sq2 = 0;
  }
  m = Math.pow(10, Math.max(sq1, sq2));
  return (num1 * m + num2 * m) / m;
};

//乘法
function numMulti(num1, num2) {
  var baseNum = 0;
  try {
    baseNum += num1.toString().split(".")[1].length;
  } catch (e) {}
  try {
    baseNum += num2.toString().split(".")[1].length;
  } catch (e) {}
  return (
    (Number(num1.toString().replace(".", "")) *
      Number(num2.toString().replace(".", ""))) /
    Math.pow(10, baseNum)
  );
}

//除法
const accDiv = (arg1, arg2) => {
  let t1 = 0;
  let t2 = 0;
  let r1, r2;
  try {
    t1 = arg1.toString().split(".")[1].length;
  } catch (e) {}
  try {
    t2 = arg2.toString().split(".")[1].length;
  } catch (e) {}
  r1 = Number(arg1.toString().replace(".", ""));
  r2 = Number(arg2.toString().replace(".", ""));
  let intDiv = r1 / r2;
  let pow = Math.pow(10, t2 - t1);
  return numMulti(intDiv, pow); // 这里用上面定义好的乘法运算
};

const accumulation = () => {
  const data = formState.detailParamList;
  let countTotal = 0;
  let priceTotal = 0;
  data.forEach((item) => {
    countTotal = addNum(
      countTotal,
      item?.settlementAmount ? item.settlementAmount : 0
    );
    priceTotal = addNum(
      priceTotal,
      item?.settlementCount ? item.settlementCount : 0
    );
  });
  formState.materialCount = priceTotal
    .toFixed(10)
    .replace(/(\.[\d]+?)0+$/, "$1")
    .replace(/\.0+$/, "");
  console.log(priceTotal, "priceTotal");
  formState.invoiceAmount = roundNumFun(countTotal, 6);
  formState.untaxedAmountTotal = roundNumFun(untaxedAmountTotalData.value, 2);
};

const settlementPriceChange = (value, record) => {
  value = value ? value : 0;
  const countNum = record?.settlementCount ? record.settlementCount : 0;
  record.settlementAmount = numMulti(value, countNum);
  record.settlementAmount = roundNumFun(numMulti(value, countNum), 2);
  console.log(value, "valuevalue", record.taxRate);
  record.untaxedPrice = roundNumFun(
    calculateNetPrice(value, Number(record.taxRate)),
    6
  );
  formState.untaxedAmountTotal = roundNumFun(untaxedAmountTotalData.value, 2);
  record.untaxedAmount = roundNumFun(
    numMulti(record.settlementCount, record.untaxedPrice),
    2
  );
  accumulation();
};

const settlementCountChange = (value, record) => {
  value = value ? value : 0;
  record.untaxedPrice = roundNumFun(
    calculateNetPrice(record.settlementPrice, record.taxRate),
    6
  );
  const priceNum = record?.settlementPrice ? record.settlementPrice : 0;
  record.settlementAmount = numMulti(value, priceNum);
  record.settlementAmount = roundNumFun(numMulti(value, priceNum), 2);
  formState.untaxedAmountTotal = untaxedAmountTotalData.value;
  record.untaxedAmount = roundNumFun(numMulti(value, record.untaxedPrice), 2);
  accumulation();
};
const onTaxRate = (value, record) => {
  value = value ? value : 0;
  record.untaxedPrice = roundNumFun(
    calculateNetPrice(record.settlementPrice, value),
    6
  );

  record.untaxedAmount = roundNumFun(
    numMulti(record.settlementCount, record.untaxedPrice),
    2
  );

  formState.untaxedAmountTotal = roundNumFun(untaxedAmountTotalData.value, 2);
};
const getDetailList = async (val) => {
  let res;
  if (val.issueInvoiceType == 2) {
    res = await getDetailV1({ settlementId: val.id });
  } else {
    res = await getDetail({ settlementId: val.id });
  }

  res.data.materialDetailVOList.forEach((item, index) => {
    item.settlementCountTotal = item.settlementCount;
  });
  for (var i in formState) {
    formState[i] = res.data[i];
    if (i == "supplierId") {
      console.log(Number(res.data[i]), "i");
      formState[i] = Number(res.data[i]);
    }
    if (i == "detailParamList") {
      console.log(Number(res.data[i]), "i");
      formState[i] = res.data.materialDetailVOList;
    }
    if (i == "defaultSettlementNumbers") {
      console.log(Number(res.data[i]), "i");
      formState[i] = res.data.settlementNumber;
    }
    console.log(i, "iii");
  }
  formState.detailParamList.map((item, index) => {
    console.log(item, "settledCount");
    item.settlementCountTotal = item.settledCount;
  });
  detailParamCopyList.value = res.data.materialDetailVOList;

  formState.untaxedAmountTotal = roundNumFun(untaxedAmountTotalData.value, 2);
  getSupplierINfo(formState.supplierId);
  // formState = res.data;
};
watch(
  () => props.billType,
  (val) => {
    formState.issueInvoiceType = val;
  }
);
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      if (props.openRecord.id) {
        getDetailList(props.openRecord);
      } else {
        formRef.value && formRef.value.resetFields();
        formState.detailParamList = [];
      }
      console.log(props.openRecord, "openRecord");
      CgSelectAllSupplierList();
    }
  }
);
</script>

<style></style>
