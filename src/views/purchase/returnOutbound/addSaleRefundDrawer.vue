<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="80%"
    :customTitle="
      props.operationType == 'add'
        ? '新增退货出库单'
        : props.operationType == 'detail'
        ? '退货出库单详情' +
          `&nbsp &nbsp &nbsp 付款单号：${detailList?.returnNo || '--'}` +
          `&nbsp &nbsp &nbsp ${
            detailList?.status == 0
              ? '研发中'
              : detailList?.status == 1
              ? '生产中'
              : detailList?.status == 2
              ? '待出库'
              : detailList?.status == 3
              ? '已邮寄'
              : detailList?.status == 7
              ? '待审批'
              : detailList?.status == 8
              ? '审批通过'
              : detailList?.status == 9
              ? '审批不通过'
              : detailList?.status == 10
              ? '审批撤回'
              : ''
          }`
        : '编辑退货出库单'
    "
    :destroyOnClose="true"
  >
    <template #header>
      <mw-button
        title="确定"
        v-if="props.operationType !== 'detail'"
        @click="formSubmit"
        :loading="submitLoading"
      ></mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
    >
      <a-row>
        <a-col :span="24" class="flex justify-between">
          <a-form-item label="供应商" name="supplierId" required class="w-1/3">
            <a-select
              style="width: 100%"
              placeholder="供应商"
              :options="supplierListOptions"
              v-model:value="formData.supplierId"
              optionFilterProp="name"
              @change="handleChange"
              :field-names="{
                label: 'name',
                value: 'id',
              }"
              show-search
              :disabled="props.operationType == 'detail' ? true : false"
            >
            </a-select>
          </a-form-item>
          <a-form-item label="操作人" v-if="props.operationType == 'detail'">
            <a-input
              v-model:value="formData.createByName"
              :disabled="props.operationType == 'detail'"
            ></a-input>
          </a-form-item>
          <a-form-item label="操作时间" v-if="props.operationType == 'detail'">
            <a-input
              v-model:value="formData.createTime"
              :disabled="props.operationType == 'detail'"
            ></a-input>
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <div>
            <a-form-item label="退货明细" required>
              <mw-button
                title="新增"
                v-if="props.operationType !== 'detail'"
                @click="handleAdd"
                :disabled="
                  formData.supplierId && props.operationType !== 'detail'
                    ? false
                    : true
                "
              ></mw-button>
              <mw-button
                title="删除"
                v-if="props.operationType !== 'detail'"
                @click="start"
                danger
                :disabled="selectedRowKeysList?.length > 0 ? false : true"
              ></mw-button>
            </a-form-item>
          </div>
          <mw-table
            :scroll="{ x: 'max-content' }"
            class="leading-5.5"
            :data-source="formData.returnMaterialDetails"
            :columns="visibleColumns"
            :rowKey="(record) => record?.wmdrId"
            :loading="tableLoading"
            :pagination="pagination"
            @change="handlePageChange"
            :row-selection="{
              selectedRowKeys: selectedRowKeysList,
              onChange: onSelectChange,
              getCheckboxProps: getCheckboxProps,
            }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'quantity'">
                <a-input-number
                  :disabled="props.operationType == 'detail'"
                  style="width: 100%"
                  v-model:value="record.quantity"
                  placeholder="请输入"
                  allow-clear
                  :formatter="formatter2"
                  :stringMode="true"
                />
              </template>
              <template v-if="column.dataIndex === 'remark'">
                <a-input
                  :disabled="props.operationType == 'detail'"
                  style="width: 100%"
                  v-model:value="record.remark"
                  placeholder="请输入备注"
                  allow-clear
                />
              </template>
            </template>
          </mw-table>
        </a-col>
        <a-col :span="12">
          <a-form-item label="备注" class="mt-4">
            <a-textarea
              :disabled="props.operationType == 'detail' ? true : false"
              :rows="4"
              style="width: 100%"
              v-model:value="formData.remark"
              placeholder="请输入备注"
              allow-clear
            />
          </a-form-item>
        </a-col>
        <a-col :span="11" class="mt-4 ml-4">
          <a-form-item label="相关文件">
            <!-- <a-input v-model:value="formData.successNum"  /> -->
            <!-- <form-upload
              v-else
              v-model:value="formData.file"
              sence="delivery"
              :fileTypes="[]"
              :fileSize="100"
              hasDownLoad
              :fileLimit="9999999999"
            ></form-upload> -->
            <form-upload-contract
              v-model:value="formData.file"
              sence="article"
              :fileTypes="[]"
              :fileSize="100"
              :readonly="props.operationType == 'detail' ? true : false"
              hasDownLoad
              :fileLName="true"
              :fileLimit="9999"
              :detailType="'1'"
              :msgCodeName="formData.msgCode"
              @del="(index) => delFile(index, 'remarkFileList')"
              :delShow="true"
            >
            </form-upload-contract>
            <!-- <div v-else>
              <div class="overflow" v-if="formData.file?.fileVisitUrl">
                <i
                  class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                  style="color: #959ec3"
                ></i>
                <a
                  :href="formData.file?.fileVisitUrl"
                  :title="formData.file?.fileName"
                  target="_blank"
                  class="underline"
                  style="color: #959ec3"
                  >{{ formData.file?.fileName }}
                </a>
              </div>
            </div> -->
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <returnsDeatil
      v-model:visible="returnsDetailVisible"
      :returnsDetailSupplierVal="returnsDetailSupplierVal"
      @onReturnsDetailList="onReturnsDetailList"
      :returnMaterialDetails="formData.returnMaterialDetails"
    ></returnsDeatil>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
  defineProps,
  defineEmits,
  watch,
} from "vue";
import FormUploadContract from "@/components/form-upload-contract.vue";
import { formatter2 } from "@/common/validate.js";
import FormUpload from "@/components/form-upload.vue";
import {
  returnAdd,
  returnSelectAllSupplier,
} from "@/api/purchase/returnOutbound.js";
import { dateFormat } from "@/utils/util.js";
import returnsDeatil from "./returnsDeatil.vue";
import { returnGetInfo } from "@/api/purchase/returnOutbound.js";
const emit = defineEmits(["update:visible", "finish"]);
const { proxy } = getCurrentInstance();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
  operationType: {
    type: String,
    required: true,
  },
});
const selectedRowsList = ref();
const selectedRowKeysList = ref();
const returnsDetailSupplierVal = ref();
const returnsDetailVisible = ref(false);
const materialListData = ref([]);
const supplierListOptions = ref([]);
const formRef = ref();
const submitLoading = ref(false);
const detailList = ref();
const formData = reactive({
  returnMaterialDetails: [],
  // refundTime: dateFormat(),
});
const columnsRelationProduct = ref([
  {
    title: "采购订单号",
    dataIndex: "purchaseNumber",
  },
  {
    title: "到货单号",
    dataIndex: "recordNumber",
  },
  {
    title: "入库流水号",
    dataIndex: "batchId",
  },

  {
    title: "物料编号",
    dataIndex: "materialNo",
  },

  {
    title: "物料名称",
    dataIndex: "materialName",
  },
  {
    title: "物料规格",
    dataIndex: "materialSpecification",
  },
  {
    title: "已入库数",
    dataIndex: "sbQuantity",
  },
  {
    title: "已结算数",
    dataIndex: "settlementCount",
  },
  {
    title: "可退货数",
    dataIndex: "returnableQuantity",
  },
  {
    title: "本次退货数",
    dataIndex: "quantity",
  },
  {
    title: "备注",
    dataIndex: "remark",
  },
]);
const rules = reactive({
  supplierId: [
    {
      required: true,
      trigger: "blur",
      message: "请选择供应商",
    },
  ],
});

const visibleColumns = computed(() => {
  if (props.operationType !== "detail") {
    return columnsRelationProduct.value; // 如果满足条件，返回所有列
  } else {
    return columnsRelationProduct.value.filter(
      (item) =>
        item.dataIndex !== "sbQuantity" &&
        item.dataIndex !== "settlementCount" &&
        item.dataIndex !== "returnableQuantity"
    );
  }
});

const onSelectChange = (selectedRowKeys, selectedRows) => {
  selectedRowsList.value = selectedRows;
  selectedRowKeysList.value = selectedRowKeys;
};
const getCheckboxProps = (record) => ({
  disabled: props.operationType == "detail",
});

// 供应商接口
const returnSelectAllSupplierList = async () => {
  let res = await returnSelectAllSupplier();
  supplierListOptions.value = res.data;
};

const handleChange = (e, val) => {
  formData.returnMaterialDetails = [];
  returnsDetailSupplierVal.value = val;
};

// 产品明细添加
const handleAdd = () => {
  returnsDetailVisible.value = true;
};
const start = () => {
  formData.returnMaterialDetails = formData.returnMaterialDetails.filter(
    (item) => !selectedRowKeysList.value.includes(item.wmdrId)
  );
  selectedRowKeysList.value = [];
};

// 确认新增
const formSubmit = async () => {
  // 校验是否所有的采购订单号一致
  const allSame = formData.returnMaterialDetails.every(
    (item) =>
      item.purchaseNumber === formData.returnMaterialDetails[0].purchaseNumber
  );
  if (!allSame) {
    proxy.$message.error("请选择相同的采购订单进行退货");
    return;
  }
  formRef.value.validate().then(async () => {
    try {
      submitLoading.value = true;
      let param = {
        ...formData,
      };
      // return;
      let res = await returnAdd(param);
      if (res.code == 200) {
        proxy.$message.success("操作成功");
        onClose();
      }
      submitLoading.value = false;
      emit("finish");
    } catch (error) {
      submitLoading.value = false;
    }
  });
};
// 关闭
function onClose() {
  emit("update:visible", false);
  formRef.value.resetFields();
  formData.file = [];
  formData.returnMaterialDetails = [];
  formData.remark = void 0;
}
const onReturnsDetailList = (val, valId) => {
  formData.returnMaterialDetails = val.map((item) => {
    delete item.quantity;
    return item;
  });
  // state.selectedRowKeys = valId;
};
// 详情接口
const detailReturnGetInfo = async (val) => {
  let res = await returnGetInfo({ id: val });
  detailList.value = res.data;
  formData.supplierId = Number(res.data.supplierId);
  formData.returnMaterialDetails = res.data.detailVOList;
  formData.remark = res.data.remark;
  formData.file = res.data.file;
  formData.createByName = res.data.createByName;
  formData.createTime = res.data.createTime;
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      returnSelectAllSupplierList();
      if (props.operationType == "detail") {
        detailReturnGetInfo(props.id);
      }
    }
  }
);
</script>
<style lang="less" scoped>
:deep(.ant-form label) {
  padding: 10px;
}
</style>
