<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="92%"
    :customTitle="props.id ? '编辑退货出库单' : '新增退货出库单'"
    :destroyOnClose="true"
  >
    <template #header>
      <mw-button
        title="确定"
        @click="formSubmit"
        :loading="submitLoading"
      ></mw-button>
    </template>
    供应商：{{ returnsDetailSupplierVal.name }}
    <div class="mb-4 text-right">
      <search :searchData="searchData" @search="refresh"> </search>
    </div>

    <mw-table
      :scroll="{ x: 'max-content' }"
      class="leading-5.5"
      :columns="columns"
      :data-source="materialListData"
      :rowKey="(record) => record.wmdrId"
      :loading="tableLoading"
      :pagination="pagination"
      @change="handlePageChange"
      :row-selection="{
        selectedRowKeys: warehouseList.value,
        onChange: onSelectChange,
        preserveSelectedRowKeys: true,
      }"
    ></mw-table>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  onMounted,
  reactive,
  getCurrentInstance,
  defineProps,
  defineEmits,
  watch,
} from "vue";
import Search from "@/components/search/index.vue";
import { returnSelectReturnableSupplier } from "@/api/purchase/returnOutbound.js";
import { usePagenation } from "@/common/setup";
const emit = defineEmits(["update:visible", "finish", "onReturnsDetailList"]);
const { proxy } = getCurrentInstance();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  returnsDetailSupplierVal: {
    type: Object,
    default: {},
  },
  returnMaterialDetails: {
    type: Array,
    default: [],
  },
});
const warehouseList = reactive([]);
const materialListData = ref([]);
const submitLoading = ref(false);
const returnsDetailList = ref();

const columns = ref([
  {
    title: "采购订单号",
    dataIndex: "purchaseNumber",
  },
  {
    title: "到货单号",
    dataIndex: "recordNumber",
  },
  {
    title: "入库流水号",
    dataIndex: "batchId",
  },
  {
    title: "物料编号",
    dataIndex: "materialNo",
  },
  {
    title: "物料名称",
    dataIndex: "materialName",
  },
  {
    title: "物料规格",
    dataIndex: "materialSpecification",
  },
  {
    title: "已入库数",
    dataIndex: "quantity",
  },
  {
    title: "已结算数",
    dataIndex: "settlementCount",
  },
  {
    title: "可退货数",
    dataIndex: "returnableQuantity",
  },
]);

const searchData = ref({
  searchButtons: [],
  operationButtons: [],
  fields: {
    keyword: {
      type: "a-input-search",
      placeholder: "请输入(采购/到货单号/入库流水号/物料编号/物料名称)",
      allowClear: true,
      width: "420px",
    },
  },
});
const rules = reactive({
  supplierId: [
    {
      required: true,
      trigger: "blur",
      message: "请选择供应商",
    },
  ],
});

//供应商下数据
// returnSelectAllSupplier
const returnSelectMaterialListBySupplierList = async (val) => {
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let res = await returnSelectReturnableSupplier({
    supplierId: props.returnsDetailSupplierVal.id,
    ...searchParam,
  });
  // res.data.forEach((item, index) => {
  //   item.materialName = item.materialNo + " / " + item.materialName;
  // });
  materialListData.value = res?.data[0]?.detailList;
};
const { paginationProps, onTableChange, refresh, pageParam } = usePagenation(
  returnSelectMaterialListBySupplierList
);

const onSelectChange = (selectedRowKeys, selectedRows) => {
  warehouseList.value = selectedRowKeys;
  returnsDetailList.value = selectedRows.map((item) => {
    return {
      ...item,
      sbQuantity: item.quantity,
    };
  });
};

// 关闭
function onClose() {
  searchData.value.fields.keyword.value = "";
  emit("update:visible", false);
}
const formSubmit = () => {
  emit("onReturnsDetailList", returnsDetailList.value, warehouseList.value);
  onClose();
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      // warehouseList.value
      let data = props.returnMaterialDetails;
      const ids = data.map((item) => item.wmdrId);
      warehouseList.value = ids;
      returnsDetailList.value = data;
      returnSelectMaterialListBySupplierList();
    }

    // warehouseList.value=
  }
);
</script>
<style lang="less" scoped></style>
