<template>
  <div class="mb-4 text-right">
    <search :searchData="searchData" @search="refresh">
      <mw-button
        title="新增"
        :font="'iconfont icon-xianxing-121'"
        @click="onOpen('add')"
      ></mw-button>
    </search>
  </div>
  <mw-table
    :scroll="{ x: 'max-content' }"
    :columns="columns"
    :data-source="dataList"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'status'">
        <dictionary
          :statusOptions="purchaseReturnsList"
          :value="record.status"
          isBackgroundColor
        />
      </template>

      <template v-if="column.key == 'button'">
        <mw-button
          title="详情"
          @click="onOpenDetail('detail', record.id)"
        ></mw-button>
      </template>
    </template>
  </mw-table>
  <!-- 新增 -->
  <add-sale-refund-drawer
    ref="refAddContractReviewDrawer"
    v-model:visible="visibleAddContractReviewDrawer"
    @finish="getList"
    v-model:id="contractReviewId"
    :operationType="operationType"
  />
</template>
<script setup>
import Search from "@/components/search/index.vue";
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import { usePagenation } from "@/common/setup";
import addSaleRefundDrawer from "./addSaleRefundDrawer.vue";
import { returnList } from "@/api/purchase/returnOutbound.js";
import { statusList, purchaseReturnsList } from "@/common/constant.js";

const columns = ref([
  {
    title: "退货单号",
    dataIndex: "returnNo",
    key: "returnNo",
    width: "130px",
  },
  {
    title: "供应商",
    dataIndex: "supplierName",
    key: "supplierName",
    width: "180px",
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status",
    width: "150px",
  },
  {
    title: "退货数量",
    dataIndex: "totalQuantity",
    key: "totalQuantity",
    width: "150px",
  },
  {
    title: "操作人",
    dataIndex: "returnByName",
    key: "returnByName",
    width: "150px",
  },
  {
    title: "操作时间",
    dataIndex: "returnDate",
    key: "returnDate",
    width: "150px",
  },
  {
    title: "操作",
    dataIndex: "button",
    key: "button",
    width: "100px",
    fixed: "right",
  },
]);

const searchData = ref({
  searchButtons: [],
  operationButtons: [],
  fields: {
    status: {
      name: "状态",
      type: "a-select",
      options: [
        { label: "全部状态", value: "" },
        ...purchaseReturnsList.filter(
          (t) => t.value == 10 || t.value == 9 || t.value == 8 || t.value == 7
        ),
      ],
      placeholder: "选择状态",
      width: "120px",

      value: "",
    },
    rangeDate: {
      type: "a-range-picker",
      value: [],
      valueFormat: "YYYY-MM-DD",
      width: "240px",
      placeholder: ["开始日期", "结束日期"],
    },
    keyword: {
      type: "a-input-search",
      placeholder: "请输入退货单号/供应商名称",
      width: "270px",
      allowClear: true,
    },
  },
});

const operationType = ref();
const { proxy } = getCurrentInstance();
const visibleDetail = ref(false);
const visibleAddContractReviewDrawer = ref(false);
const contractReviewId = ref("");
const dataList = ref([]);
const loading = ref(false);
const refAddContractReviewDrawer = ref(null);
const getList = async () => {
  loading.value = true;
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startDate = rangeDate[0];
    searchParam.endDate = rangeDate[1];
  }
  let result = await returnList(searchParam, { ...pageParam.value });
  dataList.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

const onOpen = (valType, valId) => {
  contractReviewId.value = valId;
  visibleAddContractReviewDrawer.value = true;
  operationType.value = valType;
};

const onOpenDetail = (valType, valId) => {
  contractReviewId.value = valId;
  visibleAddContractReviewDrawer.value = true;
  operationType.value = valType;
};

async function onConfirm(record) {
  getList();
}
</script>
<style lang="less" scoped>
:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  background: #fff;
}
:deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
  background: #fff;
}
</style>
