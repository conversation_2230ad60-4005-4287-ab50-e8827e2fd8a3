<template>
  <search :searchData="searchData" @search="refresh">
    <mw-button
      title="导出Excel"
      @click="exportOrg"
      :loading="exportLoading"
      :disabled="!data?.length"
      v-permission="'purchase:order:exportExcel'"
    ></mw-button>
  </search>
  <mw-table
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    :scroll="{ x: 'max-content' }"
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'supplierId'">
        <div class="">
          {{ record.supplierName }} / {{ record.relationName }} /
          {{ record.purchaseCount }}
        </div>
      </template>
      <template v-if="column.key == 'status'">
        <div class="">
          <dictionary
            :statusOptions="purchaseStatus"
            :value="record.status"
            :statusExtend="record.statusExtend"
            isBackgroundColor
          />
        </div>
      </template>
      <template v-if="column.dataIndex == 'button'">
        <mw-button
          title="详情"
          @click="rowClick(record)"
          class="mr-2"
        ></mw-button>
        <mw-button
          title="导出"
          @click="pdfOrg(record)"
          :loading="record.handleSendLoading"
          v-permission="'purchase:order:exportExcel'"
        ></mw-button>
      </template>
    </template>
  </mw-table>
  <requistion-create-drawer
    v-model:visible="addVisible"
    :id="id"
    @finish="getList"
  />
  <orderDetail
    v-model:visible="detailVisible"
    :id="detailId"
    :detailType="detailType"
  ></orderDetail>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  reactive,
  toRaw,
  getCurrentInstance,
  defineEmits,
} from "vue";
import {
  page,
  exportExcel,
  exportWarehousePdf,
  exportWord,
} from "@/api/purchase/order.js";
import { usePagenation } from "@/common/setup";
import Search from "@/components/search/index.vue";
import {
  purchaseStatus,
  searchStatus,
  purchaseStatusFilters,
  purchaseOrderType,
} from "@/common/constant.js";
import { uniqueArr, exportExecl } from "@/utils/util.js";
import { useRoute, useRouter } from "vue-router";
import requistionCreateDrawer from "./components/requistionCreateDrawer.vue";
import orderDetail from "./orderTab.vue";

const columns = ref([
  {
    title: "采购单号",
    key: "purchaseNumber",
    dataIndex: "purchaseNumber",
  },
  {
    title: "生成日期",
    key: "createTime",
    dataIndex: "createTime",
  },
  {
    title: "供应商",
    key: "supplierId",
    dataIndex: "supplierId",
  },
  {
    title: "采购状态",
    key: "status",
    dataIndex: "status",
  },
  {
    title: "采购总数",
    key: "purchaseCount",
    dataIndex: "purchaseCount",
  },
  {
    title: "到货数量",
    key: "materialArrivedCount",
    dataIndex: "materialArrivedCount",
  },
  {
    title: "已入库数量",
    key: "hasMaterialArrivedTotalCount",
    dataIndex: "hasMaterialArrivedTotalCount",
  },
  {
    title: "已结算数量",
    key: "settlementCount",
    dataIndex: "settlementCount",
  },

  {
    title: "操作",
    dataIndex: "button",
    fixed: "right",
  },
]);
const { proxy } = getCurrentInstance();
const router = useRouter();
const id = ref("");
const detailId = ref("");
const addVisible = ref(false);
const detailVisible = ref(false);
const detailType = ref();
const data = ref([]);
const loading = ref(false);
const exportLoading = ref(false);

const searchData = reactive({
  fields: {
    status: {
      name: "采购状态",
      type: "a-select",
      options: searchStatus,
      placeholder: "选择状态",
      width: "120px",
      value: "",
      allowClear: true,
    },
    rangeDate: {
      type: "a-range-picker",
      valueFormat: "YYYY-MM-DD",
      value: [],
      width: "240px",
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: "订单编号/编码搜索/供应商名称/供应商编码",
      width: "350px",
      allowClear: true,
    },
  },
});
const showCreateDrawer = () => {
  id.value = undefined;
  addVisible.value = true;
};
const getList = async () => {
  loading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await page(pageParam.value, searchParam);
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
const rowClick = (record) => {
  detailVisible.value = true;
  detailId.value = record.id;
  detailType.value = record.type;
  // router.push({ name: "PurchaseOrderDetail", query: { id: record.id } });
};
const exportOrg = async () => {
  exportLoading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await exportExcel(searchParam, pageParam.value);
  const fileName = "采购订单.xlsx";
  exportExecl(fileName, result);
  exportLoading.value = false;
};
const pdfOrg = async (record) => {
  record.handleSendLoading = true;
  // let result = await exportWarehousePdf({ purchaseId: record.id });
  let result = await exportWord({ purchaseId: record.id });
  const fileName = "采购订单_" + record.purchaseNumber + ".docx";
  exportExecl(fileName, result);
  record.handleSendLoading = false;
};
</script>
<style lang="less" scoped>
:deep(.ant-form-item) {
  margin-bottom: 16px;
}
:deep(.ant-table-cell) {
  vertical-align: top;
}
</style>
