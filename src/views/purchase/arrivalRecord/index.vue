<template>
  <search :searchData="searchData" @search="refresh"> </search>

  <mw-table
    :scroll="{ x: 'max-content' }"
    :align="center"
    :columns="columns.fatColumns"
    :data-source="arrivalRecordList"
    class="tables"
    :rowKey="(record) => record.id"
    childrenColumnName="record.settlementInfoVOList"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'lockingStatus'">
        <dictionary
          :statusOptions="arrivalRecordStatus"
          :value="record.lockingStatus"
          :statusExtend="record.statusExtend"
        />
      </template>
      <template v-if="column.key == 'operation'">
        <mw-button
          title="详情"
          @click="detailTabClick('arrivalRecordTrue', record)"
        ></mw-button>
      </template>
    </template>
  </mw-table>
  <arrivalRecord
    @finish="getDetail"
    v-model:visible="arrivalRecordVisible"
    :id="orderId"
    :openType="openType"
    :lockingStatus="lockingStatus"
    @success="success"
  ></arrivalRecord>
</template>
<script setup>
import { ref, onBeforeMount, reactive, toRaw } from "vue";
import { arrivalRecordPage } from "@/api/purchase/arrivalRecord.js";
import { usePagenation } from "@/common/setup";
import Search from "@/components/search/index.vue";
import { arrivalRecordStatus } from "@/common/constant.js";
import arrivalRecord from "../orderTabs/arrivalRecord.vue";
const arrivalRecordList = ref([]);
const orderId = ref([]);
const lockingStatus = ref();
const arrivalRecordVisible = ref(false);
const loading = ref(false);
const openType = ref();
const searchData = reactive({
  fields: {
    status: {
      name: "结算状态",
      type: "a-select",
      options: [{ label: "全部", value: "" }, ...arrivalRecordStatus],
      placeholder: "选择状态",
      width: "120px",
      value: "",
      allowClear: true,
    },
    rangeDate: {
      type: "a-range-picker",
      valueFormat: "YYYY-MM-DD",
      value: [],
      width: "240px",
      allowClear: true,
    },

    keyword: {
      type: "a-input-search",
      placeholder: `到货单号/采购单号/供应商(名称、编码)/物料编码`,
      width: "380px",
      allowClear: true,
    },
  },
});
const columns = ref({
  fatColumns: [
    {
      title: "到货单号",
      dataIndex: "recordNumber",
      key: "recordNumber",
    },
    {
      title: "采购订单号",
      dataIndex: "purchaseNumber",
      key: "purchaseNumber",
    },
    {
      title: "到货时间",
      dataIndex: "createTime",
      key: "createTime",
    },
    {
      title: "创建人",
      dataIndex: "createByName",
      key: "createByName",
    },
    {
      title: "供应商",
      dataIndex: "supplierName",
      key: "supplierName",
    },
    {
      title: "状态",
      dataIndex: "lockingStatus",
      key: "lockingStatus",
    },
    {
      title: "到货总数",
      dataIndex: "qualityTestingCount",
      key: "qualityTestingCount",
    },
    {
      title: "到货总金额",
      dataIndex: "arrivalTotalPrice",
      key: "arrivalTotalPrice",
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      fixed: "right",
      scopedSlots: {
        customRender: "operation",
      },
    },
  ],
});

const getList = async () => {
  loading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await arrivalRecordPage(pageParam.value, {
    ...searchParam,
  });
  arrivalRecordList.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
const success = () => {
  getList();
};
const detailTabClick = (type, val) => {
  openType.value = type;
  orderId.value = val.recordNumber;
  lockingStatus.value = val.lockingStatus;
  arrivalRecordVisible.value = true;
};
</script>
<style lang="less" scoped></style>
