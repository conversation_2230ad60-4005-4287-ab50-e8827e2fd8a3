<template>
  <mw-drawer
    custom-title=""
    :visible="visible"
    @close="onClose"
    @spinning="spinning"
    :closable="false"
    width="80%"
    destroyOnClose="true"
    :footer-style="{ textAlign: 'right' }"
  >
    <a-tabs v-model:activeKey="activeKey" @tabClick="getTabName" animated>
      <a-tab-pane key="1" tab="订单详情">
        <orderDetail :id="idValue"></orderDetail>
      </a-tab-pane>
      <!-- <a-tab-pane key="2" tab="审批" force-render>
        <examineDetail :id="idValue" :data="examineDetailData"></examineDetail>
      </a-tab-pane> -->

      <a-tab-pane key="3" tab="合同" v-if="detailType == 'MARKET_ORDER'">
        <contractReview
          :id="idValue"
          :data="contractReviewData"
        ></contractReview>
      </a-tab-pane>
      <a-tab-pane key="4" tab="销售订单" v-if="detailType == 'MARKET_ORDER'">
        <saleOrderDetail
          :id="idValue"
          :data="saleOrderDetailData"
        ></saleOrderDetail>
      </a-tab-pane>
      <a-tab-pane key="5" tab="采购结算">
        <purchaseBalanceDetail
          :id="idValue"
          :data="purchaseBalanceDetailData"
        ></purchaseBalanceDetail>
      </a-tab-pane>
      <a-tab-pane key="6" tab="采购需求">
        <procurementRequirements
          :id="idValue"
          :data="procurementRequirementsData"
        ></procurementRequirements>
      </a-tab-pane>
    </a-tabs>
  </mw-drawer>
</template>

<script setup>
import {
  onBeforeMount,
  reactive,
  ref,
  getCurrentInstance,
  computed,
  watch,
  defineEmits,
  defineProps,
} from "vue";
import examineDetail from "./orderTabs/examineDetail.vue";
import saleOrderDetail from "./orderTabs/saleOrderDetail.vue";
import orderDetail from "./orderTabs/orderDetail.vue";
import purchaseBalanceDetail from "./orderTabs/purchaseBalanceDetail.vue";
import contractReview from "./orderTabs/contractReview.vue";
import procurementRequirements from "./orderTabs/procurementRequirements.vue";

import {
  oaDetail,
  getContractInfo,
  getMarketOrder,
  getPurchaseSettlement,
} from "@/api/purchase/order.js";
import { getPurchaseRequire } from "@/api/purchase/procureDemand.js";

const emit = defineEmits(["update:visible", "finish"]);
const activeKey = ref("1");
const activeKeyId = ref();
const examineDetailData = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
  detailType: String,
});
const idValue = ref("");
const contractReviewData = ref();
const saleOrderDetailData = ref();
const purchaseBalanceDetailData = ref();
const procurementRequirementsData = ref();
const onClose = () => {
  emit("update:visible", false);
  activeKey.value = "1";
};
const getTabName = async (data) => {
  let param = { purchaseId: props.id };
  let res;
  if (data == 2) {
    res = await oaDetail(param);
    examineDetailData.value = res.data;
  } else if (data == 3) {
    res = await getContractInfo(param);
    contractReviewData.value = res.data;
  } else if (data == 4) {
    res = await getMarketOrder(param);
    saleOrderDetailData.value = res.data;
  } else if (data == 5) {
    res = await getPurchaseSettlement(param);
    purchaseBalanceDetailData.value = res.data;
  } else if (data == 6) {
    res = await getPurchaseRequire(param);
    procurementRequirementsData.value = res.data;
  }
};
watch(
  () => props.visible,
  async (val) => {
    idValue.value = props.id;
    // await getDetail();
  }
);
</script>

<style></style>
