<template>
  <search :searchData="searchData" @search="refresh">
    <mw-button :font="'iconfont icon-xianxing-121'" @click="onAddBill('add', 1)"
      >新增蓝字发票</mw-button
    >
    <mw-button :font="'iconfont icon-xianxing-121'" @click="onAddBill('add', 2)"
      >新增红字发票</mw-button
    >
    <mw-button
      @click="exportOrg"
      :loading="exportLoading"
      :disabled="!data?.length"
      v-permission="'purchase:settlement:exportExcel'"
      >导出</mw-button
    >
    <mw-button
      @click="onAdd('firstTime', undefined, 1)"
      v-permission="'purchase:settlement:initialTreatment'"
      >期初处理</mw-button
    >
  </search>

  <mw-table
    :scroll="{ x: 'max-content' }"
    :align="center"
    :columns="columns.fatColumns"
    :data-source="data"
    class="tables"
    :rowKey="(record) => record.id"
    childrenColumnName="record.settlementInfoVOList"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'status'">
        <!--  isBackgroundColor -->
        <dictionary
          :statusOptions="invoiceStatus"
          :value="record.status"
          :statusExtend="record.statusExtend"
        />
      </template>
      <template v-if="column.key == 'issueInvoiceType'">
        {{
          record.issueInvoiceType == 2
            ? "红字发票"
            : record.issueInvoiceType == 1
            ? "蓝字发票"
            : "-"
        }}
      </template>
      <template v-if="column.key == 'button'">
        <!-- <a-button
          type="primary"
          :size="small"
          class="ml-8"
          @click="rowClick(record)"
          >{{ record.status == 1 ? "查看" : "结算" }}</a-button
        > -->
        <mw-button
          title="详情"
          class="mr-1"
          @click="detailTabClick(record)"
        ></mw-button>

        <mw-button
          title="编辑"
          v-if="
            (record.status == 9 && record.isBeginning == 0) ||
            (record.status == 10 && record.isBeginning == 0) ||
            (record.status == 20 && record.isBeginning == 0)
          "
          @click="onAdd('edit', record)"
        ></mw-button>
      </template>
      <template v-if="column.key == 'purchaseNumber'">
        <div style="max-width: 650px">{{ record.purchaseNumber }}</div>
      </template>
    </template>
    <!-- <template #expandedRowRender="{ record }">
      <mw-table
        :columns="columns.childrenColumns"
        :data-source="record.settlementInfoVOList"
        :pagination="{ pageSize: 5 }"
      >
      </mw-table>
    </template> -->
  </mw-table>
  <settlementDetail
    v-model:visible="detailVisible"
    :id="detailId"
  ></settlementDetail>
  <settlementAddDrawer
    :openType="openType"
    v-model:visible="settlementAddVisible"
    :billType="CurrentBillType"
    @finish="getList"
    :openRecord="openRecord"
  ></settlementAddDrawer>
  <settlementAddDrawerRed
    :openType="openType"
    v-model:visible="settlementAddVisibleRed"
    :billType="CurrentBillType"
    @finish="getList"
    :openRecord="openRecord"
  />
  <settlementTabs
    v-model:visible="settlementTabsVisible"
    :id="settlementTabsId"
    :isBeginningSwitch="isBeginningSwitch"
  ></settlementTabs>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
} from "vue";
import { page, settlementExportExcel } from "@/api/settlement.js";
import settlementTabs from "./settlementTabs/settlementTab.vue";
import { usePagenation } from "@/common/setup";
import Search from "@/components/search/index.vue";
import settlementDetail from "./settlementDetail.vue";
import settlementAddDrawer from "./settlementAddDrawer.vue";
import settlementAddDrawerRed from "./settlementAddDrawerRed.vue";
import { uniqueArr, exportExecl } from "@/utils/util.js";
import {
  settlementStatus,
  settlementStatusFilters,
  purchaseStatusList,
  warehouseType,
  invoiceStatus,
  billTypes,
} from "@/common/constant.js";
import { InfoCircleFilled } from "@ant-design/icons-vue";
import { useRoute, useRouter } from "vue-router";
const settlementAddVisible = ref(false);
const settlementAddVisibleRed = ref(false);
const { proxy } = getCurrentInstance();
const router = useRouter();
const detailVisible = ref(false);
const detailId = ref("");
const data = ref([]);
const loading = ref(false);
const addVisible = ref(false);
const currendRecord = ref({});
const exportLoading = ref(false);
const settlementTabsVisible = ref(false);
const settlementTabsId = ref();
const openType = ref();
const openRecord = ref();
const isBeginningSwitch = ref();
const searchData = reactive({
  fields: {
    settleStatus: {
      name: "结算状态",
      type: "a-select",
      options: invoiceStatus,
      placeholder: "选择状态",
      width: "120px",
      value: "",
      allowClear: true,
    },
    issueInvoiceType: {
      type: "a-select",
      placeholder: "开具发票",
      options: billTypes,
      width: "120px",
      allowClear: true,
    },
    issueInvoiceType: {
      type: "a-select",
      placeholder: "发票类型",
      options: billTypes,
      width: "120px",
      allowClear: true,
    },
    rangeDate: {
      type: "a-range-picker",
      valueFormat: "YYYY-MM-DD",
      value: [],
      width: "240px",
      allowClear: true,
    },

    keyword: {
      type: "a-input-search",
      placeholder: `供应商名称/采购订单编码/结算单号`,
      width: "300px",
      allowClear: true,
    },
  },
});
const columns = ref({
  fatColumns: [
    {
      title: "结算单号",
      dataIndex: "settlementNumber",
      key: "settlementNumber",
    },
    {
      title: "开具",
      dataIndex: "issueInvoiceType",
      key: "issueInvoiceType",
    },
    {
      title: "期初",
      dataIndex: "isBeginning",
      key: "isBeginning",
      customRender: ({ record }) => {
        return record.isBeginning == 1
          ? "是"
          : record.isBeginning == 0
          ? "否"
          : "";
      },
    },

    {
      title: "供应商",
      dataIndex: "supplierName",
      key: "supplierName",
    },
    {
      title: "关联采购订单",
      dataIndex: "purchaseNumber",
      key: "purchaseNumber",
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
    },
    {
      title: "结算总数",
      dataIndex: "materialCount",
      key: "materialCount",
    },

    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
    },
    {
      title: "操作",
      dataIndex: "button",
      key: "button",
      fixed: "right",
      scopedSlots: {
        customRender: "button",
      },
    },
  ],
  // childrenColumns: [
  //   {
  //     title: "物料信息",
  //     dataIndex: "materialName",
  //     key: "materialName",
  //   },
  //   {
  //     title: "规格",
  //     dataIndex: "materialSpecification",
  //     key: "materialSpecification",
  //   },
  //   {
  //     title: "采购单价",
  //     dataIndex: "purchaseUnitPrice",
  //     key: "purchaseUnitPrice",
  //   },
  //   {
  //     title: "入仓数量",
  //     dataIndex: "beStoredCount",
  //     key: "beStoredCount  ",
  //   },
  //   {
  //     title: "入仓应付(元)",
  //     dataIndex: "payable",
  //     key: "payable",
  //   },
  //   {
  //     title: "入仓时间",
  //     dataIndex: "createTime",
  //     key: "createTime",
  //   },
  // ],
});

const rowKeyFn = (record, index) => {
  return record.id;
};

const getList = async () => {
  loading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await page(pageParam.value, {
    ...searchParam,
  });
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
const showCreateDrawer = (record) => {
  if (record.status != "1") {
    currendRecord.value = record;
    addVisible.value = true;
  }
};
// 详情
const rowClick = (item) => {
  detailVisible.value = true;
  detailId.value = item.id;
  // router.push({ name: "SettlementDetail", query: { id: item.id } });
};
const detailTabClick = (val) => {
  settlementTabsVisible.value = true;
  settlementTabsId.value = val.id;
  isBeginningSwitch.value = val.isBeginning;
};
const CurrentBillType = ref();
const onAdd = (type, record, billType) => {
  openType.value = type;
  openRecord.value = record;
  console.log(openType, "openType");
  settlementAddVisible.value = true;
  CurrentBillType.value = billType;
};

const onAddBill = (type, billType) => {
  if (type == "add") {
    openRecord.value = {};
  }
  openType.value = type;
  CurrentBillType.value = billType;
  if (billType == 2) {
    settlementAddVisibleRed.value = true;
  } else {
    settlementAddVisible.value = true;
  }
};
const exportOrg = async () => {
  exportLoading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await settlementExportExcel(searchParam, pageParam.value);
  const fileName = "采购结算.xlsx";
  exportExecl(fileName, result);
  exportLoading.value = false;
};
</script>
<style lang="less" scoped>
.list-header {
  background: theme("colors.background");
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  color: theme("colors.primary-text");
}
:deep(.ant-list-item) {
  // background: theme("colors.background");
  border: 1px solid theme("colors.dividers") !important;
  padding: 0 16px !important;
  border-radius: 8px;
}

// :deep(.ant-table-tbody > tr > td > .ant-table-wrapper:only-child .ant-table) {
//   margin: -16px;
// }
:deep(.mw-table[data-v-57a55008] .ant-table) {
  overflow-x: auto;
}
:deep(.mw-table[data-v-57a55008] .ant-table .ant-table-tbody > tr > td) {
  border-bottom: 0px solid #f0f0f0;
  // background: #fafafa;
}
// :deep(.mw-table[data-v-57a55008] .ant-table .ant-table-tbody  tr:hover) {
//   border-bottom: 0px solid #f0f0f0;
//   background: red;
// }

:deep(.ant-table-tbody > tr.ant-table-expanded-row > td) {
  background: #fff;
  padding: 10px 0 0 0;
}
</style>
