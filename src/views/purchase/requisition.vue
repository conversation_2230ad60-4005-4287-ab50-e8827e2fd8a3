<template>
  <search :searchData="searchData" @search="refresh"> </search>
  <a-row class="list-header">
    <a-col :span="6">物料信息</a-col>
    <a-col :span="4">规格</a-col>
    <a-col :span="2">采购数量</a-col>
    <a-col :span="2">采购单价</a-col>
    <a-col :span="6">供应商</a-col>
    <a-col :span="4">关联订单</a-col>
  </a-row>
  <a-list
    :data-source="data"
    :grid="{ gutter: 0, column: 1 }"
    :loading="loading"
    :pagination="{
      ...paginationProps,
      showTotal: (total) => `总计 ${total} 条数据`,
      onChange: (page) => {
        onTableChange({ current: page, pageSize: paginationProps.pageSize });
      },
    }"
  >
    <template #renderItem="{ item }">
      <a-list-item class="divide-y divide-solid divide-dividers">
        <p class="py-4">
          <span
            ><info-circle-filled class="text-primary mr-1" />采购员：{{
              item.purchaseByName
            }}</span
          >
          <span class="ml-8">采购时间：{{ item.createTime }}</span>
          <mw-button @click="onCreateDrawerdetail(item, 'detail')" class="mr-2"
            >详情</mw-button
          >
          <mw-button @click="onCreatedetel(item, 'detel')" danger
            >删除</mw-button
          >
        </p>
        <a-row
          v-for="info in item.purchasingInfoVOList"
          :key="info.id"
          class="py-4"
        >
          <a-col :span="6">
            <p>{{ info.materialName }}</p>
            <p class="text-primary-text">编码：{{ info.materialNo }}</p>
          </a-col>
          <a-col :span="4">
            {{ info.materialSpecification }}
          </a-col>
          <a-col :span="2">{{ info.purchasingCount }}</a-col>
          <a-col :span="2">{{ info.purchasingUnitPrice }}</a-col>
          <a-col :span="6">{{ info.supplierName }}</a-col>
          <a-col :span="4">
            <p>{{ info.purchaseNumber }}</p>
            <dictionary
              :statusOptions="purchaseStatus"
              :value="info.purchaseStatus"
              isBackgroundColor
            />
          </a-col>
        </a-row>
      </a-list-item>
    </template>
  </a-list>
  <requistion-create-drawer
    v-model:visible="addVisible"
    :id="id"
    @finish="getList"
  />
  <requistion-detail
    ref="refRequistiondetail"
    v-model:visible="detailVisible"
    :detailId="detailId"
    :typeState="typeState"
  />
</template>
<script setup>
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import { page, detail, getDelete } from "@/api/purchase/requistion.js";
import { usePagenation } from "@/common/setup";
import Search from "@/components/search/index.vue";
import requistionCreateDrawer from "./components/requistionCreateDrawer.vue";
import requistionDetail from "./components/requisitionDetail.vue";
import { purchaseStatus, purchaseStatusFilters } from "@/common/constant.js";
import { getAllUser } from "@/api/system/user.js";
import { InfoCircleFilled } from "@ant-design/icons-vue";

const { proxy } = getCurrentInstance();
const detailId = ref("");
const data = ref([]),
  loading = ref(false),
  addVisible = ref(false),
  detailVisible = ref(false),
  id = ref("");
const refRequistiondetail = ref(null);
const typeState = ref("");
const searchData = reactive({
  fields: {
    purchaseStatus: {
      name: "采购状态",
      type: "a-select",
      options: purchaseStatusFilters,
      placeholder: "选择状态",
      width: "120px",
      value: "",
    },
    purchaseBy: {
      name: "采购人",
      type: "a-select",
      options: [],
      placeholder: "选择采购人",
      width: "140px",
      fieldNames: {
        label: "userName",
        value: "userId",
      },
      allowClear: true,
    },
    rangeDate: {
      type: "a-range-picker",
      valueFormat: "YYYY-MM-DD",
      value: [],
      width: "240px",
    },
    keyword: {
      type: "a-input-search",
      placeholder: "输入物料名称/编码",
      width: "240px",
    },
  },
});
const getList = async () => {
  loading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await page(pageParam.value, searchParam);
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
const getUserList = async () => {
  let result = await getAllUser();
  searchData.fields.purchaseBy.options = result.data;
};
onBeforeMount(async () => {
  getUserList();
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
const rowClick = (record) => {
  return {
    onClick: (event) => {
      id.value = record.id;
      addVisible.value = true;
    },
  };
};
const showCreateDrawer = () => {
  id.value = undefined;
  addVisible.value = true;
};
const onCreateDrawerdetail = (val, typeStateValue) => {
  detailVisible.value = true;
  detailId.value = val.id;
  typeState.value = typeStateValue;
};
const onCreatedetel = async (item) => {
  await getDelete({ id: item.id });
  getList();
};
</script>
<style lang="less" scoped>
.list-header {
  background: theme("colors.background");
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  color: theme("colors.primary-text");
}
:deep(.ant-list-item) {
  // background: theme("colors.background");
  border: 1px solid theme("colors.dividers") !important;
  padding: 0 16px !important;
  border-radius: 8px;
}
</style>
