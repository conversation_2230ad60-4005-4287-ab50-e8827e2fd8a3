<template>
  <mw-drawer
    custom-title="到货记录"
    :visible="visible"
    @close="onClose"
    :spinning="spinning"
  >
    <p class="pb-4">物料名称：{{ data.materialName }}</p>
    <a-timeline
      class="mt-1"
      v-if="data.materialArrivalRecordFlowVOList?.length"
    >
      <a-timeline-item
        v-for="(item, index) in data.materialArrivalRecordFlowVOList"
        :key="index"
      >
        <p class="w-full">
          <dictionary
            :statusOptions="storedStatus"
            :value="item.status"
            isBackgroundColor
          />
          <span class="ml-2 text-secondar-text float-right">{{
            item.createTime
          }}</span>
        </p>
        <div class="mt-4" v-if="item.remark">
          {{ item.remark + "：" }}{{ item.quantity }}
        </div>
        <div
          v-if="item.detailRecordRespList?.length"
          class="text-secondar-text mt-4"
        >
          <p v-for="r in item.detailRecordRespList" :key="r.id">
            <a-space>
              <span>
                <dictionary
                  :statusOptions="warehouseTypes"
                  :value="r.warehouseInnerType"
                />:
              </span>
              <span>{{ r.warehouseName }}</span>
              <span>{{ r.warehouseArea }}</span>
              <span class="text-title">数量：{{ r.quantity }}</span>
            </a-space>
          </p>
        </div>
      </a-timeline-item>
    </a-timeline>
    <a-empty v-else />
  </mw-drawer>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  onBeforeMount,
  defineEmits,
} from "vue";
// import { recordList } from "@/api/warehouse.js";
import { selectMaterialArrivalRecordByMaterialId } from "@/api/purchase/order.js";
import { storedStatus } from "@/common/constant.js";
import { getDicByType } from "@/utils/util.js";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  materialId: String,
  purchaseNumber: String,
});
const emit = defineEmits(["update:visible"]);

const spinning = ref(false),
  data = ref({}),
  warehouseTypes = ref([]);
const onClose = () => {
  emit("update:visible", false);
};
const getWarehouseTypeOption = async () => {
  let { dics } = await getDicByType("warehouse_type", "仓库");
  warehouseTypes.value = dics;
};
onBeforeMount(async () => {
  await getWarehouseTypeOption();
});
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      spinning.value = true;
      let result = await selectMaterialArrivalRecordByMaterialId(
        props.purchaseNumber,
        props.materialId
      );
      data.value = result.data;
      spinning.value = false;
    }
  }
);
</script>
<style lang="less" scoped>
.bg {
  height: 40px;
  line-height: 40px;
  background: rgba(34, 34, 34, 0.08);
  border-radius: 8px;
  text-align: center;
  margin-bottom: 24px;
}
</style>
