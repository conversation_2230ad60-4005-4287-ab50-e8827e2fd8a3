<template>
  <mw-drawer
    custom-title="详情信息"
    width="50%"
    :visible="visible"
    @close="onClose"
    :spinning="spinning"
  >
    <template #header>
      <mw-button @click="onClose" :loading="submitLoading">确定</mw-button>
    </template>
    <a-row>
      <a-form ref="formRef" :model="formData" layout="inline" :colon="false">
        <a-col :span="8">
          <a-form-item label="选择采购人" name="purchaseBy">
            <a-select
              v-model:value="formData.purchaseBy"
              placeholder="请选择采购人"
              allow-clear
              optionFilterProp="nickName"
              :fieldNames="{
                label: 'nickName',
                value: 'userId',
              }"
              :options="userListOptions"
              showSearch
              :disabled="props.typeState == 'detail'"
            >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="采购订单类型">
            <a-tag color="green">普通采购订单</a-tag>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="采购需求" name="requirement">
            <a-textarea
              v-model:value="formData.requirement"
              placeholder="请输入采购需求"
              allow-clear
              :disabled="props.typeState == 'detail'"
            />
          </a-form-item>
        </a-col>
        <!-- <a-col :span="8">
          <a-form-item label="办公用品" name="officeSupplies">
            <a-input
              v-model:value="formData.officeSupplies"
              placeholder="输入办公用品"
              allow-clear
              :disabled="props.typeState == 'detail'"
            ></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="含设备" name="device">
            <a-input
              v-model:value="formData.device"
              placeholder="输入含设备"
              allow-clear
              :disabled="props.typeState == 'detail'"
            ></a-input>
          </a-form-item>
        </a-col> -->
        <a-col :span="8">
          <a-form-item label="备注" name="remark">
            <a-textarea
              v-model:value="formData.remark"
              placeholder="请输入采购需求"
              allow-clear
              :disabled="props.typeState == 'detail'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="附件">
            <div class="overflow" v-if="formData.deliveryOrderFiles">
              <i
                class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                style="color: #959ec3"
              ></i>
              <a
                :href="formData.deliveryOrderFiles?.fileVisitUrl"
                :title="formData.deliveryOrderFiles?.fileName"
                target="_blank"
                class="underline"
                style="color: #959ec3"
                >{{ formData.deliveryOrderFiles?.fileName }}
              </a>
            </div>

            <form-upload
              v-else
              v-model:value="formData.deliveryOrderFiles"
              sence="delivery"
              :fileTypes="[]"
              :fileSize="100"
              hasDownLoad
            ></form-upload>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item>
            <div class="text-primary-text mb-3">物料明细</div>
            <a-table
              :dataSource="formData.purchasingInfoVOList"
              :columns="columns"
            />
          </a-form-item>
        </a-col>
      </a-form>
    </a-row>
  </mw-drawer>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  onBeforeMount,
  defineEmits,
  reactive,
} from "vue";
import { add, detail } from "@/api/purchase/requistion.js";
import { AllList, getInfo } from "@/api/basicData/material.js";
import { getAllUser } from "@/api/system/user.js";
import FormUpload from "@/components/form-upload.vue";
const { proxy } = getCurrentInstance();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  detailId: String,
  typeState: String,
});

const emit = defineEmits(["update:visible", "finish"]);
var formData = reactive({
  purchaseBy: undefined,
  detailParamList: [],
  type: "NORMAL",
  deliveryOrderFiles: [],
});
const formRef = ref(),
  submitLoading = ref(false),
  spinning = ref(false),
  userListOptions = ref([]),
  addMaterialVisible = ref(false),
  addMaterialFormRef = ref(),
  selectedMaterial = ref({}),
  allMaterialList = ref([]),
  formDataM = reactive({
    materialId: undefined,
    purchasingCount: 1,
    purchasingUnitPrice: undefined,
    supplierId: undefined,
    supplierName: undefined,
  });
const columns = [
  {
    title: "物料编号",
    dataIndex: "materialNo",
    key: "materialNo",
  },
  {
    title: "物料名称",
    dataIndex: "materialName",
    key: "materialName",
  },
  {
    title: "物料规格",
    dataIndex: "materialSpecification",
    key: "materialSpecification",
  },
  {
    title: "申请采购数量",
    dataIndex: "purchasingCount",
    key: "purchasingCount",
  },
  {
    title: "采购申请单价",
    dataIndex: "purchasingUnitPrice",
    key: "purchasingUnitPrice",
  },
  {
    title: "供应商",
    dataIndex: "supplierName",
    key: "supplierName",
  },
  {
    title: "采购人",
    dataIndex: "purchaseByName",
    key: "purchaseByName",
  },
  {
    title: "采购订单编号",
    dataIndex: "purchaseNumber",
    key: "purchaseNumber",
  },
  {
    title: "采购订单状态",
    dataIndex: "purchaseStatus",
    key: "purchaseStatus",
  },
];
// 详情
const getUserList = async (id) => {
  let res = await detail({ id: id });
  // formData = res.data
  formData.purchaseBy = Number(res.data.purchaseBy);
  formData.requirement = res.data.requirement;
  formData.officeSupplies = res.data.officeSupplies;
  formData.device = res.data.device;
  formData.remark = res.data.remark;
  if (res.data?.file) {
    formData.deliveryOrderFiles = res.data?.file;
  }
  formData.purchasingInfoVOList = res.data.purchasingInfoVOList;
};
// 采购人
const showUpdatePurchaseBy = async () => {
  let result = await getAllUser();
  userListOptions.value = result.data;
};
// 关闭弹窗
const onClose = () => {
  formRef.value.resetFields();
  formData.purchaseBy = undefined;
  formData.detailParamList = [];
  emit("update:visible", false);
};

watch(
  () => props.visible,
  async (val) => {
    if (val) {
      await showUpdatePurchaseBy();
      await getUserList(props.detailId);
    }
  }
);
</script>
<style lang="less" scoped>
:deep(.ant-form-inline .ant-form-item) {
  flex: none;
  flex-wrap: nowrap;
  margin-right: 16px;
  margin-bottom: 5%;
}
</style>
