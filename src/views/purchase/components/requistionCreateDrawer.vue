<template>
  <mw-drawer
    :custom-title="id ? '修改采购申请' : '新增采购申请'"
    :visible="visible"
    @close="onClose"
    @spinning="spinning"
    destroyOnClose="true"
  >
    <template #header>
      <mw-button @click="submitForm" :loading="submitLoading">确定</mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      :label-col="{ span: 5, offset: -1 }"
    >
      <!-- status -->
      <a-form-item label="需求状态" name="status" v-if="props.id" required>
        <a-select
          v-model:value="formData.status"
          placeholder="请选择需求状态"
          allow-clear
          optionFilterProp="label"
          :fieldNames="{
            label: 'label',
            value: 'value',
          }"
          :options="requirementStatus"
          showSearch
        >
        </a-select>
      </a-form-item>
      <a-form-item label="选择采购人" name="purchaseBy">
        <a-select
          v-model:value="formData.purchaseBy"
          placeholder="请选择采购人"
          allow-clear
          optionFilterProp="nickName"
          :fieldNames="{
            label: 'nickName',
            value: 'userId',
          }"
          :options="userListOptions"
          showSearch
        >
        </a-select>
      </a-form-item>
      <a-form-item label="采购订单类型">
        <div v-if="formData.type == 'PURCHASE_REQUIRE'">需求采购</div>
        <div v-if="formData.type == 'NORMAL'">普通采购</div>
        <div v-if="formData.type == 'SAFE_STOCK'">安全库存采购订单</div>
        <div v-if="formData.type == 'MARKET_ORDER'">销售订单采购</div>
        <div v-if="formData.type == 'UPDATE_ORDER_BOM'">修改订单BOM采购</div>

        <!-- <a-input
        v-model:value="formData.type"
        placeholder="输入公司名称"
        allow-clear
      ></a-input> -->
      </a-form-item>
      <!-- <a-form-item label="采购需求" name="requirement">
        <a-textarea
          v-model:value="formData.requirement"
          placeholder="请输入采购需求"
          allow-clear
        />
      </a-form-item> -->
      <!-- <a-form-item label="办公用品" name="officeSupplies">
        <a-input
          v-model:value="formData.officeSupplies"
          placeholder="输入办公用品"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item label="含设备" name="device">
        <a-input
          v-model:value="formData.device"
          placeholder="输入含设备"
          allow-clear
        ></a-input>
      </a-form-item> -->
      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="formData.remark"
          placeholder="请输入采购需求"
          allow-clear
        />
      </a-form-item>
      <a-form-item label="附件">
        <!-- <a-input v-model:value="formData.successNum"  /> -->
        <form-upload
          v-model:value="deliveryOrderFiles"
          sence="delivery"
          :fileTypes="[]"
          :fileSize="100"
          hasDownLoad
        ></form-upload>
      </a-form-item>
      <a-form-item v-if="!props.id">
        <div class="text-primary-text mb-3">物料明细</div>
        <div class="px-4 bg-background rounded-lg divide-y divide-border">
          <div
            class="text-primary-text py-4"
            v-for="(item, index) in formData.detailParamList"
            :key="item.id"
          >
            <div class="flex justify-between items-center leading-5.5 mb-1">
              <div class="flex-1 text-title overflow">
                {{ item.materialName || "-" }}
                <span v-if="item.materialNo">（{{ item.materialNo }}）</span>
              </div>
              <div class="" @click="formData.detailParamList.splice(index, 1)">
                <i class="iconfont icon-jichu-shanchu align-middle"></i>
                <span class="align-middle ml-1">移除</span>
              </div>
            </div>
            <div class="flex justify-between items-end leading-5.5 mb-1">
              <div class="space-y-1">
                <div class="">规格：{{ item.specification }}</div>
                <div class="">采购数量：</div>
              </div>
              <div class="">
                <span>x </span>
                <a-input-number
                  v-model:value="item.purchasingCount"
                  :stringMode="true"
                />
              </div>
            </div>
            <div
              class="flex justify-between items-end leading-8 mb-1"
              v-if="env.platform == 'notInDingTalk'"
            >
              <span>采购单价：</span>
              <span>{{ item.purchasingUnitPrice }}</span>
              <!-- <a-input-number
                v-model:value="item.purchasingUnitPrice"
              />  v-if="env.platform == 'notInDingTalk'"-->
            </div>
            <div class="flex justify-between items-end leading-5.5 mb-1">
              <span>供应商：</span>
              <a-select
                style="width: 240px"
                v-model:value="item.supplierId"
                placeholder="请选择供应商"
                allow-clear
                @change="
                  (value, option) => {
                    item.supplierName = option?.label;
                  }
                "
              >
                <a-select-option
                  v-for="s in item?.materialSupplierRelationList"
                  :key="s.supplierId"
                  :value="s.supplierId"
                  :label="s.supplierName"
                  >{{ s.supplierName }}</a-select-option
                >
              </a-select>
            </div>
            <!-- <div v-else class="flex justify-between items-end leading-5.5 mb-1">
              <span>供应商：</span><span></span>{{ item.supplierName }}
            </div> -->
          </div>
          <div class="flex justify-center items-center pt-3 pb-4">
            <mw-button @click="addMaterialVisible = true">选择物料</mw-button>
          </div>
        </div>
      </a-form-item>
      <a-form-item v-else>
        <div class="text-primary-text mb-3">物料明细</div>
        <div class="px-4 bg-background rounded-lg">
          <div class="text-title overflow">
            <p style="margin-top: 10px" class="mb-1">
              {{ formData.materialName || "-" }}
              <span class="ml-2">({{ formData.materialNo || "--" }})</span>
            </p>
          </div>
          <div class="flex justify-between items-end leading-5.5 mb-1">
            <div class="space-y-1">
              <div class="">规格：{{ formData.materialSpecification }}</div>
              <div class="">采购数量：</div>
            </div>
            <div class="">
              <span>x </span>
              <a-input-number
                v-model:value="formData.purchaseCount"
                :stringMode="true"
              />
            </div>
          </div>
          <div
            class="flex justify-between items-end leading-8 mb-1"
            v-if="env.platform == 'notInDingTalk'"
          >
            <span>采购单价：</span>
            <span>{{ formData.purchaseUnitPrice }}</span>
          </div>
          <div class="flex justify-between items-end leading-5.5 mb-10">
            <span style="margin-bottom: 10px">供应商：</span>
            <a-select
              style="margin-bottom: 10px; width: 240px"
              v-model:value="formData.supplierId"
              placeholder="请选择供应商"
              allow-clear
              @change="
                (value, option) => {
                  formData.supplierName = option?.label;
                }
              "
            >
              <a-select-option
                v-for="s in formData?.materialSupplierRelationList"
                :key="s.supplierId"
                :value="s.supplierId"
                :label="s.supplierName"
                >{{ s.supplierName }}</a-select-option
              >
            </a-select>
          </div>
        </div>
      </a-form-item>
    </a-form>
  </mw-drawer>
  <mw-drawer
    :visible="addMaterialVisible"
    @close="closeMaterialDrawer()"
    customTitle="选择物料"
  >
    <template #header>
      <mw-button @click="addMaterialOnSubmit">确定</mw-button>
    </template>
    <div>
      <div>
        <a-form
          ref="addMaterialFormRef"
          :model="formDataM"
          :rules="addMaterialRules"
          layout="horizontal"
          :colon="false"
          :label-col="{ span: 4, offset: 0 }"
        >
          <a-form-item label="物料名称" name="materialId">
            <!-- <a-select
              v-model:value="formDataM.materialId"
              show-search
              placeholder="请输入物料名称"
              :show-arrow="false"
              style="width: 100%"
              :default-active-first-option="false"
              :filter-option="false"
              :not-found-content="null"
              :options="allMaterialList"
              @search="handleSearchMaterial"
              @change="handleChangeMaterial"
            ></a-select> -->
            <MaterialSelect
              v-model:value="formDataM.materialId"
              @change="handleChangeMaterial"
              :materiaSelect="materiaSelect"
              :showSpecification="true"
            />
          </a-form-item>
          <!-- <a-form-item label="采购单价" name="purchasingUnitPrice">
            <a-input-number
              v-model:value="formDataM.purchasingUnitPrice"
            />
          </a-form-item> -->
          <a-form-item label="采购数量" name="purchasingCount">
            <a-input-number
              v-model:value="formDataM.purchasingCount"
              :stringMode="true"
            />
          </a-form-item>
          <!-- name="supplierId" -->
          <a-form-item label="供应商">
            <!-- {{ formDataM.supplierId }}
            <template
              v-if="selectedMaterial?.materialSupplierRelationList?.length"
            >
              <a-row class="text-secondar-text">
                <a-col :span="2"></a-col>
                <a-col :span="16">供应商名称</a-col>
                <a-col :span="6">单价(元)</a-col>
              </a-row>
              <a-radio-group
                class="w-full"
                v-model:value="formDataM.supplierId"
                @change="handleRadioChange"
              >
                <a-row
                  v-for="item in selectedMaterial?.materialSupplierRelationList"
                  :key="item.supplierId"
                >
                  <a-col :span="2"
                    ><a-radio :value="item.supplierId" :option="item"></a-radio
                  ></a-col>
                  <a-col :span="16">{{ item.supplierName }}</a-col>
                  <a-col :span="6">{{ item.price }}</a-col>
                </a-row>
              </a-radio-group>
            </template>
            <span v-else>-</span> -->
            <a-select
              v-model:value="formDataM.supplierId"
              placeholder="请选择供应商"
              allow-clear
              @change="
                (value, option) => {
                  formDataM.supplierName = option?.label;
                  formDataM.purchasingUnitPrice = option?.price;
                }
              "
            >
              <a-select-option
                v-for="item in selectedMaterial?.materialSupplierRelationList"
                :key="item.supplierId"
                :value="item.supplierId"
                :label="item.supplierName"
                :price="item.price"
                ><span>{{ item.supplierName }}</span>
                <!-- <span>{{ item.supplierName }}</span
                ><span class="float-right"
                  >(单价：{{ item.price }}元)</span
                >
                 -->
              </a-select-option>
            </a-select>
          </a-form-item>
          <div class="text-primary-text mb-3">物料基本信息</div>
          <div class="p-4 bg-background rounded space-y-3">
            <div class="flex leading-5.5">
              <div class="w-17 text-primary-text">物料编码</div>
              <div class="flex-1 w-0">
                {{ selectedMaterial.materialNo || "-" }}
              </div>
            </div>
            <div class="flex leading-5.5">
              <div class="w-17 text-primary-text">物料规格</div>
              <div class="flex-1 w-0">
                {{ selectedMaterial.specification || "-" }}
              </div>
            </div>
            <div class="flex leading-5.5">
              <div class="w-17 text-primary-text">一级分类</div>
              <div class="flex-1 w-0">
                {{ selectedMaterial.classificationParentName || "-" }}
              </div>
            </div>
            <div class="flex leading-5.5">
              <div class="w-17 text-primary-text">二级分类</div>
              <div class="flex-1 w-0">
                {{ selectedMaterial.classificationName || "-" }}
              </div>
            </div>
            <div class="flex leading-5.5">
              <div class="w-17 text-primary-text">库存数量</div>
              <div class="flex-1 w-0">
                {{ selectedMaterial.total }}
              </div>
            </div>
            <!-- <div class="flex leading-5.5">
              <div class="w-17 text-primary-text">库存总值</div>
              <div class="flex-1 w-0">
                {{ selectedMaterial.totalPrice || "-" }}
              </div>
            </div> -->
          </div>
        </a-form>
      </div>
    </div>
  </mw-drawer>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  onBeforeMount,
  defineEmits,
  reactive,
} from "vue";
import { add } from "@/api/purchase/requistion.js";
import { AllList, getInfo } from "@/api/basicData/material.js";
import { getAllUser } from "@/api/system/user.js";
import FormUpload from "@/components/form-upload.vue";
import { requirementStatus } from "@/common/constant";
const { proxy } = getCurrentInstance();
import { env } from "dingtalk-jsapi";
import {
  requireAdd,
  requireDetail,
  requireUpdate,
} from "@/api/purchase/procureDemand.js";
import { getUserInfo } from "@/api/login.js";

const materiaSelect = {
  ignoreCancel: true,
  isPurchase: 1,
};

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: String,
  pointDemand: String,
});
const emit = defineEmits(["update:visible", "finish"]);
const deliveryOrderFiles = ref([]);
const formRef = ref(),
  submitLoading = ref(false),
  cancelLoading = ref(false),
  spinning = ref(false),
  status = ref(""),
  formData = reactive({
    requirement: undefined,
    purchaseBy: undefined,
    detailParamList: [],
    type: "NORMAL",
    remark: undefined,
    file: [],
    materialName: undefined,
    supplierId: undefined,
    materialNo: undefined,
  }),
  showRequistionNo = ref(false),
  userListOptions = ref([]),
  supplierIdListOptions = ref([]),
  rules = ref({
    purchaseBy: [
      {
        required: true,
        message: "请选择采购人",
        trigger: "change",
      },
    ],
  }),
  addMaterialVisible = ref(false),
  addMaterialFormRef = ref(),
  addMaterialRules = reactive({
    materialId: [
      {
        required: true,
        message: "请选择物料",
        trigger: "blur",
      },
    ],
    purchasingUnitPrice: [
      {
        required: true,
        message: "请输入采购单价",
        trigger: "blur",
      },
    ],
    purchasingCount: [
      {
        required: true,
        message: "请输入采购数量",
        trigger: "blur",
      },
    ],
    supplierId: [
      {
        required: true,
        message: "请选择供应商",
        trigger: "change",
      },
    ],
  }),
  selectedMaterial = ref({}),
  allMaterialList = ref([]),
  formDataM = reactive({
    materialId: undefined,
    purchasingCount: 1,
    purchasingUnitPrice: undefined,
    supplierId: undefined,
    supplierName: undefined,
  });
function handleRadioChange(e) {}
// 确认选择物料
function addMaterialOnSubmit() {
  addMaterialFormRef.value.validate().then(async () => {
    //去重
    if (env.platform !== "notInDingTalk" && !formDataM.supplierId) {
      formDataM.supplierId =
        selectedMaterial.value.materialSupplierRelationList &&
        selectedMaterial.value.materialSupplierRelationList[0]
          ? selectedMaterial.value.materialSupplierRelationList[0].supplierId
          : "";
      formDataM.purchasingUnitPrice =
        selectedMaterial.value.materialSupplierRelationList[0]?.price;
      formDataM.supplierName =
        selectedMaterial.value.materialSupplierRelationList[0]?.supplierName;
    }
    !formData.detailParamList.some(
      (item) =>
        item.id == selectedMaterial.value.id &&
        item.supplierId == formDataM.supplierId
    ) &&
      formData.detailParamList.push({
        ...selectedMaterial.value,
        ...formDataM,
      });
    // const hsaSupplierId = Array.from(formData.detailParamList).every(
    //   (obj) => obj.supplierId
    // );
    // if (hsaSupplierId) {
    //   proxy.$message.error("供应商不能为空!");
    //   return;
    // }
    closeMaterialDrawer();
  });
}
function closeMaterialDrawer() {
  addMaterialVisible.value = false;
  addMaterialFormRef.value.resetFields();
  allMaterialList.value = [];
  selectedMaterial.value = {};
  formDataM.materialId = null;
  formDataM.supplierId = void 0;
}
// 获取物料列表
async function getMaterialList(materialName) {
  let result = await AllList({
    materialName: materialName,
    statusList: [0, 1],
  });
  let { data } = result;
  allMaterialList.value = data?.map((item) => {
    return {
      label: item.materialName,
      value: item.id,
    };
  });
}

// 搜索物料
let timer;
const handleSearchMaterial = (value) => {
  if (value.length) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      getMaterialList(value);
    }, 500);
  } else {
    setTimeout(() => {
      allMaterialList.value = [];
    }, 500);
  }
};

//   选中物料
const handleChangeMaterial = async (value) => {
  // 获取选中的物料信息
  let res = await getInfo(value);
  selectedMaterial.value = res.data;
  formData.materialSupplierRelationList = res.data.materialSupplierRelationList;
};
const onClose = () => {
  props.id = "";
  formRef.value.resetFields();
  formData.purchaseBy = undefined;
  formData.detailParamList = [];
  deliveryOrderFiles.value = [];
  formData.type = "NORMAL";
  emit("update:visible", false);
};
const submitForm = () => {
  formRef.value
    .validate()
    .then(async () => {
      formData.file = deliveryOrderFiles.value
        ? deliveryOrderFiles.value[0]
        : {};
      if (!props.id) {
        formData.detailParamList.forEach((item, index) => {
          // if (!item.supplierId) {
          //   item.supplierId =
          //     item.materialSupplierRelationList &&
          //     item.materialSupplierRelationList[0]
          //       ? item.materialSupplierRelationList[0].supplierId
          //       : "";
          //   item.purchasingUnitPrice =
          //     item.materialSupplierRelationList[0]?.price;
          //   item.supplierName =
          //     item.materialSupplierRelationList[0]?.supplierName;
          // }
          if (props.pointDemand) {
            item.purchaseCount = item.purchasingCount;
            item.purchaseUnitPrice = item.purchasingUnitPrice;
          }
        });

        if (formData.detailParamList?.length) {
          submitLoading.value = true;
          let res;
          if (props.pointDemand) {
            res = await requireAdd(formData);
          } else {
            res = await add(formData);
          }
          if (res.code == 200) {
            // proxy.$message.success("添加成功");
            onClose();
          }
          submitLoading.value = false;
          deliveryOrderFiles.value = [];
          emit("finish");
        } else {
          proxy.$message.warning("请选择物料");
        }
      } else {
        formData.id = props.id;
        let res = await requireUpdate(formData);
        if (res.code == 200) {
          proxy.$message.success("修改成功");
          onClose();
        }
        submitLoading.value = false;
        deliveryOrderFiles.value = [];
        emit("finish");
      }
    })
    .catch((error) => {
      submitLoading.value = false;
    });
};
const getUserList = async () => {
  spinning.value = true;
  let result = await getAllUser();
  userListOptions.value = result.data;
  spinning.value = false;
};
const handleCategoryChange = (value, label, extra) => {
  let { allCheckedNodes } = extra;
  if (allCheckedNodes.length > 0) {
    let { autogenerationRequistionCodeFlag } = allCheckedNodes[0].node.props;
    showRequistionNo.value = !autogenerationRequistionCodeFlag;
  }
};
const getUserInfoList = async () => {
  let { user } = await getUserInfo();
  formData.purchaseBy = user.userId;
};
const detailData = async () => {
  let res = await requireDetail({ id: props.id });
  formData.remark = res.data.remark;
  formData.purchaseBy = Number(res.data.purchaseBy);
  formData.materialName = res.data.materialName;
  formData.materialSpecification = res.data.materialSpecification;
  formData.purchaseCount = res.data.purchaseCount;
  formData.purchaseUnitPrice = res.data.purchaseUnitPrice;
  formData.supplierId = res.data.supplierId;
  formData.materialNo = res.data.materialNo;
  formData.supplierName = res.data.supplierName;
  formData.materialId = res.data.materialId;
  deliveryOrderFiles.value = [res.data.file];
  formData.status = res.data.status;
  formData.type = res.data.type;
  handleChangeMaterial(res.data.materialId);
  for (const key in formData.value) {
    formData.value[key] = res.data[key];
  }
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      await getUserList();
      await getUserInfoList();
      if (props.id) {
        await detailData();
      }
    }
  }
);
</script>
<style lang="less" scoped>
.bg {
  height: 40px;
  line-height: 40px;
  background: rgba(34, 34, 34, 0.08);
  border-radius: 8px;
  text-align: center;
  margin-bottom: 24px;
}
:deep(.ant-radio-group) {
  font-size: 14px;
}
</style>
