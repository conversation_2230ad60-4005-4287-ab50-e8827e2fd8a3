<template>
  <search :searchData="searchData" @search="refresh">
    <mw-button
      title="新增"
      :font="'iconfont icon-xianxing-121'"
      v-permission="'purchase:payment:add'"
      @click="onNewPayment('add')"
    ></mw-button>
  </search>
  <mw-table
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
    :scroll="{ x: 'max-content' }"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'supplierName'">
        <span>{{ record.supplierNumber }}</span>
        <span class="ml-2">{{ record.supplierName }}</span>
      </template>
      <template v-if="column.key == 'status'">
        <!-- <dictionary :statusOptions="invoiceStatus" :value="record.status" /> -->
        <div v-if="record.status == '0'">已审核</div>
        <div v-if="record.status == '1'">财务已审核</div>
        <div v-if="record.status == '7'">待审批</div>
        <div v-if="record.status == '8'">审批通过</div>
        <div v-if="record.status == '9'">审批不通过</div>
        <div v-if="record.status == '10'">已撤销</div>
      </template>

      <template v-if="column.key == 'type'">
        <dictionary :statusOptions="paymentTypes" :value="record.type" />
      </template>

      <template v-if="column.key == 'settlementMode'">
        <dictionary
          :statusOptions="settlementMethod"
          :value="record.settlementMode"
        />
      </template>
      <template v-if="column.key == 'invoiceIssue'">
        <span v-if="record.invoiceIssue == 1"
          >已开具
          <span v-if="record.invoiceType">
            （<dictionary
              :statusOptions="billingTypeList"
              :value="record.invoiceType"
            />）
          </span>
        </span>
        <span v-if="record.invoiceIssue == 2">未开具</span>
      </template>
      <template v-if="column.key == 'operation'">
        <mw-button
          title="详情"
          @click="onDetails('detail', record)"
        ></mw-button>
      </template>
    </template>
  </mw-table>
  <newPayment
    v-model:visible="newPaymentVisible"
    @finish="getList"
    :eventType="eventType"
    :detailRecord="detailRecord"
  ></newPayment>
</template>
<script setup>
import {
  settlementMethod,
  invoiceIssuance,
  billingTypeList,
} from "@/common/constant";
import { paymentType } from "@/common/constant";
import { invoiceStatus } from "@/common/constant.js";
import { paymentPage } from "@/api/purchase/procurePayment.js";
import Search from "@/components/search/index.vue";
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import { usePagenation } from "@/common/setup";
import newPayment from "./newPayment.vue";
const { proxy } = getCurrentInstance();
const data = ref([]);
const loading = ref(false);
const newPaymentVisible = ref(false);
const eventType = ref();
const detailRecord = ref();
const searchData = reactive({
  fields: {
    type: {
      name: "结算状态",
      type: "a-select",
      options: paymentType.concat([
        {
          label: "采购退款",
          value: 3,
        },
      ]),
      placeholder: "结算状态",
      width: "120px",
      value: "",
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: `输入供应商名称/供应商编码/订单号`,
      width: "290px",
      allowClear: true,
    },
  },
});
const paymentTypes = ref(
  paymentType.concat([
    {
      label: "采购退款",
      value: 3,
    },
  ])
);
const columns = ref([
  {
    title: "付款单号",
    dataIndex: "paymentOrder",
    key: "paymentOrder",
  },
  {
    title: "供应商",
    dataIndex: "supplierName",
    key: "supplierName",
  },
  // {
  //   title: "供应商编码",
  //   dataIndex: "supplierNumber",
  //   key: "supplierNumber",
  //   width: "180px",
  // },
  {
    title: "付款类型",
    dataIndex: "type",
    key: "type",
  },
  {
    title: "付款金额",
    dataIndex: "paymentAmount",
    key: "paymentAmount",
  },
  {
    title: "结算方式",
    dataIndex: "settlementMode",
    key: "settlementMode",
  },
  {
    title: "发票开具",
    dataIndex: "invoiceIssue",
    key: "invoiceIssue",
  },
  {
    title: "实际付款日期",
    dataIndex: "actualPaymentTime",
    key: "actualPaymentTime",
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status",
  },
  {
    title: "操作",
    dataIndex: "operation",
    key: "operation",
    fixed: "right",
  },
]);

const getList = async () => {
  loading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;
  }

  let result = await paymentPage(searchParam, pageParam.value);
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

onBeforeMount(async () => {
  await getList();
});
const onNewPayment = (eventTypeVal) => {
  eventType.value = eventTypeVal;
  detailRecord.value = {};
  newPaymentVisible.value = true;
};
const onDetails = async (eventTypeVal, detailRecordVal) => {
  console.log(detailRecordVal, "detailRecordVal");
  eventType.value = eventTypeVal;
  detailRecord.value = detailRecordVal;
  newPaymentVisible.value = true;
};
</script>
<style lang="less" scoped></style>
