<template>
  <mw-drawer
    :visible="visible"
    @close="onClose"
    :customTitle="
      eventType == 'detail'
        ? '采购付款详情' +
          `&nbsp &nbsp &nbsp 付款单号：${formData.paymentOrder || '--'}` +
          `&nbsp &nbsp &nbsp ${
            formData.status == 0
              ? '待财务审核'
              : formData.status == 1
              ? '财务已审核'
              : formData.status == 7
              ? '待审批'
              : formData.status == 8
              ? '审批通过'
              : formData.status == 9
              ? '审批不通过'
              : formData.status == 10
              ? '已撤销'
              : ''
          }`
        : eventType == 'add'
        ? '新增采购付款'
        : '编辑采购付款'
    "
    :spinning="spinning"
    width="50%"
    :destroyOnClose="true"
  >
    <template #header>
      <mw-button
        title="确定"
        @click="paymentSubmit"
        :loading="submitLoading"
        v-if="eventType !== 'detail'"
      ></mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      labelAlign="right"
      :label-col="{ span: 6, offset: 0 }"
    >
      <a-form-item label="供应商" name="supplierId">
        <a-select
          placeholder="请选择客户"
          v-model:value="formData.supplierId"
          :options="supplierData"
          optionFilterProp="supplierName"
          :fieldNames="{ label: 'supplierName', value: 'id' }"
          style="width: 100%"
          allow-clear
          showSearch
          @change="handleChange"
          :disabled="props.eventType == 'detail' ? true : false"
        >
        </a-select>
      </a-form-item>
      <a-form-item label="供应商开户行信息">
        <a-input
          v-model:value="formData.openingBank"
          placeholder="输入供应商开户行信息"
          allow-clear
          :disabled="props.eventType == 'detail' ? true : false"
        ></a-input>
      </a-form-item>
      <a-form-item label="供应商银行账号">
        <a-input
          v-model:value="formData.bankAccount"
          placeholder="输入供应商银行账号"
          allow-clear
          :disabled="props.eventType == 'detail' ? true : false"
        ></a-input>
      </a-form-item>

      <a-form-item label="付款类型" name="type" required>
        <a-select
          v-model:value="formData.type"
          show-search
          placeholder="付款类型"
          style="width: 100%"
          :default-active-first-option="false"
          :options="paymentTypeOptions"
          optionFilterProp="label"
          :fieldNames="{
            label: 'label',
            value: 'value',
          }"
          :disabled="props.eventType == 'detail' ? true : false"
        ></a-select>
      </a-form-item>
      <a-form-item label="付款金额" name="paymentAmount">
        <a-input-number
          class="w-full"
          v-model:value="formData.paymentAmount"
          placeholder="付款金额"
          allow-clear
          :disabled="props.eventType == 'detail' ? true : false"
          :stringMode="true"
        >
        </a-input-number>
      </a-form-item>
      <a-form-item label="计划付款日期" name="planPaymentTime" required>
        <a-date-picker
          :disabled="props.eventType == 'detail' ? true : false"
          v-model:value="formData.planPaymentTime"
          style="width: 100%"
          valueFormat="YYYY-MM-DD"
        />
      </a-form-item>
      <!-- <a-form-item label="付款账户">
        <a-select
          v-model:value="formData.bankCode"
          :options="blankList"
          placeholder="请选择付款账户"
          allow-clear
          :disabled="props.eventType == 'detail' ? true : false"
        />
      </a-form-item> -->

      <a-form-item label="结算方式" name="settlementMode" required>
        <a-select
          v-if="!props?.detailRecord.id"
          v-model:value="formData.settlementMode"
          show-search
          placeholder="结算方式"
          style="width: 100%"
          :default-active-first-option="false"
          :options="settlementMethod"
          optionFilterProp="label"
          :fieldNames="{
            label: 'label',
            value: 'value',
          }"
          :disabled="props.eventType == 'detail' ? true : false"
        ></a-select>
        <div v-else>
          <dictionary
            :statusOptions="settlementMethod"
            :value="formData.settlementMode"
          />
          <span
            v-if="formData.settlementMode == 2 || formData.settlementMode == 6"
          >
            <span v-if="formData.billNumber"> ({{ formData.billNumber }})</span>
          </span>
        </div>
      </a-form-item>
      <a-form-item label="发票开具" name="invoiceIssue" required>
        <a-select
          v-model:value="formData.invoiceIssue"
          show-search
          placeholder="发票开具"
          style="width: 100%"
          :default-active-first-option="false"
          :options="invoiceIssuance"
          optionFilterProp="label"
          :fieldNames="{
            label: 'label',
            value: 'value',
          }"
          :disabled="props.eventType == 'detail' ? true : false"
        ></a-select>
      </a-form-item>
      <a-form-item
        label="发票类型"
        name="invoiceType"
        required
        v-if="formData.invoiceIssue == '1'"
      >
        <a-select
          v-model:value="formData.invoiceType"
          show-search
          placeholder="发票类型"
          style="width: 100%"
          :default-active-first-option="false"
          :options="billingTypeList"
          optionFilterProp="label"
          :fieldNames="{
            label: 'label',
            value: 'value',
          }"
          :disabled="props.eventType == 'detail' ? true : false"
        ></a-select>
      </a-form-item>
      <a-form-item label="附件">
        <form-upload-contract
          v-if="props.eventType !== 'detail'"
          v-model:value="formData.file"
          sence="article"
          :fileTypes="[]"
          :fileSize="100"
          :readonly="false"
          hasDownLoad
          :fileLName="true"
          :fileLimit="9999"
          :detailType="'1'"
          :msgCodeName="formData.msgCode"
          :delShow="true"
        >
        </form-upload-contract>
        <div
          v-else
          class="overflow"
          v-for="(item, index) in formData.file"
          :key="index"
        >
          <i
            class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
            style="color: #959ec3"
          ></i>
          <a
            :title="item.fileName"
            @click="downFile(item.fileVisitUrl, item.fileName)"
            target="_blank"
            class="underline"
            style="color: #959ec3"
            >{{ item.fileName }}
          </a>
        </div>
      </a-form-item>
      <a-form-item label="付款事由">
        <a-textarea
          style="width: 100%"
          v-model:value="formData.remark"
          placeholder="请输入付款事由"
          allow-clear
          :disabled="props.eventType == 'detail' ? true : false"
        />
      </a-form-item>
      <a-form-item label="实际付款日期" v-if="props.eventType == 'detail'">
        <a-input
          style="width: 100%"
          v-model:value="formData.actualPaymentTime"
          placeholder="请输入实际付款日期"
          allow-clear
          :disabled="props.eventType == 'detail' ? true : false"
        />
      </a-form-item>
      <a-form-item label="付款银行" v-if="props.eventType == 'detail'">
        <a-input
          style="width: 100%"
          v-model:value="formData.bankCode"
          placeholder="请输入付款银行"
          allow-clear
          :disabled="props.eventType == 'detail' ? true : false"
        />
      </a-form-item>
      <!-- paymentVoucher -->
      <a-form-item label="付款凭证" v-if="props.eventType == 'detail'">
        <div
          class="overflow"
          v-for="(item, index) in formData.paymentVoucher"
          :key="index"
        >
          <i
            class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
            style="color: #959ec3"
          ></i>
          <a
            :title="item.fileName"
            @click="downFile(item.fileVisitUrl, item.fileName)"
            target="_blank"
            class="underline"
            style="color: #959ec3"
            >{{ item.fileName }}
          </a>
        </div>
      </a-form-item>
    </a-form>
  </mw-drawer>
</template>

<script setup>
import { getCurrentInstance } from "vue";
import { downFile } from "@/common/setup/index.js";
import {
  paymentType,
  settlementMethod,
  invoiceIssuance,
  billingTypeList,
} from "@/common/constant";
import {
  getByLikeNumber,
  paymentAdd,
  querySupplierAndPurchase,
} from "@/api/purchase/procurePayment.js";
import FormUploadContract from "@/components/form-upload-contract.vue";
import { getBankList } from "@/api/sales/index.js";
import { ref, watch, defineEmits, defineProps, reactive } from "vue";
import _cloneDeep from "lodash/cloneDeep";
const emit = defineEmits(["update:visible", "finish"]);
const { proxy } = getCurrentInstance();
const formData = ref({
  bankCode: void 0,
  purchaseNumber: void 0,
});
const formRef = ref();
const supplierData = ref();
const submitLoading = ref(false);
const blankList = ref([]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  eventType: {
    type: String,
    default: "",
  },
  detailRecord: {
    type: Object,
    default: {},
  },
});
const rules = {
  supplierId: [
    {
      required: true,
      message: "供应商不能为空",
      trigger: "blur",
    },
  ],

  paymentAmount: [
    {
      required: true,
      message: "付款金额不能为空",
      trigger: "blur",
    },
  ],
};

const handleChange = (e, val) => {
  formData.value.purchaseNumber = void 0;
  formData.value.supplierName = val.supplierName;
  formData.value.supplierNumber = val.supplierNumber;
  formData.value.bankAccount = val.bankAccount;
  formData.value.openingBank = val.openingBank;
};
const onClose = () => {
  formRef.value.resetFields();
  emit("update:visible", false);
  formData.value = {};
  formData.value.supplierId = undefined;
  formData.value.purchaseNumber = undefined;
  formData.value.paymentAmount = undefined;
  formData.value.type = undefined;
  formData.value.openingBank = void 0;
  formData.value.bankAccount = void 0;
  formData.value.remark = void 0;
  formData.value.bankCode = void 0;
  formData.value.file = [];
};
const paymentSubmit = async () => {
  formRef.value.validate().then(async () => {
    submitLoading.value = true;
    formData.value.isContract = formData.value.isContract == true ? 1 : 0;
    let res = await paymentAdd(formData.value);
    submitLoading.value = false;
    if (res.code == 200) {
      proxy.$message.success("新增成功");
      onClose();
      emit("finish");
    }
  });
};

const querySupplierAndPurchaseData = async () => {
  let res = await querySupplierAndPurchase({});
  supplierData.value = res.data.map((item) => {
    item.id = Number(item.id);
    return item;
  });
};

const getBank = async () => {
  const { code, data } = await getBankList();
  if (code == 200) {
    blankList.value = data.map((item) => {
      return {
        label: item.bankCode + "-" + item.bankName,
        value: item.bankCode,
      };
    });
  }
};

watch(
  () => props.visible,
  async (val) => {
    if (props.detailRecord) {
      let data = _cloneDeep(props.detailRecord);
      formData.value = data;
      formData.value.bankCode = data.bankCode ? data.bankCode : "其他";
      if (data.invoiceType) {
        formData.value.invoiceType = String(data.invoiceType);
      }
    }
    querySupplierAndPurchaseData();
    getBank();
  }
);
const paymentTypeOptions = paymentType
  .filter((item) => item.label !== "全部款项")
  .concat([
    {
      label: "采购退款",
      value: 3,
    },
  ]);
</script>

<style></style>
