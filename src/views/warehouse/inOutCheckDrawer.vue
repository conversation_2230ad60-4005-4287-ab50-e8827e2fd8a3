<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    :customTitle="'新增' + title"
    width="40%"
  >
    <template #header>
      <mw-button @click="formSubmit" :loading="submitLoading"> 确定 </mw-button>
    </template>
    <div>
      <div>
        <a-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          layout="horizontal"
          :colon="false"
        >
          <a-form-item name="materialIdOrProductId" :label="name + '名称'">
            <a-select
              v-model:value="formData.materialIdOrProductId"
              show-search
              placeholder="请输入物料名称查询并选择"
              :show-arrow="false"
              style="width: 100%"
              :default-active-first-option="false"
              :filter-option="false"
              :not-found-content="null"
              :options="allMaterialList"
              @search="handleSearchMaterial"
              @change="handleChangeMaterial"
              allowClear
            ></a-select>

            <!-- <MaterialSelect
              v-if="type == 'material'"
              v-model:value="formData.materialIdOrProductId"
              @change="handleChangeMaterial"
            />
            <a-select
              v-else-if="type == 'product'"
              v-model:value="formData.materialIdOrProductId"
              show-search
              placeholder="请选择产品"
              style="width: 100%"
              :default-active-first-option="false"
              :options="allProductList"
              optionFilterProp="productName"
              :field-names="{
                label: 'productName',
                value: 'id',
              }"
              @change="handleChangeProduct"
            ></a-select> -->
          </a-form-item>

          <a-form-item
            v-if="inOutType == 'out'"
            name="receiveUser"
            label="收货人员"
          >
            <a-select
              v-model:value="formData.receiveUser"
              show-search
              placeholder="请选择收货人员"
              style="width: 100%"
              :default-active-first-option="false"
              :options="receiveUserList"
              optionFilterProp="nickName"
              :field-names="{
                label: 'nickName',
                value: 'userId',
              }"
              @change="receiveUserChange"
            ></a-select>
          </a-form-item>

          <a-form-item name="successWarehouseId" label="选择仓库">
            <a-select
              v-model:value="formData.successWarehouseId"
              placeholder="请选择仓库"
              show-search
              :options="repositoryOptions"
              optionFilterProp="warehouseName"
              :fieldNames="{
                label: title == '入库' ? 'warehouseName' : 'warehouseName',
                value: title == '入库' ? 'id' : 'warehouseId',
              }"
              @change="(id) => handleChangeRepository(id, repositoryOptions)"
            ></a-select>
          </a-form-item>

          <a-form-item name="successWarehouseArea" label="选择区域">
            <a-select
              v-model:value="formData.successWarehouseArea"
              placeholder="请选择区域"
              :options="areaOptions"
              show-search
              optionFilterProp="warehouseArea"
              @change="handelChangeArea"
              :fieldNames="{
                label: 'warehouseArea',
                value: title == '入库' ? 'id' : 'warehouseAreaId',
              }"
            ></a-select>
          </a-form-item>

          <a-form-item name="successNum" :label="title + '数量'">
            <a-input-number
              v-model:value="formData.successNum"
              :min="0"
              :max="
                title == '出库'
                  ? selectedArea?.inventory
                    ? selectedArea?.inventory
                    : 0
                  : 999999999999
              "
              @change="onUnNum"
              :stringMode="true"
            /><span
              class="leading-8 align-middle ml-2"
              v-if="title == '出库' && formData.successWarehouseArea"
              >库存：{{ selectedArea?.inventory || "" }}</span
            >
          </a-form-item>

          <div class="flex" v-if="title == '入库'">
            <a-form-item name="amount" :label="title + '金额'">
              <a-input-number
                v-model:value="formData.amount"
                :formatter="formatter2"
                @change="onUnitPrc"
                :stringMode="true"
              />
            </a-form-item>
            <a-form-item name="unitPrice" :label="'单价：'" class="ml-2"
              >{{ formData.unitPrice || "0" }}
            </a-form-item>
          </div>

          <a-form-item name="warehouseType" label="选择类型">
            <radioGroup
              v-model:value="formData.warehouseType"
              :options="currentWarehouseTypes"
              @change="base64imgChange"
            >
            </radioGroup>
          </a-form-item>
          <a-form-item
            name="association"
            label="销售订单"
            required
            v-if="formData.warehouseType == 'GIFT_OUT'"
          >
            <a-select
              v-model:value="formData.association"
              show-search
              placeholder="请选择销售订单"
              :default-active-first-option="false"
              :options="postNoContractReviewListOptions"
              optionFilterProp="orderName"
              :field-names="{
                label: 'orderName',
                value: 'orderNo',
              }"
            ></a-select>
          </a-form-item>
          <a-form-item
            label="出库单确认"
            v-if="
              inOutType !== 'in' &&
              formData.warehouseType == 'RAWMATERIAL_NORMAL_OUT'
            "
          >
            <mw-button @click="onOutboundvisible(base64img ? '1' : '')">
              {{ base64img ? "重新签名" : "出库单签名" }}
            </mw-button>

            <img
              width="480"
              height="16"
              :src="base64img"
              alt=""
              v-if="base64img"
              id="imgUrlPath"
            />
          </a-form-item>
          <div class="w-full text-left text-primary-text mb-2">
            {{ name }}基本信息
          </div>
          <div class="p-4 bg-background rounded space-y-3">
            <div class="flex leading-5.5">
              <div class="w-17 text-primary-text">{{ name }}编码</div>
              <div class="flex-1 w-0">
                {{ selectedMaterial[type + "No"] || "-" }}
              </div>
            </div>
            <div class="flex leading-5.5">
              <div class="w-17 text-primary-text">{{ name }}规格</div>
              <div class="flex-1 w-0">
                {{ selectedMaterial.specification || "-" }}
              </div>
            </div>
          </div>

          <a-form-item class="mt-5" label="备注">
            <a-input v-model:value="formData.remark" />
          </a-form-item>
          <a-form-item label="附件">
            <form-upload
              v-if="!props.id"
              v-model:value="formData.file"
              sence="article"
              :fileTypes="[]"
              :fileSize="100"
            ></form-upload>
            <div
              v-else
              @click.stop="openUrl(formData.file?.fileVisitUrl)"
              :href="formData.file?.fileVisitUrl"
              :title="formData.file?.fileName"
              class="cursor-pointer inline-block"
              style="color: #959ec3"
            >
              <i
                class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                style="color: #959ec3"
              ></i>

              <span class="underline">{{ formData.file?.fileName }}</span>
            </div>
          </a-form-item>
        </a-form>
      </div>
    </div>
    <a-modal
      id="outboundPagemodal"
      title="出库单确认区"
      v-model:visible="outboundvisible"
      @ok="handleOk"
      :loading="outBoundLoading"
      okText="生成出库单"
      :mask-closable="false"
    >
      <div id="outboundPagemodal" class="outboundPage">
        <h1>出库单</h1>
        <div class="title">
          <span>库管员：{{ createByName ? createByName : "xxx" }} </span>
          <span>取货人：{{ receiveName ? receiveName : "xxx" }}</span>
        </div>
        <div>
          <table
            class="outboundTable"
            cellspacing="20px"
            v-if="type == 'material'"
          >
            <!--给表格添加边框-->
            <tr>
              <!-- material: "RAWMATERIAL", //物料仓
                product: "FINISHED", //成品仓
                "type == 'material'" -->

              <th>物料名称</th>
              <th>物料编号</th>
              <th>物料规格</th>
              <th>使用状态</th>
            </tr>
            <tr>
              <td>{{ selectedMaterial.materialName }}</td>
              <td>{{ selectedMaterial.materialNo }}</td>
              <td>{{ selectedMaterial.specification }}</td>
              <td>{{ selectedMaterial.status == 0 ? "未使用" : "使用中" }}</td>
            </tr>
          </table>
          <table
            class="outboundTable"
            cellspacing="20px"
            v-if="type == 'product'"
          >
            <!--给表格添加边框-->
            <tr>
              <th>成品名称</th>
              <th>成品编号</th>
              <th>成品规格</th>
              <th>使用状态</th>
            </tr>
            <tr>
              <td>{{ selectedMaterial.productName }}</td>
              <td>{{ selectedMaterial.productNo }}</td>
              <td>{{ selectedMaterial.specification }}</td>
              <td>
                {{ selectedMaterial.statusType == 0 ? "未使用" : "使用中" }}
              </td>
            </tr>
          </table>
        </div>
        <div>
          <Signature
            :renewSignature="renewSignature"
            :signatureSwitch="signatureSwitch"
            ref="renewSignatureda"
            @signatureSwitch="signatureSwitchCheng"
          ></Signature>
        </div>
      </div>
    </a-modal>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  reactive,
  getCurrentInstance,
  computed,
  defineProps,
  defineEmits,
  watch,
} from "vue";
import FormUpload from "@/components/form-upload.vue";
import { AllList, getInfo } from "@/api/basicData/material.js";
import {
  checkManualIn,
  checkManualOut,
  getInfoPriceByMaterialId,
} from "@/api/warehouse.js";
import {
  list,
  getInfo as getArea,
  selectMenuListIgnoreNoStock,
} from "@/api/basicData/repository.js";
import { formatter2 } from "@/common/validate.js";
import { warehouseType } from "@/common/constant.js";
import { list as getAllProductList } from "@/api/basicData/product.js";
import { getAllUser } from "@/api/system/user.js";
import { message } from "ant-design-vue";
// import signature from '@components/signature.vue'
import Signature from "@/components/signature.vue";
import { fileUpload } from "@/api/login.js";
import { getInfo as getInfoRepository } from "@/api/basicData/repository.js";
import html2canvas from "html2canvas";
import { getSelectUnshippedlist } from "@/api/sales/index.js";
import { accDiv, roundNumFun } from "@/common/validate.js";
import Decimal from "decimal.js";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "material",
  },
  inOutType: {
    type: String,
    default: "in",
  },
  name: {
    type: String,
    default: "物料",
  },
  submitToken: {
    type: Number || String,
  },
});
const emit = defineEmits(["update:visible"]);
const { proxy } = getCurrentInstance();
const outBoundLoading = ref(false);
const btnLoading = ref(false);
const title = computed(() => {
  return props.inOutType == "in" ? "入库" : "出库";
});
const currentWarehouseTypes = computed(() => {
  // if (formData.association) {
  let result = warehouseType.filter((item) => {
    //后面是去除赠品出库加的判断

    if (item.inOutType == "out") {
      return (
        item.type == props.type &&
        item.inOutType == props.inOutType &&
        item.value !== "GIFT_OUT" &&
        item.value !== "PRODUCT_OTHER_OUT"
      );
    } else {
      return item.inOutType == props.inOutType && item.value == "OTHERS";
    }
  });
  // }
  return result;
});
const formRef = ref(),
  selectedMaterial = ref({}),
  allMaterialList = ref([]),
  outboundvisible = ref(false),
  submitLoading = ref(false),
  signatureSitch = ref(false),
  repositoryOptions = ref([]),
  areaOptions = ref([]),
  allProductList = ref([]),
  receiveUserList = ref([]),
  receiveName = ref(""),
  createByName = ref(""),
  renewSignature = ref(""),
  base64img = ref(),
  renewSignatureda = ref(null),
  signatureSwitch = ref(false),
  postNoContractReviewListOptions = ref(),
  formData = reactive({
    materialIdOrProductId: void 0,
    successNum: void 0,
    successWarehouseId: void 0,
    successWarehouseArea: void 0,
    warehouseType: void 0,
    receiveUser: void 0,
    urlPath: void 0,
    file: [],
    remark: void 0,
  }),
  rules = reactive({
    materialIdOrProductId: [
      {
        required: true,
        message: "请选择" + props.name,
        trigger: "blur",
      },
    ],
    receiveUser: [
      {
        required: true,
        message: "请选择收货人",
        trigger: "blur",
      },
    ],
    successNum: [
      {
        required: true,
        message: "请输入需求数量",
        trigger: "blur",
      },
    ],
    amount: [
      {
        required: true,
        message: "请输入需求金额",
        trigger: "blur",
      },
    ],
    successWarehouseId: [
      {
        required: true,
        message: "请选择仓库",
        trigger: "change",
      },
    ],
    successWarehouseArea: [
      {
        required: true,
        message: "请选择区域",
        trigger: "change",
      },
    ],
    warehouseType: [
      {
        required: true,
        message: "请选择类型",
        trigger: "change",
      },
    ],
  });
// 两位小数
const roundNumFuns = (num, decimals) => {
  const fraction = Math.pow(10, decimals);
  const integerPart = Math.floor(num * fraction);
  let roundNum = integerPart / fraction;
  if (roundNum) {
    const numStr = roundNum.toString();
    if (numStr.indexOf(".") !== -1) {
      const decimalPart = numStr.slice(numStr.indexOf(".") + 1);
      if (decimalPart.length === 1) {
        return roundNum;
      }
    }
  }
  return roundNum;
};
const onUnNum = (e) => {
  // 计算入库金额
  formData.amount = new Decimal(formData.successNum || 0).mul(
    new Decimal(formData.unitPrice || 0)
  );
};
const onUnitPrc = (e) => {
  if (formData.amount > 0 && formData.successNum > 0) {
    formData.unitPrice = new Decimal(formData.amount).dividedBy(
      new Decimal(formData.successNum || 0)
    );
  } else {
    formData.unitPrice = void 0;
  }
};
const signatureSwitchCheng = (val) => {
  signatureSwitch.value = val;
};
const base64imgChange = () => {
  base64img.value = void 0;
};
const receiveUserChange = (e) => {
  const janeObj = receiveUserList.value.find((obj) => obj.userId == e); //查询name === "Jane"的对象
  receiveName.value = janeObj.nickName; //拿到id
};

const repositoryObj = reactive({
  material: "RAWMATERIAL", //物料仓
  product: "FINISHED", //成品仓
});
//销售订单
const postNoContractReview = async () => {
  let res = await getSelectUnshippedlist();
  postNoContractReviewListOptions.value = res.data;
};
// 签名

const click_submit = () => {
  signatureSitch.value = true;
};
const onOutboundvisible = (val) => {
  if (
    formData.materialIdOrProductId &&
    formData.successNum !== undefined &&
    formData.successWarehouseId !== undefined &&
    formData.successWarehouseArea !== undefined &&
    formData.warehouseType !== undefined
  ) {
    outboundvisible.value = true;
    renewSignature.value = val;
    renewSignatureda.value.init();
    // renewSignatureda.value.signatureSwitchs=false
    signatureSwitch.value = false;
  } else {
    message.error("请填写完整信息才可生成出库单！");
  }
};

const showModal = () => {
  // visible.value = true;
  outboundvisible.value = false;
};

// 生成出库单
const handleOk = async (e) => {
  if (!signatureSwitch.value) {
    return proxy.$message.warning("请确认签名后进行生成");
  }
  // visible.value = false;
  outBoundLoading.value = true;
  var modalElement = [];
  await new Promise((resolve) => setTimeout(resolve, 100)); // 使用await等待异步操作的完成
  modalElement = document.getElementsByClassName("ant-modal-body");
  try {
    const canvas = await html2canvas(modalElement[0], {
      // width: 100, //截图宽度
      // height: 100, //截图高度
      // backgroundColor: "red", //画出来的图片有白色的边框,不要可设置背景为透明色（null）
      useCORS: true, //支持图片跨域
      scale: 1, //设置放大的倍数
    });

    const url = canvas.toDataURL("image/jpg", 0.1); // toDataURL: 图片格式转成 base64

    const byteCharacters = atob(url.split(",")[1]);
    const byteArrays = [];

    for (let i = 0; i < byteCharacters.length; i++) {
      byteArrays.push(byteCharacters.charCodeAt(i));
    }
    const byteArray = new Uint8Array(byteArrays);
    const blob = new Blob([byteArray], { type: "image/png" }); // 设置正确的MIME类型
    const blobUrlData = URL.createObjectURL(blob);
    const response = await fetch(blobUrlData);
    const blobData = await response.blob();
    const file = new File([blobData], "filename.jpg", { type: blobData.type });
    let fileUploadData = await fileUpload(file);
    base64img.value = blobUrlData;
    formData.urlPath = fileUploadData.data.fileVisitUrl;
    outboundvisible.value = false;
    outBoundLoading.value = false;
  } catch (error) {
    outBoundLoading.value = false;
  }
};

// 收货人员信息
async function receivingList(materialName) {
  let data = await getAllUser();
  receiveUserList.value = data.data;
}

// 获取物料列表
async function getMaterialList(materialName) {
  let result = await AllList({
    keyword: materialName,
  });
  let { data } = result;
  allMaterialList.value = data.map((item) => {
    return {
      label: item.fileNumber
        ? `${item.materialNo}\u0020/\u0020${item.materialName}\u0020/\u0020${item.specification}\u0020/\u0020${item.fileNumber}`
        : `${item.materialNo}\u0020/\u0020${item.materialName}\u0020/\u0020${item.specification}`,
      value: item.id,
      ...item,
    };
  });
}
// 搜索物料
let timer;
const handleSearchMaterial = (value) => {
  if (value.length) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      getMaterialList(value);
    }, 500);
  } else {
    setTimeout(() => {
      allMaterialList.value = [];
    }, 500);
  }
};
//   选中物料
const handleChangeMaterial = async (value) => {
  if (value) {
    formData.successWarehouseId = undefined;
    formData.successWarehouseArea = undefined;
    areaOptions.value = [];

    // 获取选中的物料信息
    let res = await getInfo(value);
    selectedMaterial.value = res.data;

    let res1 = await getInfoPriceByMaterialId({
      id: value,
    });
    if (res1.data.length) {
      formData.unitPrice = res1.data[0].price;
      formData.amount = new Decimal(formData.successNum || 0).mul(
        new Decimal(formData.unitPrice || 0)
      );
    } else {
      formData.unitPrice = 0;
    }
    if (props.inOutType != "in") {
      getRepository(value);
    }
  }
};
// 确认新增物料
function formSubmit() {
  formRef.value.validate().then(async () => {
    submitLoading.value = true;
    let param = {
      classifyType: props.type,
      type: repositoryObj[props.type],
      ...formData,
      file: formData.file && formData.file[0],
    };
    // return;
    let res;
    if (props.inOutType == "in") {
      res = await checkManualIn(param, props.submitToken);
    } else if (props.inOutType == "out") {
      res = await checkManualOut(param, props.submitToken);
    }
    emit("finish");
    if (res.code == 200) {
      proxy.$message.success("操作成功");
      onClose();
      submitLoading.value = false;
    } else {
      submitLoading.value = false;
    }
    base64img.value = "";
  });
}
function onClose() {
  formRef.value.resetFields();
  allMaterialList.value = [];
  selectedMaterial.value = {};
  repositoryOptions.value = [];
  areaOptions.value = [];
  formData.file = [];
  formData.remark = undefined;
  emit("update:visible", false);
}
const getRepository = async (valId) => {
  let key = repositoryObj[props.type];
  if (props.inOutType == "out") {
    let result = await selectMenuListIgnoreNoStock({
      warehouseType: "",
      materialId: valId,
    });
    repositoryOptions.value = result.data;

    createByName.value = result.data ? result.data[0]?.createBy : "";
  } else {
    let result = await list({ warehouseType: key });
    repositoryOptions.value = result.data;
    createByName.value = result.data ? result.data[0]?.createBy : "";
  }
};
const handleChangeRepository = async (id) => {
  formData.successWarehouseArea = undefined;
  if (props.inOutType == "out") {
    repositoryOptions.value.forEach((item, index) => {
      if (id == item.warehouseId) {
        areaOptions.value = item.warehouseMenuDetailVoList;
      }
    });
  } else {
    let result = await getArea(id);
    areaOptions.value = result.data?.warehouseMenuDetailVoList || [];
  }

  // }

  // warehouseId;
  // let result = await getArea(id);
  // areaOptions.value = result.data?.warehouseMenuDetailVoList || [];
};

//  获取产品列表
async function getAllProduct(keyword) {
  let result = await getAllProductList({
    keyword: keyword,
    ignoreCancel: true,
  });
  let { data } = result;
  allProductList.value = data.map((item) => {
    return {
      ...item,
      label: item.productName,
      value: item.productNo,
    };
  });
}

const handleChangeProduct = (e, v) => {
  selectedMaterial.value = v;
  formData.successWarehouseId = undefined;
  formData.successWarehouseArea = undefined;
  if (props.inOutType != "in") {
    getRepository(selectedMaterial.value.id);
  }
};
const selectedArea = ref();
const handelChangeArea = (e) => {
  console.log("[ e ] >", e);
  selectedArea.value = areaOptions.value.find(
    (item) => item.warehouseAreaId == e
  );
};
watch(
  () => props.visible,
  async (val) => {
    postNoContractReview();
    formData.warehouseType =
      props.inOutType == "in" ? "OTHERS" : "RAWMATERIAL_WASTE_OUT";
    // 初始化金额
    formData.amount = 0;
    if (val) {
      if (props.inOutType != "out") {
        getRepository();
      }
      receivingList();
      if (props.type == "product") {
        getAllProduct();
      }
    }
  }
);
</script>
<style lang="less" scoped>
.outboundTable {
  border: 1px solid #ccc;
  width: 100%;
  text-align: center;
  margin-top: 6%;

  tr {
    height: 40px;
    line-height: 40px;
    border: 1px solid #ccc;
  }

  td,
  th {
    border: 1px solid #ccc;
  }
}

.outboundPage {
  h1 {
    font-size: 30px;
    font-weight: bold;
    text-align: center;
    padding: 5%;
  }

  .title {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
}
</style>
