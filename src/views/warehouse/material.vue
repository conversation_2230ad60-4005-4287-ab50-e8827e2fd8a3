<template>
  <search :searchData="searchData" @search="refresh">
    <div>
      <mw-button
        @click="exportOrg"
        :loading="exportLoading"
        :disabled="!data?.length"
        v-permission="'exportOrg:button'"
        >导出Excel</mw-button
      >
      <mw-button
        :font="'iconfont icon-xianxing-121'"
        @click="showInOutCheckDrawer('in')"
        v-permission="'warehouse:insert:manualIn'"
        >新增入库</mw-button
      >
      <mw-button
        :font="'iconfont icon-xianxing-121'"
        @click="showInOutCheckDrawer('out')"
        v-permission="'warehouse:insert:manualOut'"
        >新增出库</mw-button
      >
    </div>
  </search>
  <mw-table
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
    :customRow="rowClick"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'info'">
        <div class="">
          <p class="overflow" :title="record.warehouseName">
            {{ record.warehouseName }}
          </p>
          <p class="text-secondar-text">储存区域：{{ record.warehouseArea }}</p>
        </div>
      </template>
      <template v-if="column.key == 'bizInfo'">
        <p class="overflow" :title="record.materialName">
          {{ record.materialName }}
        </p>
        <p class="text-secondar-text">编码：{{ record.materialNo }}</p>
        <p class="text-secondar-text">规格：{{ record.specification }}</p>
      </template>
      <template v-if="column.key == 'category'">
        <div class="">
          <span v-if="type == 'material'">
            {{ record.classificationParentName }}
            <span
              v-if="
                record.classificationParentName && record.classificationName
              "
              >/
            </span></span
          >{{ record.classificationName }}
        </div>
      </template>
      <template v-if="column.key == 'type'">
        <div class="">
          <dictionary
            :showBadge="false"
            :statusOptions="warehouseTypes"
            :value="record.type"
          />
        </div>
      </template>
      <template v-if="column.key == 'inventory'">
        <div class="">
          <p>{{ record.inventory }}</p>
          <!-- <a
            href="javascript:void(0)"
            @click="
              $router.push({
                name: 'WarehouseInOutRecordList',
                query: { type: 'material', materialId: record.materialId },
              })
            "
            >查看进出库记录</a
          > -->
        </div>
      </template>
    </template>
  </mw-table>
  <in-out-check-drawer
    v-model:visible="inOutCheckVisible"
    :type="type"
    :inOutType="inOutType"
    :name="title"
    :submitToken="submitToken"
    @finish="getList"
  />
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
} from "vue";
import { page, exportWarehouseDetail } from "@/api/warehouse.js";
import { usePagenation } from "@/common/setup";
import Search from "@/components/search/index.vue";
import { materialStatus } from "@/common/constant.js";
import { list as getAllCategory } from "@/api/basicData/category.js";
import { getDicByType, exportExecl } from "@/utils/util.js";
import inOutCheckDrawer from "./inOutCheckDrawer.vue";
const submitToken = new Date().getTime();
const inOutCheckVisible = ref(false),
  exportLoading = ref(false),
  inOutType = ref("in");
const showInOutCheckDrawer = (t) => {
  inOutType.value = t;
  inOutCheckVisible.value = true;
};
const props = defineProps({
  type: {
    type: String,
    default: "material",
  },
});

const title = computed(() => {
  return props.type == "material" ? "物料" : "成品";
});

const columns = ref([
  {
    title: "仓库信息",
    key: "info",
    width: "280px",
  },
  {
    title: title.value + "信息",
    key: "bizInfo",
  },
  {
    title: props.type == "material" ? "一级分类/二级分类" : "分类",
    key: "category",
    align: "left",
  },
  {
    title: "仓库类型",
    key: "type",
    align: "left",
  },
  {
    title: "库存数量",
    key: "inventory",
    align: "left",
  },
]);
const { proxy } = getCurrentInstance();
const data = ref([]),
  loading = ref(false),
  warehouseTypes = ref([]);
const finishedData = ref([
  {
    label: "全部仓库",
    value: "",
  },
  {
    label: "成品仓",
    value: "FINISHED",
  },
  {
    label: "废品仓",
    value: "WASTE_FINISHED",
  },
]);
const rawMaterialData = ref([
  {
    label: "全部仓库",
    value: "",
  },
  {
    label: "物料仓",
    value: "RAWMATERIAL",
  },
  {
    label: "废料仓",
    value: "WASTE",
  },
]);
const searchData = ref({
  fields: {
    finishTypeId: {
      type: "a-tree-select",
      placeholder: `选择${title.value}分类`,
      dropdownMatcSelectWidth: true,
      showSearch: true,
      treeNodeFilterProp: "name",
      fieldNames: { children: "children", label: "name", value: "id" },
      treeData: [],
      width: "140px",
      allowClear: true,
      value: "",
    },
    type: {
      name: "仓库类型",
      type: "a-select",
      options:
        props.type == "material" ? rawMaterialData.value : finishedData.value,
      placeholder: "仓库类型",
      value: "",
      width: "120px",
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: `输入仓库名称/${title.value}编码/名称/规格`,
      width: "290px",
      allowClear: true,
    },
  },
});

const exportOrg = async () => {
  exportLoading.value = true;
  let searchParam = {};
  pageParam.value.pageSize = 100000;
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  //单个搜索信息
  if (searchData.value.filterData) {
    searchParam[searchData.value.filterData.value.filterBy] =
      searchData.value.filterData.value.filterValue;
  }
  let param = {
    ...searchParam,
    classifyType: props.type,
  };
  let result = await exportWarehouseDetail(param, pageParam.value);
  const fileName = title.value + "库存.xlsx";
  exportExecl(fileName, result);
  exportLoading.value = false;
};

const getList = async () => {
  loading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let result = await page(pageParam.value, {
    ...searchParam,
    classifyType: props.type,
  });
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
const getClassificationIdTreeData = async () => {
  let result = await getAllCategory(props.type);
  searchData.value.fields.finishTypeId.treeData = result.data?.map((item) => {
    return {
      ...item,
      selectable: props.type == "product" || item.parentId != "0", //产品只有一级分类，所以要支持选择
    };
  });
  searchData.value.fields.finishTypeId.treeData.unshift({
    name: "全部分类",
    id: "",
  });
};
const getWarehouseTypeOption = async () => {
  let { dics, dicFilters } = await getDicByType("warehouse_type", "仓库");
  warehouseTypes.value = dics;
  // searchData.value.fields.type.options = dicFilters;//原本是全部，现在修改为局部
};
onBeforeMount(async () => {
  getClassificationIdTreeData();
  getWarehouseTypeOption();
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
const rowClick = (record) => {
  return {
    onClick: (event) => {},
  };
};
</script>

<style lang="less" scoped>
:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  // background: transparent;
}
:deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
  // background: transparent;
}
:deep(.ant-table-cell) {
  vertical-align: top;
}
.yuanliao {
  margin-top: 20px;
}
button {
  margin-right: 10px;
}
</style>
