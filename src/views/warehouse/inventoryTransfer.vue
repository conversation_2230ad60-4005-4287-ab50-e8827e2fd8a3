<template>
  <div class="inventory_transfer">
    <search :searchData="searchData" @search="refresh">
      <mw-button @click="onOpen('add')" :font="'iconfont icon-xianxing-121'"
        >新增</mw-button
      >
    </search>

    <mw-table
      :align="center"
      :columns="columns"
      :data-source="dataSource"
      class="tables"
      :rowKey="(record) => record.id"
      hasPage
      @change="onTableChange"
      :pageConfig="paginationProps"
      :scroll="{ x: 'max-content' }"
      :loading="loading"
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.key == 'outWarehouseName'">
          {{ record.outWarehouseName }}/{{ record.outWarehouseAreaName }}
        </template>
        <template v-if="column.key == 'inWarehouseName'">
          {{ record.inWarehouseName }}/{{ record.inWarehouseAreaName }}
        </template>
        <template v-if="column.key == 'operation'">
          <mw-button @click="onOpen('edit', record)" v-if="record.status == 98"
            >编辑</mw-button
          >
          <mw-button @click="onOpen('detail', record)" v-else>详情</mw-button>
        </template>
      </template>
    </mw-table>

    <mw-drawer
      :custom-title="`${
        titleName == 'add'
          ? '新增调拨'
          : titleName == 'detail'
          ? '详情'
          : '编辑'
      }`"
      :visible="visible"
      @close="onClose"
    >
      <template #header>
        <mw-button @click="submit" v-if="titleName == 'add'">确定</mw-button>
        <mw-button @click="submit" v-if="titleName == 'edit'">确定</mw-button>
      </template>
      <TransferForm
        ref="transferFormSbmit"
        :visibleRecord="visibleRecord"
        :titleName="titleName"
      />
    </mw-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import Search from "@/components/search/index.vue";
import TransferForm from "./transferForm.vue";
import { transferList, transferAdd, transferAddV2 } from "@/api/warehouse";
import { usePagenation } from "@/common/setup";
import { message } from "ant-design-vue";
const searchData = reactive({
  fields: {
    // type: {
    //   type: "a-select",
    //   options: [{label:'全部',value:''},{label:'产品',value:'product'},{label:'物料',value:'material'}],
    //   placeholder: "选择调拨类型",
    //   width: "150px",
    //   value: '',
    //   allowClear:true
    // },
    keyword: {
      type: "a-input-search",
      placeholder: `输入编码、名称、规格、单号`,
      width: "280px",
      value: void 0,
      allowClear: true,
    },
  },
});

const transferFormSbmit = ref(null);

const loading = ref(false);

const columns = ref([
  {
    title: "调拨单号",
    dataIndex: "uniqueNo",
    key: "uniqueNo",
  },
  // {
  //   title: "类型",
  //   dataIndex: "type",
  //   key: "type",
  // },
  {
    title: "编码",
    dataIndex: "materialOrProNo",
    key: "materialOrProNo",
  },
  {
    title: "名称",
    dataIndex: "materialOrProductName",
    key: "materialOrProductName",
  },
  {
    title: "规格",
    dataIndex: "specification",
    key: "specification  ",
  },
  {
    title: "调拨数量",
    dataIndex: "inventory",
    key: "inventory  ",
  },
  {
    title: "调拨前仓库/库位",
    dataIndex: "outWarehouseName",
    key: "outWarehouseName",
  },
  {
    title: "调拨后仓库/库位",
    dataIndex: "inWarehouseName",
    key: "inWarehouseName",
  },
  {
    title: "操作人",
    dataIndex: "createBy",
    key: "createBy  ",
  },
  {
    title: "操作",
    dataIndex: "operation",
    key: "operation",
  },
]);

const dataSource = ref([]);

const visible = ref(false);
const titleName = ref("");
const visibleRecord = ref();
const onOpen = (type, record) => {
  titleName.value = type;
  visible.value = true;
  visibleRecord.value = record;
};
const onClose = () => {
  visible.value = false;
};

const getList = async () => {
  let searchParam = {};
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;
  }
  loading.value = true;
  try {
    const { code, data, total } = await transferList({
      ...pageParam.value,
      ...searchParam,
    });
    if (code === 200) {
      dataSource.value = data;
    }
    paginationProps.value.total = total;
    loading.value = false;
  } catch (error) {
    loading.value = false;
  }
};
onMounted(() => {
  getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

const submit = async () => {
  if (titleName.value == "add") {
    const value = await transferFormSbmit.value.submit();
    console.log("[ value ] >", value);
    if (value) {
      const { code } = await transferAdd(value);
      if (code === 200) {
        message.success("新增库存调拨成功");
        onClose();
        refresh();
      }
    }
  } else if (titleName.value == "edit") {
    //
    const value = await transferFormSbmit.value.submit();
    console.log("[ value ] >", value);
    if (value) {
      const { code } = await transferAddV2({
        ...value,
        id: visibleRecord.value.id,
      });
      if (code === 200) {
        message.success("新增库存调拨成功");
        onClose();
        refresh();
      }
    }
  }
};
</script>
