<template>
  <mw-drawer
    custom-title="备注详情"
    :visible="visible"
    @close="onClose"
    :spinning="spinning"
  >
    <div class="space-y-1 pt-2">
      <p>{{ detail.bizName }}</p>
      <p class="text-secondar-text">备注时间：{{ detail.remarkTime }}</p>
      <p class="text-secondar-text">
        不合格数：{{ detail.disqualifiedQuantity }}
      </p>
      <p class="text-secondar-text">
        不良率：{{ detail.disqualifiedPercent || "-" }}
      </p>
      <p class="text-secondar-text flex" v-if="detail.remark">
        <span>备注内容：</span>{{ detail.remark }}
      </p>
      <p class="text-secondar-text flex" v-if="detail.remarkFile">
        <span class="mr-3">添加附件</span>
        <form-upload
          :value="detail.remarkFile ? [detail.remarkFile] : []"
          sence="quality"
          readonly
        ></form-upload>
      </p>
    </div>
  </mw-drawer>
</template>
<script setup>
import { defineProps, ref, getCurrentInstance, watch, defineEmits } from "vue";
import { remark } from "@/api/warehouse.js";
import FormUpload from "@/components/form-upload.vue";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  data: { type: Object, default: () => ({}) },
});
const emit = defineEmits(["update:visible", "finish"]);

const onClose = () => {
  emit("update:visible", false);
};
const spinning = ref(false),
  detail = ref({});
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      spinning.value = true;
      let { transferId, materialId } = props.data;
      let result = await remark({
        batchId: transferId,
        materialIdOrProductId: materialId,
      });
      detail.value = result.data;
      spinning.value = false;
    }
  }
);
</script>
<style lang="less" scoped>
.bg {
  height: 40px;
  line-height: 40px;
  background: rgba(34, 34, 34, 0.08);
  border-radius: 8px;
  text-align: center;
  margin-bottom: 24px;
}
</style>
