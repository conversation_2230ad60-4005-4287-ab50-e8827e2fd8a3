<template>
  <search :searchData="searchData" @search="refresh"> </search>
  <mw-table
    :align="center"
    :columns="columns.fatColumns"
    :data-source="data"
    class="tables"
    :rowKey="(record) => record.transferId"
    childrenColumnName="record.settlementInfoVOList"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'type'">
        <dictionary :statusOptions="warehousingTypeList" :value="record.type" />
      </template>
      <template v-if="column.key == 'status'">
        <dictionary
          :statusOptions="warehouseStatus"
          :value="record.status"
          isBackgroundColor
        />
      </template>
      <template v-if="column.key == 'purchaseNumber'">
        <span
          v-if="record.purchaseNumber && record.type == 'PURCHASE'"
          class="text-primary cursor-pointer"
          @click="
            onParentApplicantNoRemarks(
              '备注',
              'qualityInspectionRemarks',
              record
            )
          "
        >
          {{ record.purchaseNumber }}
        </span>
        <span v-if="record.orderNos">
          {{ record.orderNos }}
        </span>
      </template>
      <template v-if="column.key == 'relatesInformation'">
        <span
          v-if="record.type == 'PURCHASE'"
          class="text-primary cursor-pointer"
          @click="
            onParentApplicantNoRemarks(
              '备注',
              'qualityInspectionRemarks',
              record
            )
          "
        >
          {{ record.relatesInformation }}
        </span>
      </template>
      <template v-if="column.key == 'supplierName'">
        {{ record.supplierName }}
      </template>
      <template v-if="column.key == 'button'">
        <mw-button
          v-if="record.status !== 1"
          @click="showCreateDrawer(record)"
          class="mr-2"
          >入库</mw-button
        >
        <mw-button
          @click="
            printInventoryApplication(record, 'printInventoryApplication')
          "
          >打印</mw-button
        >
      </template>
    </template>
    <template #expandedRowRender="{ record }">
      <mw-table
        :columns="columns.childrenColumns"
        :data-source="record.insertParamList"
        :pagination="{ pageSize: 5 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'wasteNum'">
            <div>
              {{ record.wasteNum }}
            </div>
            <!-- <div>
              因为接口原因说暂时隐藏
              <a @click.stop="showDetailDrawer(record)" style="color: #959ec3"
                >查看备注</a
              >
            </div> -->
          </template>
        </template>
      </mw-table>
    </template>
  </mw-table>
  <in-storage-drawer
    :title="title"
    :type="type"
    v-model:visible="addVisible"
    :record="currendRecord"
    @finish="getList"
    :reqType="reqType"
  />
  <detail-drawer v-model:visible="detailVisible" :data="currentDetail" />
  <PublicRemarks
    v-model:visible="publicRemarksVisible"
    :publicRemarksTitle="publicRemarksTitle"
    :remarkRecord="remarkRecord"
    @finish="getList"
    :remarkType="remarkType"
    :prohibit="true"
  />
  <mobilize-drawer
    v-model:visible="mobilizeVisible"
    :data="parameterData"
  ></mobilize-drawer>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
} from "vue";
import { listInsertList } from "@/api/warehouse.js";
import { usePagenation } from "@/common/setup";
import Search from "@/components/search/index.vue";
import inStorageDrawer from "./inStorageDrawer.vue";
import detailDrawer from "./detailDrawer.vue";
import PublicRemarks from "@/components/publicRemarks.vue";
import mobilizeDrawer from "./mobilizeDrawer.vue";
import {
  warehouseStatus,
  warehouseStatusFilters,
  warehouseType,
  warehousingTypeList,
} from "@/common/constant.js";
import { InfoCircleFilled } from "@ant-design/icons-vue";
import { useRoute } from "vue-router";

const { proxy } = getCurrentInstance();

const type = computed(() => {
  return route.query.type || "material";
});
const title = computed(() => {
  return type.value == "product" ? "成品" : "物料";
});
const parameterData = ref({
  // classifyType: "material",
  keyword: "",
});
const mobilizeVisible = ref(false);
const reqType = ref("");
const data = ref([]);
const loading = ref(false);
const addVisible = ref(false);
const currendRecord = ref({});
const currentDetail = ref({});
const detailVisible = ref(false);
const route = useRoute();
const publicRemarksTitle = ref();
const publicRemarksVisible = ref();
const remarkRecord = ref();
const remarkType = ref();
const searchData = reactive({
  fields: {
    rangeDate: {
      type: "a-range-picker",
      valueFormat: "YYYY-MM-DD",
      value: [],
      width: "240px",
      allowClear: true,
    },
    status: {
      name: "质检状态",
      type: "a-select",
      options: warehouseStatusFilters,
      placeholder: "选择状态",
      width: "120px",
      value: "",
      allowClear: true,
    },
    type: {
      name: "质检类型",
      type: "a-select",
      options: [{ label: "全部类型", value: "" }, ...warehousingTypeList], // 在数组中添加一个空对象 warehousingTypeList,
      placeholder: "选择状态",
      width: "120px",
      value: "",
      allowClear: true,
    },

    keyword: {
      type: "a-input-search",
      placeholder: `输入${title.value}编码/名称/关联信息/供应商/采购订单`,
      width: "350px",
      allowClear: true,
    },
  },
});
const columns = ref({
  fatColumns: [
    {
      title: "入库类型",
      dataIndex: "type",
      key: "type",
    },
    {
      title: "申请人员",
      dataIndex: "createByName",
      key: "createByName",
    },
    {
      title: "父关联编码",
      dataIndex: "purchaseNumber",
      key: "purchaseNumber",
    },
    {
      title: "关联编码",
      dataIndex: "relatesInformation",
      key: "relatesInformation",
    },

    {
      title: "供应商",
      dataIndex: "supplierName",
      key: "supplierName",
    },
    {
      title: "申请时间",
      dataIndex: "createTime",
      key: "createTime",
    },
    // {
    //   title: "质检人员",
    //   dataIndex: "reviewerId",
    //   key: "reviewerId",
    //   width: "150px",
    // },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
    },

    {
      title: "操作",
      dataIndex: "button",
      key: "button",
    },
  ],
  childrenColumns: [
    {
      title: `${title.value}` + "信息",
      dataIndex: "materialName",
      key: "materialName",
      width: "150px",
    },
    {
      title: `${title.value}` + "编码",
      dataIndex: "materialNo",
      key: "materialNo",
      width: "150px",
    },
    {
      title: "规格",
      dataIndex: "specification",
      key: "specification",
      width: "150px",
    },
    {
      title: "入库总数",
      dataIndex: "successNum",
      key: "successNum",
      width: "150px",
    },
    // {
    //   title: "不合格" + `${title.value}`,
    //   dataIndex: "wasteNum",
    //   key: "wasteNum",
    //   width: "150px",
    // },
    // {
    //   title: "不良率",
    //   dataIndex: "wasteRate",
    //   key: "wasteRate",
    //   width: "150px",
    // },
    {
      title: "已入库",
      dataIndex: "alreadyNum",
      key: "alreadyNum",
      width: "150px",
    },
    {
      title: "待入库",
      dataIndex: "waitNum",
      key: "waitNum",
      width: "150px",
    },
  ],
});

const getList = async () => {
  loading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await listInsertList(pageParam.value, {
    ...searchParam,
    classifyType: type.value,
  });
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
const showCreateDrawer = (record) => {
  if (record.status != "1") {
    console.log("[ record ] >", record);
    currendRecord.value = record;
    addVisible.value = true;
    reqType.value = record.type;
  }
};
const showDetailDrawer = (record) => {
  currentDetail.value = record;
  detailVisible.value = true;
};
const onParentApplicantNoRemarks = (title, type, val) => {
  publicRemarksTitle.value = title;
  publicRemarksVisible.value = true;
  remarkRecord.value = val;
  remarkType.value = type;
};
const printInventoryApplication = (val, typeName) => {
  parameterData.value.keyword = val.relatesInformation;
  parameterData.value.typeName = typeName;
  parameterData.value.warehouseType = val.type;
  parameterData.value.relatesInformation = val.relatesInformation;
  parameterData.value.transferId = val.transferId;

  mobilizeVisible.value = true;
};
</script>
<style lang="less" scoped>
.list-header {
  background: theme("colors.background");
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  color: theme("colors.primary-text");
}
:deep(.ant-list-item) {
  // background: theme("colors.background");
  border: 1px solid theme("colors.dividers") !important;
  padding: 0 16px !important;
  border-radius: 8px;
}
</style>
