<template>
  <mw-drawer
    :custom-title="`${title}入库`"
    :visible="visible"
    @close="onClose"
    :spinning="spinning"
  >
    <template #header>
      <a-popconfirm
        title="确认入库无误！"
        @confirm="submitForm"
        placement="bottomRight"
      >
        <mw-button :loading="submitLoading">批量入库</mw-button>
      </a-popconfirm>
    </template>
    <div class="divide-y divide-border space-y-2">
      <!-- <p>类型：{{ detail.type }}</p> -->
      <template v-for="item in detail.insertParamList" :key="item.materialNo">
        <div class="space-y-1 pt-2 text-secondar-text">
          <p class="text-title">{{ item.materialName }}</p>
          <p>编码：{{ item.materialNo }}</p>
          <p>规格：{{ item.specification }}</p>
          <p>物料备注：{{ item.materialRemark || "--" }}</p>
          <!-- <p>待入库：{{ item.waitNum || "-" }}</p> -->
          <!-- <p>实际入库：{{ item.alreadyNum || "-" }}</p> -->
          <template v-if="item.innerSuccessParams">
            <p>选择{{ title }}仓：(等待入库数:{{ item.waitSuccessNum }})</p>
            <div class="bg-background p-4 rounded-lg">
              <div class="divide-y divide-border space-y-2">
                <div
                  class="relative space-y-1 pt-2"
                  v-for="(element, index) in item.innerSuccessParams"
                  :key="index"
                >
                  <p class="text-right" v-if="index !== 0">
                    <i
                      class="iconfont icon-jichu-shanchu align-middle cursor-pointer"
                      @click="
                        () => {
                          item.innerSuccessParams.splice(index, 1);
                        }
                      "
                    ></i
                    >移除
                  </p>

                  <p class="flex items-center justify-between">
                    {{ title }}仓：
                    <a-select
                      show-search
                      optionFilterProp="warehouseName"
                      class="w-80"
                      v-model:value="element.successWarehouseId"
                      :placeholder="`请选择${title}仓`"
                      :options="repositoryObj[type].repository.value"
                      :fieldNames="{
                        label: 'warehouseName',
                        value: 'id',
                      }"
                      :disabled="record.type == 'ORDER_RETURN'"
                      @change="(id) => handleChangeRepository(id, element)"
                    ></a-select>
                  </p>
                  <p class="flex items-center justify-between">
                    仓库区域：
                    <a-select
                      show-search
                      optionFilterProp="warehouseArea"
                      class="w-80"
                      v-model:value="element.successWarehouseArea"
                      placeholder="请选择区域"
                      :options="element.areaOptions"
                      :fieldNames="{
                        label: 'warehouseArea',
                        value: 'id',
                      }"
                      :disabled="record.type == 'ORDER_RETURN'"
                    ></a-select>
                  </p>
                  <p>
                    入库数量：<a-input-number
                      class="ml-6"
                      v-model:value="element.successNum"
                      :stringMode="true"
                    />
                  </p>
                </div>
              </div>
              <p class="text-center mt-4" v-if="record.type != 'ORDER_RETURN'">
                <mw-button @click="add(item)">新增存放区</mw-button>
              </p>
            </div>
          </template>
          <!-- 产品提出废品仓不展示，3/20号 -->
          <!-- <template v-if="item.innerFailParams && item.waitFailNum > 0">
            <p>选择废料仓：(等待入库数:{{ item.waitFailNum }})</p>
            <div
              class="bg-background p-4 rounded-lg space-y-1"
              v-for="(failItem, failIndex) in item.innerFailParams"
              :key="failIndex"
            >
              <p class="flex items-center justify-between">
                废料仓：<a-select
                  class="w-80"
                  show-search
                  optionFilterProp="warehouseName"
                  v-model:value="failItem.wasteWarehouseId"
                  placeholder="请选择废料仓"
                  :options="repositoryObj[type].waste.value"
                  :fieldNames="{
                    label: 'warehouseName',
                    value: 'id',
                  }"
                  @change="(id) => handleChangeRepository(id, failItem)"
                ></a-select>
              </p>
              <p class="flex items-center justify-between">
                仓库区域：<a-select
                  show-search
                  optionFilterProp="warehouseArea"
                  class="w-80"
                  v-model:value="failItem.wasteWarehouseArea"
                  placeholder="请选择区域"
                  :options="failItem.areaOptions"
                  :fieldNames="{
                    label: 'warehouseArea',
                    value: 'id',
                  }"
                ></a-select>
              </p>
              <p>
                入库数量：<a-input-number
                  class="ml-6"
                  v-model:value="failItem.wasteNum"
                />
              </p>
            </div>
          </template> -->
        </div>
      </template>
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="horizontal"
        :colon="false"
      >
        <a-form-item class="mt-5">
          <template #label>
            <div class="text-left text-primary-text">备注</div>
          </template>
          <a-input v-model:value="formData.remark" />
        </a-form-item>
        <a-form-item>
          <template #label>
            <div class="text-left text-primary-text">附件</div>
          </template>
          <form-upload
            v-if="!props.id"
            v-model:value="formData.file"
            sence="article"
            :fileTypes="[]"
            :fileSize="100"
          ></form-upload>
          <div
            v-else
            @click.stop="openUrl(formData.file?.fileVisitUrl)"
            :href="formData.file?.fileVisitUrl"
            :title="formData.file?.fileName"
            class="cursor-pointer inline-block"
            style="color: #959ec3"
          >
            <i
              class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
              style="color: #959ec3"
            ></i>

            <span class="underline">{{ formData.file?.fileName }}</span>
          </div>
        </a-form-item>
      </a-form>
    </div>
  </mw-drawer>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  defineEmits,
  reactive,
} from "vue";
import FormUpload from "@/components/form-upload.vue";
import { list, getInfo } from "@/api/basicData/repository.js";
import { checkIn } from "@/api/warehouse.js";
import _cloneDeep from "lodash/cloneDeep";

const { proxy } = getCurrentInstance();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  record: { type: Object, default: () => ({}) },
  title: {
    type: String,
    default: "物料",
  },
  type: {
    type: String,
    default: "material",
  },
  reqType: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["update:visible", "finish"]);

const submitLoading = ref(false),
  spinning = ref(false),
  detail = ref({});
const formData = reactive({});
const repositoryObj = reactive({
  material: {
    //物料仓
    repository: {
      key: "RAWMATERIAL",
      value: [],
    },
    //废料仓
    waste: {
      key: "WASTE",
      value: [],
    },
  },
  product: {
    //成品仓
    repository: {
      key: "FINISHED",
      value: [],
    },
    // 废品仓
    waste: {
      key: "WASTE_FINISHED",
      value: [],
    },
  },
});
const onClose = () => {
  emit("update:visible", false);
  formData.file = [];
  formData.remark = undefined;
  detail.value = {};
};

const submitForm = async () => {
  try {
    // detail.value.insertParamList?.forEach((item) => {
    //   item.innerSuccessParams.forEach((is) => {
    //     // if (typeof item.disqualifiedQuantity != "number") {
    //     //   throw new Error("请填写不合格数量");
    //     // }
    //     if (!item.remark) {
    //       throw new Error("请填写备注信息");
    //     }
    //   });
    // });
    let param = {
      ...formData,
      file: formData.file && formData.file[0],
      commonId: detail.value.transferId,
      menuType: props.type,
      type: props.reqType,
      insertParamList: detail.value.insertParamList.map((i) => {
        return {
          materialId: i.materialId,
          innerSuccessParams: i.innerSuccessParams?.map((inner) => {
            let { successNum, successWarehouseId, successWarehouseArea } =
              inner;
            return { successNum, successWarehouseId, successWarehouseArea };
          }),
          innerFailParams: i.innerFailParams?.map((inner) => {
            let { wasteNum, wasteWarehouseId, wasteWarehouseArea } = inner;
            return { wasteNum, wasteWarehouseId, wasteWarehouseArea };
          }),
        };
      }),
    };

    let checkEmp = "";
    param.insertParamList.forEach((item, index) => {
      if (item.innerSuccessParams) {
        item.innerSuccessParams.forEach((ite, index) => {
          if (!ite.successWarehouseArea || !ite.successWarehouseId) {
            checkEmp = true;
          }
        });
      }
    });
    if (checkEmp) {
      proxy.$message.error("请完整选择默认所属仓库和区域");
      return;
    }

    submitLoading.value = true;
    let res = await checkIn(param);
    if (res.code == 200) {
      proxy.$message.success("入库成功");
      onClose();
    }
    submitLoading.value = false;
    emit("finish");
  } catch (error) {
    submitLoading.value = false;
    error & error.message && proxy.$message.warning(error.message);
  }
};
const getRepository = async () => {
  for (const key in repositoryObj[props.type]) {
    let repository = repositoryObj[props.type][key];
    console.log("[ repository.key ] >", repository.key);
    let result = await list({
      warehouseType: repository.key,
    });
    repository.value = result.data;
  }
};
const emptyObj = {
  successNum: undefined,
  successWarehouseId: undefined,
  successWarehouseArea: undefined,
  areaOptions: [],
};
const handleChangeRepository = async (id, element) => {
  let result = await getInfo(id);
  if (result.data.warehouseMenuDetailVoList) {
    // emptyObj.areaOptions = result.data.warehouseMenuDetailVoList;
    element.areaOptions = result.data.warehouseMenuDetailVoList;
    const ids = result.data.warehouseMenuDetailVoList.map((item) => {
      return item.id;
    });
    console.log("[ ids ] >", ids);
  } else {
    element.areaOptions = [];
  }
  element.successWarehouseArea = undefined;
};
const add = (item) => {
  item.innerSuccessParams.push({ ...emptyObj });
};

watch(
  () => props.visible,
  async (val) => {
    if (val) {
      //过滤掉已全部完成入库的物料
      detail.value = _cloneDeep({
        ...props.record,
        insertParamList: props.record.insertParamList?.filter(
          (i) => i.status != "1"
        ),
      });
      console.log("[ detail.value ] >", detail.value);
      await getRepository();
      // detail.value.insertParamList?.forEach(async (element, index) => {
      //   //如果有待入仓的合格数
      //   if (element.waitSuccessNum) {
      //     if (element.defaultWarehouseId) {
      //       let res = await getInfo(element.defaultWarehouseId);
      //       emptyObj.areaOptions = res.data.warehouseMenuDetailVoList;
      //       if (element.defaultWarehouseId) {
      //         // 每条下面的对象
      //         for (const key in repositoryObj[props.type]) {
      //           let repository = repositoryObj[props.type][key];
      //           let result = await list({ warehouseType: repository.key });
      //           repository.value = result.data;
      //           repository.value.forEach((item, index) => {

      //             if (element.defaultWarehouseId == item.id) {
      //               emptyObj.successWarehouseId = Number(
      //                 element.defaultWarehouseId
      //               );

      //               emptyObj.areaOptions.forEach((itemArea, indexArea) => {
      //                 if (element.defaultWarehouseAreaId == itemArea.id) {
      //                   emptyObj.successWarehouseArea = String(itemArea.id);
      //                 }
      //               });
      //             }
      //           });
      //         }
      //       }
      //     }

      //     element.innerSuccessParams = [{ ...emptyObj }];
      //   }
      //   //  入库数量绑定
      //   // 物料/成品仓
      //   if (detail.value.insertParamList) {
      //     detail.value.insertParamList.forEach((item, index) => {
      //       if (item.innerSuccessParams) {
      //         item?.innerSuccessParams.forEach((ite, ind) => {
      //           ite.successNum = item.waitSuccessNum;
      //         });
      //       }
      //     });
      //   }

      //   if (element.waitFailNum) {
      //     //废料仓
      //     element.innerFailParams = [
      //       {
      //         wasteNum: undefined,
      //         wasteWarehouseId: undefined,
      //         wasteWarehouseArea: undefined,
      //       },
      //     ];
      //   }
      // });
      const insertParamList = detail.value.insertParamList;
      formData.remark = detail.value.headerRemark;
      insertParamList.forEach(async (element) => {
        console.log(element, "formData.remark");
        const emptyObj = {};
        if (props.record.type == "ORDER_RETURN") {
          if (element.lockWarehouseId) {
            emptyObj.successWarehouseId = Number(element.lockWarehouseId);
            let res = await getInfo(element.lockWarehouseId);
            emptyObj.areaOptions = res?.data?.warehouseMenuDetailVoList || [];
          } else {
            emptyObj.successWarehouseId = void 0;
            emptyObj.areaOptions = [];
          }
          emptyObj.successWarehouseArea = element?.lockWarehouseAreaId
            ? String(element?.lockWarehouseAreaId)
            : void 0;
        } else {
          if (element.defaultWarehouseId) {
            emptyObj.successWarehouseId = Number(element.defaultWarehouseId);
            let res = await getInfo(element.defaultWarehouseId);
            emptyObj.areaOptions = res?.data?.warehouseMenuDetailVoList || [];
          } else {
            emptyObj.successWarehouseId = void 0;
            emptyObj.areaOptions = [];
          }
          emptyObj.successWarehouseArea = element?.defaultWarehouseAreaId
            ? String(element?.defaultWarehouseAreaId)
            : void 0;
        }

        emptyObj.successNum = element.waitSuccessNum;
        element.innerSuccessParams = [{ ...emptyObj }];

        if (element.waitFailNum) {
          //废料仓
          element.innerFailParams = [
            {
              wasteNum: undefined,
              wasteWarehouseId: undefined,
              wasteWarehouseArea: undefined,
            },
          ];
        }
      });
    }
  }
);
</script>
<style lang="less" scoped>
.bg {
  height: 40px;
  line-height: 40px;
  background: rgba(34, 34, 34, 0.08);
  border-radius: 8px;
  text-align: center;
  margin-bottom: 24px;
}
</style>
