<template>
  <mw-drawer
    custom-title="物料出库"
    :visible="visible"
    @close="onClose"
    width="100%"
  >
    <template #header> </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="inline"
      :colon="false"
      class="mb-6"
    >
      <a-form-item name="lineId" label="生产线">
        <a-select
          v-model:value="formData.lineId"
          show-search
          allowClear
          placeholder="请选择生产线"
          style="width: 220px"
          :default-active-first-option="false"
          :options="allLineList"
          optionFilterProp="lineName"
          :fieldNames="{
            label: 'lineName',
            value: 'id',
          }"
          @change="warehouseListOutMaterialList(formData)"
        ></a-select>
      </a-form-item>
      <a-form-item name="queryDetailStatus" label="状态">
        <a-select
          v-model:value="formData.queryDetailStatus"
          show-search
          allowClear
          placeholder="请选择状态"
          style="width: 220px"
          :default-active-first-option="false"
          :options="warehouseStatusFilters"
          optionFilterProp="label"
          @change="warehouseListOutMaterialList(formData)"
        ></a-select>
      </a-form-item>
    </a-form>

    <a-spin tip="Loading..." :spinning="spinning">
      <div class="flex mb-2">
        <mw-button
          v-if="![7, 9, 10].includes(detail.status)"
          @click="submitForm"
          :loading="submitLoading"
          :disabled="detailInsertParamList.length > 0 ? false : true"
          class="mr-2"
          >出库</mw-button
        >

        <a-popconfirm
          title="提示：删除后将无法进行出库操作"
          ok-text="确定"
          cancel-text="取消"
          @confirm="onDelete(index)"
          :disabled="detailInsertParamList.length > 0 ? false : true"
        >
          <mw-button
            :loading="submitLoading"
            :disabled="detailInsertParamList.length > 0 ? false : true"
            >删除</mw-button
          >
        </a-popconfirm>

        <!-- detail.insertParamList -->
        <div class="ml-10 flex" style="width: 90%">
          <div class="mt-1">备注：</div>
          <div class="ml-1" style="width: 90%">
            <!-- <a-textarea class="w-full" v-model:value="headerRemark" /> -->
            <a-input class="w-full" v-model:value="headerRemark"></a-input>
          </div>
        </div>
      </div>
      <mw-table
        :scroll="{ x: 'max-content' }"
        class="leading-5.5"
        :columns="columns"
        :data-source="detail.insertParamList"
        :rowKey="(record) => record.id"
        :loading="tableLoading"
        :pagination="pagination"
        @change="handlePageChange"
        :row-selection="{
          selectedRowKeys: warehouseRemoveList.value,
          onChange: onSelectChange,
          getCheckboxProps: getCheckboxProps,
        }"
      >
        <!-- 分页组件 -->

        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'lineName'">{{
            record.lineName ? record.lineName : "无"
          }}</template>

          <template v-if="column.dataIndex == 'materialName'">
            <div class="w-25 cursor-pointer">
              {{ record.materialName }}
            </div>
          </template>
          <template v-if="column.dataIndex == 'specification'">
            <div class="w-25 cursor-pointer">
              {{ record.specification }}
            </div>
          </template>
          <template v-if="column.dataIndex == 'successWarehouseId'">
            <!-- :disabled="record.type == 'PRODUCT_SHIPPING_OUT'" -->
            <a-select
              v-model:value="record.defaultWarehouseId"
              placeholder="请选择物料仓"
              :options="record.repositoryOptions"
              :fieldNames="{
                label: 'warehouseName',
                value: 'warehouseId',
              }"
              :disabled="record.lockWarehouseId"
              @change="
                (e, r) => {
                  // record.totalInventory = r.totalInventory;
                  record.successWarehouseArea = _cloneDeep(null);
                  record.areaOptions = r.warehouseMenuDetailVoList;
                  detail.insertParamList.forEach((items, index) => {
                    if (items.id == record.id) {
                      items.defaultWarehouseAreaId = undefined;
                      items.defaultWarehouseAreaName = undefined;
                    }
                  });
                }
              "
              style="min-width: 100px"
            ></a-select>
          </template>

          <template v-if="column.dataIndex == 'successWarehouseArea'">
            <a-select
              v-model:value="record.defaultWarehouseAreaId"
              placeholder="请选择区域"
              :options="record.areaOptions"
              :fieldNames="{
                label: 'warehouseArea',
                value: 'warehouseAreaId',
              }"
              @change="
                (e, r) => {
                  record.inventory = r.inventory;
                }
              "
              style="min-width: 100px"
            ></a-select
          ></template>

          <template v-if="column.dataIndex == 'orderNumTot'">
            {{ record.orderNumTot }}{{ record.unitName }}
          </template>
          <template v-if="column.dataIndex == 'alreadyNum'">
            {{ record.alreadyNum }}{{ record.unitName }}
          </template>
          <template v-if="column.dataIndex == 'waitNum'">
            {{ record.waitNum }}{{ record.unitName }}
          </template>
          <template v-if="column.dataIndex == 'totalInventory'">
            {{ record.totalInventory ? record.totalInventory : 0
            }}{{ record.unitName }}
          </template>
          <template v-if="column.dataIndex == 'inventory'">
            {{ record.inventory ? record.inventory : 0 }}{{ record.unitName }}
          </template>
          <template v-if="column.dataIndex == 'orderNum'">
            <a-input-number
              v-model:value="record.orderNum"
              :stringMode="true"
            />{{ record.unitName }}
          </template>
          <template v-if="column.dataIndex == 'remark'">
            <a-textarea v-model:value="record.remark" />
          </template>
        </template>
      </mw-table>
    </a-spin>
  </mw-drawer>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  defineEmits,
  reactive,
} from "vue";
import {
  checkOut,
  warehouseListOutMaterial,
  warehouseRemove,
} from "@/api/warehouse.js";
import _cloneDeep from "lodash/cloneDeep";
import _ from "lodash";
import { warehouseStatusFilters } from "@/common/constant.js";
import { list as getAllLineList } from "@/api/basicData/productionLine.js";
const { proxy } = getCurrentInstance();
import Decimal from "decimal.js";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  record: { type: Object, default: () => ({}) },
  title: {
    type: String,
    default: "物料",
  },
});

const emit = defineEmits(["update:visible", "finish"]);
const submitLoading = ref(false),
  spinning = ref(false),
  detail = ref({});
const warehouseRemoveList = reactive([]);
const detailInsertParamList = ref([]);
const allLineList = ref();
const headerRemark = ref();
const formData = ref({ lineNo: "", queryDetailStatus: 0, lineId: "" });

const pagination = ref({
  total: 0,
  current: 1,
  pageSize: 7,
  showSizeChanger: true,
  pageSizeOptions: ["7", "10", "50", "100"],
});

const columns = ref([
  {
    title: "生产线",
    dataIndex: "lineName",
  },
  {
    title: "物料编码",
    dataIndex: "materialNo",
  },
  {
    title: "物料名称",
    dataIndex: "materialName",
    key: "materialName",
    scopedSlots: { customRender: "materialName" },
  },
  {
    title: "物料规格",
    dataIndex: "specification",
    key: "specification",
    scopedSlots: { customRender: "specification" },
  },
  {
    title: "物料申请总数",
    dataIndex: "orderNumTot",
  },
  {
    title: "已出库数",
    dataIndex: "alreadyNum",
  },
  {
    title: "待出库数",
    dataIndex: "waitNum",
  },
  {
    title: "总库存",
    dataIndex: "totalInventory",
  },
  {
    title: "仓库",
    dataIndex: "successWarehouseId",
  },
  {
    title: "库位",
    dataIndex: "successWarehouseArea",
  },
  {
    title: "库位库存",
    dataIndex: "inventory",
  },
  {
    title: "本次出库",
    dataIndex: "orderNum",
  },
  {
    title: "备注",
    dataIndex: "remark",
  },
]);
// const handleTableChange = (pag, filters, sorter) => {
//   return {
//     results: pag.pageSize,
//     page: pag?.current,
//     sortField: sorter.field,
//     sortOrder: sorter.order,
//     ...filters,
//   };
// };
const handlePageChange = (page) => {
  if (!page?.current) return;
  pagination.value.current = page.current;
  pagination.value.pageSize = page.pageSize;
  pagination.value.total = page.total;
};
// 获取产线列表
async function getAllLine() {
  let result = await getAllLineList({
    ignoreCancel: true,
    onlyUnUsed: false,
  });
  let { data } = result;
  allLineList.value = [
    { lineName: "全部", id: "" },
    { lineName: "无", id: "other" },
    ...data,
  ];
}
const onDelete = async () => {
  let transferDetailIds = warehouseRemoveList.value.join(",");

  let res = await warehouseRemove({
    transferDetailIds,
  });
  if (res.code == 200) {
    proxy.$message.success(res.msg);
    onClose();
  }
};
const onSelectChange = (selectedRowKeys, selectedRows) => {
  warehouseRemoveList.value = selectedRowKeys;
  detailInsertParamList.value = selectedRows;
};
const getCheckboxProps = (record) => ({
  disabled: record.status == 1,
});
const submitForm = async () => {
  submitLoading.value = true;
  try {
    let param = {
      warehouseType: "FINISHED",
      batchId: detail.value.transferId,
      menuType: "material",
      insertParamList: detailInsertParamList.value.map((i) => {
        return {
          transferDetailId: i.id,
          materialId: i.materialId,
          headerRemark: headerRemark.value,
          innerParams: [
            {
              successWarehouseArea: i.defaultWarehouseAreaId,
              successWarehouseId: i.defaultWarehouseId,
              orderNum: i.orderNum,
              remark: i.remark,
            },
          ],
        };
      }),
    };
    let res = await checkOut(param);
    if (res.code == 200) {
      proxy.$message.success("出库成功");
      onClose();
    }
    if (res.code != 200) {
      if (res.data) {
        document.getElementById(res.data).scrollIntoView({
          behavior: "smooth",
        });
      }
    }
    submitLoading.value = false;
    emit("finish");
  } catch (error) {
    submitLoading.value = false;
    // error && error.message && proxy.$message.warning(error.message);
  }
};
const emptyObj = ref({
  orderNum: undefined,
  successWarehouseId: undefined,
  successWarehouseArea: undefined,
  areaOptions: [],
  inventory: 0,
  innerParams: [],
});

const add = (item) => {
  if (item.innerParams) {
    item.innerParams.push({
      orderNum: 0,
      successWarehouseId: undefined,
      successWarehouseArea: undefined,
      areaOptions: [],
      inventory: 0,
    });
  }
};
const onClose = () => {
  emit("update:visible", false);
  detailInsertParamList.value = [];
  emptyObj.orderNum = undefined;
  emptyObj.successWarehouseId = undefined;
  emptyObj.successWarehouseArea = undefined;
  emptyObj.areaOptions = [];
  emptyObj.inventory = 0;
  formData.value.lineId = "";
  formData.value.queryDetailStatus = 0;
  headerRemark.value = void 0;
};

const warehouseListOutMaterialList = async (val) => {
  spinning.value = true;
  submitLoading.value = true;
  warehouseRemoveList.value = [];
  let param = {
    ...{
      transferId: props.record.transferId,
      type: props.record.type,
      queryDetailStatus: val?.queryDetailStatus,
      lineId: val?.lineId,
    },
    // ...formData.value,
  };
  const res = await warehouseListOutMaterial(
    { pageNum: 1, pageSize: 1000 },
    {
      ...param,
      // lineEdgeLibraryType:
      //   props.record.lineEdgeLibraryType == 7
      //     ? "PACK_LINE_OTHERS"
      //     : props.record.lineEdgeLibraryType == 8
      //     ? "BATTERY_LINE_OTHERS"
      //     : undefined,
    }
  );
  submitLoading.value = false;
  const [row] = res?.data;
  if (row) {
    // 产品说今天先改成不过滤,明天放开,代码不要删除,暂时注释

    const data = row.insertParamList;
    row.insertParamList = data;
    pagination.value.total = row.insertParamList
      ? row.insertParamList.length
      : 0;
    if (row.insertParamList && row.insertParamList.length > 0) {
      row.insertParamList.forEach((item) => {
        item.orderNumTot = item.orderNum;
        item.orderNum = item.waitNum;
        if (item.lockWarehouseId) {
          item.defaultWarehouseId = item.lockWarehouseId;
          if (item.vo && item.vo.length > 0) {
            item.totalInventory = item.vo
              .reduce((sum, voItem) => {
                return sum.plus(new Decimal(voItem.totalInventory || 0));
              }, new Decimal(0))
              .toNumber();
            item.repositoryOptions = item.vo;
            let voList = item.vo.filter(
              (ite) => ite.warehouseId == item.lockWarehouseId
            )[0];
            item.areaOptions = voList?.warehouseMenuDetailVoList.map(
              (ite, index) => {
                item.inventory =
                  voList?.warehouseMenuDetailVoList[0]?.inventory;
                ite.warehouseAreaId = String(ite.warehouseAreaId);
                return ite;
              }
            );
            item.defaultWarehouseAreaId =
              item.areaOptions?.length > 0
                ? item.areaOptions[0]?.warehouseAreaId
                : "";
          } else {
            item.repositoryOptions = [
              {
                warehouseId: String(item.lockWarehouseId),
                warehouseName: item.lockWarehouseName,
                // totalInventory: undefined,
                // sort: undefined,
                materialName: item.materialName,
                isSendWarehouse: item.isSendWarehouse,
              },
            ];
            item.defaultWarehouseAreaId = "";
          }
        } else {
          item.repositoryOptions = item.vo;
          if (item.vo && item.vo.length > 0) {
            // item.totalInventory = item.vo.reduce((sum, item) => sum + (Number(item.totalInventory) || 0), 0);
            item.totalInventory = item.vo
              .reduce((sum, voItem) => {
                return sum.plus(new Decimal(voItem.totalInventory || 0));
              }, new Decimal(0))
              .toNumber();
            item.defaultWarehouseId = item.vo[0].warehouseId;
            let voList = item.vo.filter(
              (ite) => ite.warehouseId == item.defaultWarehouseId
            )[0];
            item.areaOptions = voList?.warehouseMenuDetailVoList.map(
              (ite, index) => {
                item.inventory =
                  voList?.warehouseMenuDetailVoList[0]?.inventory;
                ite.warehouseAreaId = String(ite.warehouseAreaId);
                return ite;
              }
            );
            item.defaultWarehouseAreaId =
              item.areaOptions?.length > 0
                ? item.areaOptions[0]?.warehouseAreaId
                : "";
          } else {
            if (item.defaultWarehouseId) {
              item.repositoryOptions = [
                {
                  warehouseId: String(item.defaultWarehouseId),
                  warehouseName: item.defaultWarehouseName,
                  // totalInventory: undefined,
                  // sort: undefined,
                  materialName: item.materialName,
                  isSendWarehouse: item.isSendWarehouse,
                },
              ];
            } else {
            }
            item.defaultWarehouseAreaId = "";
          }
        }

        // if (item.vo && item.vo.length > 0) {
        // 	// 累加总库存
        // 	item.totalInventory = item.vo.reduce((sum, item) => sum + (Number(item.totalInventory) || 0), 0);
        // 	// 嘿嘿嘿
        // 	if (item.lock)
        // 		item.areaOptions = item?.vo[0]?.warehouseMenuDetailVoList.map((ite, index) => {
        // 			item.inventory = item?.vo[0]?.warehouseMenuDetailVoList[0]?.inventory;
        // 			ite.warehouseAreaId = String(ite.warehouseAreaId);
        // 			return ite;
        // 		});
        // 	item.defaultWarehouseId = "";
        // 	item.defaultWarehouseAreaId = "";

        // 	if (item.areaOptions?.length > 0) {
        // 		item.defaultWarehouseId = item.repositoryOptions[0].warehouseId;
        // 		item.defaultWarehouseAreaId = item.areaOptions[0].warehouseAreaId;
        // 	} else {
        // 		item.defaultWarehouseId = "";
        // 		item.defaultWarehouseAreaId = "";
        // 	}
        // } else {
        // 	item.defaultWarehouseId = void 0;
        // 	item.defaultWarehouseAreaId = void 0;
        // }
      });
    }

    detail.value = row;
    spinning.value = false;
    spinning.value = false;
    submitLoading.value = false;
  }
  submitLoading.value = false;
  spinning.value = false;
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      try {
        headerRemark.value = props.record.headerRemark;
        getAllLine();
        warehouseListOutMaterialList({ queryDetailStatus: 0, lineId: "" });
      } catch (error) {
        spinning.value = false;
        submitLoading.value = false;
      }
    }
  }
);

const onChange = (e, val, itemVal) => {
  detail.value.insertParamList.map((item) => {
    if (item.id == itemVal.id) {
      // item.orderNum = e;
      let num = 0;
      item.innerParams.forEach((ite, ind) => {
        // ite.orderNum = e;
        num += ite?.orderNum || 0;
      });
      item.orderNum = num;
    }
  });
};
const onDetail = (val) => {
  detail.value.insertParamList.forEach((item, index) => {
    if (val.id == item.id) {
      detail.value.insertParamList.splice(index, 1);
    }
  });
};
</script>
<style lang="less" scoped>
.bg {
  height: 40px;
  line-height: 40px;
  background: rgba(34, 34, 34, 0.08);
  border-radius: 8px;
  text-align: center;
  margin-bottom: 24px;
}

.fle {
  display: flex;
  justify-content: space-between;
}
</style>
