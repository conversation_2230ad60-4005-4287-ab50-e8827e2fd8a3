<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    destroyOnClose="true"
    width="30%"
    :customTitle="
      btnType == 'add'
        ? '新增盘点'
        : btnType == 'edit'
        ? '编辑盘点'
        : '盘点详情'
    "
  >
    <template #header>
      <mw-button
        v-if="btnType == 'add' || btnType == 'edit'"
        @click="formSubmit"
        :loading="loading"
        >确定</mw-button
      >
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      labelAlign="left"
    >
      <!-- <a-form-item label="盘点类型" name="type" required>
        <a-select
          :options="mateOrProdType.filter((item) => item.value !== '')"
          v-model:value="formData.type"
          optionFilterProp="name"
          :placeholder="'盘点类型'"
          show-search
          allowClear
          @change="changeMateOrProd"
          :disabled="props.btnType == 'detail'"
        ></a-select>
      </a-form-item> -->
      <a-form-item label="盘点货物" name="materialId" required>
        <a-select
          :options="optionsRawMaterial"
          v-model:value="formData.materialId"
          :optionFilterProp="'materialNames'"
          @change="handleChange"
          @search="handleSearch"
          :field-names="{
            label: 'materialNames',
            value: 'id',
          }"
          :placeholder="'盘点货物'"
          show-search
          allowClear
          :disabled="props.btnType == 'detail'"
        ></a-select>
      </a-form-item>
      <a-form-item label="选择仓库" name="warehouseId" required>
        <a-select
          :options="optionsWarehouseList"
          v-model:value="formData.warehouseId"
          optionFilterProp="warehouseName"
          @change="handleChangeWarehouse"
          :field-names="{
            label: 'warehouseName',
            value: 'id',
          }"
          :placeholder="'选择仓库'"
          show-search
          allowClear
          :disabled="props.btnType == 'detail'"
        ></a-select>
      </a-form-item>
      <a-form-item label="选择库位" name="warehouseAreaId" required>
        <a-select
          :options="optionsRegionList"
          v-model:value="formData.warehouseAreaId"
          optionFilterProp="warehouseArea"
          :field-names="{
            label: 'warehouseArea',
            value: 'id',
          }"
          :placeholder="'选择库位'"
          show-search
          allowClear
          :disabled="props.btnType == 'detail'"
          @change="handleChangeRegion"
        ></a-select>
      </a-form-item>
      <a-form-item label="最近盘点时间" required>
        {{ formData.inventoryTime || "-" }}</a-form-item
      >
      <a-form-item label="实际库存" required>
        <!--  {{ formData.availableStock }} {{ formData.unitName }} -->
        <span v-if="formData.warehouseAreaId">{{
          countAlgorithm(formData.oldNum)
        }}</span>
        <span v-if="!formData.warehouseAreaId">{{ "--" }}</span>
        {{ formData.unitName }}
        <!-- <a-input-number v-model:value="formData.oldnum"  placeholder="盘点数量"></a-input-number> -->
      </a-form-item>
      <a-form-item label="盘点数量" name="num" required>
        <a-input-number
          class="w-4/5"
          v-model:value="formData.num"
          placeholder="盘点数量"
          :disabled="props.btnType == 'detail'"
          :stringMode="true"
          @change="
            {
              formData.num = countAlgorithm(
                roundNumFunNoHalfAdjust(formData.num, 8)
              );
            }
          "
        ></a-input-number>
        <span class="ml-3 mt-10">{{ formData.unitName }}</span>
      </a-form-item>
      <a-form-item
        label="盘亏："
        v-if="inventorySurplusNum == 0"
        name="inventorySurplus"
        required
      >
        <span>
          {{
            countAlgorithm(
              roundNumFunNoHalfAdjust(
                Number(formData.num) - Number(formData.oldNum) < 0
                  ? Math.abs(Number(formData.num) - Number(formData.oldNum))
                  : Number(formData.num) - Number(formData.oldNum),
                8
              )
            )
          }}
          <span class="ml-3 mt-10">{{ formData.unitName }}</span></span
        >
      </a-form-item>
      <a-form-item
        label="盘盈："
        v-if="inventorySurplusNum == 1"
        name="inventorySurplus"
        required
      >
        <span
          >{{
            countAlgorithm(
              roundNumFunNoHalfAdjust(formData.num - formData.oldNum, 8)
            )
          }}
          <span class="ml-3 mt-10">{{ formData.unitName }}</span></span
        >
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea
          :rows="6"
          v-model:value="formData.remark"
          placeholder="备注"
          :disabled="props.btnType == 'detail'"
        ></a-textarea
      ></a-form-item>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
  defineProps,
  defineEmits,
  watch,
} from "vue";

import { roundNumFunNoHalfAdjust } from "@/common/validate.js";
import { mateOrProdType } from "@/common/constant.js";
import { AllPage } from "@/api/basicData/material.js";
import { list } from "@/api/basicData/product.js";
import { inventoryAdd } from "@/api/inventoryCount.js";
import { menuListCollect } from "@/api/inventoryAllocation.js";
import { getInfo as getInfoRepository } from "@/api/basicData/repository.js";
import { selectMenuListIgnoreNoStock } from "@/api/basicData/repository.js";
import { page as pageList } from "@/api/warehouse.js";
import { debounce } from "lodash";

const emit = defineEmits(["update:visible", "finish"]);
const { proxy } = getCurrentInstance();
const formRef = ref();
const loading = ref(false);
const optionsRawMaterial = ref();
const optionsWarehouseList = ref();
const optionsRegionList = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
  btnType: String,
  recordVal: Object,
});
const formData = reactive({
  type: "material",
});
const inventorySurplusNum = computed(() => {
  if (formData.num < formData.oldNum) {
    formData.inventorySurplus = 0;
  } else if (formData.num >= formData.oldNum) {
    formData.inventorySurplus = 1;
  } else {
    formData.inventorySurplus = undefined;
  }

  return formData.inventorySurplus;
});
// 科学计数法
const countAlgorithm = (num) => {
  let result = String(num);
  if (result.indexOf("-") >= 0 && !result.includes("e")) {
    return result;
  }
  if (result.indexOf("-") >= 0) {
    result = "0" + String(Number(result) + 1).substr(1);
  }
  return result;
};

// 物料/成品列表接口
const page = async (keyword) => {
  const { data } = await AllPage({ ignoreStop: true, keyword });
  data.forEach((item) => {
    item.materialNames = `${item.materialNo}/${item.materialName}/${item.specification}`;
  });
  optionsRawMaterial.value = data;
};
const debouncedSearch = debounce(async (e) => {
  console.log("[ e ] >", e);
  await page(e);
  //
}, 300);
const handleSearch = async (e) => {
  debouncedSearch(e);
};
// 盘点类型
const changeMateOrProd = () => {
  optionsRawMaterial.value = [];
  formData.materialId = undefined;
  formData.warehouseId = undefined;
  formData.warehouseAreaId = undefined;
  page();
};

// 库存
const MenuListIgnoreNoStockList = async (val, warehouseId) => {
  let res = await menuListCollect(
    {
      warehouseType: formData.type == "material" ? "RAWMATERIAL" : "FINISHED",
    },
    { pageSize: 999999 }
  );
  optionsWarehouseList.value = res.data;
};
// 选择物料
const handleChange = (e, val) => {
  optionsWarehouseList.value = [];
  formData.warehouseId = void 0;
  optionsRegionList.value = [];
  formData.warehouseAreaId = void 0;
  MenuListIgnoreNoStockList(e);
  // formData.availableStock = val.availableStock;
  formData.unitName = val.unitName || val.unit;
  formData.inventoryTime = void 0;
};
// 选择仓库区域
const handleChangeWarehouse = async (e) => {
  formData.warehouseAreaId = void 0;
  formData.inventoryTime = void 0;
  if (e) {
    let res = await getInfoRepository(e);
    optionsRegionList.value = res.data.warehouseMenuDetailVoList;
  }
};
// 关闭弹窗
function onClose() {
  formData.inventoryTime = void 0;
  formData.remark = void 0;
  props.btnType = "";
  optionsRegionList.value = [];
  optionsWarehouseList.value = [];
  optionsRawMaterial.value = [];
  mateOrProdType.value = "";
  emit("update:visible", false);
  formRef.value.resetFields();
}
// 提交
const formSubmit = async () => {
  formRef.value.validate().then(async () => {
    // formData.oldNum = Number(formData.oldNum);
    // delete formData.unitName;
    try {
      if (formData.num == formData.oldNum) {
        return proxy.$message.error("库存未变更，无需盘点");
      }
      loading.value = true;
      let res = await inventoryAdd(formData);
      loading.value = false;
      if (res.code == 200) {
        onClose();
        emit("finish");
      }
    } catch (error) {
      loading.value = false;
    }
  });
};
const pageLists = async () => {
  let res = await pageList(
    { pageNum: 1, pageSize: 9999 },
    {
      classifyType: formData.type,
      warehouseArea: formData.warehouseAreaId,
      warehouseId: formData.warehouseId,
      materialIdOrProductId: formData.materialId,
    }
  );
  console.log(res, "res");
  formData.oldNum = res.data.length > 0 ? res.data[0]?.inventory : 0;
  if (res.data && res.data.length > 0) {
    formData.inventoryTime = res.data[0]?.inventoryTime;
  } else {
    formData.inventoryTime = void 0;
  }
};
const filterOption = (input, option) => {
  return option.materialName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const handleChangeRegion = () => {
  pageLists();
};

watch(
  () => props.visible,
  async (val) => {
    if (props.btnType == "detail") {
      // await page(props.recordVal.type);
      await page();
    } else {
      // changeMateOrProd("material");
      changeMateOrProd();
    }

    if (props.btnType == "detail" && props.recordVal) {
      console.log(props, "props");

      formData.oldNum = props.recordVal.oldNum;
      formData.value = props.recordVal.value;
      formData.type = props.recordVal.type;
      formData.num = props.recordVal.num;

      formData.remark = props.recordVal.remark;
      formData.materialId = String(props.recordVal.materialId);
      formData.warehouseId = props.recordVal.warehouseId;
      await MenuListIgnoreNoStockList(formData.type);
      await handleChangeWarehouse(formData.warehouseId);
      formData.warehouseAreaId = String(props.recordVal.warehouseAreaId);
      formData.inventoryTime = props.recordVal.inventoryTime;
      // pageLists();
    } else {
      optionsRawMaterial.value = [];
      optionsRegionList.value = [];
      optionsWarehouseList.value = [];
      mateOrProdType.value = "";
      formData.value = undefined;
      // formData.type = undefined;
      formData.num = undefined;
      formData.materialId = undefined;
      formData.warehouseAreaId = undefined;
      formData.warehouseId = undefined;
    }
  }
);
</script>

<style lang="scss" scoped></style>
