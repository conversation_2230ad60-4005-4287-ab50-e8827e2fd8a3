<template>
  <div class="mb-4 text-right">
    <search :searchData="searchData" @search="refresh">
      <mw-button @click="onOpen('add')" :font="'iconfont icon-xianxing-121'"
        >新增</mw-button
      >
    </search>
  </div>
  <mw-table
    :columns="columns"
    :data-source="dataList"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    :scroll="{ x: 'max-content' }"
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'type'">
        {{ record.type == "material" ? "物料" : "成品" }}
      </template>
      <template v-if="column.key == 'warehouseName'">
        {{ record.warehouseName }}
        <span v-if="record.warehouseName && record.warehouseAreaName">/</span>
        {{ record.warehouseAreaName }}
      </template>
      <template v-if="column.key == 'inventorySurplusNum'">
        <span v-if="record.oldNum > record.num"
          >盘亏：{{
            roundNumFun(Math.abs(record.oldNum - record.num), 8)
          }}</span
        >
        <span v-if="record.oldNum <= record.num"
          >盘盈：{{
            roundNumFun(Math.abs(record.oldNum - record.num), 8)
          }}</span
        >
      </template>

      <template v-if="column.key == 'operation'">
        <mw-button @click="onOpen('detail', record)">详情</mw-button>
      </template>
    </template>
  </mw-table>
  <inventory-count-drawer
    ref="refInventoryCountDrawer"
    @finish="getList"
    v-model:visible="visibleInventoryCountDrawer"
    :btnType="btnType"
    :recordVal="recordVal"
  ></inventory-count-drawer>
</template>
<script setup>
import { formatter8 } from "@/common/validate.js";
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import Search from "@/components/search/index.vue";
import { inventoryPage } from "@/api/inventoryCount.js";
import { mateOrProdType, calculateType } from "@/common/constant.js";
import { usePagenation } from "@/common/setup";
import inventoryCountDrawer from "./inventoryCountDrawer.vue";
import { roundNumFun } from "@/common/validate.js";

const { proxy } = getCurrentInstance();
const loading = ref(false);
const btnType = ref();
const dataList = ref();
const recordVal = ref();
const visibleInventoryCountDrawer = ref(false);

const searchData = ref({
  fields: {
    // type: {
    //   name: "类型",
    //   type: "a-select",
    //   options: mateOrProdType,
    //   placeholder: "选择分类",
    //   fieldNames: {
    //     label: "label",
    //     value: "value",
    //   },
    //   value: "material",
    // },
    inventorySurplus: {
      name: "类型",
      type: "a-select",
      options: calculateType,
      placeholder: "选择分类",
      fieldNames: {
        label: "label",
        value: "value",
      },
      width: "150px",
      value: "",
      allowClear: true,
    },

    keyword: {
      type: "a-input-search",
      placeholder: "产品/物料编码/名称/规格/盘点单号",
      width: "300px",
      allowClear: true,
    },
  },
});
const columns = ref([
  {
    title: "盘点单号",
    dataIndex: "uniqueNo",
    key: "uniqueNo",
  },
  // {
  //   title: "类型",
  //   dataIndex: "type",
  //   key: "type",
  // },
  {
    title: "编码",
    dataIndex: "materialOrProNo",
    key: "materialOrProNo",
  },
  {
    title: "名称",
    dataIndex: "materialOrProductName",
    key: "materialOrProductName",
  },
  {
    title: "规格",
    dataIndex: "specification",
    key: "specification",
  },
  {
    title: "实际数量",
    dataIndex: "oldNum",
    key: "num",
  },
  {
    title: "盘点数量",
    dataIndex: "num",
    key: "num",
  },
  {
    title: "结果",
    dataIndex: "inventorySurplusNum",
    key: "inventorySurplusNum",
  },
  // {
  //   title: "仓库/仓位",
  //   dataIndex: "materialOrProductName",
  //   key: "materialOrProductName",
  // },

  // {
  //   title: "备注",
  //   dataIndex: "remark",
  //   key: "remark",
  // },
  {
    title: "仓库/仓位",
    dataIndex: "warehouseName",
    key: "warehouseName",
  },
  {
    title: "操作人",
    dataIndex: "createBy",
    key: "createBy",
  },

  {
    title: "操作",
    dataIndex: "operation",
    key: "operation",
    fixed: "right",
  },
]);
onBeforeMount(async () => {
  await getList();
});
const getList = async () => {
  loading.value = true;
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let result = await inventoryPage({
    ...pageParam.value,
    ...searchParam,
    type: "material",
  });
  loading.value = false;
  dataList.value = result.data;
  paginationProps.value.total = result.total;
};

const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

const onOpen = (btnTypeVal, record) => {
  btnType.value = btnTypeVal;
  recordVal.value = record;
  visibleInventoryCountDrawer.value = true;
};
</script>

<style lang="scss" scoped></style>
