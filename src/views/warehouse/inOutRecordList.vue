<template>
  <search :searchData="searchData" @search="refresh">
    <!-- <a-dropdown> -->
    <mw-button @click="exportOrg()" :loading="exportAllLoading">导出</mw-button>
  </search>
  <mw-table
    :align="center"
    :columns="columns.fatColumns"
    :data-source="data"
    class="tables"
    :rowKey="(id) => id"
    childrenColumnName="record.settlementInfoVOList"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
    ellipsis="true"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'status'">
        <dictionary
          :statusOptions="settlementStatus"
          :value="record.status"
          isBackgroundColor
          :statusExtend="record.statusExtend"
        />
      </template>
      <template v-if="column.key == 'type'">
        <div class="">
          <dictionary
            :showBadge="false"
            :statusOptions="outOrIn"
            :value="record.type"
          />
        </div>
      </template>
      <template v-if="column.key == 'warehouseType'">
        <dictionary
          :showBadge="false"
          :statusOptions="warehouseType"
          :value="record.warehouseType"
        />
      </template>
      <template v-if="column.key == 'button'">
        <mw-button
          @click="exportOrg('pdf', record)"
          :loading="record.exportLoading"
          class="mr-2"
          >导出PDF</mw-button
        >
        <mw-button @click="onInOutRecordPrint(record)">打印</mw-button>
      </template>
    </template>
    <template #expandedRowRender="{ record, index }">
      <mw-table
        :columns="columns.childrenColumns"
        :data-source="record.list"
        :pagination="pagination"
        ellipsis="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'type'">
            <div class="">
              <dictionary
                :showBadge="false"
                :statusOptions="warehouseType"
                :value="record.warehouseType"
              />
            </div>
          </template>

          <template v-if="column.key == 'parentAssociation'">
            <div class="">{{ data[index]?.parentAssociation }}</div>
          </template>
          <template v-if="column.key == 'quantity'">
            <div class="">{{ record.quantity }} {{ record.unit }}</div>
          </template>
          <template v-if="column.key == 'remark'">
            <!-- <a-popover
              style="width: 500px"
              title="Hover title"
              trigger="hover"
              :open="hovered"
            >
              <template #content>
                {{ record.remark || "-" }}
              </template>
</a-popover> -->
            <a-popover
              arrow-point-at-center
              width="100px"
              title="Hover title"
              trigger="hover"
              :open="hovered"
              placement="top"
            >
              <template #content>
                {{ record.remark || "-" }}
              </template>
            </a-popover>
          </template>
        </template>
      </mw-table>
    </template>
  </mw-table>
  <in-out-check-drawer
    v-model:visible="inOutCheckVisible"
    :type="type"
    :in-out-type="inOutType"
    :name="title"
    @finish="getList"
  />

  <a-modal v-model:visible="detailsVisible" title="进出库详情单" @ok="handleOk">
    <img :src="detailsUrlPath" alt="" />
  </a-modal>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
} from "vue";
import {
  recordListV1,
  exportWarehouseExport,
  exportWarehousePdf,
  getWarehouseTypeList,
} from "@/api/warehouse.js";
import { usePagenation } from "@/common/setup";
import Search from "@/components/search/index.vue";
import {
  warehouseType,
  warehouseTypeFilters,
  outOrIn,
} from "@/common/constant.js";
import { exportExecl } from "@/utils/util.js";
import { useRoute } from "vue-router";
import { getAllUser } from "@/api/system/user.js";
import inOutCheckDrawer from "./inOutCheckDrawer.vue";
import { transferExportPdf } from "@/api/warehouse.js";
import { formattedToday } from "@/common/validate.js";
import printJS from "print-js";

const route = useRoute();
const type = computed(() => {
  return route.query.type || "material";
});
const title = computed(() => {
  return type.value == "product" ? "成品" : "物料";
});

const columns = ref({
  fatColumns: [
    {
      title: "批次号",
      dataIndex: "batchId",
      key: "batchId",
    },
    {
      title: "创建人",
      dataIndex: "createBy",
      key: "createBy",
    },
    {
      title: "出入库",
      dataIndex: "type",
      key: "type",
    },
    {
      title: "出入库类别",
      dataIndex: "warehouseType",
      key: "warehouseType",
    },
    {
      title: "出入库时间",
      dataIndex: "createTime",
      key: "createTime",
    },

    {
      title: "操作",
      dataIndex: "button",
      key: "button",
    },
  ],
  childrenColumns: [
    {
      title: "序号",
      dataIndex: "index",
      width: 80,
    },
    {
      title: "关联编码",
      dataIndex: "association",
      width: 200,
    },
    {
      title: "父关联编码",
      dataIndex: "parentAssociation",
      key: "parentAssociation",
      scopedSlots: { customRender: "parentAssociation" },
      width: 200,
    },
    {
      title: "仓库名称",
      dataIndex: "warehouseName",
    },
    {
      title: "材料名称",
      dataIndex: "keyName",
    },
    {
      title: "材料编号",
      dataIndex: "keyNo",
    },
    {
      title: "规格型号",
      dataIndex: "specification",
    },

    {
      title: "数量",
      dataIndex: "quantity",
      key: "quantity",
      align: "left",
      scopedSlots: { customRender: "quantity" },
    },
    {
      title: "备注",
      dataIndex: "remark",
      ellipsis: true,
      scopedSlots: { customRender: "remark" },
    },
  ],
});

const data = ref([]),
  loading = ref(false),
  detailsUrlPath = ref(),
  detailsVisible = ref(false),
  exportAllLoading = ref(false),
  inOutType = ref("in");
const warehouseTypeList = ref([]);

const searchData = reactive({
  fields: {
    rangeDate: {
      type: "a-range-picker",
      valueFormat: "YYYY-MM-DD",
      value: [],
      width: "240px",
      allowClear: true,
    },
    // warehouseTypeFilters.filter(
    // (t) => t.type == type.value || t.value == ""
    // ),
    warehouseType: {
      name: "仓库类型",
      type: "a-select",
      options: warehouseTypeList,
      placeholder: "选择进出库类型",
      width: "160px",
      allowClear: true,
      fieldNames: {
        label: "name",
        value: "code",
      },
    },
    type: {
      name: "出入库类型",
      type: "a-select",
      options: outOrIn,
      placeholder: "全部出入库",
      value: "",
      width: "140px",
      allowClear: true,
    },

    operator: {
      name: "操作员",
      type: "a-select",
      options: [],
      placeholder: "选择操作员",
      width: "140px",
      fieldNames: {
        label: "nickName",
        value: "userId",
      },
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: `仓库名/${title.value}编码/名称/关联计划编码`,
      width: "280px",
      allowClear: true,
    },
  },
});
const getList = async () => {
  loading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await recordListV1(pageParam.value, {
    ...searchParam,
    // classifyType: type.value,
  });
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
const getUserList = async () => {
  let result = await getAllUser();
  searchData.fields.operator.options = result.data;
};
const warehouseTypeListData = async () => {
  let res = await getWarehouseTypeList();
  warehouseTypeList.value = res.data.typeList;
};
onBeforeMount(async () => {
  getUserList();
  warehouseTypeListData();
  await getList(route.query.materialId);
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

// 进出库详情单详情
// const urlPathDetails = (val) => {
//   detailsVisible.value = true;
//   detailsUrlPath.value = val;
// };
// 关闭进出库详情单
const handleOk = () => {
  detailsVisible.value = false;
};
const exportOrg = async (val, record) => {
  console.log(val, "val");

  pageParam.value.pageSize = 100000;
  let searchParam = {
    classifyType: "material",
  };
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;
  }
  //单个搜索信息
  if (searchData.filterData) {
    searchParam[searchData.filterData.value.filterBy] =
      searchData.filterData.value.filterValue;
  }
  if (record) {
    searchParam.batchId = record.batchId;
  }

  if (val == "pdf") {
    if (searchParam.keyword) {
      delete searchParam.keyword;
    }
  }
  if (!val) {
    exportAllLoading.value = true;
  } else {
    record.exportLoading = true;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result =
    val == "pdf"
      ? await exportWarehousePdf(searchParam, pageParam.value)
      : await exportWarehouseExport(searchParam, pageParam.value);

  const fileName =
    val == "pdf"
      ? type.value == "product"
        ? "成品进出库.pdf"
        : "物料进出库.pdf"
      : val == "product"
      ? "产品进出库"
      : val == "material"
      ? "物料进出库"
      : "出入库导出_" + formattedToday + ".xlsx";
  exportExecl(fileName, result);

  if (!val) {
    exportAllLoading.value = false;
  } else {
    record.exportLoading = false;
  }
};
const onInOutRecordPrint = async (record) => {
  // 假设 transferExportPdf 是一个返回 Promise 的函数
  let result = await transferExportPdf({ batchId: record.batchId });
  const blob = new Blob([result], {
    type: result.type,
  });
  let dom = document.createElement("a");
  let url = window.URL.createObjectURL(blob);
  dom.href = url;
  printJS({
    printable: dom,
    type: "pdf",
    landscape: true, // 设置横向打印，如果设置为true，则内容将横向打印。如果设置为false或未设置，则内容将纵向打印。
  });
};
</script>

<style lang="less" scoped>
:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  // background: transparent;
}

:deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
  // background: transparent;
}

:deep(.ant-table-cell) {
  vertical-align: top;
}

// :deep(.mw-table[data-v-57a55008] .ant-table) {
//   overflow-x: auto;
// }
</style>
