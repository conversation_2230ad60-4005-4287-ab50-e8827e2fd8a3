<template>
  <div class="transferForm">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      labelAlign="left"
    >
      <a-form-item label="调拨货物" name="materialId">
        <a-select
          show-search
          optionFilterProp="label"
          :options="productList"
          placeholder="请选择调拨货物"
          v-model:value="formData.materialId"
          @change="materialChange"
          @search="handleSearch"
          :disabled="
            ['detail', 'edit'].includes(props.titleName) ? true : false
          "
        />
      </a-form-item>
      <div class="mb-4">调出</div>
      <a-form-item
        label="outWarehouseName"
        name="outWarehouseName"
        style="display: none"
      >
        <a-input v-model:value="formData.outWarehouseName" />
      </a-form-item>
      <a-form-item label="调出仓库" name="outWarehouseId">
        <a-select
          :options="outWarehouseOption"
          :fieldNames="{ label: 'warehouseName', value: 'warehouseId' }"
          placeholder="请选择调出仓库"
          v-model:value="formData.outWarehouseId"
          @change="outWarehousChange"
          :disabled="props.titleName == 'detail' ? true : false"
        />
      </a-form-item>
      <a-form-item
        label="outWarehouseAreaName"
        name="outWarehouseAreaName"
        style="display: none"
      >
        <a-input v-model:value="formData.outWarehouseAreaName" />
      </a-form-item>
      <a-form-item label="调出库位" name="outWarehouseAreaId">
        <span v-if="props.titleName == 'detail'">
          <a-input
            v-model:value="formData.outWarehouseAreaName"
            disabled
          ></a-input>
        </span>
        <a-select
          v-else
          :options="outWarehouseAreaOption"
          :fieldNames="{ label: 'warehouseArea', value: 'warehouseAreaId' }"
          placeholder="请选择调出库位"
          v-model:value="formData.outWarehouseAreaId"
          @change="outWarehouseAreaChange"
          :disabled="props.titleName == 'detail' ? true : false"
        />
      </a-form-item>
      <div class="mb-4">调出仓实际库存：{{ outPhysicalInventory }}</div>
      <a-form-item label="调出数量" name="inventory">
        <a-input-number
          class="w-full"
          v-model:value="formData.inventory"
          :parser="(value) => value.match(/^[0-9]*\.([0-9]{0,8})|^[0-9]*/)[0]"
          :disabled="
            ['detail', 'edit'].includes(props.titleName) ? true : false
          "
          :stringMode="true"
        /><span>{{ unit }}</span>
      </a-form-item>
      <div class="mb-4">调入</div>
      <a-form-item
        label="inWarehouseName"
        name="inWarehouseName"
        style="display: none"
      >
        <a-input v-model:value="formData.inWarehouseName" />
      </a-form-item>
      <a-form-item label="调入仓库" name="inWarehouseId">
        <a-select
          :options="inWarehouseOption"
          placeholder="请选择调入仓库"
          v-model:value="formData.inWarehouseId"
          @change="inWarehouseChange"
          :disabled="props.titleName == 'detail' ? true : false"
        />
      </a-form-item>
      <a-form-item
        label="inWarehouseAreaName"
        name="inWarehouseAreaName"
        style="display: none"
      >
        <a-input v-model:value="formData.inWarehouseAreaName" />
      </a-form-item>
      <a-form-item label="调入库位" name="inWarehouseAreaId">
        <a-select
          :options="inWarehouseAreaOption"
          placeholder="请选择调入库位"
          v-model:value="formData.inWarehouseAreaId"
          :fieldNames="{ label: 'warehouseArea', value: 'id' }"
          @change="inWarehouseAreaChange"
          :disabled="props.titleName == 'detail' ? true : false"
        />
      </a-form-item>
      <!-- <div>调入仓实际库存：{{ inPhysicalInventory }}</div> -->
      <a-form-item label="" name="materialOrProductName" style="display: none">
        <a-input v-model:value="formData.materialOrProductName" />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea
          :rows="5"
          v-model:value="formData.remark"
          placeholder="备注"
          :disabled="props.titleName == 'detail' ? true : false"
        ></a-textarea
      ></a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { reactive, ref, defineExpose, defineProps, computed } from "vue";
import { list } from "@/api/basicData/product";
import { AllList, AllPage, getInfoRepository } from "@/api/basicData/material";
import { menuListCollect } from "@/api/inventoryAllocation";
import { selectMenuListIgnoreNoStock } from "@/api/basicData/repository";
import { page } from "@/api/warehouse";
import { debounce } from "lodash";
const formData = reactive({
  type: "material",
  materialId: void 0,
  outWarehouseId: void 0,
  outWarehouseName: void 0,
  outWarehouseAreaId: void 0,
  outWarehouseAreaName: void 0,
  inventory: 0,
  inWarehouseAreaId: void 0,
  inWarehouseAreaName: void 0,
  inWarehouseId: void 0,
  inWarehouseName: void 0,
  materialOrProductName: void 0,
});
//
const props = defineProps({
  visibleRecord: {
    type: Object,
    default: () => {},
  },
  titleName: {
    type: String,
    default: "",
  },
});
const unit = ref(null);

const formRef = ref(null);

const outPhysicalInventory = ref("-");

const inPhysicalInventory = ref("-");

const rules = {
  materialId: [
    { required: true, message: "请选择调拨货物", trigger: "change" },
  ],
  inventory: [{ required: true, message: "调出数量", trigger: "blur" }],
  outWarehouseId: [
    { required: true, message: "请选择调出仓库", trigger: "change" },
  ],
  outWarehouseAreaId: [
    { required: true, message: "请选择调出库位", trigger: "change" },
  ],
  inWarehouseId: [
    { required: true, message: "请选择调入仓库", trigger: "change" },
  ],
  inWarehouseAreaId: [
    { required: true, message: "请选择调入库位", trigger: "change" },
  ],
  materialOrProductName: [
    { required: true, message: "请选择调入库位", trigger: "blur" },
  ],
};

const productList = ref([]);

const outWarehouseOption = ref([]);

const inWarehouseOption = ref([]);

const outWarehouseAreaOption = ref([]);

const inWarehouseAreaOption = ref([]);

const max = ref(999999999999);

const getMaterialList = async (keyword) => {
  const { code, data } = await AllPage({
    ignoreStop: true,
    keyword,
    pageSize: 999999,
    materialIds: props.visibleRecord?.materialId
      ? [props.visibleRecord.materialId]
      : undefined,
  });
  if (code === 200) {
    productList.value = data
      ?.filter((item) => item.status == 0 || item.status == 1)
      ?.map((item) => {
        return {
          ...item,
          label:
            item.materialNo +
            "/" +
            item.materialName +
            "/" +
            item.specification,
          value: item.id,
        };
      });
  }
};

const debouncedSearch = debounce(async (e) => {
  console.log("[ e ] >", e);
  await getMaterialList(e);
  //
}, 300);
const handleSearch = async (e) => {
  debouncedSearch(e);
};

const allClear = () => {
  productList.value = [];
  formData.materialId = void 0;
  formData.outWarehouseId = void 0;
  formData.outWarehouseAreaId = void 0;
  formData.inWarehouseAreaId = void 0;
  formData.inWarehouseId = void 0;
  outWarehouseAreaOption.value = [];
  outWarehouseAreaOption.value = [];
  inWarehouseOption.value = [];
  inWarehouseAreaOption.value = [];
  // inPhysicalInventory.value = "-";
  outPhysicalInventory.value = "-";
  max.value = 999999999999;
  formData.outWarehouseName = void 0;
  formData.inWarehouseName = void 0;
  formData.outWarehouseAreaName = void 0;
  formData.inWarehouseAreaName = void 0;
};

const typeChange = async () => {
  allClear();
  await getMaterialList();
  if (props.visibleRecord) {
    console.log("[ props.visibleRecord ] >", props.visibleRecord);
    formData.materialId = String(props.visibleRecord.materialId);
    const params = {
      materialId: props.visibleRecord.materialId,
      warehouseType: "",
      classifyType: "material",
    };
    getSelectMenuListIgnoreNoStock(params, props.visibleRecord.outWarehouseId);
    formData.outWarehouseId = props.visibleRecord.outWarehouseId
      ? String(props.visibleRecord.outWarehouseId)
      : "";

    formData.outWarehouseAreaId = props.visibleRecord.outWarehouseAreaId
      ? String(props.visibleRecord.outWarehouseAreaId)
      : "";
    formData.outWarehouseAreaName = props.visibleRecord.outWarehouseAreaName;

    outPhysicalInventory.value = props.visibleRecord.outInventory;
    formData.inventory = String(props.visibleRecord.inventory);
    formData.inWarehouseId = props.visibleRecord.inWarehouseId || void 0;
    formData.inWarehouseId && inWarehouseChange(formData.inWarehouseId);
    formData.inWarehouseAreaId = props.visibleRecord.inWarehouseAreaId
      ? String(props.visibleRecord.inWarehouseAreaId)
      : void 0;

    formData.unit = String(props.visibleRecord.unit);
    // inPhysicalInventory.value = props.visibleRecord.inInventory;
    formData.remark = props.visibleRecord.remark;
  }
  await menuListCollectList("");
};

//获取调入库仓库 获取所有
const menuListCollectList = async (type) => {
  //FINISHED 产品
  //RAWMATERIAL 物料
  inWarehouseOption.value = [];
  let warehouseType =
    selectedMaterial.value?.lineEdgeLibraryType == 7
      ? "PACK_LINE_OTHERS"
      : selectedMaterial.value?.lineEdgeLibraryType == 8
      ? "BATTERY_LINE_OTHERS"
      : type;
  const { data, code } = await menuListCollect(
    {
      warehouseType: props.titleName == "edit" ? warehouseType : "",
      type: props.titleName === "edit" ? props.visibleRecord.type : undefined,
    },
    { pageSize: 999999999 }
  );
  if (code === 200) {
    inWarehouseOption.value =
      data?.map((item) => {
        return {
          label: item.warehouseName,
          value: item.id,
        };
      }) || [];
  }
};

typeChange();

//根据选择的货物获取出库的列表
const getSelectMenuListIgnoreNoStock = async (params, outWarehouseId) => {
  const { data, code } = await selectMenuListIgnoreNoStock(params);
  if (code === 200) {
    outWarehouseOption.value = data || [];
  }
};

const materialChange = async (val, option) => {
  console.log("[ option ] >", option);
  const params = {
    materialId: val,
    warehouseType:
      selectedMaterial.value?.lineEdgeLibraryType == 7
        ? "PACK_LINE_OTHERS"
        : selectedMaterial.value?.lineEdgeLibraryType == 8
        ? "BATTERY_LINE_OTHERS"
        : "",
    classifyType: "material",
  };
  formData.outWarehouseId = void 0;
  formData.outWarehouseAreaId = void 0;
  outWarehouseAreaOption.value = [];
  outWarehouseAreaOption.value = [];
  outPhysicalInventory.value = "-";
  await getSelectMenuListIgnoreNoStock(params);
  await menuListCollectList("");
  // 默认选择控‘
  formData.inWarehouseId = void 0;
  formData.inWarehouseName = void 0;
  formData.materialOrProductName = selectedMaterial.value.materialName;
};

const outWarehousChange = (_, option) => {
  formData.outWarehouseName = option?.warehouseName;
  formData.outWarehouseAreaId = void 0;
  formData.outWarehouseAreaName = void 0;
  outPhysicalInventory.value = "-";
  if (option) {
    outWarehouseAreaOption.value = option?.warehouseMenuDetailVoList || [];
  }
};

const outWarehouseAreaChange = (_, option) => {
  formData.outWarehouseAreaName = option?.warehouseArea;
  if (option) {
    outPhysicalInventory.value = option?.inventory || 0;
    max.value =
      option?.inventory || option?.inventory == 0
        ? option?.inventory
        : 999999999999;
  }
};

const inWarehouseChange = async (val, option) => {
  console.log("[ val,option ] >", val, option);
  formData.inWarehouseAreaId = void 0;
  formData.inWarehouseAreaName = void 0;
  formData.inWarehouseName = option?.label;
  // inPhysicalInventory.value = "-";
  const { data, code } = await getInfoRepository(val);
  if (code == 200) {
    inWarehouseAreaOption.value = data?.warehouseMenuDetailVoList || [];
  }
};

const inWarehouseAreaChange = async (value, option) => {
  // console.log(formData, "..");
  formData.inWarehouseAreaName = option?.warehouseArea;
  formData.materialOrProductName = selectedMaterial.value.materialName;
};

const selectedMaterial = computed(() => {
  return productList.value.find((item) => item.id == formData.materialId);
});
const submit = async () => {
  if (props.titleName == "edit") {
    if (props.visibleRecord.inventory) {
    }
  }

  const va = await formRef.value.validate();
  return formRef.value.validate();
};

const resetFields = () => {
  formRef.value.resetFields();
};

const clearValidate = () => {
  formRef.value.clearValidate;
};

defineExpose({ submit, resetFields, clearValidate });
</script>
