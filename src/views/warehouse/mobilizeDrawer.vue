<template>
  <mw-drawer
    custom-title="导出"
    :visible="visible"
    @close="onClose"
    width="70%"
  >
    <mw-button @click="onExport(data.typeName)" class="mb-2">
      {{
        data.typeName == "printInventoryApplication"
          ? "导出入库清单"
          : "导出出库清单"
      }}
    </mw-button>

    <mw-table
      :align="center"
      :columns="columns.fatColumns"
      :data-source="dataList"
      class="tables"
      :rowKey="(record) => record.batchId"
      childrenColumnName="record.settlementInfoVOList"
      @change="onTableChange"
      :pageConfig="paginationProps"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key == 'operate'">
          <mw-button @click="pdfOrg(record)">打印</mw-button>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <mw-table :columns="columns.childrenColumns" :data-source="record.list">
        </mw-table> </template
    ></mw-table>
  </mw-drawer>
</template>
<script setup>
import {
  defineProps,
  ref,
  getCurrentInstance,
  watch,
  onBeforeMount,
  defineEmits,
  reactive,
} from "vue";
const { proxy } = getCurrentInstance();
import {
  recordListV1,
  transferExportPdf,
  transferExportExcel,
} from "@/api/warehouse.js";
import { usePagenation } from "@/common/setup";
import printJS from "print-js";
import { exportExecl } from "@/utils/util.js";
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  data: { type: Object, default: () => ({}) },
});
const emit = defineEmits(["update:visible", "finish"]);
const dataList = ref();
const exportLoading = ref(false);
const columns = ref({
  fatColumns: [
    {
      title: "批次号",
      dataIndex: "batchId",
      key: "batchId",
      width: "150px",
    },
    {
      title: "创建人",
      dataIndex: "createBy",
      key: "createBy",
      width: "150px",
    },
    {
      title: "出库时间",
      dataIndex: "createTime",
      key: "createTime",
      width: "150px",
    },
    {
      title: "操作",
      dataIndex: "operate",
      key: "operate",
      width: "150px",
    },
  ],
  childrenColumns: [
    {
      title: "仓库名称",
      dataIndex: "warehouseName",
      key: "warehouseName",
      width: "150px",
    },
    {
      title: "物料名称",
      dataIndex: "keyName",
      key: "keyName",
      width: "150px",
    },
    {
      title: "物料编码",
      dataIndex: "keyNo",
      key: "keyNo",
      width: "150px",
    },
    {
      title: "数量",
      dataIndex: "quantity",
      key: "quantity",
      width: "150px",
    },
  ],
});
const onClose = () => {
  emit("update:visible", false);
};
const recordList = async (val) => {
  let res = await recordListV1(pageParam.value, val);
  dataList.value = res.data;
  paginationProps.value.total = res.total;
};
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(recordList);

const pdfOrg = async (record) => {
  try {
    record.handleSendLoading = true;

    // 假设 transferExportPdf 是一个返回 Promise 的函数
    let result = await transferExportPdf({ batchId: record.batchId });
    const blob = new Blob([result], {
      type: result.type,
    });
    if (blob.type == "application/json") {
      //判断返回的为json信息
      const reader = new FileReader(); //创建一个FileReader实例
      reader.readAsText(blob, "utf-8"); //读取文件
      console.log(reader, "reader");
      reader.onload = function () {
        //文件读取成功进行信息处理
        const msg = JSON.parse(reader.result); //获取到后端返回的json信息
        console.log(msg, "msg");
        if (msg.code == 500) {
          proxy.$message.error(msg.msg);
        } else {
          let dom = document.createElement("a");
          let url = window.URL.createObjectURL(blob);
          dom.href = url;
          printJS({
            printable: dom,
            type: "pdf",
            size: "landscape",
          });
          record.handleSendLoading = false;
        }
      };
    }
  } catch (error) {
    console.log(error);
  }
};

const onExport = async (val) => {
  exportLoading.value = true;
  let prams = {
    type: val == "printInventoryApplication" ? "in" : "out",
    warehouseType: props.data.warehouseType,
    relatesInformation: props.data.relatesInformation,
    transferId: props.data.transferId,
  };
  let res = await transferExportExcel(prams);
  const fileName =
    val == "printInventoryApplication" ? "入库清单.xlsx" : "出库清单.xlsx";
  exportExecl(fileName, res);
  exportLoading.value = false;
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      recordList(props.data);
    }
  }
);
</script>
<style lang="less" scoped></style>
