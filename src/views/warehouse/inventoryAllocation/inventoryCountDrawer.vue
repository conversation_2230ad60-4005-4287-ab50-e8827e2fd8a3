<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="30%"
    :customTitle="
      btnType == 'add'
        ? '新增调拨'
        : btnType == 'edit'
        ? '编辑调拨'
        : '调拨详情'
    "
  >
    <template #header>
      <mw-button @click="formSubmit" :loading="loading">确定</mw-button>
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      labelAlign="left"
    >
      <a-form-item label="调拨类型" name="type" required>
        <a-select
          :options="mateOrProdType"
          v-model:value="formData.type"
          optionFilterProp="name"
          :placeholder="'调拨类型'"
          show-search
          allowClear
          @change="changeMateOrProd"
          :disabled="props.btnType == 'detail'"
        ></a-select>
      </a-form-item>
      <a-form-item label="调拨货物" name="materialId" required>
        <a-select
          :options="optionsRawMaterial"
          v-model:value="formData.materialId"
          optionFilterProp="name"
          @change="handleChange"
          :field-names="{
            label: formData.type == 'material' ? 'materialName' : 'productName',
            value: 'id',
          }"
          :placeholder="'调拨货物'"
          show-search
          allowClear
          :disabled="props.btnType == 'detail'"
        ></a-select>
      </a-form-item>

      <a-form-item label="出库仓库" name="outWarehouseId" required>
        <a-select
          :options="optionsWarehouseList"
          v-model:value="formData.outWarehouseId"
          optionFilterProp="warehouseName"
          @change="handleChangeWarehouse($event, 'out')"
          :field-names="{
            label: 'warehouseName',
            value: 'warehouseId',
          }"
          :placeholder="'出库仓库'"
          show-search
          allowClear
          :disabled="props.btnType == 'detail'"
        ></a-select>
      </a-form-item>
      <a-form-item label="出库区域" name="outWarehouseAreaId" required>
        <a-select
          :options="optionsRegionList"
          v-model:value="formData.outWarehouseAreaId"
          optionFilterProp="warehouseArea"
          :field-names="{
            label: 'warehouseArea',
            value: 'warehouseAreaId',
          }"
          :placeholder="'出库区域'"
          show-search
          allowClear
          :disabled="props.btnType == 'detail'"
        ></a-select>
      </a-form-item>
      <a-form-item label="入库仓库" name="inWarehouseId" required>
        <a-select
          :options="optionsMenuListCollectList"
          v-model:value="formData.inWarehouseId"
          optionFilterProp="warehouseName"
          @change="handleChangeWarehouse($event, 'in')"
          :field-names="{
            label: 'warehouseName',
            value: 'id',
          }"
          :placeholder="'入库仓库'"
          show-search
          allowClear
          :disabled="props.btnType == 'detail'"
        ></a-select>
      </a-form-item>
      <a-form-item label="入库区域" name="inWarehouseAreaId" required>
        <a-select
          :options="optionsMenuListCollectAreaList"
          v-model:value="formData.inWarehouseAreaId"
          optionFilterProp="warehouseArea"
          :field-names="{
            label: 'warehouseArea',
            value: 'id',
          }"
          :placeholder="'入库区域'"
          show-search
          allowClear
          :disabled="props.btnType == 'detail'"
        ></a-select>
      </a-form-item>
      <a-form-item label="调拨数量" name="inventory" required>
        <a-input-number
          v-model:value="formData.inventory"
          placeholder="调拨数量"
          :disabled="props.btnType == 'detail'"
          :stringMode="true"
        ></a-input-number>
      </a-form-item>

      <!-- <a-form-item label="备注" name="remark" required>
        <a-input v-model:value="remark"></a-input>
      </a-form-item> -->
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
  defineProps,
  defineEmits,
  watch,
  toRef,
} from "vue";
import { mateOrProdType } from "@/common/constant.js";
import { AllList, getInfoRepository } from "@/api/basicData/material.js";
import { list } from "@/api/basicData/product.js";
import {
  allocationAdd,
  warehouseCollect,
  menuListCollect,
} from "@/api/inventoryAllocation.js";

import { selectMenuListIgnoreNoStock } from "@/api/basicData/repository.js";
const emit = defineEmits(["update:visible", "finish"]);
const { proxy } = getCurrentInstance();
const formRef = ref();
const loading = ref(false);
const optionsRawMaterial = ref();
const optionsWarehouseList = ref();
const optionsRegionList = ref();
const optionsMenuListCollectList = ref();
const optionsMenuListCollectAreaList = ref();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
  btnType: String,
  recordVal: Object,
});
const formData = reactive({
  type: undefined,
});
onBeforeMount(() => {
  // formRef.value.resetFields();
});
// 物料/成品列表接口
const page = async (val) => {
  let res =
    val == "material" ? await AllList() : await list({ ignoreCancel: true });
  optionsRawMaterial.value = res.data;
};
// 盘点类型
const changeMateOrProd = (e) => {
  optionsRawMaterial.value = [];
  formData.materialId = undefined;
  formData.warehouseId = undefined;
  formData.warehouseAreaId = undefined;
  page(e);
};
// 库存
const MenuListIgnoreNoStockList = async (val) => {
  let res = await selectMenuListIgnoreNoStock({
    // warehouseType: key,
    materialId: val,
  });
  optionsWarehouseList.value = res.data;
  if (props.recordVal.outWarehouseId) {
    res.data.forEach((element) => {
      if (element.warehouseId == props.recordVal.outWarehouseId) {
        optionsRegionList.value = element.warehouseMenuDetailVoList;
      }
    });
  }

  // if (props.btnType == "detail") {
  //   optionsRegionList.value = res.data[0].warehouseMenuDetailVoList;
  // }
};

// 全部库存

const menuListCollectList = async (val) => {
  let res = await menuListCollect({
    warehouseType: formData.type == "material" ? "RAWMATERIAL" : "FINISHED",
    materialId: val,
  });
  optionsMenuListCollectList.value = res.data;
  // if (props.btnType == "detail") {
  //   optionsRegionList.value = res.data[0].warehouseMenuDetailVoList;
  // }
};
const getInfoRepositoryList = async (val) => {
  let res = await getInfoRepository(val);
  optionsMenuListCollectAreaList.value = res.data.warehouseMenuDetailVoList;
};

// 选择物料
const handleChange = (e) => {
  MenuListIgnoreNoStockList(e);
  menuListCollectList(e);
};
// 选择仓库
const handleChangeWarehouse = (e, val) => {
  if (val == "in") {
    getInfoRepositoryList(e);
  }
  optionsRegionList.value = optionsWarehouseList.value.find(
    (obj) => obj.warehouseId == e
  ).warehouseMenuDetailVoList;
};
// 关闭弹窗
function onClose() {
  emit("update:visible", false);
  formRef.value.resetFields();
}
// 提交
const formSubmit = async () => {
  formRef.value.validate().then(async () => {
    try {
      loading.value = true;
      await allocationAdd(formData);
      loading.value = false;
      onClose();
      emit("finish");
    } catch (error) {
      loading.value = false;
    }
  });
};

watch(
  () => props.visible,
  async (val) => {
    // page();
    if (props.btnType == "detail" && props.recordVal) {
      page(props.recordVal.type);
      formData.value = props.recordVal.value;
      formData.type = props.recordVal.type;
      formData.inventory = props.recordVal.inventory;
      formData.materialId = String(props.recordVal.materialId);

      formData.inWarehouseAreaId = String(props.recordVal.inWarehouseAreaId);
      formData.inWarehouseId = props.recordVal.inWarehouseId;
      formData.outWarehouseAreaId = props.recordVal.outWarehouseAreaId;
      formData.outWarehouseId = String(props.recordVal.outWarehouseId);
      MenuListIgnoreNoStockList(props.recordVal.materialId);
      menuListCollectList(props.recordVal.materialId);
      getInfoRepositoryList(props.recordVal.inWarehouseId);
    } else {
      formData.value = undefined;
      formData.type = undefined;
      formData.inventory = undefined;
      formData.materialId = undefined;
      formData.inWarehouseAreaId = undefined;
      formData.inWarehouseId = undefined;
      formData.inventory = undefined;
      formData.materialId = undefined;
      formData.outWarehouseAreaId = undefined;
      formData.outWarehouseId = undefined;
    }
  }
);
</script>

<style lang="scss" scoped></style>
