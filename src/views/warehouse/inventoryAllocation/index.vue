<template>
  <div class="mb-4 text-right">
    <search :searchData="searchData" @search="refresh">
      <mw-button @click="onOpen('add')">新增</mw-button>
    </search>
  </div>
  <mw-table
    :columns="columns"
    :data-source="dataList"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'type'">
        {{ record.type == "material" ? "物料" : "成品" }}
      </template>
      <template v-if="column.key == 'operation'">
        <mw-button @click="onOpen('detail', record)"> 详情 </mw-button>
      </template>
    </template>
  </mw-table>
  <inventory-count-drawer
    ref="refInventoryCountDrawer"
    @finish="getList"
    v-model:visible="visibleInventoryCountDrawer"
    :btnType="btnType"
    :recordVal="recordVal"
  ></inventory-count-drawer>
</template>
<script setup>
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import Search from "@/components/search/index.vue";
import { allocationPage } from "@/api/inventoryAllocation.js";
import { mateOrProdType } from "@/common/constant.js";
import { usePagenation } from "@/common/setup";
import inventoryCountDrawer from "./inventoryCountDrawer.vue";

const { proxy } = getCurrentInstance();
const loading = ref(false);
const btnType = ref();
const dataList = ref();
const recordVal = ref();
const visibleInventoryCountDrawer = ref(false);

const searchData = ref({
  fields: {
    type: {
      name: "类型",
      type: "a-select",
      options: mateOrProdType,
      placeholder: "选择分类",
      width: "220px",
      fieldNames: {
        label: "label",
        value: "value",
      },
    },
    // keyword: {
    //   type: "a-input-search",
    //   placeholder: "请输入合同编号/合同名称/客户名称",
    //   width: "240px",
    // },
  },
});
const columns = ref([
  {
    title: "调拨名称",
    dataIndex: "materialName",
    key: "materialName",
  },

  {
    title: "调拨类型",
    dataIndex: "type",
    key: "type",
  },

  {
    title: "出库仓库",
    dataIndex: "outWarehouseName",
    key: "outWarehouseName",
  },
  {
    title: "出库区域",
    dataIndex: "outWarehouseAreaName",
    key: "outWarehouseAreaName",
  },
  {
    title: "入库仓库",
    dataIndex: "inWarehouseName",
    key: "inWarehouseName",
  },
  {
    title: "入库区域",
    dataIndex: "inWarehouseAreaName",
    key: "inWarehouseAreaName",
  },
  {
    title: "操作时间",
    dataIndex: "createTime",
    key: "createTime",
  },

  {
    title: "操作",
    dataIndex: "operation",
    key: "operation",
    width: "200px",
    fixed: "right",
  },
]);
onBeforeMount(async () => {
  await getList();
});
const getList = async () => {
  loading.value = true;
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let result = await allocationPage({ ...pageParam.value, ...searchParam });
  loading.value = false;
  dataList.value = result.data;
  paginationProps.value.total = result.total;
};

const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

const onOpen = (btnTypeVal, record) => {
  btnType.value = btnTypeVal;
  recordVal.value = record;
  visibleInventoryCountDrawer.value = true;
};
</script>

<style lang="scss" scoped></style>
