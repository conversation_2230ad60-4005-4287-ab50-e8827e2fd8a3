<template>
  <search :searchData="searchData" @search="refresh"> </search>

  <mw-table
    :align="center"
    :columns="columns.fatColumns"
    :data-source="data"
    class="tables"
    :rowKey="(record) => record.transferId"
    childrenColumnName="record.settlementInfoVOList"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
    :scroll="{ x: 'max-content' }"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'type'">
        <dictionary
          :statusOptions="newWarehouseType"
          :value="record.type"
          :showBadge="false"
        />
      </template>
      <template v-if="column.key == 'relatesInformation'">
        <div
          @click="poCorrelationOrder(record)"
          v-if="
            record.type == 'PRODUCTION_MATERIAL_OUT' ||
            record.type == 'PRODUCTION_INCREASE_OUT'
          "
        >
          <span style="color: #1890ff">
            {{ record.relatesInformation }}
          </span>
        </div>
      </template>
      <template v-if="column.key == 'status'">
        <dictionary
          :statusOptions="warehouseStatus"
          :value="record.status"
          :showBadge="false"
        />
      </template>

      <template v-if="column.key == 'button'">
        <mw-button
          @click="showCreateDrawer(record)"
          v-if="record.status == 0"
          class="mr-1"
          >出库</mw-button
        >
        <mw-button @click="mobilizeExportOrg(record)">打印</mw-button>
      </template>
    </template>
    <template #expandedRowRender="{ record }">
      <div
        v-if="
          record.type == 'PRODUCTION_MATERIAL_OUT' ||
          record.type == 'PRODUCTION_INCREASE_OUT'
        "
      >
        <div class="flex items-center header">
          <div class="materialNo w-40 m-header">产品编码</div>
          <div class="materialName flex-1 m-header">产品名称</div>
          <div class="specification flex-1 m-header">产品规格</div>
        </div>
        <div class="flex items-center content">
          <div class="materialNo w-40 m-content">{{ record.materialNo }}</div>
          <div class="materialName flex-1 m-content">
            {{ record.materialName }}
          </div>
          <div class="specification flex-1 m-content">
            {{ record.specification }}
          </div>
        </div>
      </div>

      <mw-table
        :columns="columns.childrenColumns"
        :data-source="record.insertParamList"
        :pagination="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'orderNum'">
            {{ record.orderNum }}{{ record.unitName }}
          </template>
          <template v-if="column.key == 'alreadyNum'">
            {{ record.alreadyNum }} {{ record.unitName }}
          </template></template
        >
      </mw-table>
    </template>
  </mw-table>

  <!-- <allot-drawer
    v-model:visible="addVisible"
    :record="currendRecord"
    @finish="getList"
  /> -->

  <newAllotDrawer
    v-model:visible="addVisible"
    :record="currendRecord"
    @finish="getList"
  ></newAllotDrawer>
  <mobilize-drawer
    v-model:visible="mobilizeVisible"
    :data="parameterData"
  ></mobilize-drawer>
  <BomDrawer v-model:visible="bomDrawerVisible" :planNo="planNo"></BomDrawer>
  <mw-drawer
    custom-title="线边库物料领用"
    :visible="allotVisible"
    @close="onClose"
  >
    <template #header>
      <mw-button @click="submit">确定</mw-button>
    </template>
    <AllotTransferForm
      ref="transferFormSbmit"
      :visibleRecord="visibleRecord"
      :titleName="titleName"
      v-model:allotVisible="allotVisible"
    />
  </mw-drawer>
</template>
<script setup>
import { ref, onBeforeMount, reactive, toRaw } from "vue";
import { listOutMaterial } from "@/api/warehouse.js";
import { usePagenation } from "@/common/setup";
import Search from "@/components/search/index.vue";
// import allotDrawer from "./allotDrawer.vue";
import newAllotDrawer from "./newAllotDrawer.vue";
import mobilizeDrawer from "./mobilizeDrawer.vue";
import { useRouter } from "vue-router";
import {
  warehouseStatus,
  warehouseStatusFilters,
  newWarehouseType,
} from "@/common/constant.js";
import BomDrawer from "@/views/production/BomDrawer.vue";
import AllotTransferForm from "./allotTransferForm.vue";
import { transferAddV1 } from "@/api/warehouse";
import { message } from "ant-design-vue";
const router = useRouter();
const mobilizeVisible = ref(false);
const bomDrawerVisible = ref(false);
const planNo = ref();
const data = ref([]);
const loading = ref(false);
const addVisible = ref(false);
const currendRecord = ref({});
const parameterData = ref({
  keyword: "",
});
const searchData = reactive({
  fields: {
    rangeDate: {
      type: "a-range-picker",
      valueFormat: "YYYY-MM-DD",
      value: [],
      width: "240px",
      allowClear: true,
    },
    status: {
      name: "调拨状态",
      type: "a-select",
      options: warehouseStatusFilters,
      placeholder: "选择状态",
      width: "120px",
      value: "",
      allowClear: true,
    },
    queryType: {
      name: "出库类型",
      type: "a-select",
      options: [{ label: "全部类型", value: "" }, ...newWarehouseType],
      placeholder: "选择出库类型",
      width: "150px",
      value: "",
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: `输入物料编码/名称/关联信息`,
      width: "240px",
      allowClear: true,
    },
  },
});
const columns = ref({
  fatColumns: [
    {
      title: "出库类型",
      dataIndex: "type",
      key: "type",
    },
    {
      title: "申请人员",
      dataIndex: "reviewerId",
      key: "reviewerId",
    },

    {
      title: "父关联编码",
      dataIndex: "orderNos",
      key: "orderNos",
    },
    {
      title: "关联编码",
      dataIndex: "relatesInformation",
      key: "relatesInformation",
    },
    {
      title: "申请时间",
      dataIndex: "createTime",
      key: "createTime",
    },
    {
      title: "生产线",
      dataIndex: "lineName",
      key: "lineName",
    },
    {
      title: "物料总数",
      dataIndex: "orderNum",
      key: "orderNum",
    },
    {
      title: "已出库总数",
      dataIndex: "alreadyNum",
      key: "alreadyNum",
    },
    {
      title: "未出库总数",
      dataIndex: "waitNum",
      key: "waitNum",
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
    }, //产品让去掉
    {
      title: "操作",
      dataIndex: "button",
      key: "button",
      fixed: "right",
    },
  ],
  childrenColumns: [
    {
      title: "物料编码",
      dataIndex: "materialNo",
      key: "materialNo",
    },
    {
      title: "物料信息",
      dataIndex: "materialName",
      key: "materialName",
    },
    {
      title: "规格",
      dataIndex: "specification",
      key: "specification",
    },
    {
      title: "申请出库数量",
      dataIndex: "orderNum",
      key: "orderNum",
    },
    {
      title: "已经出库数量",
      dataIndex: "alreadyNum",
      key: "alreadyNum",
    },
  ],
});
const poCorrelationOrder = (record) => {
  bomDrawerVisible.value = true;
  planNo.value = record.relatesInformation;
};
const getList = async () => {
  loading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await listOutMaterial(pageParam.value, {
    ...searchParam,
  });
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
const showCreateDrawer = (record) => {
  console.log(record);
  if (["BATTERY_LINE_OTHERS", "PACK_LINE_OTHERS"].includes(record.type)) {
    visibleRecord.value = record;
    allotVisible.value = true;
  } else {
    if (record.status != "1") {
      if (record.storageType == "out") {
        currendRecord.value = record;
        addVisible.value = true;
      } else {
        router.push({
          path: "productWarehouse",
        });
      }
    }
  }
};
const mobilizeExportOrg = (val) => {
  parameterData.value.keyword = val.relatesInformation;
  mobilizeVisible.value = true;
  parameterData.value.keyword = val.relatesInformation;
  parameterData.value.warehouseType = val.type;
  parameterData.value.relatesInformation = val.relatesInformation;
  parameterData.value.transferId = val.transferId;
};
const allotVisible = ref(false);
const visibleRecord = ref();
const onClose = () => {
  allotVisible.value = false;
};
const transferFormSbmit = ref();
const submit = async () => {
  const value = await transferFormSbmit.value.submit();
  console.log("[ value ] >", value);
  if (value) {
    const { code } = await transferAddV1({
      ...value,
      type: visibleRecord.value.type,
      transferId: visibleRecord.value.transferId,
    });
    if (code === 200) {
      message.success("调拨成功");
      onClose();
      refresh();
    }
  }
};
</script>
<style lang="less" scoped>
.list-header {
  background: theme("colors.background");
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  color: theme("colors.primary-text");
}

:deep(.ant-list-item) {
  border: 1px solid theme("colors.dividers") !important;
  padding: 0 16px !important;
  border-radius: 8px;
}
.header {
  overflow: hidden;
  border-radius: 8px;
}
.m-header {
  background: #f0f0f0;
  font-weight: 400;
  color: rgba(34, 34, 34, 0.65);
  padding: 10px 12px;
  line-height: 20px;
  height: 40px;
  border: none;
}
.content {
  // padding: 10px 12px;
}
.m-content {
  padding: 10px 12px;
}
</style>
