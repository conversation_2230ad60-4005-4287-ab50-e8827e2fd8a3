<template>
  <div class="bg-ff">
    <div class="w-full flex justify-center items-center page-404">
      <div class="pb-30" style="width: 1000px">
        <div
          class="w-full flex justify-center items-center gap-x-40 text-center"
        >
          <div class="" style="width: 580px">
            <img src="@/assets/404.svg" alt="" />
          </div>
          <div class="text-left pr-15">
            <h1 class="font-medium" style="font-size: 56px">404</h1>
            <p class="text-primary-text mt-2">抱歉，你访问的页面不存在</p>
            <div class="text-left mt-7">
              <span class="text-secondar-text"></span>
              <a-button shape="round" type="primary" @click="$router.push('/')"
                >回到首页</a-button
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="text-center text-xs text-secondar-text h-25">
      <span class="align-middle">
        ©2024 上海明我相忆网络科技有限公司版权所有 |
      </span>
      <a
        href="https://beian.miit.gov.cn/#/Integrated/index"
        target="_blank"
        class="align-middle"
        >沪ICP备13029193号-7</a
      >
    </div>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";

const router = useRouter();
</script>

<style lang="less" scoped>
.page-404 {
  height: calc(100vh - 100px);
}
</style>
