<template>
  <div class="form-upload" :class="type == 'content' ? 'flex-col' : 'gap-3'">
    <!-- <a-image-preview-group v-if="value.length > 0"> -->
    <template v-if="showPreview">
      <div
        :class="value[0] ? 'file-item' : ''"
        v-for="(item, index) in value"
        :key="index"
      >
        <!-- <template #count>
          <close-circle-outlined
            class="cursor"
            style="color: #979797;font-size:16px;"
            
          />
        </template> -->
        <template v-if="item">
          <img
            v-if="item.mimeType == 'image/jpeg'"
            class="image rounded"
            :style="
              type == 'content'
                ? {}
                : {
                    width: config.width + 'px',
                    height: config.height + 'px',
                  }
            "
            :src="
              item.mimeType == 'image/jpeg'
                ? item.fileVisitUrl
                : item.fileName.includes('pdf')
                ? pdfIcon
                : defaultIcon
            "
          />
          <div
            v-else
            class="msword"
            :style="
              type == 'content'
                ? {}
                : {
                    width: config.width + 'px',
                    height: config.height + 'px',
                  }
            "
          >
            {{
              item.fileName.split(".")[0].length > 3
                ? item.fileName.substring(0, 3) +
                  "..." +
                  "." +
                  item.fileName.split(".").pop()
                : item.fileName
            }}
          </div>

          <div
            class="option"
            :class="
              type == 'content'
                ? 'justify-end pt-3 pr-3'
                : 'items-center justify-center'
            "
          >
            <template v-if="type == 'content'">
              <div class="circle" v-if="index != 0" @click="up(index)">
                <ArrowUpOutlined />
              </div>
              <div
                class="circle"
                v-if="index != value.length - 1"
                @click="down(index)"
              >
                <ArrowDownOutlined />
              </div>
            </template>

            <a-popconfirm
              v-if="!readonly && showDeletdPopup"
              title="是否确认删除？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="removeFile(index)"
            >
              <div class="circle">
                <i class="iconfont icon-shanchu font-bold"></i>
              </div>
            </a-popconfirm>
            <!-- <div
              v-if="!readonly && !showDeletdPopup"
              class="circle"
              @click="removeFile(index)"
            >
              <i class="iconfont icon-shanchu font-bold"></i>
            </div> -->
            <div
              v-if="
                (item.mimeType == 'image/jpeg' ||
                  item.fileName.includes('.pdf')) &&
                !hidePreviewBtn
              "
              class="circle"
              @click="preview(item)"
            >
              <i class="iconfont icon-pc-yulan font-bold"></i>
            </div>
            <div
              v-if="hasDownLoad"
              class="circle"
              @click="download(item.fileVisitUrl, item.fileName)"
            >
              <i class="iconfont icon-xiazai font-bold"></i>
            </div>
          </div>
        </template>
        <div
          class="abs absolute top-full w-full overflow-hidden overflow-ellipsis whitespace-nowrap fileName"
          v-if="showTitle"
          :title="item?.fileName || ''"
        >
          {{ item?.fileName }}
        </div>
      </div>
    </template>
    <!-- v-if="
    showUpload && (value.length < fileLimit || !showPreview) && !readonly
  " -->
    <a-upload
      v-if="showUpload && !value[0] && !readonly"
      v-model:file-list="fileList"
      name="file"
      list-type="picture-card"
      class="avatar-uploader"
      :show-upload-list="false"
      :action="fileUploadUrl"
      :headers="{
        Authorization: `Bearer ${getToken()}`,
      }"
      :before-upload="beforeUpload"
      :max-count="fileLimit"
      @change="handleUploadChange"
    >
      <slot></slot>
      <div
        v-if="!hasSlot"
        :style="
          type == 'content'
            ? {}
            : { width: `${config.width}px`, height: `${config.height}px` }
        "
        class="flex flex-col justify-center gap-1 uploadFlex"
      >
        <loading-outlined v-if="uploadLoading"></loading-outlined>
        <plus-outlined
          v-else
          :style="{ fontSize: `${config.iconSize}px`, color: '#595959' }"
        ></plus-outlined>
        <p class="text-59" :style="{ fontSize: `${config.fontSize}px` }">
          {{ title }}
        </p>
        <!-- <add-box :config="config">
          <loading-outlined v-if="uploadLoading"></loading-outlined>
        </add-box> -->
      </div>
    </a-upload>
    <!-- </a-image-preview-group> -->
    <!-- {{ value[0] }} -->
    <a-modal
      :visible="previewVisible"
      :footer="null"
      @cancel="previewVisible = false"
      width="1000px"
    >
      <img
        alt="example"
        style="width: 100%; margin-top: 10px"
        :src="previewImage"
      />
    </a-modal>
  </div>
</template>
<script>
import { computed, defineComponent, toRefs, useSlots, watch } from "vue";
import {
  LoadingOutlined,
  PlusOutlined,
  ArrowDownOutlined,
  ArrowUpOutlined,
} from "@ant-design/icons-vue";
import _cloneDeep from "lodash/cloneDeep";
import { suffixThumbnails } from "@/common/constant";
// import { useStore } from "vuex";
// import { useRouter } from "vue-router";
// import AddBox from "./addBox.vue";
import uploadLogic from "@/common/setup/uploadLogic.js";
import { getToken } from "@/utils/auth";
import pdfIcon from "@/assets/fileIcon/pdf.svg";
import defaultIcon from "@/assets/fileIcon/default.svg";

export default defineComponent({
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    fileLimit: {
      type: Number,
      default: 1,
    },
    fileSize: {
      type: Number,
      default: 1, //默认1M
    },
    // "jpeg", "png", "jpg"需求要求支持所有类型---暂时去掉
    fileTypes: {
      type: Array,
      default: () => [],
    },
    sence: {
      type: String,
      default: "",
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    hasDownLoad: {
      type: Boolean,
      default: false,
    },
    showDeletdPopup: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "上传文件",
    },
    config: {
      type: Object,
      default: () => ({
        width: 104,
        height: 104,
        iconSize: 24,
        fontSize: 14,
      }),
    },
    type: {
      type: String,
      default: "normal",
    },
    hidePreviewBtn: {
      type: Boolean,
      default: false,
    },
    showUpload: {
      type: Boolean,
      default: true,
    },
    showTitle: {
      type: Boolean,
      default: false,
    },
    showPreview: {
      type: Boolean,
      default: true,
    },
    needWaterRemark: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    LoadingOutlined,
    PlusOutlined,
    ArrowDownOutlined,
    ArrowUpOutlined,
  },
  setup(props, { emit }) {
    const { value } = toRefs(props);
    const {
      state,
      handleUploadChange,
      beforeUpload,
      fileUploadUrl,
      removeFile,
      preview,
      download,
      emitValue,
      up,
      down,
    } = uploadLogic({ ...toRefs(props), emit });
    watch(
      value,
      (val) => {
        state.files = _cloneDeep(val);
      },
      { deep: true, immediate: true }
    );
    const hasSlot = computed(() => !!useSlots().default);
    return {
      ...toRefs(state),
      handleUploadChange,
      beforeUpload,
      fileUploadUrl,
      removeFile,
      preview,
      download,
      emitValue,
      suffixThumbnails,
      up,
      down,
      hasSlot,
      getToken,
      pdfIcon,
      defaultIcon,
    };
  },
});
</script>
<style lang="less" scoped>
.form-upload {
  display: flex;
  flex-wrap: wrap;
  :deep(.ant-image-img) {
    height: 100%;
  }
  .file-item {
    // margin-top: 10px;
    border: 1px solid #eee;
    position: relative;
    border-radius: 4px;
    .option {
      display: none;
      position: absolute;
      width: 100%;
      height: 100%;
      background: #f5f5f5;
      opacity: 0.9;
      top: 0;
      left: 0;
      border-radius: 4px;
      // align-items: center;
      // justify-content: center;
      .circle {
        width: 30px;
        height: 30px;
        background: #e6e6e8;
        border-radius: 50%;
        text-align: center;
        line-height: 30px;
        cursor: pointer;
        img {
          width: 16px;
          height: 16px;
        }
        + .circle {
          margin-left: 2px;
        }
      }
    }
    &:hover .option {
      display: flex;
    }
  }
  :deep(.ant-upload-picture-card-wrapper) {
    width: auto;
    // margin-top: 10px;
  }
  :deep(.ant-upload.ant-upload-select-picture-card) {
    background: #fff;
    border: none;
    margin: 0;
    height: auto;
  }
  :deep(.ant-upload) {
    padding: 0;
    width: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    // background: #f5f5f5;
  }
}
// .fileName {
//   &:hover {
//     width: auto;
//     background: #fff;
//     z-index: 9;
//   }
// }
</style>
<style lang="less">
.ant-modal-close-x {
  width: 32px;
  height: 32px;
  line-height: 30px;
}
.msword {
  text-align: center;
  line-height: 100px;
  padding: 0 5px;
  word-break: break-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}
.uploadFlex {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
