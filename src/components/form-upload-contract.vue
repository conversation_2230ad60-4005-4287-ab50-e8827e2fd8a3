<template>
  <div :class="flexT == true ? 'flex' : 'form - upload'">
    <!-- </a-image-preview-group> -->
    <a-upload
      v-if="!readonly"
      name="file"
      list-type="picture-card"
      class="avatar-uploader align-middle"
      :show-upload-list="false"
      :action="fileUploadUrl"
      :headers="{
        Authorization: `Bear<PERSON> ${getToken()}`,
      }"
      :before-upload="beforeUpload"
      :max-count="fileLimit"
      @change="handleUploadChange"
      :multiple="multiple"
    >
      <a-button type="primary" size="small" ghost :loading="uploadLoading">
        <span v-if="fileLName && detailType !== 'detail'">上传附件</span>
        <span v-else-if="detailType !== 'detail'">{{
          files.length > 0 ? "变更合同" : "上传合同"
        }}</span>
      </a-button>
    </a-upload>
    <!-- <a-image-preview-group v-if="value.length > 0"> -->
    <div :class="flexT == true ? 'ml-5' : ''">
      <span
        v-for="(item, index) in value"
        class="flex items-center"
        :key="index"
      >
        <a
          style="max-width: 220px"
          class="inline-block align-middle overflow-hidden overflow-ellipsis mr-2 whitespace-nowrap relative"
          :key="index"
          target="_blank"
          @click="downFile(item.fileVisitUrl, item.fileName)"
        >
          <i
            class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
            style="color: #959ec3"
          ></i
          ><span class="underline" style="color: #959ec3">{{
            item.fileName
          }}</span>
        </a>
        <span class="ml-0.5 pointer" @click="delFile(index)" v-if="delShow">
          <i class="iconfont icon-shanchu font-bold"></i>
        </span>
      </span>
    </div>
  </div>
</template>
<script>
import { defineComponent, toRefs, watch } from "vue";
import _cloneDeep from "lodash/cloneDeep";
import { suffixThumbnails } from "@/common/constant";
// import { useStore } from "vuex";
// import { useRouter } from "vue-router";
// import AddBox from "./addBox.vue";
import uploadLogic from "@/common/setup/uploadLogic";
import { getToken } from "@/utils/auth";
import { downFile } from "@/common/setup/index.js";

export default defineComponent({
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    fileLimit: {
      type: Number,
      default: 1,
    },
    fileLName: {
      type: String,
    },
    detailType: {
      type: String,
    },
    fileSize: {
      type: Number,
      default: 1, //默认1M
    },
    // "jpeg", "png", "jpg"
    fileTypes: {
      type: Array,
      default: () => [],
    },
    sence: {
      type: String,
      default: "",
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    hasDownLoad: {
      type: Boolean,
      default: false,
    },
    showDeletdPopup: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "上传文件",
    },
    config: {
      type: Object,
      default: () => ({
        width: 104,
        height: 104,
      }),
    },
    type: {
      type: String,
      default: "normal",
    },
    delShow: {
      type: Boolean,
      default: false,
    },
    flexT: {
      type: Boolean,
      default: false,
    },
    msgCodeName: { type: String, default: "" },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  components: {},
  setup(props, { emit }) {
    const { value } = toRefs(props);
    const {
      state,
      handleUploadChange,
      beforeUpload,
      fileUploadUrl,
      removeFile,
      preview,
      download,
      emitValue,
      up,
      down,
    } = uploadLogic({ ...toRefs(props), emit });

    const delFile = (index, val) => {
      let ind = index;
      value.value.splice(ind, 1);
      // value.value = value.value.filter((item, i) => i !== ind);
      emit("del", index, value.value);
    };

    watch(
      value,
      (val) => {
        state.files = _cloneDeep(val);
        state.msgCodeName = _cloneDeep(props.msgCodeName);
      },
      { deep: true, immediate: true }
    );
    return {
      ...toRefs(state),
      handleUploadChange,
      beforeUpload,
      fileUploadUrl,
      removeFile,
      preview,
      download,
      emitValue,
      suffixThumbnails,
      up,
      down,
      getToken,
      delFile,
      downFile,
    };
  },
});
</script>
<style lang="less" scoped>
// .form-upload {
//   display: inline-block;
// }
:deep(.ant-upload-picture-card-wrapper) {
  width: auto;
  height: auto;
}
:deep(.ant-upload.ant-upload-select-picture-card) {
  width: auto;
  height: auto;
  margin: 0 !important;
  border: none;
}
:deep(.ant-upload.ant-upload-select-picture-card > .ant-upload) {
  padding: 0;
}
</style>
<style lang="less">
// .ant-modal-close-x {
//   width: 32px;
//   height: 32px;
//   line-height: 30px;
// }
.ant-upload-wrapper.ant-upload-picture-card-wrapper
  .ant-upload.ant-upload-select {
  width: auto;
  height: auto;
  margin: 0 !important;
  border: none;
}
</style>
