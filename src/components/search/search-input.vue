<template>
  <a-input-group class="search-input" compact>
    <a-select
      v-model:value="filterBy"
      @change="handleFilterByChange"
      :dropdownMatchSelectWidth="false"
      :style="
        filterData.config && filterData.config.filterByWidth
          ? { width: filterData.config.filterByWidth }
          : {}
      "
    >
      <template v-for="(val, key) in filterData.options" :key="key">
        <template v-if="val.display && typeof val.display == 'function'">
          <!-- 如果有设置是否展示 -->
          <a-select-option :value="key" v-if="val.display()">{{
            val.name
          }}</a-select-option>
        </template>
        <a-select-option :value="key" v-else>{{ val.name }}</a-select-option>
      </template>
    </a-select>
    <component
      :is="currentSearch.type"
      v-model:value="filterValue"
      style="width: 188px"
      allowClear
      showSearch
      @search="handleSearch"
      @change="handleChange(currentSearch.type)"
      v-bind="currentSearch"
      :loading="selectLoading"
    />
  </a-input-group>
</template>
<script>
import { computed, reactive, toRaw, toRefs } from "vue";
export default {
  name: "search-input",
  props: {
    filterData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    fnChangeFilterBy: Function,
  },
  setup(props, { emit }) {
    let { filterData } = toRefs(props);
    filterData.value.value.filterValue;
    const state = reactive({
      filterBy:
        filterData.value.value.filterBy ||
        Object.keys(filterData.value.options)[0],
      filterValue: filterData.value.value.filterValue,
      selectLoading: false,
    });
    const currentSearch = computed(() => {
      return filterData.value.options[state.filterBy] || {};
    });
    const handleFilterByChange = async (val) => {
      state.filterValue = undefined;
      let fnChangeFilterBy = filterData.value.fnChangeFilterBy;
      if (fnChangeFilterBy && typeof fnChangeFilterBy == "function") {
        state.selectLoading = true;
        await fnChangeFilterBy(val);
        state.selectLoading = false;
      }
      // search();
    };
    const handleSearch = (val) => {
      //如果有传handleSearch方法的，就直接调用该方法
      if (
        currentSearch.value.handleSearch &&
        typeof currentSearch.value.handleSearch == "function"
      ) {
        currentSearch.value.handleSearch(val);
      } else {
        search();
      }
    };
    const handleChange = (type) => {
      //search组件不触发change
      if (type != "a-input-search") {
        search();
      }
    };
    const search = () => {
      emit("search", toRaw(state));
    };
    return {
      ...toRefs(state),
      currentSearch,
      handleFilterByChange,
      handleSearch,
      handleChange,
    };
  },
};
</script>
<style lang="less">
.search-input.ant-input-group-compact .ant-select {
  border-right: none !important;
}
</style>
