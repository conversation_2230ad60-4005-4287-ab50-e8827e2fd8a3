<template>
  <div class="search">
    <!-- <p class="font-medium">{{ title }}</p> -->
    <a-space class="options" :size="12">
      <!-- 搜索字段 -->
      <template v-for="(item, key) in searchDataList" :key="key">
        <component
          :style="{ width: item.width || '108px', 'text-align': 'left' }"
          :is="item.type"
          v-model:value="item.value"
          @search="search"
          @change="handleChange(item.type)"
          v-bind="item"
          v-if="item.permission"
          v-permission="item.permission"
        >
          <template
            #suffixIcon
            v-if="item.type == 'a-range-picker' || item.type == 'a-date-picker'"
          >
            <i class="iconfont icon-xianxing-01"></i>
          </template>
        </component>
        <component
          :style="{ width: item.width || '108px', 'text-align': 'left' }"
          :is="item.type"
          v-model:value="item.value"
          @search="search"
          @change="handleChange(item.type)"
          v-bind="item"
          v-else
        >
          <template
            #suffixIcon
            v-if="item.type == 'a-range-picker' || item.type == 'a-date-picker'"
          >
            <i class="iconfont icon-xianxing-01"></i>
          </template>
        </component>
      </template>
      <span v-if="arrayOfObjects.length > initialShowCount">
        <button @click="toggleMore">
          <span v-if="toggleMoreSwitch">收起...</span>
          <span v-if="!toggleMoreSwitch">更多...</span>
        </button>
      </span>

      <!-- 联合搜索框 -->
      <search-input
        v-if="searchData.filterData"
        :filterData="searchData.filterData"
        @search="handleFilter"
      ></search-input>
      <a-button
        v-for="(btn, index) in searchData.operationButtons"
        :key="index"
        @click="btn.click"
        :disabled="btn.disabled"
        :icon="btn.icon"
        :loading="btn.loading"
      >
        {{ btn.name }}</a-button
      >
    </a-space>

    <a-space :size="12">
      <a-button v-if="showRefresh" @click="search" shape="round" class="flex">
        <i class="iconfont icon-xianxing-31beifen"></i>
        刷新
      </a-button>
      <slot> </slot>
    </a-space>
  </div>
</template>
<script>
import { ref } from "vue";
import SearchInput from "./search-input.vue";
import { ReloadOutlined } from "@ant-design/icons-vue";

export default {
  name: "search",
  components: {
    SearchInput,
    ReloadOutlined,
  },
  props: {
    searchData: {
      type: Object,
      default: () => {},
    },
    title: String,
    filterData: {
      type: Object,
      default: () => {},
    },
    icon: String,
    showRefresh: {
      type: Boolean,
      default: true,
    },
  },
  setup(props, { emit }) {
    const search = () => {
      emit("search");
    };
    const toggleMoreSwitch = ref(false);
    const initialShowCount = 4;
    const searchDataList = ref();
    const searchList = ref();
    // 将对象的每个属性转换为一个数组
    const arrayOfObjects = Object.keys(props.searchData.fields).map((key) => {
      const item = props.searchData.fields[key];
      return { [key]: item };
    });
    searchList.value = arrayOfObjects.slice(0, initialShowCount);
    searchDataList.value = Object.assign({}, ...searchList.value);
    const toggleMore = () => {
      toggleMoreSwitch.value = !toggleMoreSwitch.value;
      if (toggleMoreSwitch.value) {
        searchDataList.value = props.searchData.fields;
      } else {
        searchDataList.value = Object.assign({}, ...searchList.value);
      }
      console.log(searchDataList.value, "searchDataList");
    };
    const handleChange = (type) => {
      //search组件不触发change
      if (type != "a-input-search") {
        search();
      }
    };
    const handleFilter = (val) => {
      emit("update:filter", val);
      search();
    };
    return {
      searchDataList,
      toggleMore,
      toggleMoreSwitch,
      searchList,
      initialShowCount,
      arrayOfObjects,
      search,
      handleChange,
      handleFilter,
    };
  },
};
</script>
<style lang="less" scoped>
.search {
  padding-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  .options {
    float: right;
    flex-wrap: wrap;
  }
}
</style>

<style scoped lang="less">
// :deep(.ant-select:not(.ant-select-disabled):hover .ant-select-selector) {
//   border-color: @primary-color;
//   // box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.1);
// }
// :deep(.ant-input-affix-wrapper:focus) {
//   border-color: @primary-color;
//   // box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.1);
// }
// :deep(.ant-input-affix-wrapper:hover) {
//   border-color: @primary-color;
//   // box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.1);
// }
:deep(.ant-select-arrow) {
  z-index: 2;
}
:deep(.ant-input) {
  border-radius: 4px;
}
:deep(.ant-picker) {
  border-radius: 4px;
}
:deep(.ant-select-selector) {
  border-radius: 4px;
}
:deep(.ant-input-search-button:hover .anticon-search) {
  color: theme("colors.primary");
}
:deep(.ant-select-arrow) {
  width: 16px;
  height: 16px;
  font-size: 16px;
  margin-top: -8px;
}
:deep(
    .ant-input-search
      > .ant-input-group
      > .ant-input-group-addon:last-child
      .ant-input-search-button:not(.ant-btn-primary)
  ) {
  color: rgba(0, 0, 0, 0.25);
}
:deep(.anticon-reload) {
  color: rgba(0, 0, 0, 0.25);
}
.ant-button:hover .anticon-reload {
  color: theme("colors.primary");
}
:deep(.ant-btn:active .anticon-reload) {
  color: theme("colors.primary");
}
.ant-btn:hover,
.ant-btn:focus,
.ant-btn:active {
  :deep(.anticon-reload) {
    color: theme("colors.primary");
  }
}
</style>
