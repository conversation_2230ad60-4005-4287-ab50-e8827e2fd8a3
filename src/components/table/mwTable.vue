<template>
  <div class="mw-table">
    <a-config-provider
      :transformCellText="({ text }) => (text === '' ? '-' : text)"
    >
      <template #renderEmpty>
        <empty-data :description="emptyText">
          <slot name="empty">
            <a-empty :image="simpleImage" />
          </slot>
        </empty-data>
      </template>
      <a-table
        :pagination="
          hasPage ? { ...pagination, position: ['bottomCenter'] } : false
        "
        v-bind="$attrs"
      >
        <template v-for="(index, name) in $slots" v-slot:[name]="slotProps">
          <slot :name="name" v-bind="slotProps" />
        </template>
      </a-table>
    </a-config-provider>
  </div>
</template>
<script setup>
import { computed, toRefs, useSlots, defineProps, ref } from "vue";
import { Empty } from "ant-design-vue";
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const props = defineProps({
  hasPage: { type: Boolean, default: false },
  pageConfig: {
    type: Object,
    default: () => ({}),
  },
  emptyText: {
    type: String,
    default: "暂无数据",
  },
});
const pagination = computed(() => {
  return {
    ...props.pageConfig,
    showTotal: (total) => `总计 ${total} 条数据`,
  };
});
</script>
<style lang="less" scoped>
.mw-table {
  :deep(.ant-table) {
    color: theme("colors.title");
  }
  //封装得a-table，滚动条当内容超出时，没有实际效果，
  // :deep(.ant-table) {
  //   overflow-x: scroll;
  // }

  :deep(.mw-table .ant-table .ant-table-tbody > tr > td) {
    border-bottom: 0px solid #f0f0f0;
  }

  :deep(.ant-table-tbody > tr.ant-table-expanded-row > td) {
    background: #fff;
    padding: 10px 0 0 0;
  }
  :deep(.ant-table-thead) {
    background: rgba(34, 34, 34, 0.04);
    height: 34px;
    border-radius: 8px;
  }

  :deep(.ant-table-thead > tr > th) {
    background: #f0f0f0;
    font-weight: 400;
    color: rgba(34, 34, 34, 0.65);
    padding: 10px 12px;
    line-height: 20px;
    height: 40px;
    border: none;
  }

  :deep(
      .ant-table-thead
        > tr
        > th:not(:last-child):not(.ant-table-selection-column):not(
          .ant-table-row-expand-icon-cell
        ):not([colspan])::before
    ) {
    display: none;
    border: none;
    border-color: transparent;
    width: 0;
    height: 0;
  }
  :deep(.ant-table-container table > thead > tr:first-child th:first-child) {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }
  :deep(.ant-table-container table > thead > tr:first-child th:last-child) {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }
  :deep(tr.ant-table-expanded-row > td, tr.ant-table-expanded-row:hover > td) {
    background: theme("colors.background");
  }
}
</style>
