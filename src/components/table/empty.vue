<template>
  <div class="mx-auto" style="width: 400px">
    <div class="mx-auto mb-6" style="width: 400px; height: 240px">
      <img src="@/assets/empty.png" alt="" srcset="" />
    </div>
    <div class="text-base mb-2">{{ currentPage.title }}</div>
    <div class="text-secondar-text leading-5.5 space-y-2">
      <template v-for="item in process" :key="item.name">
        <div class="empty-title" :class="item.name == name ? 'text-title' : ''">
          <span class="align-middle">{{ item.content }}</span>
        </div>
      </template>
      <a-button shape="round" @click="handleClick()" primary>{{
        currentPage.btnText
      }}</a-button>
    </div>
  </div>
</template>
<script setup>
import { computed, ref, defineEmits, defineProps } from "vue";
import { useRoute, useRouter } from "vue-router";
import { updateGuidePage } from "@/api/login.js";
import { useUserStore } from "@/stores/user.js";
const router = useRouter(),
  route = useRoute(),
  store = useUserStore();
const emit = defineEmits(["createNew"]);
const props = defineProps({
  name: {
    type: String,
    default: "",
  },
});
const process = ref([
  {
    name: "Production",
    title: "当前没有生产计划",
    btnText: "新建计划",
    content:
      "您可以创建一个新的生产计划！创建完成后即可对生产全流程进行数字化管理",
  },
  {
    name: "Material",
    title: "当前没有仓库没有物料",
    btnText: "新增物料",
    content: "执行生产流程需要新增必备物料，这样才能进行BOM的构建",
  },
  {
    name: "Bom",
    title: "当前没有BOM计划",
    btnText: "新增BOM",
    content:
      "BOM是一款产品生产的基础物料构造成清单，可以对物料库存与调拨进行管理优化",
  },
  {
    name: "ProductionLine",
    title: "当前没有生产线",
    btnText: "新增生产线",
    content: "生产线关系到生产计划具体落地执行，对现有生产线的构造管理非常重要",
  },
  {
    name: "Product",
    title: "当前产品库没有产品",
    btnText: "新增产品",
    content: "产品是生产计划中的最核心元素，如何生存、物料配制非常重要",
  },
  {
    name: "Role",
    title: "当前没有角色",
    btnText: "新增管理角色",
    content:
      "生产的各个环节都需要各类角色参与，创建好管理角色可以使生产高效运转",
  },
]);
const handleClick = async () => {
  const index = process.value.findIndex((item) => item.name == props.name);
  if (index == process.value.length - 1) {
    // 做点啥
    await updateGuidePage(true);
    store.getInfo();
  } else {
    router.push({ name: process.value[index + 1].name });
  }
};
const currentPage = computed(() => {
  return process.value.find((item) => item.name == props.name);
});
</script>

<style lang="less" scoped>
.empty-title {
  position: relative;
  padding-left: 12px;
  &::after {
    display: block;
    content: "";
    width: 4px;
    height: 4px;
    border-radius: 4px;
    background: theme("colors.secondar-text");
    position: absolute;
    left: 0;
    top: 9px;
  }
  &.text-title {
    &::after {
      background: theme("colors.title");
    }
  }
}
</style>
