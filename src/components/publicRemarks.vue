<template>
  <!-- 备注公共组件 -->
  <mw-drawer
    :visible="visible"
    @close="closeRemarkDrawer"
    :customTitle="publicRemarksTitle"
  >
    <template #header>
      <mw-button
        shape="round"
        v-if="!prohibit"
        type="primary"
        @click="submitForm"
        :loading="submitRemarkLoading"
        >确定</mw-button
      >
    </template>

    <a-form ref="formRef" :model="formData" layout="horizontal" :colon="false">
      <div class="mb-5" v-if="remarkType == 'qualityInspectionRemarks'">
        <b>采购订单备注:</b>
        <p>{{ remarkRecord.purchaseOrderRemark }}</p>
      </div>

      <a-form-item label="" name="arrivalRemark" v-if="!prohibit">
        <b class="mb-2" v-if="remarkType !== 'bomRemark'"
          >{{ publicRemarksTitle }}:</b
        >
        <a-textarea
          :rows="10"
          v-model:value="formData.arrivalRemark"
          placeholder="请输入备注信息"
          allow-clear
        ></a-textarea>
      </a-form-item>
      <a-form-item label="" name="arrivalRemark" v-if="prohibit">
        <b class="mb-2">{{ publicRemarksTitle }}:</b>
        <div>
          {{ formData.arrivalRemark }}
        </div>
      </a-form-item>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  onBeforeMount,
  reactive,
  ref,
  getCurrentInstance,
  computed,
  watch,
  defineEmits,
  defineProps,
} from "vue";
import { updateMaterialArrivalRecordRemark } from "@/api/purchase/order.js";
import { postQualityTestRemark } from "@/api/quality.js";
import { bomUpdateBomRemark } from "@/api/basicData/orderBomChange.js";
const formData = ref({
  arrivalRemark: "",
});
const formRef = ref();
const submitRemarkLoading = ref(false);
const { proxy } = getCurrentInstance();
const emit = defineEmits(["update:visible", "finish"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  publicRemarksTitle: {
    type: String,
    default: "",
  },
  remarkType: {
    type: String,
    default: "",
  },
  remarkRecord: {
    type: Object,
    default: () => {},
  },

  prohibit: {
    type: Boolean,
    default: false,
  },
});
const closeRemarkDrawer = () => {
  formRef.value.resetFields();
  emit("update:visible", false);
};
const submitForm = () => {
  formRef.value
    .validate()
    .then(async () => {
      submitRemarkLoading.value = true;
      let res;
      if (props.remarkType == "qualityInspectionRemarks") {
        res = await postQualityTestRemark({
          testNo: props.remarkRecord.testNo,
          remark: formData.value.arrivalRemark,
        });
      }
      if (props.remarkType == "bomRemark") {
        res = await bomUpdateBomRemark({
          bomNo: props.remarkRecord.bomNo,
          bomRemark: formData.value.arrivalRemark,
        });
      } else {
        res = await updateMaterialArrivalRecordRemark({
          recordNumber: props.remarkRecord.recordNumber,
          arrivalRemark: formData.value.arrivalRemark,
        });
      }
      if (res.code == 200) {
        proxy.$message.success("备注成功");
        closeRemarkDrawer();
        emit("finish");
      }
      submitRemarkLoading.value = false;
    })
    .catch((error) => {
      submitRemarkLoading.value = false;
    });
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      if (props.remarkRecord) {
        formData.value.arrivalRemark =
          props.remarkType == "bomRemark"
            ? props.remarkRecord.bomRemark
            : props.remarkRecord.arrivalRemark;
      }
    }
  },
  { deep: true }
);
</script>
<style lang="scss" scoped></style>
