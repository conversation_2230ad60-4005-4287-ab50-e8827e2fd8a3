<template>
  <a-input
    autocomplete="off"
    placeholder="短信验证码"
    :value="value"
    @input="$emit('update:value', $event.target.value)"
    :maxlength="6"
    v-bind="$attrs"
    size="large"
  >
    <template #prefix> <slot></slot> </template>
    <template #suffix>
      <a-button
        v-if="disableTime == 0"
        type="link"
        @click="onChangeCode"
        style="
          height: 22px;
          line-height: 22px;
          padding: 0;
          position: absolute;
          right: 4px;
        "
        >｜获取验证码</a-button
      >
      <span class="disable-time" v-else>｜倒计时{{ disableTime }}s</span>
    </template>
  </a-input>
</template>
<script>
import { defineComponent, toRefs, getCurrentInstance, ref } from "vue";
import {
  getSmsCode,
  getDingtalkSmsCode,
  getTenantByMobilePhone,
} from "@/api/login.js";
import { useDisableTime } from "@/common/setup/index.js";
import { mobile } from "@/common/reg";
import { env } from "dingtalk-jsapi";

export default defineComponent({
  props: {
    phone: {
      type: String,
    },
    skipTime: {
      type: Number,
      default: 60,
    },
    value: String,
    smsVerifyType: String,
    thirdUserIdentify: String,
    type: String,
    optionsLength: String,
    isInDingtalk: String,
  },
  setup(props, { emit }) {
    const { phone, thirdUserIdentify, type, optionsLength, isInDingtalk } =
      toRefs(props);
    const { disableTime, setDisableTime } = useDisableTime();
    const { proxy } = getCurrentInstance();
    const agentIdVal = ref();
    const tenantIdVal = ref();
    const onChangeCode = async () => {
      if (!mobile.test(phone.value)) {
        proxy.$message.error("请填写正确的手机号码");
      } else {
        let res = await getTenantByMobilePhone(phone.value);
        // emit("onChangeCode", );
        res.data.forEach((item) => {
          if (item.index == type.value) {
            agentIdVal.value = item.agentId;
            tenantIdVal.value = item.tenantId;
          } else {
            if (res.data.length <= 1) {
              tenantIdVal.value = item.tenantId;
            }
          }
        });
        emit("onChangeCode", res, type.value); //这里的入参代表没选择企业前面的传参
        if (res.data.length > 1 && !type.value && !isInDingtalk.value) {
          proxy.$message.warning("请选择企业");
          return;
        }

        setDisableTime();

        emit(
          "onChangeCode",
          res,
          type.value,
          agentIdVal.value,
          tenantIdVal.value
        ); //这里的入参代表选择企业后面再次的传参

        if (env.platform != "notInDingTalk") {
          //如果在钉钉内部
          await getDingtalkSmsCode(phone.value, thirdUserIdentify.value);
        } else {
          await getSmsCode(phone.value, agentIdVal.value, tenantIdVal.value);
        }
        // return;
      }

      // emit("onChangeCode", async () => {
      //   setDisableTime();
      //   let result = await service.getSmsCode(phone.value);
      // });
    };
    return {
      onChangeCode,
      disableTime,
    };
  },
});
</script>
<style lang="less" scoped>
.disable-time {
  color: #e5e5e5;
}

</style>
