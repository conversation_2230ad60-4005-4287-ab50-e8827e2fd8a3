<template>
	<div v-loading="loading">
		<div class="signature">
			<div class="signature-content" v-show="!signatureSwitch">
				<div class="signature-content-inside">
					<canvas class="canvas" v-show="!signatureSwitch" />
				</div>
			</div>
			<div class="signatureConImg" v-show="signatureSwitch">
				<img id="signatureImg" class="signatureImg" src="" alt="" />
			</div>
		</div>

		<!-- <img width="480" id="signatureImg" height="16" :src="base64img" alt=""> -->
		<div class="signature-buttoms" v-show="!signatureSwitch">
			<mw-button @click="click_del" class="mt-3">重新签名</mw-button>
			<mw-button @click="click_submit" class="mt-3">确认</mw-button>
		</div>
	</div>
</template>
<script>
import { onMounted, reactive, ref, toRefs } from "vue";
import SignaturePad from "signature_pad";
const base64img = ref();

export default {
	props: {
		signatureObj: {
			type: Object,
			default: () => {
				return {
					height: 240,
					width: 460,
				};
			},
		},
		// signatureSwitchs: {
		//   type: Object,
		//   default:false
		// },
	},

	setup(props, { emit }) {
		const signatureSwitch = ref(false);

		const data = reactive({
			signaturePad: null, // 存放竖屏SignaturePad对象
			signature_value: null,
			loading: false,
		});
		// 初始化画板
		function init() {
			signatureSwitch.value = false;
			let canvas = document.querySelector(".canvas");
			data.signaturePad = new SignaturePad(canvas, {
				minWidth: 2,
				maxWidth: 2,
				penColor: "rgb(0, 0, 0)",
			});
			// 画板高度
			canvas.height = props.signatureObj.height;
			// 画板宽度
			canvas.width = document.body.clientWidth - props.signatureObj.width;
		}
		// 清除画板
		function click_del() {
			data.signaturePad.clear();
		}
		// 确认画板

		function click_submit() {
			var name = document.getElementById("signatureImg");
			name.src = data.signaturePad.toDataURL("image/jpg", 0.1);
			data.signature_value = data.signaturePad.toDataURL("image/jpg");
			data.loading = true;
			setTimeout(() => {
				data.loading = false;
				signatureSwitch.value = true;
				data.signaturePad.clear();
				emit("signatureSwitch", signatureSwitch.value);
			}, 100);
		}
		onMounted(() => {
			init();
			console.log(signatureSwitch, "signatureSwitch");
		});
		return {
			...toRefs(data),
			click_del,
			click_submit,
			props,
			signatureSwitch,
			init,
		};
	},
};
</script>
<style lang="less" scoped>
.signature-content {
	width: 100%;
	background: #eee;
	overflow: hidden;
	margin-top: 10%;
}
.signature-buttoms {
	display: flex;
	justify-content: flex-end;
}
.signature-buttoms button:nth-child(1) {
	margin-right: 10px;
}
.signatureConImg {
	display: flex;
	justify-content: flex-end;
}
.signatureImg {
	width: 68%;
	display: flex;
	justify-content: flex-end;
	margin-bottom: 2%;
	margin-top: 2%;
}
.signature {
	&-content {
		padding: 15px;
		width: 100%;
		height: auto;
		background-color: #ccc;
		display: flex;
		justify-content: center;

		&-inside {
			display: flex;
			position: relative;
			background-color: rgb(242, 242, 242);
		}
	}
	&-buttoms {
		width: 214px;
		margin: 0px auto;
	}
}
</style>
