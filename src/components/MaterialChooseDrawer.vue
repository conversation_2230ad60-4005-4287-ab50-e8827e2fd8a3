<template>
  <mw-drawer :visible="visible" @close="onClose()" customTitle="物料选择">
    <template #header>
      <a-button shape="round" type="primary" @click="addMaterialOnSubmit"
        >确定</a-button
      >
    </template>
    <div>
      <div>
        <a-form
          ref="formRef"
          :model="formData"
          :rules="addMaterialRules"
          layout="horizontal"
          :colon="false"
        >
          <a-form-item name="materialId">
            <template #label>
              <div class="w-15 text-left text-primary-text">物料名称</div>
            </template>
            <MaterialSelect
              v-model:value="formData.materialId"
              @change="handleChangeMaterial"
            />
          </a-form-item>
          <a-form-item>
            <template #label>
              <div class="w-full text-left text-primary-text">物料基本信息</div>
            </template>
          </a-form-item>
          <div class="p-4 bg-background rounded space-y-3">
            <div class="flex leading-5.5">
              <div class="w-17 text-primary-text">物料编码</div>
              <div class="flex-1 w-0">
                {{ selectedMaterial.materialNo || "-" }}
              </div>
            </div>
            <div class="flex leading-5.5">
              <div class="w-17 text-primary-text">物料规格</div>
              <div class="flex-1 w-0">
                {{ selectedMaterial.specification || "-" }}
              </div>
            </div>
            <div class="flex leading-5.5">
              <div class="w-17 text-primary-text">一级分类</div>
              <div class="flex-1 w-0">
                {{ selectedMaterial.classificationParentName || "-" }}
              </div>
            </div>
            <div class="flex leading-5.5">
              <div class="w-17 text-primary-text">二级分类</div>
              <div class="flex-1 w-0">
                {{ selectedMaterial.classificationName || "-" }}
              </div>
            </div>
            <div class="flex leading-5.5">
              <div class="w-17 text-primary-text">库存数量</div>
              <div class="flex-1 w-0">
                {{ selectedMaterial.total }}
              </div>
            </div>
          </div>
        </a-form>
      </div>
    </div>
  </mw-drawer>
</template>
<script setup>
import {
  defineProps,
  defineEmits,
  ref,
  getCurrentInstance,
  watch,
  reactive,
} from "vue";
import { AllList, getInfo } from "@/api/basicData/material.js";
const { proxy } = getCurrentInstance();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["update:visible", "choose"]);
const formRef = ref(),
  selectedMaterial = ref({}),
  formData = reactive({
    materialId: undefined,
    // quantity: 1,
  }),
  addMaterialRules = reactive({
    materialId: [
      {
        required: true,
        message: "请选择物料",
        trigger: "blur",
      },
    ],
  });
const handleChangeMaterial = async (value) => {
  // 获取选中的物料信息
  let res = await getInfo(value);
  selectedMaterial.value = res.data;
};
// 确认新增物料
function addMaterialOnSubmit() {
  formRef.value.validate().then(async () => {
    emit("choose", selectedMaterial.value);
    onClose();
  });
}
const onClose = () => {
  formRef.value.resetFields();
  selectedMaterial.value = {};
  emit("update:visible", false);
};
</script>
