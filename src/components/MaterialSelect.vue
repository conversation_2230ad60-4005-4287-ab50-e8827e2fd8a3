<template>
  <a-select
    :value="valueKey"
    show-search
    :filter-option="filterOption"
    style="width: 100%"
    :default-active-first-option="false"
    :options="allMaterialList"
    @change="handleChangeMaterial"
    @search="handleSearch"
    placeholder="请输入物料编码/名称"
  ></a-select>
</template>

<script setup>
import { onBeforeMount, ref } from "vue";
import { AllList, AllPage } from "@/api/basicData/material.js";
import { debounce } from "lodash";
const props = defineProps({
  value: {
    type: String,
    default: "",
  },
  materiaSelect: {
    type: Object,
    default: () => {
      return {};
    },
  },
  showSpecification: {
    type: Boolean,
    default: false,
  },
});
const valueKey = ref(props.value || void 0);
const emit = defineEmits(["update:value", "change"]);
const allMaterialList = ref([]),
  value = ref(props.value || void 0);
async function getMaterialList(materialName, materiaSelect = {}, keyword) {
  let result = await AllPage({
    materialName: materialName,
    ignoreStop: true,
    ...materiaSelect,
    keyword,
  });
  let { data } = result;
  allMaterialList.value = data.map((item) => {
    return {
      label: item.fileNumber
        ? `${item.materialNo}\u0020/\u0020${item.materialName}\u0020/\u0020${item.specification}\u0020/\u0020${item.fileNumber}`
        : `${item.materialNo}\u0020/\u0020${item.materialName}\u0020/\u0020${item.specification}`,
      value: item.id,
      ...item,
    };
  });
}

onBeforeMount(() => {
  // 获取物料列表
  getMaterialList(void 0, props.materiaSelect);
});
const debouncedSearch = debounce(async (e) => {
  console.log("[ e ] >", e);
  getMaterialList(void 0, props.materiaSelect, e);
  //
}, 300);
const handleSearch = async (e) => {
  debouncedSearch(e);
  //
};
//   选中物料
const handleChangeMaterial = async (value, option) => {
  // 获取选中的物料信息
  valueKey.value = value;
  await emit("change", value, option);
  await emit("update:value", value);

  console.log(value, option);
};
const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
</script>

<style lang="scss" scoped></style>
