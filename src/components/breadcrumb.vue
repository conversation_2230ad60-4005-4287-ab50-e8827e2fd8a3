<template>
  <a-breadcrumb class="content-area my-4">
    <!--  text-ff bg-title -->
    <div
      class="hover-border inline flexC w-8 h-8 mr-1 rounded text-title cursor-pointer text-center"
      @click="$router.go(-1)"
    >
      <i class="iconfont icon-jichu-you mt-0.5"></i>
    </div>
    <a-breadcrumb-item v-for="item in routes" :key="item.name">
      {{ item.meta.title }}
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script setup>
import { computed } from "vue";
import { useRoute, useRouter } from "vue-router";
const route = useRoute(),
  router = useRouter();
const routes = computed(() => {
  let matchedRoutes = route.matched;
  let result = [];
  if (matchedRoutes?.length && matchedRoutes[0]?.children?.length) {
    // 这边特殊处理，比如菜单material的子页面是materialCategory
    //很多物料和产品的模块是公用的菜单，用type来区分
    let routeName = ((route.query.type || "") + route.name).toLowerCase();
    let parent = matchedRoutes[0].children.find(
      (item) =>
        routeName.includes(item.name.toLowerCase()) && route.name != item.name
    );
    parent && (result[0] = parent);
  }
  result = [...result, route];
  return result;
});
</script>

<style lang="less" scoped>
.flexC {
  display: flex;
  justify-content: center;
  //color: #fff;
  align-items: center;
  // background: rgba(34, 34, 34, 0.65);
}
.hover-border:hover {
  background: #1890ff; /* 设置悬停时边框的颜色 */
  color: #fff;
}
.iconfont {
  font-size: 20px !important;
}

.content-area {
  :deep(ol) {
    align-items: center;
  }
}
</style>
