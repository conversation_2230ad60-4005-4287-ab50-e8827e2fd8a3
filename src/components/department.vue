<template>
  <!-- <div v-if="typeClick == 'detail'">
    <div class="select-header prohibit" @click="toggleOptions">
      <span :class="customerManagerName ? '' : 'select-header-color'">{{
        customerManagerName ? customerManagerName : "请选择客户经理"
      }}</span>
      <i :class="{ 'arrow-up': showOptions, 'arrow-down': !showOptions }"></i>
    </div>
  </div> -->
  <div class="custom-select">
    <div class="select-header" @click="toggleOptions">
      <span :class="customerManagerName ? '' : 'select-header-color'">{{
        customerManagerName ? customerManagerName : "请选择客户经理"
      }}</span>
      <i :class="{ 'arrow-up': showOptions, 'arrow-down': !showOptions }"></i>
    </div>
    <div class="select-options" v-show="showOptions">
      <div style="border-right: 1px solid #e0e3e9">
        <div class="title-name">部门</div>
        <a-tree
          :defaultExpandAll="true"
          :tree-data="deptTreeData"
          :fieldNames="{
            children: 'children',
            title: 'label',
            key: 'id',
          }"
          v-model:selectedKeys="selectedKeys"
          @select="handleDeptChange"
        />
      </div>

      <div class="staffTree" style="">
        <div class="title-name">人员</div>
        <div
          class="ml-2 personnel"
          v-for="(item, index) in staffTreeData"
          :key="index"
          @click="onSiteTreeLi(item.nickName, item.userId)"
        >
          <span
            :class="customerManagerId == item.userId ? 'calm' : 'personnelList'"
          >
            <a-icon component="UserOutlined" />
            <UserOutlined />{{ item.nickName }}</span
          >
        </div>
      </div>
      <!-- <div v-else class="notHave">暂无数据</div> -->
    </div>
  </div>
</template>

<script setup>
import { deptTreeSelect, list, alldeptTreeSelect } from "@/api/system/user.js";
import { ref, watch } from "vue";
import { TreeSelect } from "ant-design-vue";
const emit = defineEmits(["update:value"]);
const deptTreeData = ref([]);
const staffTreeData = ref([]);
const showOptions = ref(false);
const selectedKeys = ref([]);
const customerManagerId = ref();
const customerManagerName = ref("");
const nodeLabelSwitch = ref(false);
// 客户
const customList = async () => {
  let result = await deptTreeSelect();
  deptTreeData.value = result.data;
};
const memberList = async (value) => {
  let params = {
    pageNum: 1,
    pageSize: 999999,
    sortOrder: "desc",
    deptId: value[0],
  };
  let res = await list({ ...params });
  staffTreeData.value = res.data;
};
const handleDeptChange = (value, info) => {
  nodeLabelSwitch.value = false;
  // customerManagerBelongDeptName = info.node.label;

  memberList(value);
};
const toggleOptions = () => {
  showOptions.value = !showOptions.value;
  memberList(selectedKeys.value);
  customList();
};
const onSiteTreeLi = (val, userId) => {
  customerManagerName.value = val;
  customerManagerId.value = userId;
  nodeLabelSwitch.value = true;
  showOptions.value = false;
  emit("update:value", userId);
};
watch(() => async () => {
  customList();
});
</script>

<style lang="less" scoped>
.custom-select {
  position: relative;
  z-index: 99;
}
.prohibit {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  box-shadow: none;
  cursor: not-allowed;
  opacity: 1;
}
.select-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  border: 1px solid #ddd;
  // width: 50%;
  cursor: pointer;
}
.select-header-color {
  color: #ccc;
  background-color: #fff;
}

.select-header span {
  margin-right: 8px;
}

.select-header i {
  font-size: 12px;
  transition: transform 0.3s;
}

.select-header .arrow-up {
  transform: rotate(-180deg);
}

.select-options {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  /* max-height: 200px; */
  height: 300px;
  /* overflow-y: auto; */
  background-color: #fff;
  border: 1px solid #ddd;
  display: flex;
  box-shadow: 0 0 16px #ddd;
}

.select-options .option {
  padding: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.select-options .option:hover {
  background-color: #f5f5f5;
}
.staffTree {
  width: 50%;
  div {
    display: block;
    height: 35px;
    padding-left: 10%;
    width: 100%;
    line-height: 35px;
  }
  span:hover {
    background-color: #f0f0f0;
    padding: 5px 2px;
    transform: scale(1);
  }
}
.calm {
  background-color: #bae7ff;
  padding: 5px 2px;
}
.notHave {
  text-align: center;
}
.title-name {
  text-align: center;
  height: 35px;
  line-height: 35px;
  border-bottom: 1px solid #ddd;
}
.personnel {
  text-align: center;
}
</style>
