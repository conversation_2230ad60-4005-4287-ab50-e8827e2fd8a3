<template>
  <div style="color: #959ec3">
    <img v-if="url" :src="url" alt="" class="w-full h-full cover rounded" />
    <div
      v-else-if="name"
      class="w-full h-full text-center flex justify-center flex-col rounded text-xl"
      style="background: rgba(149, 158, 195, 0.1)"
    >
      <span :class="size == 'small' ? 'text-base' : 'text-xl'">
        {{ name.slice(0, 2) }}</span
      >
      <span v-if="size != 'small'">{{ name.slice(2, 4) }}</span>
    </div>
  </div>
</template>
<script>
import { toRefs } from "vue";
export default {
  props: {
    url: String,
    name: String,
    size: {
      type: String,
      default: () => "normal",
    },
  },
  setup(props) {
    const { url, name, size } = toRefs(props);
  },
};
</script>
