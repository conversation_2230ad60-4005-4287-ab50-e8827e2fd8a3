<template>
  <template v-if="(statusObj.color && showBadge) || isBackgroundColor">
    <span v-if="isTextColor" :style="{ color: statusObj.color }">
      {{ label }}
    </span>
    <span v-if="label">
      <span
        v-if="isBackgroundColor"
        class="isBackgroundColor rounded-sm"
        :style="{
          fontSize: '12px',
          background: statusObj.backGroundColor || 'rgba(0,0,0,.1)',
          color: statusObj.color || '#141414',
          height: '22px',
          lineHeight: '22px',
        }"
      >
        {{ label ? label : "--" }}
      </span>
      <!-- <a-badge v-else :color="statusObj.color" :text="label" /> -->
      <span v-else>
        <b
          class="iconBadge"
          :style="{
            backgroundColor: statusObj.color,
          }"
        ></b>
        <!--  boxShadow: `0px 0px 2px 0px ${statusObj.shadowColor}`, -->
        <span class="text">{{ label }}</span>
      </span>
    </span>
  </template>
  <span v-else>{{ label }}</span>
</template>
<script>
import { toRefs, computed, watch, toRef, reactive } from "vue";
export default {
  props: {
    statusOptions: {
      type: Array,
      default: () => [],
    },
    value: [String, Number],
    labelKey: {
      type: String,
      default: "label",
    },
    isTextColor: {
      type: Boolean,
      default: false,
    },
    isBackgroundColor: {
      type: Boolean,
      default: false,
    },
    showBadge: {
      type: Boolean,
      default: true,
    },
    statusExtend: {
      type: String,
      default: "",
    },
  },
  setup(props) {
    const { statusOptions, value, isBackgroundColor, labelKey, statusExtend } =
      toRefs(props);
    const statusObj = computed(() => {
      const item =
        statusOptions.value.find((s) => s.value === value.value) || {};
      return item;
    });
    const label = computed(() => {
      if(!labelKey.value) return ''
      let result = statusObj.value[labelKey.value];
      if (value.value == 7 && statusExtend.value) {
        result += `[${statusExtend.value}]`;
      }
      return result;
    });
    // const state = reactive({
    //   backgroundColor: false,
    // });
    // watch(
    //   isBackgroundColor,
    //   (val) => {
    //     state.backgroundColor = val;
    //   },
    //   { immediate: true }
    // );
    return {
      statusObj,
      label,
      // ...toRefs(state),
    };
  },
};
</script>
<style lang="less" scoped>
.badge {
  display: flex;
  align-items: center;
  .circle {
    width: 10px;
    height: 10px;
    background: rgba(255, 125, 0, 1);
    box-shadow: 0px 0px 4px 0px rgba(255, 125, 0, 0.5);
    border: 1px solid #ffffff;
    border-radius: 50%;
    margin-right: 4px;
  }
}
.isBackgroundColor {
  display: inline-block;
  height: 100%;
  padding: 0 8px;
}
.iconBadge {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  // border: 1px solid #ffffff;
  vertical-align: middle;
  margin-right: 4px;
  margin-top: 0;
}
.text {
  vertical-align: middle;
}
</style>
