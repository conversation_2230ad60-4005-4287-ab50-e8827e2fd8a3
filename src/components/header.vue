<meta name="wpk-bid" content="dta_2_134079" />
<template>
  <div
    :style="{ backgroundColor: $route.meta.headerBg }"
    class="border-b border-d9"
  >
    <a-layout-header
      :style="{ backgroundColor: $route.meta.headerBg || '#f5f5f5' }"
      v-if="visible"
    >
      <!-- bg-ff头部背景色 -->
      <div class="">
        <div class="header w-content-area xxl:w-full mx-auto my-0 xxl:m-auto">
          <!-- 左侧logo -->
          <div class="relative leading-6 hidden md:block">
            <div
              class="logo cursor h-full float-right flex"
              @click="$router.push({ name: 'SubscriptionNotice' })"
            >
              <img class="h-11" :src="logoUrl" alt="" />

              <div class="ml-3">
                <p class="font-zie">智能制造</p>
                <p class="menu-item-text hidden lg:block">
                  {{ userStore.tenantName }}
                </p>
              </div>
              <div class="ml-3 flex flex-col justify-end" @click.stop>
                <a-dropdown placement="bottomLeft" :trigger="['click']">
                  <template #overlay>
                    <a-menu>
                      <a-menu-item
                        v-for="item in tenatList"
                        :key="item.tenantId"
                        @click="onChangeTenant(item)"
                        >{{ item.tenantName }}</a-menu-item
                      >
                    </a-menu>
                  </template>
                  <a-button type="primary" shape="round" size="small">
                    <template #icon>
                      <div class="flex items-center gap-x-1">
                        <SwapOutlined />
                        切换组织
                      </div>
                    </template>
                  </a-button>
                </a-dropdown>
              </div>
            </div>
          </div>
          <!-- 中间的菜单 -->
          <div class="element">
            <div class="flex items-center h-full content">
              <template v-for="(item, index) in topMenus" :key="index">
                <div
                  v-if="!item.hidden"
                  class="xxl:px-6 px-6 menu-item relative cursor-pointer content"
                  :class="[
                    isMenuActive(item.name) && 'active ',
                    userStore.$state.cacheObject ? 'auto' : 'disabled',
                  ]"
                  @click="goto(item)"
                >
                  <div
                    class="flex flex-row justify-center items-center leading-8"
                  >
                    <i
                      class="menu-item-icon iconfont"
                      :class="item.meta.icon"
                    ></i>
                    <div
                      class="menu-item-text whitespace-nowrap"
                      :class="isMenuActive(item.name) && 'font-bold '"
                    >
                      {{ item.meta.title }}
                    </div>
                  </div>
                </div>
              </template>
            </div>

            <!-- <a-space style="margin-top: 22px" class="absolute right-4 setting" :size="16">
              <div
                class="w-11 h-11 bg-f5 rounded-full flex justify-center items-center cursor-pointer"
                @click="openNotice"
              >
                <a-badge
                  :count="$store.state.user.reminderCount"
                  :overflow-count="99"
                  :number-style="{
                    height: '12px',
                    lineHeight: '12px',
                    verticalAlign: 'middle',
                    padding: '0 6px'
                  }"
                  ><i class="iconfont icon-a-daohang-8beifen21 text-2xl leading-6"></i>
                </a-badge>
              </div>
              <div
                v-if="
                  $store.state.user.menuKeys.indexOf('ES_SETTING_MANAGER') >= 0 ||
                  $store.getters['user/isManager']
                "
                class="w-11 h-11 bg-f5 rounded-full flex justify-center items-center cursor-pointer"
                @click="$router.push('/configurationCenter')"
              >
                <i
                  class="iconfont icon-a-daohang-8beifen3 text-2xl leading-6"
                  :class="$route.name == 'configurationCenter' ? 'active' : ''"
                ></i>
              </div>
            </a-space> -->
          </div>
          <!-- 右侧的个人头像 -->
          <div class="relative">
            <div class="account">
              <span v-if="!getToken()">未登录</span>
              <div v-else class="flex items-center">
                <a-dropdown
                  overlayClassName="header-account-dropdown"
                  placement="bottomRight"
                >
                  <div class="welcome cursor flex items-center">
                    <div v-if="userStore.name || userStore.phone">
                      <div class="flex items-center">
                        <customer-logo
                          :url="userStore?.avatar?.fileVisitUrl"
                          :name="userStore?.name"
                          size="small"
                          class="w-12 h-12 rounded-full overflow-hidden mr-1"
                        ></customer-logo>
                        <!-- 左侧信息注释，误删，防止后期使用 -->
                        <!-- <div>
                          <div class="text-title flex items-center">
                            <span
                              class="align-middle mr-2 text-base leading-6 overflow"
                              style="max-width: 140px"
                            >
                              HI,{{ userStore.name }}</span
                            >
                            <i
                              class="iconfont icon-qt-2 text-59 text-sm align-middle"
                            ></i>
                          </div>
                          <div
                            class="text-primary-text overflow text-xs leading-5"
                            style="max-width: 160px"
                          >
                            {{ userStore.tenantName }}
                          </div>
                        </div> -->
                      </div>
                    </div>
                  </div>
                  <template #overlay>
                    <a-menu style="width: 164px">
                      <!-- 后期不确定是否继续使用，暂时注释 -->
                      <!-- <a-menu-item @click="openHelp"> 帮助中心 </a-menu-item> -->
                      <a-menu-item @click="openAi">
                        智能助 {{ isDingtalk ? "理" : "手" }}</a-menu-item
                      >
                      <a-menu-item @click="logout" v-if="!isInDingtalk">
                        退出登录
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-layout-header>
    <a-modal
      v-model:visible="iframeVisible"
      title="智能助手"
      :footer="null"
      :destroyOnClose="true"
      :width="850"
      @close="onModalClose"
    >
      <div>
        <iframe
          :src="iframeUrl"
          style="width: 100%; height: 640px"
          frameborder="0"
          @load="handleIframeLoad"
        ></iframe>
      </div>
    </a-modal>
  </div>
</template>

<script>
import {
  computed,
  nextTick,
  reactive,
  toRefs,
  ref,
  watch,
  onMounted,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import { enterpriseServiceDomain } from "@/config/env";
import { usePermissionStore } from "@/stores/permission.js";
import { useUserStore } from "@/stores/user.js";
// import service from '@/apiService'
import logoUrl from "@/assets/logo.jpg";
import CustomerLogo from "./CustomerLogo.vue";
import { getToken } from "@/utils/auth.js";
import { dictionary } from "@/api/applyForWarehouse/index.js";
import {
  getTenantByMobilePhone,
  loginInnerV1,
  loginInnerV2,
} from "@/api/login.js";
import { SwapOutlined } from "@ant-design/icons-vue";
import { runtime } from "dingtalk-jsapi";
import * as dd from "dingtalk-jsapi";

export default {
  components: { CustomerLogo, SwapOutlined },
  setup() {
    const store = usePermissionStore();
    const userStore = useUserStore();
    const topMenus = computed(() => {
      return store.topbarRouters;
    });
    const isInDingtalk = computed(() => {
      return false;
      // return env.platform != "notInDingTalk";
    });
    const route = useRoute(),
      router = useRouter(),
      selectedKeys = ref(["/"]);

    const gotopc = async (id) => {
      window.open(enterpriseServiceDomain, "_blank");
      // router.push("/userStore");
    };
    const gotoUserInfo = () => {
      router.push("/userStore");
    };
    const state = reactive({
      visible: true,
      buyVersionVisible: false,
      noticeVisible: false,
      buyTitle: "请选择购买正式版本的服务吧",
      path: "",
    });
    const logout = async () => {
      let result = await userStore.LogOut();
      state.visible = false;
      nextTick(() => {
        state.visible = true;
        router.push("/login");
      });
    };
    // 后期不确定是否继续使用，暂时注释
    // const openHelp = () => {
    //   window.open("https://www.yuque.com/mingwoqifu/wxo83q", "_blank");
    // };
    const openAi = () => {
      // router.push({
      // path: '/intelligent-assistant',
      // })
      iframeVisible.value = true;
    };
    const goto = (item) => {
      if (userStore.$state.cacheObject) {
        //默认进入目录下的第一个菜单
        let name = item.children?.length ? item.children[0].name : "home";
        store.sidebarRouters = item.children || [];
        router.push({ name });
      }
    };
    const getParentPath = (val) => {
      //根据当前路由，展示二级菜单
      if (val.startsWith("/")) {
        val = val.substring(1);
      }
      let parentPath = val.split("/")[0];
      return parentPath;
    };
    const isMenuActive = (name) => {
      let val = route.path;
      if (val.startsWith("/")) {
        val = val.substring(1);
      }
      let parentPath = val.split("/")[0];
      return name.toLowerCase() == parentPath.toLowerCase();
    };

    var isDingtalk = ref(navigator && /DingTalk/.test(navigator.userAgent));
    var isProductEnv =
      window &&
      window.location &&
      window.location.host &&
      window.location.host.indexOf("127.0.0.1") === -1 &&
      window.location.host.indexOf("localhost") === -1 &&
      window.location.host.indexOf("192.168.") === -1;
    // 如果有其它测试域名，请一起排掉，减少测试环境对生产环境监控的干扰
    if (isProductEnv) {
      !(function (c, i, e, b) {
        var h = i.createElement("script");
        var f = i.getElementsByTagName("script")[0];
        h.type = "text/javascript";
        h.crossorigin = true;
        h.onload = function () {
          c[b] || (c[b] = new c.wpkReporter({ bid: "dta_2_134079" }));
          c[b].installAll();
        };
        f.parentNode.insertBefore(h, f);
        h.src = e;
      })(
        window,
        document,
        "https://g.alicdn.com/woodpeckerx/jssdk??wpkReporter.js",
        "__wpk"
      );
    }
    watch(
      () => route.path,
      (val) => {
        //根据当前路由，展示二级菜单
        let current = topMenus.value.find((m) => isMenuActive(m.name));
        store.sidebarRouters = current?.children || [];
      },
      { immediate: true }
    );
    const iframeUrl = ref("");
    const dictionaryData = async () => {
      let res = await dictionary("sys_ai_url");
      console.log(res.data, 123123123123);
      let tenantId = userStore.$state.user.tenantId;
      iframeUrl.value = res.data.find(
        (item) => item.dictLabel == tenantId
      ).dictValue;
    };
    const iframeVisible = ref(false);
    const onModalClose = () => {
      iframeVisible.value = true;
    };
    const tenatList = ref([]);
    const phoneNumber = computed(() => {
      return (
        userStore.$state.user.phonenumber ||
        localStorage.getItem("user").phonenumber
      );
    });
    const getTenats = async () => {
      console.log(userStore.$state.user, 22);
      let res = await getTenantByMobilePhone(phoneNumber.value);
      tenatList.value = res.data;
    };
    onMounted(() => {
      dictionaryData();
      getTenats();
    });
    const handleIframeLoad = () => {
      //
    };
    // 综合方案：依次尝试多种钉钉 API
    const doLoginWithMultipleApproaches = async (item) => {
      // if (!isDingtalk.value) {
      let res = await loginInnerV2(
        {
          type: "mobile",
          agentId: item.agentId,
          mobile: phoneNumber.value,
          tenantId: item.tenantId,
        },
        item.tenantId
      );
      if (res.data.access_token) {
        location.reload();
      }
      userStore.setLoginData(res, "/");
      // }

      // if (isDingtalk.value) {
      //   const currentUrl = new URL(window.location.href);
      //   const corpId = currentUrl.searchParams.get("corpId");
      //   const type = currentUrl.searchParams.get("type");
      //   console.log("1.", dd);
      //   if (item.agentId) {
      //     localStorage.setItem("dingtalkCorpId", corpId);
      //     localStorage.setItem("dingtalkAppId", item.agentId);
      //     localStorage.setItem("dingtalkType", type || "1");

      //     // const targetUrl = `https://newdigitalizesit.mingwork.com/login?redirect=/?corpId=${corpId}&appid=${
      //     //   item.agentId
      //     // }&type=${type || "1"}`;
      //     const targetUrl = `http://192.168.99.44:3005/login?redirect=/?corpId=${
      //       item.tenantId
      //     }&appid=${item.agentId}&type=${type || "1"}`;
      //     console.log(2);

      //     // // 尝试方案一：openMicroApp
      //     // try {
      //     //   console.log("尝试使用 openMicroApp...");
      //     //   dd.openMicroApp({
      //     //     corpId: corpId,
      //     //     appId: item.agentId,
      //     //     path: "/",
      //     //     onSuccess: function (result) {
      //     //       console.log("openMicroApp 成功:", result);
      //     //       return;
      //     //     },
      //     //     onFail: function (err) {
      //     //       console.error("openMicroApp 失败，尝试下一个方案:", err);
      //     //       tryOpenLink();
      //     //     },
      //     //   });
      //     // } catch (error) {
      //     //   console.error("openMicroApp API 调用异常，尝试下一个方案:", error);
      //     //   tryOpenLink();
      //     // }
      //     // tryOpenLink();
      //     // // 尝试方案二：openLink
      //     // function tryOpenLink() {
      //     //   try {
      //     //     console.log("尝试使用 openLink...");
      //     //     dd.biz.util.openLink({
      //     //       url: targetUrl,
      //     //       onSuccess: function (result) {
      //     //         console.log("openLink 成功:", result);
      //     //         return;
      //     //       },
      //     //       onFail: function (err) {
      //     //         console.error("openLink 失败，尝试下一个方案:", err);
      //     //         tryNavigateToPage();
      //     //       },
      //     //     });
      //     //   } catch (error) {
      //     //     console.error("openLink API 调用异常，尝试下一个方案:", error);
      //     //     tryNavigateToPage();
      //     //   }
      //     // }
      //     tryNavigateToPage();
      //     // 尝试方案三：navigateToPage
      //     function tryNavigateToPage() {
      //       try {
      //         console.log("尝试使用 navigateToPage...");
      //         dd.navigateToPage({
      //           url: targetUrl,
      //           onSuccess: function (result) {
      //             console.log("navigateToPage 成功:", result);
      //             return;
      //           },
      //           onFail: function (err) {
      //             console.error("navigateToPage 失败，使用降级方案:", err);
      //             fallbackToWindowLocation();
      //           },
      //         });
      //       } catch (error) {
      //         console.error(
      //           "navigateToPage API 调用异常，使用降级方案:",
      //           error
      //         );
      //         fallbackToWindowLocation();
      //       }
      //     }

      //     // 最终降级方案：直接跳转
      //     function fallbackToWindowLocation() {
      //       console.log(
      //         "所有钉钉 API 都失败，使用 window.location.replace 降级方案"
      //       );
      //       window.location.replace(targetUrl);
      //     }
      //   } else {
      //     location.reload();
      //   }
      // } else {
      //   location.reload();
      // }
    };

    const onChangeTenant = async (item) => {
      console.log("切换组织:", item);

      // 选择你想要使用的方案：

      // 方案一：使用 openLink API（简单方案）
      // doLoginInnerV1(item);

      // 方案二：使用 openMicroApp API（备选）
      // doLoginWithOpenMicroApp(item);

      // 方案三：使用 navigateToPage API（备选）
      // doLoginWithNavigateToPage(item);

      // 方案四：综合方案，依次尝试多种 API（推荐）
      doLoginWithMultipleApproaches(item);
    };
    return {
      ...toRefs(state),
      gotopc,
      logout,
      gotoUserInfo,
      isInDingtalk,
      // openHelp,暂时注释
      openAi,
      goto,
      topMenus,
      logoUrl,
      userStore,
      getToken,
      isMenuActive,
      iframeUrl,
      iframeVisible,
      onModalClose,
      handleIframeLoad,
      tenatList,
      onChangeTenant,
      isDingtalk,
    };
  },
};
</script>

<style lang="less" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88px;
  line-height: 88px;
  // width: 100%;
  width: 90vw;
  .menu {
    overflow: hidden;
    position: relative;
    height: 100%;

    .content {
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch; /* 为iOS启用流畅滚动 */
    }
    .content::-webkit-scrollbar {
      width: 0;
      height: 0; /* 设置滚动条的高度为0（根据需求调整）*/
      background: transparent; /* 如果需要的话 */
      display: none;
    }
  }
}
/* 响应式布局样式 */
@media (max-width: 992px) {
  .sidebar {
    opacity: 0;
    transition: opacity 1s ease-in;
    display: none;
  }
}
.ant-layout-header {
  height: auto;
  padding: 0;
  // height: 60px;
  // line-height: 60px;
  // background-color: @body-background;
}
.logo {
  vertical-align: middle;
  flex: 1;
}
.welcome,
.phone {
  line-height: 22px;
}
.setting {
  .iconfont {
    color: theme("colors.primary-text");
    &.active {
      color: @primary-color;
    }
  }
}
.account {
  display: flex;
  align-items: center;
  .login-name {
    width: 32px;
    height: 32px;
    background: #ea0c28;
    border-radius: 50%;
    line-height: 32px;
    text-align: center;
    font-size: 12px;
    color: #fff;
    margin-right: 8px;
  }
}
.bubble:after {
  content: "";
  position: absolute;
  top: 8px;
  width: 0;
  height: 0;
  border-width: 8px;
  border-style: solid;
  border-color: transparent;
  border-right-width: 8px;
  border-right-color: currentColor;
  color: #000;
  left: -13px;
}
.bubble {
  line-height: 32px;
  border-radius: 32px;
  padding: 0 20px;
  margin-left: 10px;
  position: relative;
  display: inline-block;
  background: @primary-color;
  color: #fff;
}
.menu {
  &-item {
    display: block;
    // height: 88px;
    // line-height: 88px;
    // width: 100px;

    &::after {
      display: block;
      content: "";
      height: 32px;
      width: 85px;
      background: #fff;
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      transition: width 0.5s ease;
      border-radius: 20px;
    }
    &.disabled {
      cursor: not-allowed;
    }
    .menu-item-icon {
      font-size: 22px;
      line-height: 22px;
    }
    .menu-item-icon,
    .menu-item-text {
      z-index: 1;
      // color: theme("colors.secondar-text");
    }
    &.active {
      &::after {
        // height: 100%;
        background: @primary-color;
      }
      .menu-item-icon,
      .menu-item-text {
        // color: @primary-color;
        z-index: 1;
        color: @primary-white;
      }
    }
  }
}
.font-zie {
  font-size: 17px;
  font-weight: 600;
}
</style>
<style lang="less">
.header-account-dropdown {
  .ant-dropdown-menu {
    margin-top: 14px;
  }
  .ant-dropdown-menu-item,
  .ant-dropdown-menu-submenu-title {
    padding: 12px;
  }
  .ant-dropdown-menu-submenu-title > span > .anticon:first-child {
    font-size: 20px;
  }
}
.element {
  height: 100%;
  overflow: auto; /* 内容超出时显示滚动条 */
  // scrollbar-width: none; /* Firefox */
  // -ms-overflow-style: none; /* Internet Explorer 10+ */
}
@media (max-width: 600px) {
  .element {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
  }
}
/* 适用于WebKit和Blink引擎的浏览器 */
.element::-webkit-scrollbar {
  // width: 2px; /* 滚动条的宽度 */
  height: 5px;
}

/* 滚动条的轨道 */
::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条轨道的背景色 */
}

/* 滚动条的滑块 */
::-webkit-scrollbar-thumb {
  background: rgba(178, 175, 175, 0.2); /* 滑块拖动时的颜色 */
  border-radius: 10px; /* 滑块边角的圆角 */
}

/* 滑块在拖动时的样式 */
::-webkit-scrollbar-thumb:active {
  background: #c6c3c3; /* 滑块拖动时的颜色 */
}

/* 滚动条两端的按钮（通常不可见） */
::-webkit-scrollbar-button {
  display: none; /* 隐藏滚动条两端的按钮 */
}

/* 滚动条两端的角落（通常不可见） */
::-webkit-scrollbar-corner {
  background: #fff; /* 滚动条角落的背景色 */
}
</style>
