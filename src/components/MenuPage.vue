<template>
  <!-- <div style="display: inline-block"> -->
  <div class="menu-page content-area">
    <template v-if="!hide">
      <div class="menu-pageScroll">
        <p class="my-4 subMenusL1" v-if="subMenus.length == 1">
          {{ subMenus[0].meta?.title }}
        </p>
        <a-menu
          v-else
          v-model:selectedKeys="current"
          mode="horizontal"
          @click="goto"
        >
          <a-menu-item
            v-for="menu in subMenus"
            :key="menu.name"
            :disabled="!userStore.$state.cacheObject"
            style="white-space: nowrap; overflow: visible"
          >
            {{ menu.meta?.title }}
          </a-menu-item>
        </a-menu>
      </div>
    </template>
  </div>
  <!-- </div> -->
</template>
<script setup>
import { onMounted, ref, computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { usePermissionStore } from "@/stores/permission.js";
import { useUserStore } from "@/stores/user.js";

const props = defineProps({
  menus: {
    type: Array,
    default: () => [],
  },
  hide: {
    type: Boolean,
    default: false,
  },
});

const store = usePermissionStore(),
  userStore = useUserStore();
const subMenus = computed(() => {
  return store.sidebarRouters.filter((s) => !s.hidden);
});
const route = useRoute(),
  router = useRouter();
const goto = ({ key }) => {
  console.log("[ key ] >", key);
  router.push({ name: key });
};
const current = ref([]);
watch(
  () => route.name,
  (val) => {
    current.value = [val];
  },
  { immediate: true }
);
// console.log(route.name);
// onMounted(() => {
//   current.value.push(route.name);
// });
</script>
<style lang="less" scoped>
.menu-page {
  overflow: hidden;
  // -webkit-overflow-scrolling: touch; /* 为 iOS 启用流畅滚动 */
  :deep(.ant-menu-horizontal) {
    // background: @body-background; //新样式去掉原来组员设置的灰色
    width: 100%;
    border-bottom: none;
    padding: 0.6rem 1rem 0.2rem 1rem;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    line-height: 32px;
    .ant-menu-title-content {
      height: 32px;
      line-height: 32px;
      color: #333;
    }
    & > .ant-menu-item-selected {
      font-weight: bold;
      .ant-menu-title-content {
        text-shadow: 0 0 0.25px currentColor;
        color: @primary-color;
      }
    }
    &:not(.ant-menu-dark) > .ant-menu-item:hover::after,
    &:not(.ant-menu-dark) > .ant-menu-submenu:hover::after,
    &:not(.ant-menu-dark) > .ant-menu-item-active::after,
    &:not(.ant-menu-dark) > .ant-menu-submenu-active::after,
    &:not(.ant-menu-dark) > .ant-menu-item-open::after,
    &:not(.ant-menu-dark) > .ant-menu-submenu-open::after,
    &:not(.ant-menu-dark) > .ant-menu-item-selected::after,
    &:not(.ant-menu-dark) > .ant-menu-submenu-selected::after {
      border-bottom: 3px solid @primary-color;
    }
    & > .ant-menu-item::after,
    & > .ant-menu-submenu::after {
      border-bottom: 3px solid transparent;
    }

    & > .ant-menu-item::after,
    & > .ant-menu-submenu::after {
      width: 32px;
      transform: translateX(-50%);
      left: 50%;
      right: auto;
    }
    &:not(.ant-menu-dark) {
      & > .ant-menu-item {
        padding: 0;
        & + .ant-menu-item {
          margin-left: 28px;
        }
      }
    }
  }
}
.menu-pageScroll {
  overflow: auto;
  background: #fff;
  // display: inline-flex;
  margin-top: 1rem;
  min-width: 50vw;
}
@media (max-width: 600px) {
  .menu-pageScroll {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
  }
}
/* 适用于WebKit和Blink引擎的浏览器 */
.menu-pageScroll ::-webkit-scrollbar {
  // width: 2px; /* 滚动条的宽度 */
  height: 4px;
}

/* 滚动条的轨道 */
.menu-pageScroll ::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条轨道的背景色 */
}

/* 滚动条的滑块 */
.menu-pageScroll ::-webkit-scrollbar-thumb {
  background: rgba(178, 175, 175, 0.2); /* 滑块拖动时的颜色 */
  border-radius: 10px; /* 滑块边角的圆角 */
}

/* 滑块在拖动时的样式 */
.menu-pageScroll ::-webkit-scrollbar-thumb:active {
  background: #c6c3c3; /* 滑块拖动时的颜色 */
}

/* 滚动条两端的按钮（通常不可见） */
.menu-pageScroll ::-webkit-scrollbar-button {
  display: none; /* 隐藏滚动条两端的按钮 */
}

/* 滚动条两端的角落（通常不可见） */
.menu-pageScroll ::-webkit-scrollbar-corner {
  background: #fff; /* 滚动条角落的背景色 */
}
.subMenusL1 {
  background: #fff;
  padding: 0 16px;
  margin-bottom: 0;
}
</style>
