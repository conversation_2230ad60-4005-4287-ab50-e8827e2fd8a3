<template>
  <a-button :shape="shape" :type="type" :loading="loading">
    <div class="flex" v-if="font">
      <i :class="font" v-if="font"></i>
      {{ title }}
      <slot></slot>
    </div>
    <span v-if="!font">
      <i :class="font" v-if="font"></i>
      {{ title }}
      <slot></slot>
    </span>
  </a-button>
</template>
<script setup>
import { defineProps, defineEmits } from "vue";

const props = defineProps({
  shape: {
    type: String,
    default: "round",
  },
  type: {
    type: String,
    default: "primary",
  },
  font: {
    type: String,
    default: "",
  },
  title: {
    type: String,
    default: "",
  },
  loading: {
    type: Boolean,
    default: false,
  },
});
</script>

<style lang="scss" scoped></style>
