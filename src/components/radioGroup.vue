<template>
  <a-radio-group button-style="solid" @change="handleChange">
    <a-radio-button
      v-for="option in options"
      :key="option.value"
      :value="option.value"
      :disabled="radioDisabled"
    >
      {{ option.label }}
    </a-radio-button>
  </a-radio-group>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: undefined,
  },
  options: {
    type: Array,
    default: () => [],
  },
  radioDisabled: { type: Boolean, default: false },
});

const emit = defineEmits(["update:modelValue"]);

const handleChange = (event) => {
  emit("update:modelValue", event.target.value);
};
</script>
<style lang="less" scoped>
.ant-form label {
  border-radius: 30px;
  background: #f5f5f5;
  border: 0;
}
.ant-radio-button-wrapper:first-child {
  border-radius: 30px;
}
.ant-radio-group {
  background: #f5f5f5;
  padding: 3px;
  border-radius: 30px;
}
.ant-radio-button-wrapper:not(:first-child)::before {
  width: 0;
}
.ant-radio-button-wrapper-disabled.ant-radio-button-wrapper-checked {
  background-color: rgba(0, 0, 0, 0.15) !important;
  border-color: #d9d9d9 !important;
}
</style>
