<template>
  <a-drawer
    class="mw-drawer"
    width="486"
    placement="right"
    :closable="false"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <a-spin :spinning="spinning">
      <header class="border-b border-d9 px-5 flex justify-between items-center">
        <span class="font-medium"
          >{{ customTitle }}
          <slot name="tag"></slot>
        </span>
        <a-space>
          <a-button
            shape="round"
            @click="$attrs.onClose"
            type="primary"
            ghost
            >{{ closeText }}</a-button
          >
          <slot name="header"></slot>
        </a-space>
      </header>
      <!-- {{ JSON.stringify({ ...$attrs, title: "标题" }) }} -->
      <!-- <a-drawer v-bind="$attrs" width="486" :maskClosable="false"> -->
      <div
        id="content"
        class="px-5 my-5"
        style="height: calc(100vh - 54px - 40px); overflow: auto"
      >
        <slot></slot>
      </div>
    </a-spin>
  </a-drawer>
</template>
<script>
export default {
  props: {
    customTitle: String,
    closeText: {
      type: String,
      default: "关闭",
    },
    spinning: {
      type: Boolean,
      default: false,
    },
  },
  setup() {},
};
</script>
<style lang="less" scoped>
.mw-drawer {
  header {
    height: 54px;
    line-height: 54px;
  }
}
:deep(.ant-btn[disabled]) {
  background: #f5f5f5;
}
</style>
<style lang="less">
.mw-drawer {
  .ant-drawer-body {
    padding: 0;
  }
}
</style>

<style lang="less">
// .mw-drawer {
//   .ant-space-item {
//     font-size: 0;
//   }
// }
</style>
