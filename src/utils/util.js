import { dicType } from "@/api/system/dict.js";
import dayjs from "dayjs";
// 获取当天时间
export function dateFormat(val, format = "YYYY-MM-DD HH:mm:ss") {
  return dayjs(val).format(format);
}
// 当天之前的不可选（包括当天）,
export function disabledDateConThenBefore(current) {
  return current && current < dayjs().endOf("day");
}
// 当天之前的不可选（不包括当天）,
export function disabledDateNoConThenBefore(current) {
  return current && current < dayjs().subtract(1, "days").endOf("day");
}
// 当天之后不可以选（包括当天）,
export function disabledDateConThenAFter(current) {
  return current && current > dayjs().endOf("day");
}
// 当天之后不可以选（不包括当天）,
export function disabledDateNoConThenAFter(current) {
  return current && current > dayjs().subtract(1, "days").endOf("day");
}
export async function getDicByType(type, name) {
  let result = await dicType(type);
  let dics = result.data?.map((item) => {
    return { label: item.dictLabel, value: item.dictValue };
  });
  return {
    dics,
    dicFilters: [{ label: "全部" + name, value: "" }, ...(dics || [])],
  };
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, parentId, children) {
  let config = {
    id: id || "id",
    parentId: parentId || "parentId",
    childrenList: children || "children",
  };

  var childrenListMap = {};
  var nodeIds = {};
  var tree = [];

  for (let d of data) {
    let parentId = d[config.parentId];
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = [];
    }
    nodeIds[d[config.id]] = d;
    childrenListMap[parentId].push(d);
  }

  for (let d of data) {
    let parentId = d[config.parentId];
    if (nodeIds[parentId] == null) {
      tree.push(d);
    }
  }

  for (let t of tree) {
    adaptToChildrenList(t);
  }

  function adaptToChildrenList(o) {
    if (childrenListMap[o[config.id]] !== null) {
      o[config.childrenList] = childrenListMap[o[config.id]];
    }
    if (o[config.childrenList]) {
      for (let c of o[config.childrenList]) {
        adaptToChildrenList(c);
      }
    }
  }
  return tree;
}
// 转换字符串，undefined,null等转化为""
export function parseStrEmpty(str) {
  if (!str || str == "undefined" || str == "null") {
    return "";
  }
  return str;
}

export function uniqueArr(oldArr, newArr) {
  // 获取 oldArr 中的所有 id 值，并保存到一个新的 Set 对象中
  const idSet = new Set(oldArr.map((item) => item.id));
  // 使用 filter() 方法和 some() 方法过滤掉 newArr 中与 oldArr 中重复的项
  const resultArr = newArr.filter((item) => !idSet.has(item.id));
  return [...resultArr, ...oldArr];
}

export function exportExecl(fileName, result) {
  // 此处当返回json文件时需要先对data进行JSON.stringify处理，其他类型文件不用做处理
  const blob = new Blob([result], {
    type: result.type,
  });
  let dom = document.createElement("a");
  let url = window.URL.createObjectURL(blob);
  dom.href = url;
  dom.download = decodeURI(fileName);
  dom.style.display = "none";
  document.body.appendChild(dom);
  dom.click();
  dom.parentNode.removeChild(dom);
  window.URL.revokeObjectURL(url);
}

export function dingTalkLoginApi() {
  return new Promise(function (resolve, reject) {
    const tag = document.getElementsByTagName("script");
    for (let i of tag) {
      if (
        i.src ===
        "https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js"
      ) {
        resolve(window.DTFrameLogin);
        return;
      }
    }
    const script = document.createElement("script");
    script.type = "text/javascript";
    script.src =
      "https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js";
    script.onerror = reject;
    document.body.appendChild(script);
    script.onload = () => {
      resolve(window.DTFrameLogin);
    };
  });
}
