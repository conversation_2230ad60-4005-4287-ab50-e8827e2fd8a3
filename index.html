<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>明我智造</title>
  <!-- <script src='https://g.alicdn.com/code/npm/@ali/dingtalk-h5-remote-debug/0.1.3/index.js'></script> -->
</head>
<!-- class="overflow-y-scroll" 存在会将头部两边间距不对称，-->

<body>
  <div id="app"></div>
  <script type="module" src="/src/main.js"></script>
  <!-- <link rel="stylesheet" crossorigin href="https://g.alicdn.com/aliyun-documentation/web-chatbot-ui/0.0.24/index.css" /> -->
  <!-- <script type="module" crossorigin
    src="https://g.alicdn.com/aliyun-documentation/web-chatbot-ui/0.0.24/index.js"></script> -->
  <script>
    console.log(window)
    // window.CHATBOT_CONFIG = {
      // endpoint: "https://webchat-bot-fim-jktrdusadm.cn-hangzhou.fcapp.run/chat", // 可以替换为 https://{your-fc-http-trigger-domain}/chat
      // displayByDefault: false, // 默认不显示 AI 助手对话框
      // title: '福瑞助手', // 自定义 AI 助手标题
      // draggable: true, // 是否开启拖拽
      // aiChatOptions: { // 自定义取值参考：https://docs.nlkit.com/nlux/reference/ui/ai-chat#conversation-options

      //   conversationOptions: { // 自定义取值参考：https://docs.nlkit.com/nlux/reference/ui/ai-chat#conversation-options
      //     layout: "bubbles",
      //     conversationStarters: [
      //       { prompt: '预测一下磷酸铁锂未来半年的价格趋势？' },
      //       { prompt: '福瑞集团2月份采购了多少种物料？' },
      //       { prompt: '减速器市场行情?' },
      //     ]
      //   },
      //   displayOptions: { // 自定义取值参考：https://docs.nlkit.com/nlux/reference/ui/ai-chat#display-options
      //     height: 600,
      //     // width: 400,
      //   },
      //   personaOptions: { // 自定义取值参考：https://docs.nlkit.com/nlux/reference/ui/ai-chat#chat-personas
      //     assistant: {
      //       name: '你好，我是你的 AI 助手',
      //       // AI 助手的图标
      //       avatar: 'https://img.alicdn.com/imgextra/i2/O1CN01Pda9nq1YDV0mnZ31H_!!6000000003025-54-tps-120-120.apng',
      //       tagline: '您可以尝试点击下方的快捷入口开启体验！',
      //     }
      //   },
      //   messageOptions: { // 自定义取值参考：https://docs.nlkit.com/nlux/reference/ui/ai-chat#message-options
      //     waitTimeBeforeStreamCompletion: 'always'
      //   }
      // },
      // dataProcessor: {
      //   /**
      //   * 在向后端大模型应用发起请求前改写 Prompt。
      //   * 比如可以用于总结网页场景，在发送前将网页内容包含在内，同时避免在前端显示这些内容。
      //   * @param {string} prompt - 用户输入的 Prompt
      //   * @param {string}  - 改写后的 Prompt
      //   */
      //   rewritePrompt(prompt) {
      //     return prompt;
      //   }
      // }
    // };
  </script>

  <style>
    :root {
      /* webchat 工具栏的颜色 */
      --webchat-toolbar-background-color: #1464E4;
      /* webchat 工具栏文字和按钮的颜色 */
      --webchat-toolbar-text-color: #FFF;
    }

    /* webchat 对话框如果被遮挡，可以尝试通过 z-index、bottom、right 等设置来调整位置 */
    .webchat-container {
      z-index: 100;
      bottom: 40px;
      right: 40px;
    }

    /* webchat 的唤起按钮如果被遮挡，可以尝试通过 z-index、bottom、right 等设置来调整位置 */
    .webchat-bubble-tip {
      z-index: 99;
      bottom: 20px;
      right: 20px;
    }
  </style>
</body>

</html>