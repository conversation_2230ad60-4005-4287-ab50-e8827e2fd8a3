{"name": "digitalize", "version": "0.0.0", "private": true, "scripts": {"start": "vite --host 0.0.0.0", "dev": "vite", "build:beta": "vite build --mode test", "build:stage1": "vite build --mode stage1", "build": "vite build", "preview": "vite preview", "test:unit": "vitest", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@tailwindcss/postcss7-compat": "^2.2.17", "ant-design-vue": "^4.2.5", "autoprefixer": "^9.8.8", "axios": "^1.4.0", "clipboard": "^2.0.11", "dayjs": "^1.11.8", "decimal.js": "^10.4.3", "dingtalk-jsapi": "^3.0.25", "dotenv": "^16.1.4", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "less": "^4.1.3", "less-loader": "^11.1.2", "nprogress": "^0.2.0", "pinia": "^2.0.36", "postcss": "^7.0.39", "print-js": "^1.6.0", "qrcode": "^1.5.3", "qrcodejs": "^1.0.0", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "vue": "^3.3.2", "vue-router": "^4.2.0", "vue-signature-pad": "^3.0.2"}, "devDependencies": {"@rushstack/eslint-patch": "^1.2.0", "@types/node": "^22.7.4", "@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-prettier": "^7.1.0", "@vue/test-utils": "^2.3.2", "eslint": "^8.39.0", "eslint-plugin-vue": "^9.11.0", "jsdom": "^22.0.0", "prettier": "^2.8.8", "vite": "^4.3.5", "vitest": "^0.31.0"}}